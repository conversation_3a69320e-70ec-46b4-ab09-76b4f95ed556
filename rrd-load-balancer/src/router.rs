use sha2::{Digest, Sha256};
use std::collections::BTreeMap;

/// Consistent hash router for distributing requests across rrd-service instances
#[derive(Debug)]
pub struct ConsistentHashRouter {
    /// Virtual nodes mapping hash values to instance indices
    ring: BTreeMap<u64, usize>,
    /// Base number of virtual nodes per physical instance (for better distribution)
    base_virtual_nodes: usize,
    /// Number of actual instances
    instance_count: usize,
    /// Instance weights for weighted consistent hashing
    instance_weights: Vec<u32>,
}

impl ConsistentHashRouter {
    /// Create a new consistent hash router with equal weights
    pub fn new(instance_count: usize) -> Self {
        let weights = vec![1; instance_count]; // Default weight of 1 for all instances
        Self::new_with_weights(weights)
    }

    /// Create a new consistent hash router with custom weights
    pub fn new_with_weights(weights: Vec<u32>) -> Self {
        let instance_count = weights.len();
        let base_virtual_nodes = 150; // Standard value for good distribution
        let mut router = Self {
            ring: BTreeMap::new(),
            base_virtual_nodes,
            instance_count,
            instance_weights: weights,
        };

        router.build_ring();
        router
    }

    /// Build the consistent hash ring
    fn build_ring(&mut self) {
        self.ring.clear();

        // Calculate total weight, ensure it's not zero
        let total_weight: u32 = self.instance_weights.iter().sum();
        if total_weight == 0 {
            log::warn!("Total weight is zero, using equal weights");
            // Fallback to equal weights
            for instance_index in 0..self.instance_count {
                for virtual_node in 0..self.base_virtual_nodes {
                    let key = format!("instance-{}-{}", instance_index, virtual_node);
                    let hash = self.hash_key(&key);
                    self.ring.insert(hash, instance_index);
                }
            }
            return;
        }

        let mut total_virtual_nodes = 0;
        for instance_index in 0..self.instance_count {
            let weight = self.instance_weights[instance_index];
            // Calculate virtual nodes based on weight ratio
            let virtual_nodes_count = (self.base_virtual_nodes as f64 * weight as f64 / total_weight as f64).round() as usize;
            let actual_virtual_nodes = virtual_nodes_count.max(1); // Ensure at least 1 virtual node

            for virtual_node in 0..actual_virtual_nodes {
                let key = format!("instance-{}-{}", instance_index, virtual_node);
                let hash = self.hash_key(&key);
                self.ring.insert(hash, instance_index);
            }
            total_virtual_nodes += actual_virtual_nodes;

            log::debug!(
                "Instance {} (weight: {}) assigned {} virtual nodes",
                instance_index,
                weight,
                actual_virtual_nodes
            );
        }

        log::info!(
            "Built weighted consistent hash ring with {} instances and {} total virtual nodes",
            self.instance_count,
            total_virtual_nodes
        );
    }

    /// Hash a key to a 64-bit value
    fn hash_key(&self, key: &str) -> u64 {
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let result = hasher.finalize();

        // Take first 8 bytes and convert to u64
        let mut bytes = [0u8; 8];
        bytes.copy_from_slice(&result[0..8]);
        u64::from_be_bytes(bytes)
    }

    /// Route a request based on server_id and config_id to get instance index
    pub fn route(&self, server_id: i32, config_id: i32) -> usize {
        let routing_key = format!("{}:{}", server_id, config_id);
        let hash = self.hash_key(&routing_key);

        // Find the first instance with hash >= our hash
        let instance_index = self
            .ring
            .range(hash..)
            .next()
            .or_else(|| self.ring.iter().next()) // Wrap around to the beginning
            .map(|(_, &instance_index)| instance_index)
            .unwrap_or(0); // Fallback to first instance

        log::debug!(
            "Routing {}:{} (hash: {}) to instance {}",
            server_id,
            config_id,
            hash,
            instance_index
        );

        instance_index
    }

    /// Update the router when instances change (for future use)
    pub fn update_instances(&mut self, new_instance_count: usize) {
        if new_instance_count != self.instance_count {
            log::info!(
                "Updating consistent hash router from {} to {} instances",
                self.instance_count,
                new_instance_count
            );
            self.instance_count = new_instance_count;
            // Reset weights to default (all equal)
            self.instance_weights = vec![1; new_instance_count];
            self.build_ring();
        }
    }

    /// Update the router with new weights
    pub fn update_weights(&mut self, new_weights: Vec<u32>) {
        if new_weights.len() != self.instance_count {
            log::error!(
                "Weight count {} does not match instance count {}",
                new_weights.len(),
                self.instance_count
            );
            return;
        }

        if new_weights != self.instance_weights {
            log::info!("Updating instance weights: {:?}", new_weights);
            self.instance_weights = new_weights;
            self.build_ring();
        }
    }

    /// Get statistics about the router
    pub fn get_stats(&self) -> RouterStats {
        RouterStats {
            instance_count: self.instance_count,
            base_virtual_nodes: self.base_virtual_nodes,
            total_virtual_nodes: self.ring.len(),
            instance_weights: self.instance_weights.clone(),
        }
    }
}

#[derive(Debug, serde::Serialize)]
pub struct RouterStats {
    pub instance_count: usize,
    pub base_virtual_nodes: usize,
    pub total_virtual_nodes: usize,
    pub instance_weights: Vec<u32>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_consistent_routing() {
        let router = ConsistentHashRouter::new(4);

        // Test that same server_id + config_id always routes to same instance
        let instance1 = router.route(1, 100);
        let instance2 = router.route(1, 100);
        assert_eq!(instance1, instance2);

        // Test that different combinations can route to different instances
        let mut distribution = HashMap::new();
        for server_id in 1..=10 {
            for config_id in 1..=10 {
                let instance = router.route(server_id, config_id);
                *distribution.entry(instance).or_insert(0) += 1;
            }
        }

        // Should distribute across multiple instances (not all to one)
        assert!(distribution.len() > 1);
        println!("Distribution: {:?}", distribution);
    }

    #[test]
    fn test_weighted_routing() {
        // Test weighted distribution
        let weights = vec![1, 2, 3, 4]; // Different weights
        let router = ConsistentHashRouter::new_with_weights(weights);

        let mut distribution = HashMap::new();
        for server_id in 1..=100 {
            for config_id in 1..=100 {
                let instance = router.route(server_id, config_id);
                *distribution.entry(instance).or_insert(0) += 1;
            }
        }

        println!("Weighted distribution: {:?}", distribution);
        
        // Instance 3 (weight 4) should get more requests than instance 0 (weight 1)
        let instance_0_count = distribution.get(&0).unwrap_or(&0);
        let instance_3_count = distribution.get(&3).unwrap_or(&0);
        assert!(instance_3_count > instance_0_count);
    }

    #[test]
    fn test_weight_update() {
        let initial_weights = vec![1, 1, 1];
        let mut router = ConsistentHashRouter::new_with_weights(initial_weights);

        // Test initial equal distribution
        let mut initial_distribution = HashMap::new();
        for i in 1..=300 {
            let instance = router.route(i, i);
            *initial_distribution.entry(instance).or_insert(0) += 1;
        }

        // Update weights to give instance 0 more load
        let new_weights = vec![5, 1, 1];
        router.update_weights(new_weights);

        // Test new distribution
        let mut new_distribution = HashMap::new();
        for i in 1..=300 {
            let instance = router.route(i, i);
            *new_distribution.entry(instance).or_insert(0) += 1;
        }

        println!("Initial distribution: {:?}", initial_distribution);
        println!("New weighted distribution: {:?}", new_distribution);

        // Instance 0 should now get significantly more requests
        let instance_0_new = new_distribution.get(&0).unwrap_or(&0);
        let instance_1_new = new_distribution.get(&1).unwrap_or(&0);
        assert!(instance_0_new > instance_1_new);
    }

    #[test]
    fn test_zero_weight_handling() {
        // Test that zero weights are not allowed in build_ring
        let weights = vec![0, 1, 2];
        let router = ConsistentHashRouter::new_with_weights(weights);
        
        // Ring should be empty due to zero total weight being handled
        // In our implementation, we should have at least some nodes even with zero weight
        assert!(!router.ring.is_empty());
    }

    #[test]
    fn test_router_consistency_after_rebuild() {
        let mut router = ConsistentHashRouter::new(3);

        // Record initial routing
        let mut initial_routes = HashMap::new();
        for server_id in 1..=5 {
            for config_id in 1..=5 {
                let instance = router.route(server_id, config_id);
                initial_routes.insert((server_id, config_id), instance);
            }
        }

        // Update with same instance count (should be the same)
        router.update_instances(3);

        // Verify routing is consistent
        for ((server_id, config_id), expected_instance) in initial_routes {
            let actual_instance = router.route(server_id, config_id);
            assert_eq!(expected_instance, actual_instance);
        }
    }

    #[test]
    fn test_instance_bounds() {
        let router = ConsistentHashRouter::new(4);

        for server_id in 1..=100 {
            for config_id in 1..=100 {
                let instance = router.route(server_id, config_id);
                assert!(instance < 4, "Instance index {} out of bounds", instance);
            }
        }
    }
}
