use crate::{
    config::Config,
    instance::{Instance, InstanceConfig, InstanceStatus, LocalInstance, RemoteInstance},
};
use anyhow::Result;
use std::{
    collections::HashMap,
    sync::{Arc, Mutex},
    time::Duration,
};
use tokio::time;

/// 通用实例管理器
pub struct InstanceManager {
    config: Arc<Config>,
    instances: Arc<Mutex<HashMap<usize, Box<dyn Instance>>>>,
    client: reqwest::Client,
}

impl InstanceManager {
    /// 创建新的实例管理器
    pub fn new(config: Arc<Config>) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .expect("Failed to create HTTP client");

        Ok(Self {
            config,
            instances: Arc::new(Mutex::new(HashMap::new())),
            client,
        })
    }

    /// 启动所有实例
    pub async fn start_all_instances(&self) -> Result<()> {
        let instances_config = self.config.get_instances_config()?;
        log::info!("Starting {} instances", instances_config.len());

        for (index, instance_config) in instances_config.into_iter().enumerate() {
            self.start_instance(index, instance_config).await?;
        }

        // 等待实例启动
        time::sleep(Duration::from_secs(2)).await;

        // 初始健康检查
        self.check_all_health().await;

        Ok(())
    }

    /// 启动单个实例
    async fn start_instance(&self, index: usize, instance_config: InstanceConfig) -> Result<()> {
        let mut instance: Box<dyn Instance> = match &instance_config {
            InstanceConfig::Local { .. } => Box::new(LocalInstance::new(
                index,
                instance_config,
                self.config.log_level.clone(),
            )?),
            InstanceConfig::Remote { .. } => Box::new(RemoteInstance::new(index, instance_config)?),
        };

        instance.start().await?;

        {
            let mut instances = self.instances.lock().unwrap();
            instances.insert(index, instance);
        }

        Ok(())
    }

    /// 停止所有实例
    pub async fn stop_all_instances(&self) -> Result<()> {
        log::info!("Stopping all instances");

        // 获取所有实例的索引
        let indices: Vec<usize> = {
            let instances = self.instances.lock().unwrap();
            instances.keys().copied().collect()
        };

        // 逐个移除并停止实例
        for index in indices {
            let mut instance_opt = {
                let mut instances = self.instances.lock().unwrap();
                instances.remove(&index)
            };

            if let Some(ref mut instance) = instance_opt {
                log::info!("Stopping instance {}", index);
                if let Err(e) = instance.stop().await {
                    log::warn!("Failed to stop instance {}: {}", index, e);
                }
            }
        }

        Ok(())
    }

    /// 检查所有实例健康状态
    async fn check_all_health(&self) {
        let instance_infos: Vec<(usize, String)> = {
            let instances = self.instances.lock().unwrap();
            instances
                .iter()
                .map(|(&index, instance)| (index, instance.get_url()))
                .collect()
        };

        for (index, url) in instance_infos {
            // 在 await 之前释放锁
            let is_healthy = self.check_instance_health(&url).await;

            {
                let mut instances = self.instances.lock().unwrap();
                if let Some(instance) = instances.get_mut(&index) {
                    instance.set_healthy(is_healthy);
                }
            }

            if is_healthy {
                log::debug!("Instance {} is healthy", index);
            } else {
                log::warn!("Instance {} is unhealthy", index);
            }
        }
    }

    /// 检查实例健康状态（不持有锁）
    async fn check_instance_health(&self, url: &str) -> bool {
        match self
            .client
            .get(&format!("{}/health", url))
            .timeout(Duration::from_secs(10))
            .send()
            .await
        {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }

    /// 重启不健康的实例
    async fn restart_instance(&self, index: usize) -> Result<()> {
        log::warn!("Restarting unhealthy instance {}", index);

        let instance_config = {
            let mut instances = self.instances.lock().unwrap();
            if let Some(instance) = instances.get_mut(&index) {
                instance.increment_restart_count();
                if let Err(e) = instance.stop().await {
                    log::warn!("Failed to stop instance {}: {}", index, e);
                }

                let status = instance.get_status();
                instances.remove(&index);
                status.config
            } else {
                return Err(anyhow::anyhow!("Instance {} not found", index));
            }
        };

        // 等待一秒后重启
        time::sleep(Duration::from_secs(1)).await;
        self.start_instance(index, instance_config).await?;

        log::info!("Restarted instance {}", index);
        Ok(())
    }

    /// 启动健康监控任务
    pub fn start_health_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let manager = InstanceManagerHandle {
            config: Arc::clone(&self.config),
            instances: Arc::clone(&self.instances),
            client: self.client.clone(),
        };

        tokio::spawn(async move {
            let mut interval =
                time::interval(Duration::from_secs(manager.config.health_check_interval));

            loop {
                interval.tick().await;
                manager.health_check_cycle().await;
            }
        })
    }

    /// 获取实例信息
    pub fn get_instance_info(&self, index: usize) -> Option<(String, bool)> {
        let instances = self.instances.lock().unwrap();
        instances
            .get(&index)
            .map(|instance| (instance.get_url(), instance.is_healthy()))
    }

    /// 获取所有健康实例
    pub fn get_healthy_instances(&self) -> Vec<usize> {
        let instances = self.instances.lock().unwrap();
        instances
            .iter()
            .filter(|(_, instance)| instance.is_healthy())
            .map(|(&index, _)| index)
            .collect()
    }

    /// 获取所有实例状态
    pub fn get_status(&self) -> Vec<InstanceStatus> {
        let instances = self.instances.lock().unwrap();
        instances
            .iter()
            .map(|(&_index, instance)| instance.get_status())
            .collect()
    }

    /// 获取实例总数
    pub fn get_instance_count(&self) -> usize {
        let instances = self.instances.lock().unwrap();
        instances.len()
    }
}

/// 健康监控任务句柄
struct InstanceManagerHandle {
    config: Arc<Config>,
    instances: Arc<Mutex<HashMap<usize, Box<dyn Instance>>>>,
    client: reqwest::Client,
}

impl InstanceManagerHandle {
    async fn health_check_cycle(&self) {
        log::debug!("Running health check cycle");

        let unhealthy_instances = self.identify_unhealthy_instances().await;

        for index in unhealthy_instances {
            if let Err(e) = self.restart_instance(index).await {
                log::error!("Failed to restart instance {}: {}", index, e);
            }
        }
    }

    async fn identify_unhealthy_instances(&self) -> Vec<usize> {
        let instance_infos: Vec<(usize, String)> = {
            let instances = self.instances.lock().unwrap();
            instances
                .iter()
                .map(|(&index, instance)| (index, instance.get_url()))
                .collect()
        };

        let mut unhealthy = Vec::new();

        for (index, _url) in instance_infos {
            // 获取实例URL，避免在 await 期间持有锁
            let health_url = {
                let instances = self.instances.lock().unwrap();
                if let Some(instance) = instances.get(&index) {
                    Some(format!("{}/health", instance.get_url()))
                } else {
                    None
                }
            };

            let is_healthy = if let Some(url) = health_url {
                match self
                    .client
                    .get(&url)
                    .timeout(Duration::from_secs(10))
                    .send()
                    .await
                {
                    Ok(response) => response.status().is_success(),
                    Err(_) => false,
                }
            } else {
                false
            };

            {
                let mut instances = self.instances.lock().unwrap();
                if let Some(instance) = instances.get_mut(&index) {
                    let was_healthy = instance.is_healthy();
                    instance.set_healthy(is_healthy);

                    if was_healthy && !is_healthy {
                        log::warn!("Instance {} became unhealthy", index);
                    } else if !was_healthy && is_healthy {
                        log::info!("Instance {} recovered", index);
                    }
                }
            }

            if !is_healthy {
                unhealthy.push(index);
            }
        }

        unhealthy
    }

    async fn restart_instance(&self, index: usize) -> Result<()> {
        // 只重启本地实例，远程实例不需要重启
        let should_restart = {
            let instances = self.instances.lock().unwrap();
            if let Some(instance) = instances.get(&index) {
                let status = instance.get_status();
                matches!(status.config, InstanceConfig::Local { .. })
            } else {
                false
            }
        };

        if !should_restart {
            log::debug!("Skipping restart for remote instance {}", index);
            return Ok(());
        }

        log::warn!("Restarting unhealthy local instance {}", index);

        // 先停止实例，再获取配置
        let instance_config = {
            // 获取实例并从 map 中移除
            let instance_opt = {
                let mut instances = self.instances.lock().unwrap();
                if let Some(mut instance) = instances.remove(&index) {
                    instance.increment_restart_count();
                    let config = instance.get_status().config;
                    Some((instance, config))
                } else {
                    None
                }
            };

            if let Some((mut instance, config)) = instance_opt {
                // 在锁外停止实例
                if let Err(e) = instance.stop().await {
                    log::warn!("Failed to stop instance {}: {}", index, e);
                }
                config
            } else {
                return Err(anyhow::anyhow!("Instance {} not found", index));
            }
        };

        // 等待一秒后重启
        time::sleep(Duration::from_secs(1)).await;
        self.start_new_instance(index, instance_config).await?;

        log::info!("Restarted instance {}", index);
        Ok(())
    }

    async fn start_new_instance(
        &self,
        index: usize,
        instance_config: InstanceConfig,
    ) -> Result<()> {
        let mut instance: Box<dyn Instance> = match &instance_config {
            InstanceConfig::Local { .. } => Box::new(LocalInstance::new(
                index,
                instance_config,
                self.config.log_level.clone(),
            )?),
            InstanceConfig::Remote { .. } => Box::new(RemoteInstance::new(index, instance_config)?),
        };

        instance.start().await?;

        {
            let mut instances = self.instances.lock().unwrap();
            instances.insert(index, instance);
        }

        Ok(())
    }
}

impl Drop for InstanceManager {
    fn drop(&mut self) {
        // 清理关闭所有实例
        if let Ok(runtime) = tokio::runtime::Runtime::new() {
            runtime.block_on(async {
                // 获取所有实例的索引
                let indices: Vec<usize> = {
                    let instances = self.instances.lock().unwrap();
                    instances.keys().copied().collect()
                };

                // 逐个停止实例
                for index in indices {
                    let mut instance_opt = {
                        let mut instances = self.instances.lock().unwrap();
                        instances.remove(&index)
                    };

                    if let Some(ref mut instance) = instance_opt {
                        log::info!("Shutting down instance {} on drop", index);
                        if let Err(e) = instance.stop().await {
                            log::warn!("Failed to stop instance {} on drop: {}", index, e);
                        }
                    }
                }
            });
        } else {
            log::warn!("Failed to create tokio runtime for cleanup");
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    fn create_test_config() -> Config {
        Config {
            instance_count: 2,
            start_port: 8083,
            rrd_service_binary: PathBuf::from("/usr/bin/echo"), // 使用 echo 作为测试
            rrd_data_path: PathBuf::from("/tmp"),
            instances_config: None,
            bind_address: "127.0.0.1".to_string(),
            port: 8082,
            log_level: "debug".to_string(),
            health_check_interval: 5,
            request_timeout: 30,
        }
    }

    #[tokio::test]
    async fn test_instance_manager_creation() {
        let config = Arc::new(create_test_config());
        let manager = InstanceManager::new(config).unwrap();
        assert_eq!(manager.get_instance_count(), 0);
    }
}
