use crate::load_balancer::LoadBalancer;
use serde::Serialize;
use std::sync::Arc;
use warp::{
    hyper::{body::Bytes, Method, Uri},
    reject::Rejection,
    Filter, Reply,
};

/// Handle all requests by proxying to appropriate rrd-service instance
pub async fn proxy_handler(
    method: Method,
    path: warp::path::FullPath,
    query: Option<String>,
    headers: warp::hyper::HeaderMap,
    body: Bytes,
    load_balancer: Arc<LoadBalancer>,
) -> Result<impl Reply, Rejection> {
    // Build URI from path and query
    let uri = if let Some(q) = query {
        format!("{}?{}", path.as_str(), q)
    } else {
        path.as_str().to_string()
    };

    let uri = uri.parse::<Uri>().unwrap_or_else(|_| Uri::from_static("/"));

    match load_balancer
        .proxy_request(
            &method.to_string(),
            uri.path(),
            uri.query().map(|q| q.to_string()),
            headers,
            body,
        )
        .await
    {
        Ok(response) => {
            let mut reply = warp::http::Response::builder().status(response.status);

            // Copy response headers
            for (name, value) in response.headers.iter() {
                reply = reply.header(name, value);
            }

            Ok(reply
                .body(response.body)
                .unwrap_or_else(|_| warp::http::Response::new(Bytes::new())))
        }
        Err(e) => {
            log::error!("Proxy error: {}", e);
            Err(warp::reject::custom(ProxyError(e.to_string())))
        }
    }
}

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    service: String,
    timestamp: chrono::DateTime<chrono::Utc>,
}

/// Health check for the load balancer itself
pub async fn health_handler() -> Result<impl Reply, Rejection> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "rrd-load-balancer".to_string(),
        timestamp: chrono::Utc::now(),
    };
    Ok(warp::reply::json(&response))
}

/// Status endpoint showing load balancer state
pub async fn status_handler(load_balancer: Arc<LoadBalancer>) -> Result<impl Reply, Rejection> {
    let status = load_balancer.get_status();
    Ok(warp::reply::json(&status))
}

/// Custom error for proxy failures
#[derive(Debug)]
struct ProxyError(String);

impl warp::reject::Reject for ProxyError {}

#[derive(Serialize)]
struct ErrorResponse {
    error: String,
    code: u16,
}

/// Handle rejections and convert to HTTP responses
pub async fn handle_rejection(err: Rejection) -> Result<impl Reply, std::convert::Infallible> {
    let code;
    let message;

    if err.is_not_found() {
        code = warp::http::StatusCode::NOT_FOUND;
        message = "Not Found";
    } else if let Some(proxy_error) = err.find::<ProxyError>() {
        log::error!("Proxy error: {}", proxy_error.0);
        code = warp::http::StatusCode::BAD_GATEWAY;
        message = "Bad Gateway";
    } else if err.find::<warp::reject::MethodNotAllowed>().is_some() {
        code = warp::http::StatusCode::METHOD_NOT_ALLOWED;
        message = "Method Not Allowed";
    } else {
        log::error!("Unhandled rejection: {:?}", err);
        code = warp::http::StatusCode::INTERNAL_SERVER_ERROR;
        message = "Internal Server Error";
    }

    let response = ErrorResponse {
        error: message.to_string(),
        code: code.as_u16(),
    };
    let json = warp::reply::json(&response);

    Ok(warp::reply::with_status(json, code))
}

/// Create load balancer filter that injects the load balancer instance
pub fn with_load_balancer(
    load_balancer: Arc<LoadBalancer>,
) -> impl warp::Filter<Extract = (Arc<LoadBalancer>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || Arc::clone(&load_balancer))
}
