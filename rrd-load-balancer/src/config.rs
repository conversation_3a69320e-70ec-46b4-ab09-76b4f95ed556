use crate::instance::InstanceConfig;
use clap::Parse<PERSON>;
use std::path::PathBuf;

#[derive(Parse<PERSON>, Debug, Clone)]
#[command(name = "rrd-load-balancer")]
#[command(about = "RRD service load balancer for horizontal scaling")]
#[command(version)]
pub struct Config {
    /// Number of rrd-service instances to spawn (deprecated, use --instances-config)
    #[arg(long, env = "RRD_INSTANCE_COUNT", default_value = "4")]
    pub instance_count: usize,

    /// Starting port for rrd-service instances (deprecated, use --instances-config)
    #[arg(long, env = "RRD_START_PORT", default_value = "8083")]
    pub start_port: u16,

    /// Path to the rrd-service binary (deprecated, use --instances-config)
    #[arg(
        long,
        env = "RRD_SERVICE_BINARY",
        default_value = "/usr/local/bin/rrd-service"
    )]
    pub rrd_service_binary: PathBuf,

    /// Path to the RRD data directory (deprecated, use --instances-config)
    #[arg(long, env = "RRD_DATA_PATH", default_value = "/data/rrd")]
    pub rrd_data_path: PathBuf,

    /// Path to instances configuration JSON file
    #[arg(long, env = "INSTANCES_CONFIG")]
    pub instances_config: Option<PathBuf>,

    /// Bind address for the load balancer HTTP server
    #[arg(long, env = "BIND_ADDRESS", default_value = "0.0.0.0")]
    pub bind_address: String,

    /// Port number for the load balancer to listen on
    #[arg(short, long, env = "PORT", default_value = "8082")]
    pub port: u16,

    /// Log level (error, warn, info, debug, trace)
    #[arg(long, env = "LOG_LEVEL", default_value = "info")]
    pub log_level: String,

    /// Health check interval in seconds
    #[arg(long, env = "HEALTH_CHECK_INTERVAL", default_value = "30")]
    pub health_check_interval: u64,

    /// Request timeout in seconds
    #[arg(long, env = "REQUEST_TIMEOUT", default_value = "60")]
    pub request_timeout: u64,
}

impl Config {
    pub fn new() -> Self {
        Self::parse()
    }

    pub fn get_bind_addr(&self) -> ([u8; 4], u16) {
        let octets: Vec<u8> = self
            .bind_address
            .split('.')
            .map(|s| s.parse().unwrap_or(0))
            .collect();

        if octets.len() == 4 {
            ([octets[0], octets[1], octets[2], octets[3]], self.port)
        } else {
            ([0, 0, 0, 0], self.port)
        }
    }

    /// Get instances configuration
    pub fn get_instances_config(&self) -> anyhow::Result<Vec<InstanceConfig>> {
        if let Some(config_path) = &self.instances_config {
            // 从 JSON 文件加载配置
            let content = std::fs::read_to_string(config_path).map_err(|e| {
                anyhow::anyhow!(
                    "Failed to read instances config file {}: {}",
                    config_path.display(),
                    e
                )
            })?;

            let configs: Vec<InstanceConfig> = serde_json::from_str(&content)
                .map_err(|e| anyhow::anyhow!("Failed to parse instances config: {}", e))?;

            Ok(configs)
        } else {
            // 使用传统配置生成本地实例
            let mut configs = Vec::new();
            for i in 0..self.instance_count {
                configs.push(InstanceConfig::Local {
                    port: self.start_port + i as u16,
                    binary_path: self.rrd_service_binary.clone(),
                    data_path: self.rrd_data_path.clone(),
                    weight: 1, // 默认权重为1
                });
            }
            Ok(configs)
        }
    }


    /// Validate configuration
    pub fn validate(&self) -> anyhow::Result<()> {
        let instances_config = self.get_instances_config()?;

        if instances_config.is_empty() {
            return Err(anyhow::anyhow!("No instances configured"));
        }

        if instances_config.len() > 100 {
            return Err(anyhow::anyhow!("Instance count should not exceed 100"));
        }

        // 验证每个实例配置
        let mut used_ports = std::collections::HashSet::new();
        for (index, instance_config) in instances_config.iter().enumerate() {
            match instance_config {
                InstanceConfig::Local {
                    port,
                    binary_path,
                    data_path,
                    weight,
                } => {
                    // 验证权重值
                    if *weight == 0 {
                        return Err(anyhow::anyhow!(
                            "Weight must be greater than 0 for instance {}",
                            index
                        ));
                    }
                    if *weight > 100 {
                        return Err(anyhow::anyhow!(
                            "Weight should not exceed 100 for instance {}",
                            index
                        ));
                    }
                    // 检查端口冲突
                    if *port == self.port {
                        return Err(anyhow::anyhow!(
                            "Port conflict: instance {} port {} conflicts with load balancer port",
                            index,
                            port
                        ));
                    }

                    if used_ports.contains(port) {
                        return Err(anyhow::anyhow!(
                            "Port conflict: port {} is used by multiple local instances",
                            port
                        ));
                    }
                    used_ports.insert(*port);

                    // 检查二进制文件是否存在
                    if !binary_path.exists() {
                        return Err(anyhow::anyhow!(
                            "RRD service binary not found for instance {}: {}",
                            index,
                            binary_path.display()
                        ));
                    }

                    // 检查数据目录
                    if !data_path.exists() {
                        log::warn!(
                            "RRD data path does not exist for instance {}: {}, instance will need to create it",
                            index, data_path.display()
                        );
                    }
                }
                InstanceConfig::Remote { url, weight, .. } => {
                    // 验证权重值
                    if *weight == 0 {
                        return Err(anyhow::anyhow!(
                            "Weight must be greater than 0 for instance {}",
                            index
                        ));
                    }
                    if *weight > 100 {
                        return Err(anyhow::anyhow!(
                            "Weight should not exceed 100 for instance {}",
                            index
                        ));
                    }
                    // 验证远程 URL 格式
                    if let Err(e) = url::Url::parse(url) {
                        return Err(anyhow::anyhow!(
                            "Invalid URL for remote instance {}: {} ({})",
                            index,
                            url,
                            e
                        ));
                    }
                }
            }
        }

        Ok(())
    }
}
