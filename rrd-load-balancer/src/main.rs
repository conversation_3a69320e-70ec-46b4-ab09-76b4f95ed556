use std::sync::Arc;
use warp::Filter;

mod config;
mod handlers;
mod instance;
mod instance_manager;
mod load_balancer;
mod router;

use config::Config;
use load_balancer::LoadBalancer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();

    // Parse configuration
    let config = Config::new();

    // Initialize logging
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or(&config.log_level))
        .init();

    log::info!("Starting RRD Load Balancer");
    log::info!(
        "Configuration: instance_count={}, start_port={}, bind={}:{}",
        config.instance_count,
        config.start_port,
        config.bind_address,
        config.port
    );

    // Create and start load balancer
    let load_balancer = Arc::new(LoadBalancer::new(config.clone())?);

    // Start all instances and health monitoring
    if let Err(e) = load_balancer.start().await {
        log::error!("Failed to start load balancer: {}", e);
        return Err(e.into());
    }

    // Setup graceful shutdown
    let load_balancer_for_shutdown = Arc::clone(&load_balancer);
    tokio::spawn(async move {
        tokio::signal::ctrl_c()
            .await
            .expect("Failed to listen for ctrl+c");
        log::info!("Received shutdown signal");
        if let Err(e) = load_balancer_for_shutdown.stop().await {
            log::error!("Error during shutdown: {}", e);
        }
        std::process::exit(0);
    });

    // Create warp filters
    let load_balancer_filter = handlers::with_load_balancer(Arc::clone(&load_balancer));

    // Health check endpoint for the load balancer itself
    let health = warp::path("health")
        .and(warp::get())
        .and_then(handlers::health_handler);

    // Status endpoint showing instance health
    let status = warp::path("status")
        .and(warp::get())
        .and(load_balancer_filter.clone())
        .and_then(handlers::status_handler);

    // Proxy all other requests to rrd-service instances
    let proxy = warp::method()
        .and(warp::path::full())
        .and(
            warp::query::raw()
                .or(warp::any().map(|| String::new()))
                .unify(),
        )
        .and(warp::header::headers_cloned())
        .and(warp::body::bytes())
        .and(load_balancer_filter)
        .and_then(
            |method, path, query: String, headers, body, lb| async move {
                let query_opt = if query.is_empty() { None } else { Some(query) };
                handlers::proxy_handler(method, path, query_opt, headers, body, lb).await
            },
        );

    // Combine routes - health and status first, then proxy as fallback
    let routes = health
        .or(status)
        .or(proxy)
        .with(
            warp::cors()
                .allow_any_origin()
                .allow_headers(vec!["content-type"])
                .allow_methods(vec!["GET", "POST", "PUT", "DELETE"]),
        )
        .recover(handlers::handle_rejection);

    let bind_addr = config.get_bind_addr();
    log::info!(
        "RRD Load Balancer listening on http://{}:{}",
        config.bind_address,
        config.port
    );
    log::info!(
        "Health check available at http://{}:{}/health",
        config.bind_address,
        config.port
    );
    log::info!(
        "Status endpoint available at http://{}:{}/status",
        config.bind_address,
        config.port
    );

    // Start the server
    warp::serve(routes).run(bind_addr).await;

    Ok(())
}
