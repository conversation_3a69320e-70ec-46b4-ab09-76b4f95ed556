use crate::{config::Config, instance_manager::Instance<PERSON>anager, router::ConsistentHashRouter};
use anyhow::{anyhow, Result};
use serde_json::Value;
use std::{sync::Arc, time::Duration};
use warp::http::{<PERSON>erName, HeaderValue};
use warp::hyper::body::Bytes;

/// Core load balancer that coordinates routing and instance management
pub struct LoadBalancer {
    config: Arc<Config>,
    router: ConsistentHashRouter,
    instance_manager: InstanceManager,
    client: reqwest::Client,
}

impl LoadBalancer {
    /// Create a new load balancer
    pub fn new(config: Config) -> anyhow::Result<Self> {
        let config = Arc::new(config);
        let instances_config = config.get_instances_config()?;
        
        // Extract weights from instance configurations
        let weights: Vec<u32> = instances_config.iter()
            .map(|instance| instance.get_weight())
            .collect();
        
        let router = ConsistentHashRouter::new_with_weights(weights);
        let instance_manager = InstanceManager::new(Arc::clone(&config))?;

        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.request_timeout))
            .build()
            .expect("Failed to create HTTP client");

        Ok(Self {
            config,
            router,
            instance_manager,
            client,
        })
    }

    /// Start the load balancer and all instances
    pub async fn start(&self) -> Result<()> {
        log::info!("Starting RRD Load Balancer");

        // Validate configuration
        self.config.validate()?;

        // Start all instances
        self.instance_manager.start_all_instances().await?;

        // Start health monitoring
        let _health_task = self.instance_manager.start_health_monitoring();

        let instance_count = self.instance_manager.get_instance_count();
        log::info!(
            "RRD Load Balancer started successfully with {} instances",
            instance_count
        );
        Ok(())
    }

    /// Stop the load balancer and all instances
    pub async fn stop(&self) -> Result<()> {
        log::info!("Stopping RRD Load Balancer");
        self.instance_manager.stop_all_instances().await
    }

    /// Proxy a request to the appropriate rrd-service instance
    pub async fn proxy_request(
        &self,
        method: &str,
        path: &str,
        query: Option<String>,
        headers: warp::hyper::HeaderMap,
        body: Bytes,
    ) -> Result<ProxyResponse> {
        // Extract server_id and config_id from the request for routing
        let (server_id, config_id) = self.extract_routing_key(path, &body)?;

        // Route to appropriate instance
        let instance_index = self.router.route(server_id, config_id);

        // Get instance URL (with fallback for unhealthy instances)
        let instance_url = self.get_available_instance_url(instance_index).await?;

        // Build full URL
        let full_url = if let Some(query) = query {
            format!("{}{}?{}", instance_url, path, query)
        } else {
            format!("{}{}", instance_url, path)
        };

        log::debug!(
            "Proxying {} {} (server_id: {}, config_id: {}) to instance {}",
            method,
            path,
            server_id,
            config_id,
            instance_index
        );

        // Make request to target instance
        let mut request_builder = match method {
            "GET" => self.client.get(&full_url),
            "POST" => self.client.post(&full_url),
            "PUT" => self.client.put(&full_url),
            "DELETE" => self.client.delete(&full_url),
            _ => return Err(anyhow!("Unsupported HTTP method: {}", method)),
        };

        // Copy headers (excluding hop-by-hop headers)
        for (name, value) in headers.iter() {
            let name_str = name.as_str();
            if !is_hop_by_hop_header(name_str) {
                if let Ok(value_str) = value.to_str() {
                    request_builder = request_builder.header(name_str, value_str);
                }
            }
        }

        // Add body if present
        if !body.is_empty() {
            request_builder = request_builder.body(body);
        }

        // Send request
        let response = request_builder.send().await.map_err(|e| {
            anyhow!(
                "Failed to proxy request to instance {}: {}",
                instance_index,
                e
            )
        })?;

        // Convert response
        let status = response.status();
        let reqwest_headers = response.headers().clone();
        let response_body = response
            .bytes()
            .await
            .map_err(|e| anyhow!("Failed to read response body: {}", e))?;

        // Convert reqwest headers to warp headers
        let mut warp_headers = warp::hyper::HeaderMap::new();
        for (name, value) in reqwest_headers.iter() {
            if let (Ok(warp_name), Some(warp_value)) = (
                name.as_str().parse::<HeaderName>(),
                value
                    .to_str()
                    .ok()
                    .and_then(|v| v.parse::<HeaderValue>().ok()),
            ) {
                warp_headers.insert(warp_name, warp_value);
            }
        }

        Ok(ProxyResponse {
            status: status.as_u16(),
            headers: warp_headers,
            body: response_body,
        })
    }

    /// Extract routing key (server_id, config_id) from request
    fn extract_routing_key(&self, path: &str, body: &Bytes) -> Result<(i32, i32)> {
        // Try to extract from path first (for exists and delete endpoints)
        if let Some(captures) = extract_ids_from_path(path) {
            return Ok(captures);
        }

        // Try to extract from JSON body
        if !body.is_empty() {
            if let Ok(json) = serde_json::from_slice::<Value>(body) {
                if let (Some(server_id), Some(config_id)) = (
                    json.get("server_id").and_then(|v| v.as_i64()),
                    json.get("config_id").and_then(|v| v.as_i64()),
                ) {
                    return Ok((server_id as i32, config_id as i32));
                }
            }
        }

        // Fallback for health check and other endpoints without routing requirements
        Ok((0, 0))
    }

    /// Get an available instance URL, with fallback for unhealthy instances
    async fn get_available_instance_url(&self, preferred_index: usize) -> Result<String> {
        // Try preferred instance first
        if let Some((url, is_healthy)) = self.instance_manager.get_instance_info(preferred_index) {
            if is_healthy {
                return Ok(url);
            }
        }

        // If preferred instance is unhealthy, try to find any healthy instance
        // This is not ideal for consistency, but provides fault tolerance
        let healthy_instances = self.instance_manager.get_healthy_instances();

        if healthy_instances.is_empty() {
            return Err(anyhow!("No healthy instances available"));
        }

        // Use the first healthy instance as fallback
        // Note: This breaks consistency guarantees but prevents total failure
        let fallback_index = healthy_instances[0];
        if let Some((url, _)) = self.instance_manager.get_instance_info(fallback_index) {
            log::warn!(
                "Using fallback instance {} instead of preferred instance {}",
                fallback_index,
                preferred_index
            );
            Ok(url)
        } else {
            Err(anyhow!("Failed to get fallback instance URL"))
        }
    }

    /// Get load balancer status
    pub fn get_status(&self) -> LoadBalancerStatus {
        let instances = self.instance_manager.get_status();
        let router_stats = self.router.get_stats();

        LoadBalancerStatus {
            total_instances: self.instance_manager.get_instance_count(),
            healthy_instances: instances.iter().filter(|i| i.is_healthy).count(),
            instances,
            router_stats,
        }
    }
}

/// Response from proxied request
pub struct ProxyResponse {
    pub status: u16,
    pub headers: warp::hyper::HeaderMap,
    pub body: Bytes,
}

/// Load balancer status information
#[derive(serde::Serialize)]
pub struct LoadBalancerStatus {
    pub total_instances: usize,
    pub healthy_instances: usize,
    pub instances: Vec<crate::instance::InstanceStatus>,
    pub router_stats: crate::router::RouterStats,
}

/// Extract server_id and config_id from URL path
fn extract_ids_from_path(path: &str) -> Option<(i32, i32)> {
    // Handle paths like /rrd/exists/123/456 or /rrd/123/456
    let parts: Vec<&str> = path.split('/').collect();

    if parts.len() >= 4 {
        // Try different patterns
        if parts.len() >= 5 && (parts[2] == "exists" || parts[1] == "rrd") {
            // /rrd/exists/server_id/config_id
            if let (Ok(server_id), Ok(config_id)) =
                (parts[3].parse::<i32>(), parts[4].parse::<i32>())
            {
                return Some((server_id, config_id));
            }
        } else if parts.len() >= 4 && parts[1] == "rrd" {
            // /rrd/server_id/config_id
            if let (Ok(server_id), Ok(config_id)) =
                (parts[2].parse::<i32>(), parts[3].parse::<i32>())
            {
                return Some((server_id, config_id));
            }
        }
    }

    None
}

/// Check if header is hop-by-hop and should not be forwarded
fn is_hop_by_hop_header(name: &str) -> bool {
    matches!(
        name.to_lowercase().as_str(),
        "connection"
            | "keep-alive"
            | "proxy-authenticate"
            | "proxy-authorization"
            | "te"
            | "trailers"
            | "transfer-encoding"
            | "upgrade"
            | "host"
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_ids_from_path() {
        assert_eq!(
            extract_ids_from_path("/rrd/exists/123/456"),
            Some((123, 456))
        );
        assert_eq!(extract_ids_from_path("/rrd/123/456"), Some((123, 456)));
        assert_eq!(extract_ids_from_path("/health"), None);
        assert_eq!(extract_ids_from_path("/invalid/path"), None);
    }

    #[test]
    fn test_hop_by_hop_headers() {
        assert!(is_hop_by_hop_header("connection"));
        assert!(is_hop_by_hop_header("Connection"));
        assert!(is_hop_by_hop_header("HOST"));
        assert!(!is_hop_by_hop_header("content-type"));
        assert!(!is_hop_by_hop_header("authorization"));
    }
}
