use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::process::{Child, Command, Stdio};
use std::time::Duration;

/// 实例类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum InstanceConfig {
    /// 本地进程实例
    #[serde(rename = "local")]
    Local {
        /// 实例端口
        port: u16,
        /// rrd-service 二进制路径
        binary_path: std::path::PathBuf,
        /// RRD 数据目录
        data_path: std::path::PathBuf,
        /// 实例权重（默认 1）
        #[serde(default = "default_weight")]
        weight: u32,
    },
    /// 远程实例
    #[serde(rename = "remote")]
    Remote {
        /// 远程实例 URL
        url: String,
        /// 健康检查路径（默认 /health）
        #[serde(default = "default_health_path")]
        health_path: String,
        /// 实例权重（默认 1）
        #[serde(default = "default_weight")]
        weight: u32,
    },
}

fn default_health_path() -> String {
    "/health".to_string()
}

fn default_weight() -> u32 {
    1
}

impl InstanceConfig {
    /// 获取实例的 URL
    pub fn get_url(&self) -> String {
        match self {
            InstanceConfig::Local { port, .. } => format!("http://127.0.0.1:{}", port),
            InstanceConfig::Remote { url, .. } => url.clone(),
        }
    }

    /// 获取健康检查 URL
    pub fn get_health_url(&self) -> String {
        match self {
            InstanceConfig::Local { port, .. } => format!("http://127.0.0.1:{}/health", port),
            InstanceConfig::Remote { url, health_path, .. } => {
                format!("{}{}", url.trim_end_matches('/'), health_path)
            }
        }
    }

    /// 获取实例权重
    pub fn get_weight(&self) -> u32 {
        match self {
            InstanceConfig::Local { weight, .. } => *weight,
            InstanceConfig::Remote { weight, .. } => *weight,
        }
    }
}

/// 实例状态信息
#[derive(Debug, Clone, Serialize)]
pub struct InstanceStatus {
    pub index: usize,
    pub config: InstanceConfig,
    pub is_healthy: bool,
    pub restart_count: u32,
    pub instance_type: String,
    pub weight: u32,
}

/// 抽象实例接口
#[async_trait::async_trait]
pub trait Instance: Send + Sync {
    /// 启动实例
    async fn start(&mut self) -> Result<()>;

    /// 停止实例
    async fn stop(&mut self) -> Result<()>;

    /// 检查实例健康状态
    async fn check_health(&self, client: &reqwest::Client) -> bool;

    /// 获取实例状态
    fn get_status(&self) -> InstanceStatus;

    /// 获取实例 URL
    fn get_url(&self) -> String;

    /// 是否健康
    fn is_healthy(&self) -> bool;

    /// 设置健康状态
    fn set_healthy(&mut self, healthy: bool);

    /// 获取重启次数
    fn get_restart_count(&self) -> u32;

    /// 增加重启次数
    fn increment_restart_count(&mut self);
}

/// 本地进程实例
pub struct LocalInstance {
    pub index: usize,
    pub config: InstanceConfig,
    pub process: Option<Child>,
    pub is_healthy: bool,
    pub restart_count: u32,
    pub log_level: String,
}

impl LocalInstance {
    pub fn new(index: usize, config: InstanceConfig, log_level: String) -> Result<Self> {
        match &config {
            InstanceConfig::Local { .. } => Ok(Self {
                index,
                config,
                process: None,
                is_healthy: false,
                restart_count: 0,
                log_level,
            }),
            _ => Err(anyhow!("Invalid config type for LocalInstance")),
        }
    }
}

#[async_trait::async_trait]
impl Instance for LocalInstance {
    async fn start(&mut self) -> Result<()> {
        if let InstanceConfig::Local {
            port,
            binary_path,
            data_path,
            ..
        } = &self.config
        {
            log::info!(
                "Starting local rrd-service instance {} on port {}",
                self.index,
                port
            );

            let mut cmd = Command::new(binary_path);
            cmd.env("PORT", port.to_string())
                .env("RRD_DATA_PATH", data_path)
                .env("LOG_LEVEL", &self.log_level)
                .env("BIND_ADDRESS", "127.0.0.1")
                .stdout(Stdio::null())
                .stderr(Stdio::null());

            let child = cmd.spawn().map_err(|e| {
                anyhow!("Failed to start rrd-service instance {}: {}", self.index, e)
            })?;

            self.process = Some(child);
            self.is_healthy = false;

            log::info!(
                "Started local instance {} at {}",
                self.index,
                self.get_url()
            );
            Ok(())
        } else {
            Err(anyhow!("Invalid config for local instance"))
        }
    }

    async fn stop(&mut self) -> Result<()> {
        if let Some(ref mut process) = self.process {
            log::info!("Stopping local instance {}", self.index);
            if let Err(e) = process.kill() {
                log::warn!("Failed to kill instance {}: {}", self.index, e);
            }
            if let Err(e) = process.wait() {
                log::warn!("Error waiting for instance {} to exit: {}", self.index, e);
            }
            self.process = None;
        }
        Ok(())
    }

    async fn check_health(&self, client: &reqwest::Client) -> bool {
        let health_url = self.config.get_health_url();
        match client
            .get(&health_url)
            .timeout(Duration::from_secs(5))
            .send()
            .await
        {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }

    fn get_status(&self) -> InstanceStatus {
        InstanceStatus {
            index: self.index,
            config: self.config.clone(),
            is_healthy: self.is_healthy,
            restart_count: self.restart_count,
            instance_type: "local".to_string(),
            weight: self.config.get_weight(),
        }
    }

    fn get_url(&self) -> String {
        self.config.get_url()
    }

    fn is_healthy(&self) -> bool {
        self.is_healthy
    }

    fn set_healthy(&mut self, healthy: bool) {
        self.is_healthy = healthy;
    }

    fn get_restart_count(&self) -> u32 {
        self.restart_count
    }

    fn increment_restart_count(&mut self) {
        self.restart_count += 1;
    }
}

/// 远程实例
pub struct RemoteInstance {
    pub index: usize,
    pub config: InstanceConfig,
    pub is_healthy: bool,
    pub restart_count: u32,
}

impl RemoteInstance {
    pub fn new(index: usize, config: InstanceConfig) -> Result<Self> {
        match &config {
            InstanceConfig::Remote { .. } => Ok(Self {
                index,
                config,
                is_healthy: false,
                restart_count: 0,
            }),
            _ => Err(anyhow!("Invalid config type for RemoteInstance")),
        }
    }
}

#[async_trait::async_trait]
impl Instance for RemoteInstance {
    async fn start(&mut self) -> Result<()> {
        log::info!(
            "Connecting to remote instance {} at {}",
            self.index,
            self.get_url()
        );
        // 远程实例不需要启动，只需要标记为连接状态
        self.is_healthy = false;
        Ok(())
    }

    async fn stop(&mut self) -> Result<()> {
        log::info!("Disconnecting from remote instance {}", self.index);
        // 远程实例不需要停止，只需要标记为断开状态
        self.is_healthy = false;
        Ok(())
    }

    async fn check_health(&self, client: &reqwest::Client) -> bool {
        let health_url = self.config.get_health_url();
        match client
            .get(&health_url)
            .timeout(Duration::from_secs(10)) // 远程实例超时时间稍长
            .send()
            .await
        {
            Ok(response) => response.status().is_success(),
            Err(e) => {
                log::debug!("Remote instance {} health check failed: {}", self.index, e);
                false
            }
        }
    }

    fn get_status(&self) -> InstanceStatus {
        InstanceStatus {
            index: self.index,
            config: self.config.clone(),
            is_healthy: self.is_healthy,
            restart_count: self.restart_count,
            instance_type: "remote".to_string(),
            weight: self.config.get_weight(),
        }
    }

    fn get_url(&self) -> String {
        self.config.get_url()
    }

    fn is_healthy(&self) -> bool {
        self.is_healthy
    }

    fn set_healthy(&mut self, healthy: bool) {
        self.is_healthy = healthy;
    }

    fn get_restart_count(&self) -> u32 {
        self.restart_count
    }

    fn increment_restart_count(&mut self) {
        self.restart_count += 1;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_instance_config_urls() {
        let local_config = InstanceConfig::Local {
            port: 8083,
            binary_path: "/usr/local/bin/rrd-service".into(),
            data_path: "/data/rrd".into(),
            weight: 1,
        };
        assert_eq!(local_config.get_url(), "http://127.0.0.1:8083");
        assert_eq!(
            local_config.get_health_url(),
            "http://127.0.0.1:8083/health"
        );
        assert_eq!(local_config.get_weight(), 1);

        let remote_config = InstanceConfig::Remote {
            url: "http://192.168.1.100:8083".to_string(),
            health_path: "/health".to_string(),
            weight: 2,
        };
        assert_eq!(remote_config.get_url(), "http://192.168.1.100:8083");
        assert_eq!(
            remote_config.get_health_url(),
            "http://192.168.1.100:8083/health"
        );
        assert_eq!(remote_config.get_weight(), 2);
    }

    #[test]
    fn test_serde() {
        let local_config = InstanceConfig::Local {
            port: 8083,
            binary_path: "/usr/local/bin/rrd-service".into(),
            data_path: "/data/rrd".into(),
            weight: 3,
        };

        let json = serde_json::to_string(&local_config).unwrap();
        let deserialized: InstanceConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.get_url(), local_config.get_url());
        assert_eq!(deserialized.get_weight(), 3);

        let remote_config = InstanceConfig::Remote {
            url: "http://192.168.1.100:8083".to_string(),
            health_path: "/api/health".to_string(),
            weight: 4,
        };

        let json = serde_json::to_string(&remote_config).unwrap();
        let deserialized: InstanceConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.get_url(), remote_config.get_url());
        assert_eq!(deserialized.get_weight(), 4);
    }
}
