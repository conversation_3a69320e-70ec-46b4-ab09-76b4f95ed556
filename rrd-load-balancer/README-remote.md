# RRD Load Balancer - 远程实例支持

本文档说明如何配置 rrd-load-balancer 支持远程 rrd-service 实例。

## 概述

rrd-load-balancer 现在支持三种部署模式：

1. **纯本地模式** - 只管理本地进程实例（原有模式）
2. **纯远程模式** - 只代理远程实例
3. **混合模式** - 同时支持本地进程和远程实例

## 配置方法

### 方法1: 传统配置（向后兼容）

使用命令行参数或环境变量配置本地实例：

```bash
rrd-load-balancer --instance-count 4 --start-port 8083
```

### 方法2: JSON 配置文件（推荐）

创建 `instances-config.json` 文件来配置混合实例：

```json
[
  {
    "type": "local",
    "port": 8083,
    "binary_path": "/usr/local/bin/rrd-service",
    "data_path": "/data/rrd"
  },
  {
    "type": "remote",
    "url": "http://192.168.1.100:8083",
    "health_path": "/health"
  }
]
```

然后使用 `--instances-config` 参数：

```bash
rrd-load-balancer --instances-config instances-config.json
```

## 实例类型

### 本地实例 (`local`)

本地实例由负载均衡器管理进程生命周期：

```json
{
  "type": "local",
  "port": 8083,
  "binary_path": "/usr/local/bin/rrd-service", 
  "data_path": "/data/rrd"
}
```

- `port`: 实例监听端口
- `binary_path`: rrd-service 二进制文件路径
- `data_path`: RRD 数据目录

### 远程实例 (`remote`)

远程实例只进行反向代理，不管理进程：

```json
{
  "type": "remote",
  "url": "http://192.168.1.100:8083",
  "health_path": "/health"
}
```

- `url`: 远程实例 URL（必需）
- `health_path`: 健康检查路径（可选，默认 `/health`）

## 使用示例

### 示例1: 纯远程代理

```json
[
  {
    "type": "remote",
    "url": "http://server1.example.com:8083"
  },
  {
    "type": "remote", 
    "url": "http://server2.example.com:8083"
  },
  {
    "type": "remote",
    "url": "http://server3.example.com:8083",
    "health_path": "/api/health"
  }
]
```

### 示例2: 混合部署

```json
[
  {
    "type": "local",
    "port": 8083,
    "binary_path": "/usr/local/bin/rrd-service",
    "data_path": "/data/rrd/instance1"
  },
  {
    "type": "local",
    "port": 8084,
    "binary_path": "/usr/local/bin/rrd-service", 
    "data_path": "/data/rrd/instance2"
  },
  {
    "type": "remote",
    "url": "http://192.168.1.100:8083"
  },
  {
    "type": "remote",
    "url": "http://192.168.1.101:8083"
  }
]
```

## 健康检查

- **本地实例**: 检查 `http://127.0.0.1:{port}/health`
- **远程实例**: 检查 `{url}{health_path}`

远程实例的健康检查超时时间更长（10秒 vs 本地实例的5秒）。

## 故障处理

- **本地实例**: 不健康时会自动重启进程
- **远程实例**: 不健康时只标记为不可用，不会尝试重启

## 路由一致性

负载均衡器使用一致性哈希算法，确保相同的 `(server_id, config_id)` 始终路由到同一个实例，无论是本地还是远程。

## 监控和状态

访问 `/status` 端点查看所有实例状态：

```bash
curl http://localhost:8082/status
```

响应包含：
- 实例类型（`local` 或 `remote`）
- 健康状态
- 重启次数（仅本地实例）
- URL 信息

## 注意事项

1. 远程实例必须已经运行，负载均衡器不会启动远程服务
2. 确保网络连通性和防火墙设置正确
3. 远程实例故障不会触发进程重启，只会标记为不健康
4. 混合部署时，本地和远程实例享有相同的路由权重

## 环境变量

除了 JSON 配置，还可以使用环境变量：

```bash
INSTANCES_CONFIG=/path/to/instances-config.json
HEALTH_CHECK_INTERVAL=30
REQUEST_TIMEOUT=60
LOG_LEVEL=info
```