[package]
name = "rrd-load-balancer"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
clap = { version = "4.0", features = ["derive", "env"] }
env_logger = "0.11"
log = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
url = "2.5"
async-trait = "0.1"
warp = "0.3"
reqwest = { version = "0.12", default-features=false, features = ["json", "rustls-tls"] }
sha2 = "0.10"
dotenv = "0.15"
chrono = { version = "0.4", features = ["serde"] }
