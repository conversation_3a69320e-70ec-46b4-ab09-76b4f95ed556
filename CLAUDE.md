## Graphiti MCP 工具 Agent 记忆使用说明

### 开始任何任务之前

- **始终先搜索：** 在开始工作前，使用 `search_nodes` 工具查找相关的偏好和流程。
- **也要搜索事实：** 使用 `search_facts` 工具发现可能与您的任务相关的关系和事实信息。
- **按实体类型筛选：** 在您的节点搜索中指定 `Preference` (偏好)、`Procedure` (流程) 或 `Requirement` (要求)，以获得有针对性的结果。
- **审查所有匹配项：** 仔细检查与您当前任务匹配的任何偏好、流程或事实。

### 始终保存新的或更新的信息

- **立即捕获要求和偏好：** 当用户表达一项要求或偏好时，应立即使用 `add_memory` 将其存储。
  - _最佳实践：_ 将非常长的要求拆分成更短、符合逻辑的块。
- **如果某项内容是对现有知识的更新，请明确说明。** 只将更改或新增的内容添加到知识图中。
- **清晰地记录流程：** 当您发现用户希望如何完成某项工作时，将其记录为一个流程。
- **记录事实关系：** 当您了解到实体之间的联系时，将这些作为事实存储起来。
- **使用明确的类别：** 为偏好和流程打上清晰的类别标签，以便日后更好地检索。

### 工作期间

- **尊重已发现的偏好：** 使您的工作与已发现的任何偏好保持一致。
- **严格遵循流程：** 如果您找到了当前任务的流程，请严格按照步骤执行。
- **应用相关事实：** 使用事实信息来为您的决策和建议提供依据。
- **保持一致性：** 与之前确定的偏好、流程和事实保持一致。

### 最佳实践

- **在提出建议前先搜索：** 在提出建议之前，务必检查是否存在已建立的知识。
- **结合节点和事实搜索：** 对于复杂任务，同时搜索节点和事实以构建完整的视图。
- **使用 `center_node_uuid`：** 在探索相关信息时，将搜索围绕一个特定的节点进行。
- **优先考虑具体匹配项：** 更具体的信息优先于一般信息。
- **保持主动性：** 如果您注意到用户行为中的模式，可以考虑将其存储为偏好或流程。

**请记住：** 知识图谱就是您的记忆。持续使用它，以便在尊重用户已建立的偏好、流程和事实背景的前提下，提供个性化的帮助。

如果修改了prisma schema 文件
需要使用命令生成prisma代码
cargo prisma generate --schema PATH_TO_SCHEMA_FILE

如果修改了zfc-web-ui前端，需要注意i18n也要对应修改。

## TDengine 自动初始化

在迁移到TDengine后，数据库和表会在`zf-controler`和`zf-web`启动时自动创建：
- 自动创建 `zfc` 数据库（如果不存在）
- 自动创建 `netcard_speed`、`system_stats`、`user_line_info` 超级表
- 如果初始化失败，应用会停止启动并报错

初始化逻辑位于 `common/src/tdengine_init.rs`，避免了代码重复。

无需手动执行SQL脚本或创建表结构。

## 规范
不能使用serde_json::json!创建临时的json数据，应该使用结构体的方式实现类型化，充分利用编译器检查。
前端开发的时候可以不用考虑和已有后端的兼容性，因为产品还没有发布任何线上版本。
除非我明确要求你commit and push，否则任何时候你都不能自发commit and push
如果修改了rrd_service crate的东西，需要我来编译，因为macos无法编译。

## 专用术语
TOT: Tcp Over Tcp, 是一种用于带宽聚合的协议.