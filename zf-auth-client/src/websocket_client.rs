use crate::{AuthError, Entitlements, JwtClaims};
use anyhow::Result;
use chrono::{DateTime, Utc};
use common::{
    ApiResponse, ApplyRenewalRequest, CalculateRenewalPriceResponse, CreateRenewalRequestRequest, HeartbeatRequest, LicenseInfo, PaginatedResponse, PublicKeyResponse, RenewalHistoryItem, RenewalRequestResponse, SignedOfflineToken, WebSocketMessage, SendRenewalRequestMessageRequest, RenewalRequestMessageResponse
};
use futures_util::{SinkExt, StreamExt};
use jsonwebtoken::{decode, Algorithm, DecodingKey, Validation};
use std::{
    path::PathBuf,
    process,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
    time::Duration,
};
use tokio::{
    fs,
    net::TcpStream,
    sync::{mpsc, RwLock},
    time::{interval, sleep},
};
use tokio_tungstenite::{connect_async, MaybeTlsStream, WebSocketStream};


// Use AuthConfig from parent module

#[derive(Debug, Clone)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    OfflineMode(DateTime<Utc>), // When offline mode started
}

pub struct WebSocketAuthClient {
    config: crate::AuthConfig,
    http_client: reqwest::Client,
    connection_state: Arc<RwLock<ConnectionState>>,
    offline_token: Arc<RwLock<Option<SignedOfflineToken>>>,
    device_fingerprint: String,
    token_cache_path: PathBuf,
    offline_token_cache_path: PathBuf,
    shutdown: Arc<AtomicBool>,
    instance_id: Option<String>,
    api_key: Option<String>,
    public_key_cache: Arc<RwLock<Option<String>>>,
    cached_entitlements: Arc<RwLock<Option<Entitlements>>>,
    entitlements_updated_tx: Arc<RwLock<Option<mpsc::UnboundedSender<Entitlements>>>>,
}

impl WebSocketAuthClient {
    pub fn new(config: crate::AuthConfig) -> Result<Self, AuthError> {
        // Set up cache directory
        let cache_dir = dirs::cache_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("zf-auth-client");

        let token_cache_path = cache_dir.join(format!("{}.jwt", config.instance_id));
        let offline_token_cache_path = cache_dir.join(format!("{}.offline", config.instance_id));

        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()?;

        let device_fingerprint = generate_device_fingerprint()?;

        Ok(Self {
            config,
            http_client,
            connection_state: Arc::new(RwLock::new(ConnectionState::Disconnected)),
            offline_token: Arc::new(RwLock::new(None)),
            device_fingerprint,
            token_cache_path,
            offline_token_cache_path,
            shutdown: Arc::new(AtomicBool::new(false)),
            instance_id: None,
            api_key: None,
            public_key_cache: Arc::new(RwLock::new(None)),
            cached_entitlements: Arc::new(RwLock::new(None)),
            entitlements_updated_tx: Arc::new(RwLock::new(None)),
        })
    }

    pub async fn set_instance_credentials(&mut self, instance_id: String, api_key: String) {
        self.instance_id = Some(instance_id);
        self.api_key = Some(api_key);
    }

    pub async fn set_entitlements_update_channel(&self, tx: mpsc::UnboundedSender<Entitlements>) {
        *self.entitlements_updated_tx.write().await = Some(tx);
    }

    pub async fn get_cached_entitlements(&self) -> Option<Entitlements> {
        self.cached_entitlements.read().await.clone()
    }

    pub async fn start(&self) -> Result<Entitlements, AuthError> {
        log::info!("Starting WebSocket auth client...");

        // Load cached offline token
        self.load_offline_token_from_cache().await;

        // Start WebSocket connection task
        let client_clone = self.clone_for_task();
        tokio::spawn(async move {
            log::info!("Starting WebSocket connection task...");
            client_clone.websocket_connection_task().await;
        });

        // Start offline validation task
        let client_clone = self.clone_for_task();
        tokio::spawn(async move {
            log::info!("Starting offline validation task...");
            client_clone.offline_validation_task().await;
        });

        // Wait for initial connection or use offline token
        for _ in 0..30 {
            // 30 second timeout
            let state = self.connection_state.read().await;
            match *state {
                ConnectionState::Connected => {
                    // Get entitlements from online connection
                    return self.get_current_entitlements().await;
                }
                ConnectionState::OfflineMode(_) => {
                    // Use offline token
                    return self.get_offline_entitlements().await;
                }
                _ => {
                    sleep(Duration::from_secs(1)).await;
                }
            }
        }

        // Fallback to offline mode if available
        self.get_offline_entitlements().await
    }

    async fn websocket_connection_task(&self) {
        log::info!("WebSocket connection task started");
        let mut retry_count = 0;
        let mut force_refresh_token = false;
        const MAX_RETRIES: u32 = 5;

        while !self.shutdown.load(Ordering::Relaxed) {
            // Check if we should enter offline mode
            if retry_count >= MAX_RETRIES {
                log::warn!("Max WebSocket retries reached, entering offline mode");
                self.enter_offline_mode().await;
                sleep(Duration::from_secs(60)).await; // Wait before retrying
                retry_count = 0;
                force_refresh_token = false;
                continue;
            }

            log::info!(
                "Attempting WebSocket connection (attempt {})",
                retry_count + 1
            );
            match self.init_websocket_connection(force_refresh_token).await {
                Ok(true) => {
                    log::info!("WebSocket connected successfully");
                    retry_count = 0;
                    force_refresh_token = false;
                }
                Ok(false) => {
                    log::error!("WebSocket authentication rejected");
                    return;
                }
                Err(e) => {
                    log::error!(
                        "WebSocket connection failed (attempt {}): {}",
                        retry_count + 1,
                        e
                    );

                    // If authentication failed, clear cached tokens and retry with fresh token
                    if matches!(e, AuthError::Unauthorized) {
                        log::info!(
                            "Authentication failed during connection, will retry with fresh token"
                        );
                        self.clear_cached_token().await;
                        force_refresh_token = true;
                    }

                    retry_count += 1;

                    let delay = std::cmp::min(1 << retry_count, 60); // Exponential backoff, max 60s
                    sleep(Duration::from_secs(delay as u64)).await;
                }
            }
        }
    }

    async fn init_websocket_connection(
        &self,
        force_refresh_token: bool,
    ) -> Result<bool, AuthError> {
        // Update state
        *self.connection_state.write().await = ConnectionState::Connecting;

        // Check if credentials are set
        let instance_id = self.instance_id.as_ref().ok_or_else(|| {
            log::error!("WebSocket connection failed: Instance ID not set");
            AuthError::ServerRejected("Instance ID not set".to_string())
        })?;

        let _api_key = self.api_key.as_ref().ok_or_else(|| {
            log::error!("WebSocket connection failed: API key not set");
            AuthError::ServerRejected("API key not set".to_string())
        })?;

        log::info!("WebSocket connecting for instance: {}", instance_id);

        // Get JWT token for authentication
        let jwt_token = self.get_jwt_token_with_retry(force_refresh_token).await?;

        let ws_url = self
            .config
            .server_url
            .replace("http://", "ws://")
            .replace("https://", "wss://");
        let full_url = format!("{}/ws/instance/{}/auth", ws_url, instance_id);

        log::info!("Attempting WebSocket connection to: {}", full_url);

        // Connect to WebSocket
        let (ws_stream, _) = connect_async(&full_url).await.map_err(|e| {
            log::error!("WebSocket connection failed to {}: {}", full_url, e);
            AuthError::NetworkError(format!("WebSocket connection failed: {}", e))
        })?;

        // Split WebSocket for separate send/receive
        let (mut ws_tx, ws_rx) = ws_stream.split();
        let (inner_tx, mut inner_rx) = mpsc::unbounded_channel::<WebSocketMessage>();

        // Send authentication request first
        let auth_request = WebSocketMessage::AuthRequest {
            jwt: jwt_token,
            device_fingerprint: self.device_fingerprint.clone(),
        };
        let message_text =
            serde_json::to_string(&auth_request).map_err(|e| AuthError::JsonError(e))?;
        ws_tx
            .send(tokio_tungstenite::tungstenite::Message::Text(message_text))
            .await
            .map_err(|e| AuthError::NetworkError(format!("Failed to send auth request: {}", e)))?;

        // Start sender task
        let sender_handle = tokio::spawn(async move {
            while let Some(message) = inner_rx.recv().await {
                let message_text = match serde_json::to_string(&message) {
                    Ok(text) => text,
                    Err(e) => {
                        log::error!("Failed to serialize message: {}", e);
                        continue;
                    }
                };
                if let Err(e) = ws_tx
                    .send(tokio_tungstenite::tungstenite::Message::Text(message_text))
                    .await
                {
                    log::error!("WebSocket send failed: {}", e);
                    break;
                }
            }
        });

        // Update state to connected
        *self.connection_state.write().await = ConnectionState::Connected;

        // Handle receiver task
        let client_clone = self.clone_for_task();
        let receiver_handle = tokio::spawn(async move {
            client_clone
                .handle_websocket_receiver(ws_rx, inner_tx.clone())
                .await
        });

        // Wait for receiver task to complete
        let receiver_result = receiver_handle.await;

        // Clean up sender
        sender_handle.abort();

        match receiver_result {
            Ok(Ok(should_retry)) => Ok(should_retry),
            Ok(Err(e)) => Err(e),
            Err(_) => {
                log::error!("WebSocket receiver task panicked");
                Ok(true) // Retry on panic
            }
        }
    }

    async fn handle_websocket_receiver(
        &self,
        mut ws_rx: futures_util::stream::SplitStream<WebSocketStream<MaybeTlsStream<TcpStream>>>,
        message_sender: mpsc::UnboundedSender<WebSocketMessage>,
    ) -> Result<bool, AuthError> {
        use futures_util::StreamExt;
        use tokio_tungstenite::tungstenite::Message;

        // Start heartbeat task with message sender
        let heartbeat_sender = message_sender.clone();
        let heartbeat_handle = {
            let shutdown = self.shutdown.clone();
            let connection_state = self.connection_state.clone();
            tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(30));
                while !shutdown.load(Ordering::Relaxed) {
                    interval.tick().await;
                    let state = connection_state.read().await;
                    if matches!(*state, ConnectionState::Connected) {
                        drop(state);
                        let ping_message = WebSocketMessage::Ping {
                            timestamp: chrono::Utc::now(),
                        };
                        if let Err(_) = heartbeat_sender.send(ping_message) {
                            break;
                        }
                    }
                }
            })
        };

        // Start entitlements refresh task
        let entitlements_sender = message_sender.clone();
        let entitlements_handle = {
            let shutdown = self.shutdown.clone();
            let connection_state = self.connection_state.clone();
            tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(30)); // 30 second refresh
                while !shutdown.load(Ordering::Relaxed) {
                    interval.tick().await;
                    let state = connection_state.read().await;
                    if matches!(*state, ConnectionState::Connected) {
                        drop(state);
                        let entitlements_request = WebSocketMessage::EntitlementsRequest {
                            timestamp: chrono::Utc::now(),
                        };
                        if let Err(_) = entitlements_sender.send(entitlements_request) {
                            break;
                        }
                    }
                }
            })
        };

        while !self.shutdown.load(Ordering::Relaxed) {
            match ws_rx.next().await {
                Some(Ok(message)) => {
                    match message {
                        Message::Text(text) => {
                            if let Ok(ws_msg) = serde_json::from_str::<WebSocketMessage>(&text) {
                                match self.handle_received_message(ws_msg).await {
                                    Ok(_) => {}
                                    Err(AuthError::Unauthorized) => {
                                        heartbeat_handle.abort();
                                        return Ok(true); // Retry with new token
                                    }
                                    Err(e) => {
                                        log::error!("Error handling received message: {}", e);
                                        heartbeat_handle.abort();
                                        return Err(e);
                                    }
                                }
                            }
                        }
                        Message::Close(_) => {
                            log::warn!("WebSocket closed by server");
                            break;
                        }
                        _ => {}
                    }
                }
                Some(Err(e)) => {
                    log::error!("WebSocket error: {}", e);
                    break;
                }
                None => {
                    log::warn!("WebSocket stream ended");
                    break;
                }
            }
        }

        // Clean up
        heartbeat_handle.abort();
        entitlements_handle.abort();
        *self.connection_state.write().await = ConnectionState::Disconnected;

        Ok(true) // Retry connection
    }

    async fn handle_received_message(&self, ws_msg: WebSocketMessage) -> Result<(), AuthError> {
        match ws_msg {
            WebSocketMessage::AuthResponse {
                success,
                offline_token,
                error,
            } => {
                if success {
                    if let Some(token) = offline_token {
                        // Store offline token
                        *self.offline_token.write().await = Some(token.clone());
                        self.save_offline_token_to_cache(&token).await?;
                        log::info!(
                            "Received offline token, valid until: {}",
                            token.token.expires_at
                        );
                    }
                } else {
                    log::error!("Authentication failed: {:?}", error);
                    return Err(AuthError::Unauthorized);
                }
            }
            WebSocketMessage::Pong { timestamp: _ } => {
                // Pong received, connection is alive
                log::debug!("Pong received, connection is alive");
            }
            WebSocketMessage::LicenseRevoked { reason } => {
                log::error!("License revoked: {}", reason);
                process::exit(1);
            }
            WebSocketMessage::ConnectionConflict { message } => {
                log::error!("Connection conflict: {}", message);
                process::exit(1);
            }
            WebSocketMessage::EntitlementsResponse {
                success,
                entitlements,
                error,
            } => {
                if success {
                    if let Some(entitlements_json) = entitlements {
                        match serde_json::from_value::<Entitlements>(entitlements_json) {
                            Ok(entitlements) => {
                                log::info!("Received updated entitlements: {:?}", entitlements);

                                // Update cached entitlements
                                *self.cached_entitlements.write().await =
                                    Some(entitlements.clone());

                                // Notify subscribers if channel is set
                                if let Some(ref tx) = *self.entitlements_updated_tx.read().await {
                                    if let Err(e) = tx.send(entitlements) {
                                        log::warn!(
                                            "Failed to send entitlements update notification: {}",
                                            e
                                        );
                                    }
                                }
                            }
                            Err(e) => {
                                log::error!("Failed to parse entitlements: {}", e);
                            }
                        }
                    }
                } else {
                    log::error!("Failed to refresh entitlements: {:?}", error);
                }
            }
            _ => {}
        }
        Ok(())
    }

    async fn offline_validation_task(&self) {
        let mut interval = interval(Duration::from_secs(300)); // 5 minute check

        while !self.shutdown.load(Ordering::Relaxed) {
            interval.tick().await;

            let state = self.connection_state.read().await;
            if let ConnectionState::OfflineMode(started_at) = *state {
                drop(state);

                // Check if offline mode has expired (24 hours)
                if Utc::now().signed_duration_since(started_at).num_hours() >= 24 {
                    log::error!("Offline mode expired after 24 hours, terminating");
                    process::exit(1);
                }

                // Validate offline token periodically
                if let Err(e) = self.validate_offline_token().await {
                    log::error!("Offline token validation failed: {}", e);
                    process::exit(1);
                }
            }
        }
    }

    async fn enter_offline_mode(&self) {
        log::warn!("Entering offline mode due to connection failures");
        *self.connection_state.write().await = ConnectionState::OfflineMode(Utc::now());
    }

    async fn get_jwt_token_with_retry(&self, force_refresh: bool) -> Result<String, AuthError> {
        // Try cached token first (unless forced refresh)
        if !force_refresh {
            if let Some(cached_token) = self.load_cached_token().await {
                return Ok(cached_token);
            }
        }

        // Perform HTTP heartbeat to get new token
        self.perform_http_heartbeat(None).await
    }

    async fn perform_http_heartbeat(
        &self,
        existing_token: Option<&str>,
    ) -> Result<String, AuthError> {
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance ID not available".to_string()))?;

        let heartbeat_request = HeartbeatRequest {
            instance_id: self.config.instance_id.clone(),
        };

        let mut request_builder = self
            .http_client
            .post(format!(
                "{}/instance/{}/heartbeat",
                self.config.server_url, instance_id
            ))
            .header("x-api-key", api_key)
            .json(&heartbeat_request);

        if let Some(token) = existing_token {
            request_builder = request_builder.bearer_auth(token);
        }

        let response = request_builder.send().await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let response_json: serde_json::Value = response.json().await?;
                let token = response_json["token"]
                    .as_str()
                    .ok_or_else(|| AuthError::ServerRejected("No token in response".to_string()))?;

                // Cache the token
                self.save_token_to_cache(token).await?;

                Ok(token.to_string())
            }
            reqwest::StatusCode::FORBIDDEN => Err(AuthError::LicenseExpired),
            reqwest::StatusCode::CONFLICT => Err(AuthError::LicenseInUse),
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // Helper methods for caching and token management
    async fn load_cached_token(&self) -> Option<String> {
        match fs::read_to_string(&self.token_cache_path).await {
            Ok(token) => {
                if self.verify_token_locally(&token).await.is_ok() {
                    Some(token)
                } else {
                    None
                }
            }
            Err(_) => None,
        }
    }

    async fn save_token_to_cache(&self, token: &str) -> Result<(), AuthError> {
        if let Some(parent) = self.token_cache_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        fs::write(&self.token_cache_path, token).await?;
        Ok(())
    }

    async fn clear_cached_token(&self) {
        if let Err(e) = fs::remove_file(&self.token_cache_path).await {
            if e.kind() != std::io::ErrorKind::NotFound {
                log::warn!("Failed to clear cached token: {}", e);
            }
        } else {
            log::info!("Cleared cached JWT token");
        }
    }

    async fn load_offline_token_from_cache(&self) {
        if let Ok(token_data) = fs::read_to_string(&self.offline_token_cache_path).await {
            if let Ok(signed_token) = serde_json::from_str::<SignedOfflineToken>(&token_data) {
                *self.offline_token.write().await = Some(signed_token);
            }
        }
    }

    async fn save_offline_token_to_cache(
        &self,
        token: &SignedOfflineToken,
    ) -> Result<(), AuthError> {
        if let Some(parent) = self.offline_token_cache_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        let token_json = serde_json::to_string(token)?;
        fs::write(&self.offline_token_cache_path, token_json).await?;
        Ok(())
    }

    async fn verify_token_locally(&self, token: &str) -> Result<JwtClaims, AuthError> {
        let mut validation = Validation::new(Algorithm::RS256);
        validation.validate_exp = true;

        let public_key_pem = self.get_public_key().await?;
        let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())
            .map_err(|e| AuthError::TokenInvalid(format!("Invalid public key: {}", e)))?;

        let token_data = decode::<JwtClaims>(token, &decoding_key, &validation)
            .map_err(|e| AuthError::TokenInvalid(format!("Token verification failed: {}", e)))?;

        Ok(token_data.claims)
    }

    async fn validate_offline_token(&self) -> Result<(), AuthError> {
        let offline_token = self.offline_token.read().await;
        if let Some(ref token) = *offline_token {
            if token.token.expires_at < Utc::now() {
                return Err(AuthError::LicenseExpired);
            }
            if token.token.device_fingerprint != self.device_fingerprint {
                return Err(AuthError::ServerRejected(
                    "Device fingerprint mismatch".to_string(),
                ));
            }
            Ok(())
        } else {
            Err(AuthError::ServerRejected(
                "No offline token available".to_string(),
            ))
        }
    }

    async fn get_public_key(&self) -> Result<String, AuthError> {
        // Check cache first
        {
            let cache = self.public_key_cache.read().await;
            if let Some(ref key) = *cache {
                return Ok(key.clone());
            }
        }

        // Retry logic for public key fetching
        const MAX_RETRIES: u32 = 3;
        const RETRY_DELAY_MS: u64 = 1000;

        let mut last_error = None;

        for attempt in 1..=MAX_RETRIES {
            match self.fetch_public_key_once().await {
                Ok(public_key) => {
                    // Cache the public key
                    {
                        let mut cache = self.public_key_cache.write().await;
                        *cache = Some(public_key.clone());
                    }
                    return Ok(public_key);
                }
                Err(e) => {
                    log::warn!("Public key fetch attempt {} failed: {}", attempt, e);
                    last_error = Some(e);

                    if attempt < MAX_RETRIES {
                        tokio::time::sleep(Duration::from_millis(RETRY_DELAY_MS)).await;
                    }
                }
            }
        }

        log::error!("Failed to fetch public key after {} attempts", MAX_RETRIES);
        Err(last_error.unwrap())
    }

    async fn fetch_public_key_once(&self) -> Result<String, AuthError> {
        // Fetch from server with timeout
        let response = self
            .http_client
            .get(format!("{}/public-key", self.config.server_url))
            .timeout(Duration::from_secs(10))
            .send()
            .await
            .map_err(|e| {
                if e.is_timeout() {
                    AuthError::NetworkError("Public key fetch timeout".to_string())
                } else if e.is_connect() {
                    AuthError::NetworkError(format!("Connection failed: {}", e))
                } else {
                    AuthError::NetworkError(format!("Request failed: {}", e))
                }
            })?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<PublicKeyResponse> =
                    response.json().await.map_err(|e| {
                        AuthError::ServerRejected(format!("Invalid JSON response: {}", e))
                    })?;

                if let Some(data) = api_response.data {
                    if data.public_key.is_empty() {
                        return Err(AuthError::ServerRejected(
                            "Empty public key received".to_string(),
                        ));
                    }
                    if data.algorithm != "RS256" {
                        log::warn!("Unexpected algorithm: {}, expected RS256", data.algorithm);
                    }
                    Ok(data.public_key)
                } else {
                    Err(AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No public key data in response".to_string()),
                    ))
                }
            }
            reqwest::StatusCode::NOT_FOUND => Err(AuthError::ServerRejected(
                "Public key endpoint not found".to_string(),
            )),
            reqwest::StatusCode::SERVICE_UNAVAILABLE => Err(AuthError::NetworkError(
                "Authentication server unavailable".to_string(),
            )),
            _ => {
                let status = response.status();
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(format!(
                    "HTTP {}: {}",
                    status, error_text
                )))
            }
        }
    }

    async fn get_current_entitlements(&self) -> Result<Entitlements, AuthError> {
        // Try to get entitlements from current JWT token
        if let Some(cached_token) = self.load_cached_token().await {
            if let Ok(claims) = self.verify_token_locally(&cached_token).await {
                return serde_json::from_value(claims.entitlements).map_err(|e| {
                    AuthError::TokenInvalid(format!("Invalid entitlements format: {}", e))
                });
            }
        }

        // If JWT is not available or invalid, try offline token
        self.get_offline_entitlements().await
    }

    async fn get_offline_entitlements(&self) -> Result<Entitlements, AuthError> {
        let offline_token = self.offline_token.read().await;
        if let Some(ref token) = *offline_token {
            self.validate_offline_token().await?;

            // Extract entitlements from offline token
            let entitlements: Entitlements =
                serde_json::from_value(token.token.entitlements.clone())?;
            Ok(entitlements)
        } else {
            Err(AuthError::ServerRejected(
                "No offline token available".to_string(),
            ))
        }
    }

    fn clone_for_task(&self) -> WebSocketAuthClient {
        WebSocketAuthClient {
            config: self.config.clone(),
            http_client: self.http_client.clone(),
            connection_state: self.connection_state.clone(),
            offline_token: self.offline_token.clone(),
            device_fingerprint: self.device_fingerprint.clone(),
            token_cache_path: self.token_cache_path.clone(),
            offline_token_cache_path: self.offline_token_cache_path.clone(),
            shutdown: self.shutdown.clone(),
            instance_id: self.instance_id.clone(),
            api_key: self.api_key.clone(),
            public_key_cache: self.public_key_cache.clone(),
            cached_entitlements: self.cached_entitlements.clone(),
            entitlements_updated_tx: self.entitlements_updated_tx.clone(),
        }
    }

    // Public API methods (maintaining compatibility)
    pub async fn apply_renewal_code(&self, renewal_code: String) -> Result<(), AuthError> {
        // Use HTTP API for renewal
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let request = ApplyRenewalRequest {
            renewal_code_id: renewal_code,
        };

        let response = self
            .http_client
            .post(format!(
                "{}/instance/{}/renew",
                self.config.server_url, instance_id
            ))
            .header("x-api-key", api_key)
            .json(&request)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => Ok(()),
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    pub async fn get_license_info(&self) -> Result<LicenseInfo, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .get(format!(
                "{}/instance/{}/status",
                self.config.server_url, instance_id
            ))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: serde_json::Value = response.json().await?;
                let license_info: LicenseInfo =
                    serde_json::from_value(api_response["data"].clone())?;
                Ok(license_info)
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    pub async fn get_renewal_history(&self) -> Result<Vec<RenewalHistoryItem>, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .get(format!(
                "{}/instance/{}/renewal-history",
                self.config.server_url, instance_id
            ))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: serde_json::Value = response.json().await?;
                let history: Vec<RenewalHistoryItem> =
                    serde_json::from_value(api_response["data"].clone())?;
                Ok(history)
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    pub fn shutdown(&self) {
        self.shutdown.store(true, Ordering::Relaxed);
    }

    pub async fn calculate_renewal_price(
        &self,
        requested_duration: i32,
    ) -> Result<CalculateRenewalPriceResponse, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .get(format!(
                "{}/instance/{}/calculate-renewal-price?requested_duration={}",
                self.config.server_url, instance_id, requested_duration
            ))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<CalculateRenewalPriceResponse> =
                    response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // 续费请求相关方法

    pub async fn create_renewal_request(
        &self,
        request: CreateRenewalRequestRequest,
    ) -> Result<RenewalRequestResponse, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .post(format!(
                "{}/instance/{}/renewal-requests",
                self.config.server_url, instance_id
            ))
            .header("x-api-key", api_key)
            .json(&request)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::CREATED => {
                let api_response: ApiResponse<RenewalRequestResponse> = response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    pub async fn get_renewal_requests(
        &self,
        page: Option<u32>,
        page_size: Option<u32>,
    ) -> Result<PaginatedResponse<RenewalRequestResponse>, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let mut url = format!(
            "{}/instance/{}/renewal-requests",
            self.config.server_url, instance_id
        );

        // 添加查询参数
        let mut query_params = Vec::new();
        if let Some(p) = page {
            query_params.push(format!("page={}", p));
        }
        if let Some(ps) = page_size {
            query_params.push(format!("pageSize={}", ps));
        }

        if !query_params.is_empty() {
            url.push('?');
            url.push_str(&query_params.join("&"));
        }

        let response = self
            .http_client
            .get(&url)
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<PaginatedResponse<RenewalRequestResponse>> =
                    response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    pub async fn cancel_renewal_request(
        &self,
        request_id: String,
    ) -> Result<RenewalRequestResponse, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .delete(format!(
                "{}/instance/{}/renewal-requests/{}",
                self.config.server_url, instance_id, request_id
            ))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<RenewalRequestResponse> = response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // 消息相关方法

    // 发送续费请求消息
    pub async fn send_renewal_request_message(
        &self,
        request_id: String,
        message_request: SendRenewalRequestMessageRequest,
    ) -> Result<RenewalRequestMessageResponse, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .post(format!(
                "{}/instance/{}/renewal-requests/{}/messages",
                self.config.server_url, instance_id, request_id
            ))
            .header("x-api-key", api_key)
            .json(&message_request)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::CREATED => {
                let api_response: ApiResponse<RenewalRequestMessageResponse> = response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // 获取续费请求消息历史
    pub async fn get_renewal_request_messages(
        &self,
        request_id: String,
    ) -> Result<Vec<RenewalRequestMessageResponse>, AuthError> {
        let instance_id = self
            .instance_id
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self
            .api_key
            .as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .http_client
            .get(format!(
                "{}/instance/{}/renewal-requests/{}/messages",
                self.config.server_url, instance_id, request_id
            ))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<Vec<RenewalRequestMessageResponse>> = response.json().await?;
                api_response.data.ok_or_else(|| {
                    AuthError::ServerRejected(
                        api_response
                            .error
                            .unwrap_or_else(|| "No data received".to_string()),
                    )
                })
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

}

pub fn generate_device_fingerprint() -> Result<String, AuthError> {
    let id = machine_uid::get().map_err(|e| AuthError::MachineUidError(e.to_string()))?;
    log::info!("Machine UID: {}", id);
    Ok(id)
}
