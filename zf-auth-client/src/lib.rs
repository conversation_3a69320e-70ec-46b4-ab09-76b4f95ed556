use anyhow::Result;
use common::{
    ApiResponse, CalculateRenewalPriceResponse, CreateRenewalRequestRequest, Entitlements,
    ForwarderVersionResponse, LicenseInfo, PaginatedResponse, RenewalHistoryItem,
    RenewalRequestResponse, WorkerVersionResponse, SendRenewalRequestMessageRequest,
    RenewalRequestMessageResponse,
};
use serde::{Deserialize, Serialize};
use std::{path::Path, sync::Arc, time::Duration};

mod websocket_client;
pub use websocket_client::WebSocketAuthClient;

#[derive(Debug, Clone)]
pub struct AuthConfig {
    pub instance_id: String,
    pub server_url: String,
}

#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("HTTP error: {0}")]
    HttpError(#[from] reqwest::Error),

    #[error("Token invalid: {0}")]
    TokenInvalid(String),

    #[error("License expired")]
    LicenseExpired,

    #[error("License in use by another instance")]
    LicenseInUse,

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Server rejected request: {0}")]
    ServerRejected(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),

    #[error("Machine UID error: {0}")]
    MachineUidError(String),
}

#[derive(Debug, Serialize, Deserialize)]
struct JwtClaims {
    sub: String,
    sid: String,
    exp: u64,
    iat: u64,
    entitlements: serde_json::Value,
}

pub async fn initialize_auth(
    config: AuthConfig,
    instance_id: String,
    api_key: String,
) -> Result<(Arc<WebSocketAuthClient>, Entitlements), AuthError> {
    let mut client = WebSocketAuthClient::new(config)?;

    // Set instance credentials for future API calls
    client.set_instance_credentials(instance_id, api_key).await;

    let client = Arc::new(client);

    // Start WebSocket connection and get entitlements
    let entitlements = client.start().await?;

    log::info!(
        "Authentication successful, entitlements: {:?}",
        entitlements
    );
    Ok((client, entitlements))
}

// Apply renewal code using the provided client
pub async fn apply_renewal_code(
    client: &Arc<WebSocketAuthClient>,
    renewal_code: String,
) -> Result<(), AuthError> {
    client.apply_renewal_code(renewal_code).await
}

// Get license information using the provided client
pub async fn get_license_info(client: &Arc<WebSocketAuthClient>) -> Result<LicenseInfo, AuthError> {
    client.get_license_info().await
}

// Get renewal history using the provided client
pub async fn get_renewal_history(
    client: &Arc<WebSocketAuthClient>,
) -> Result<Vec<RenewalHistoryItem>, AuthError> {
    client.get_renewal_history().await
}

// Utility function to shutdown the background task gracefully
pub fn shutdown_auth(client: &Arc<WebSocketAuthClient>) {
    client.shutdown();
    log::info!("Auth shutdown requested");
}

// Get recommended worker version from auth server
pub async fn get_recommended_worker_version(
    config: &AuthConfig,
    instance_id: String,
    api_key: String,
    controller_version: String,
) -> Result<String, AuthError> {
    let url = format!(
        "{}/instance/{}/worker-version",
        config.server_url, instance_id
    );

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("X-API-Key", api_key)
        .query(&[("controller_version", &controller_version)])
        .send()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to query worker version: {}", e)))?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(AuthError::ServerRejected(format!(
            "Version query failed with status {}: {}",
            status, error_text
        )));
    }

    let api_response: ApiResponse<WorkerVersionResponse> = response
        .json()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to parse response: {}", e)))?;

    match api_response.data {
        Some(version_data) => {
            log::info!(
                "Server recommends worker version {} for controller version {}",
                version_data.recommended_version,
                version_data.controller_version
            );
            Ok(version_data.recommended_version)
        }
        None => Err(AuthError::ServerRejected(
            api_response
                .error
                .unwrap_or_else(|| "No version data received".to_string()),
        )),
    }
}

// Download worker binary from auth server
pub async fn download_worker_binary(
    config: &AuthConfig,
    instance_id: String,
    api_key: String,
    save_path: String,
    controller_version: Option<String>,
) -> Result<(), AuthError> {
    let url = format!(
        "{}/instance/{}/download-worker",
        config.server_url, instance_id
    );

    let client = reqwest::Client::new();
    let mut request = client.get(&url).header("X-API-Key", api_key);

    // Add controller version as query parameter if provided
    if let Some(version) = &controller_version {
        request = request.query(&[("controller_version", version)]);
        log::info!(
            "Downloading worker binary (controller v{}) from: {}",
            version,
            url
        );
    } else {
        log::info!("Downloading worker binary from: {}", url);
    }

    let response = request
        .timeout(Duration::from_secs(30))
        .send()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to download worker binary: {}", e)))?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(AuthError::ServerRejected(format!(
            "Download failed with status {}: {}",
            status, error_text
        )));
    }

    let binary_data = response
        .bytes()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to read binary data: {}", e)))?;

    // make sure the directory exists
    let dir = Path::new(&save_path).parent().unwrap();
    if !dir.exists() {
        tokio::fs::create_dir_all(dir)
            .await
            .map_err(|e| AuthError::IoError(e))?;
    }
    // Save the binary to the specified path
    tokio::fs::write(&save_path, binary_data)
        .await
        .map_err(|e| AuthError::IoError(e))?;

    // Set executable permissions on Unix systems
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;
        let mut perms = tokio::fs::metadata(&save_path)
            .await
            .map_err(|e| AuthError::IoError(e))?
            .permissions();
        perms.set_mode(0o755); // rwxr-xr-x
        tokio::fs::set_permissions(&save_path, perms)
            .await
            .map_err(|e| AuthError::IoError(e))?;
    }

    log::info!("Worker binary downloaded and saved to: {}", save_path);
    Ok(())
}

// Get recommended forwarder version from auth server
pub async fn get_recommended_forwarder_version(
    config: &AuthConfig,
    instance_id: String,
    api_key: String,
    controller_version: String,
) -> Result<String, AuthError> {
    let url = format!(
        "{}/instance/{}/forwarder-version",
        config.server_url, instance_id
    );

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("X-API-Key", api_key)
        .query(&[("controller_version", &controller_version)])
        .send()
        .await
        .map_err(|e| {
            AuthError::NetworkError(format!("Failed to query forwarder version: {}", e))
        })?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(AuthError::ServerRejected(format!(
            "Version query failed with status {}: {}",
            status, error_text
        )));
    }

    let api_response: ApiResponse<ForwarderVersionResponse> = response
        .json()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to parse response: {}", e)))?;

    match api_response.data {
        Some(version_data) => {
            log::info!(
                "Server recommends forwarder version {} for controller version {}",
                version_data.recommended_version,
                version_data.controller_version
            );
            Ok(version_data.recommended_version)
        }
        None => Err(AuthError::ServerRejected(
            api_response
                .error
                .unwrap_or_else(|| "No version data received".to_string()),
        )),
    }
}

// Download forwarder binary from auth server
pub async fn download_forwarder_binary(
    config: &AuthConfig,
    instance_id: String,
    api_key: String,
    save_path: String,
    controller_version: Option<String>,
) -> Result<(), AuthError> {
    let url = format!(
        "{}/instance/{}/download-forwarder",
        config.server_url, instance_id
    );

    let client = reqwest::Client::new();
    let mut request = client.get(&url).header("X-API-Key", api_key);

    // Add controller version as query parameter if provided
    if let Some(version) = &controller_version {
        request = request.query(&[("controller_version", version)]);
        log::info!(
            "Downloading forwarder binary (controller v{}) from: {}",
            version,
            url
        );
    } else {
        log::info!("Downloading forwarder binary from: {}", url);
    }

    let response = request
        .timeout(Duration::from_secs(30))
        .send()
        .await
        .map_err(|e| {
            AuthError::NetworkError(format!("Failed to download forwarder binary: {}", e))
        })?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(AuthError::ServerRejected(format!(
            "Download failed with status {}: {}",
            status, error_text
        )));
    }

    let binary_data = response
        .bytes()
        .await
        .map_err(|e| AuthError::NetworkError(format!("Failed to read binary data: {}", e)))?;

    // make sure the directory exists
    let dir = Path::new(&save_path).parent().unwrap();
    if !dir.exists() {
        tokio::fs::create_dir_all(dir)
            .await
            .map_err(|e| AuthError::IoError(e))?;
    }
    // Save the binary to the specified path
    tokio::fs::write(&save_path, binary_data)
        .await
        .map_err(|e| AuthError::IoError(e))?;

    // Set executable permissions on Unix systems
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;
        let mut perms = tokio::fs::metadata(&save_path)
            .await
            .map_err(|e| AuthError::IoError(e))?
            .permissions();
        perms.set_mode(0o755); // rwxr-xr-x
        tokio::fs::set_permissions(&save_path, perms)
            .await
            .map_err(|e| AuthError::IoError(e))?;
    }

    log::info!("Forwarder binary downloaded and saved to: {}", save_path);
    Ok(())
}

// 续费请求相关函数

// 创建续费请求
pub async fn create_renewal_request(
    client: &Arc<WebSocketAuthClient>,
    request: CreateRenewalRequestRequest,
) -> Result<RenewalRequestResponse, AuthError> {
    client.create_renewal_request(request).await
}

// 获取续费请求列表
pub async fn get_renewal_requests(
    client: &Arc<WebSocketAuthClient>,
    page: Option<u32>,
    page_size: Option<u32>,
) -> Result<PaginatedResponse<RenewalRequestResponse>, AuthError> {
    client.get_renewal_requests(page, page_size).await
}

// 取消续费请求
pub async fn cancel_renewal_request(
    client: &Arc<WebSocketAuthClient>,
    request_id: String,
) -> Result<RenewalRequestResponse, AuthError> {
    client.cancel_renewal_request(request_id).await
}

// 计算续费价格
pub async fn calculate_renewal_price(
    client: &Arc<WebSocketAuthClient>,
    requested_duration: i32,
) -> Result<CalculateRenewalPriceResponse, AuthError> {
    client.calculate_renewal_price(requested_duration).await
}

// 消息相关函数

// 发送续费请求消息
pub async fn send_renewal_request_message(
    client: &Arc<WebSocketAuthClient>,
    request_id: String,
    message: SendRenewalRequestMessageRequest,
) -> Result<RenewalRequestMessageResponse, AuthError> {
    client.send_renewal_request_message(request_id, message).await
}

// 获取续费请求消息历史
pub async fn get_renewal_request_messages(
    client: &Arc<WebSocketAuthClient>,
    request_id: String,
) -> Result<Vec<RenewalRequestMessageResponse>, AuthError> {
    client.get_renewal_request_messages(request_id).await
}

