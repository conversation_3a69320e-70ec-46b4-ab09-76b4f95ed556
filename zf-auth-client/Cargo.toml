[package]
name = "zf-auth-client"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1"
log = "0.4"
tokio = { version = "1.25", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync", "fs"] }
reqwest = { version = "0.12", default-features=false, features = ["json","rustls-tls"]}
tokio-tungstenite = { version = "0.21", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3.28"
hostname = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
jsonwebtoken = "9.2"
ed25519-dalek = { version = "2.0", features = ["serde"] }
rsa = { version = "0.9", features = ["serde"] }
hex = "0.4"
sha2 = "0.10"
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
dirs = "5.0"
machine-uid = "0.3.0"
common = { path = "../common" }