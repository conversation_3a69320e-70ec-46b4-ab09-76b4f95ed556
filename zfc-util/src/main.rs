use anyhow::Result;
use clap::{Parser, Subcommand};
use common::{
    app_message::{check_signature, sign_message},
    chrono::{DateTime, SecondsFormat, Utc},
    crypto::{
        parse_public_key, parse_secret_key, random_key, stringify_public_key_from_secret_key,
        stringify_secret_key,
    },
};

#[derive(Debug, Parser)]
pub struct Options {
    #[clap(subcommand)]
    command: Command,
}

#[derive(Debug, Subcommand)]
enum Command {
    GenerateKey,
    PrintKey {
        #[arg(short, long, env)]
        key_str: String,
    },
    SignMessage {
        #[arg(short, long, env)]
        key_str: String,
        #[arg(short, long, env)]
        message: String,
    },
    CheckSignature {
        #[arg(short, long, env)]
        key_str: String,
        #[arg(short, long, env)]
        message: String,
        #[arg(short, long, env)]
        ts: String,
        #[arg(short, long, env)]
        singature_hex: String,
        #[arg(short, long, env, default_value_t = false)]
        ignore_time_difference: bool,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let opt = Options::parse();

    match &opt.command {
        Command::GenerateKey => {
            let key = random_key();
            println!("private_key: {}", stringify_secret_key(&key));
            println!("public_key: {}", stringify_public_key_from_secret_key(&key));
        }
        Command::PrintKey { key_str } => {
            let key = parse_secret_key(key_str)?;
            println!("private_key: {}", stringify_secret_key(&key));
            println!("public_key: {}", stringify_public_key_from_secret_key(&key));
        }
        Command::SignMessage { key_str, message } => {
            let key = parse_secret_key(key_str)?;
            let (signature, time) = sign_message(key, message.as_bytes().into())?;
            println!("signature: {:?}", hex::encode(signature));
            println!(
                "time: {:?}",
                time.to_rfc3339_opts(SecondsFormat::Millis, true)
            );
        }
        Command::CheckSignature {
            key_str,
            message,
            ts,
            singature_hex,
            ignore_time_difference,
        } => {
            let key = parse_public_key(key_str)?;
            let time: DateTime<Utc> = DateTime::parse_from_rfc3339(ts)?.into();
            let singature = hex::decode(&singature_hex)?;
            let result = check_signature(
                key,
                message.as_bytes().into(),
                time,
                singature,
                *ignore_time_difference,
            )?;
            println!("{}", result)
        }
    }

    Ok(())
}
