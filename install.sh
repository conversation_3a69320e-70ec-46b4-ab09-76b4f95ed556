#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logo
show_logo() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                     ZFC 一键安装脚本                           ║"
    echo "║                  Zero Forward Control                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Show main menu
show_menu() {
    show_logo
    echo
    echo -e "${GREEN}请选择操作:${NC}"
    echo "1. 全新安装"
    echo "2. 更新镜像（保留数据）"
    echo "3. 卸载系统"
    echo "4. 查看管理员密码"
    echo "5. 退出"
    echo
}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Cross-platform sed in-place editing
sed_inplace() {
    local file="$1"
    shift
    # Create backup, apply changes, remove backup
    sed "$@" "$file" > "$file.tmp" && mv "$file.tmp" "$file"
}

# Generate random password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Safe quoting for environment variables
quote_env_value() {
    local value="$1"
    # If value contains special characters, quote it
    if [[ "$value" =~ [[:space:]#\$\"\'\\] ]]; then
        # Escape any existing quotes and wrap in double quotes
        printf '"%s"' "${value//\"/\\\"}"
    else
        printf '%s' "$value"
    fi
}

# Generate JWT secret
generate_jwt_secret() {
    openssl rand -hex 32
}

# Get the actual docker-compose network name
get_compose_network() {
    local network_name=""
    
    # First try to get the network from a running postgres service
    local postgres_container=$(docker ps --filter "label=com.docker.compose.service=postgres" --format "{{.Names}}" | head -1)
    if [[ -n "$postgres_container" ]]; then
        network_name=$(docker inspect "$postgres_container" --format '{{range $net, $conf := .NetworkSettings.Networks}}{{$net}}{{end}}' 2>/dev/null | head -1)
        if [[ -n "$network_name" && "$network_name" != "bridge" ]]; then
            echo "$network_name"
            return 0
        fi
    fi
    
    # Try to find from any running compose service
    local compose_containers=$(docker ps --filter "label=com.docker.compose.project" --format "{{.Names}}" | head -1)
    if [[ -n "$compose_containers" ]]; then
        network_name=$(docker inspect "$compose_containers" --format '{{range $net, $conf := .NetworkSettings.Networks}}{{$net}}{{end}}' 2>/dev/null | head -1)
        if [[ -n "$network_name" && "$network_name" != "bridge" ]]; then
            echo "$network_name"
            return 0
        fi
    fi
    
    # Fallback: try to find compose network by pattern
    local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
    network_name=$(docker network ls --format "{{.Name}}" | grep -E "(${project_name}_default|${project_name}_|zfc_default|zfc_)" | head -1)
    if [[ -n "$network_name" ]]; then
        echo "$network_name"
        return 0
    fi
    
    # Final fallback: use default compose network name
    echo "${project_name}_default"
}

# Database connection validation functions
validate_postgres_connection() {
    local host="$1"
    local port="$2"
    local user="$3"
    local password="$4"
    local database="$5"
    
    log_info "验证 PostgreSQL 连接: $user@$host:$port/$database"
    
    # Test connection using docker with postgres client
    local connection_test_output
    connection_test_output=$(timeout 10 docker run --rm \
        -e PGPASSWORD="$password" \
        postgres:15-alpine \
        psql -h "$host" -p "$port" -U "$user" -d "$database" -c "SELECT 1;" 2>&1) || {
        log_error "PostgreSQL 连接失败"
        log_error "错误信息: $connection_test_output"
        log_error "请检查："
        log_error "  1. 主机地址和端口是否正确"
        log_error "  2. 用户名和密码是否正确"
        log_error "  3. 数据库是否存在"
        log_error "  4. 防火墙设置是否允许连接"
        return 1
    }
    
    log_success "PostgreSQL 连接验证成功"
    return 0
}

validate_redis_connection() {
    local host="$1"
    local port="$2"
    local password="$3"
    
    log_info "验证 Redis 连接: $host:$port"
    
    # Test connection using docker with redis client
    local connection_test_output
    if [[ -n "$password" ]]; then
        connection_test_output=$(timeout 10 docker run --rm \
            redis:7-alpine \
            redis-cli -h "$host" -p "$port" -a "$password" ping 2>&1) || {
            log_error "Redis 连接失败"
            log_error "错误信息: $connection_test_output"
            log_error "请检查："
            log_error "  1. 主机地址和端口是否正确"
            log_error "  2. 密码是否正确"
            log_error "  3. 防火墙设置是否允许连接"
            return 1
        }
    else
        connection_test_output=$(timeout 10 docker run --rm \
            redis:7-alpine \
            redis-cli -h "$host" -p "$port" ping 2>&1) || {
            log_error "Redis 连接失败"
            log_error "错误信息: $connection_test_output"
            log_error "请检查："
            log_error "  1. 主机地址和端口是否正确"
            log_error "  2. 防火墙设置是否允许连接"
            return 1
        }
    fi
    
    if echo "$connection_test_output" | grep -q "PONG"; then
        log_success "Redis 连接验证成功"
        return 0
    else
        log_error "Redis 连接验证失败: $connection_test_output"
        return 1
    fi
}

validate_tdengine_connection() {
    local host="$1"
    local port="$2"
    local user="$3"
    local password="$4"
    
    log_info "验证 TDengine 连接: $user@$host:$port"
    
    # Test connection using curl to TDengine REST API
    local connection_test_output
    connection_test_output=$(timeout 10 curl -s \
        -H "Authorization: Basic $(echo -n "$user:$password" | base64)" \
        "http://$host:$port/rest/sql" \
        -d "SELECT SERVER_VERSION();" 2>&1) || {
        log_error "TDengine 连接失败"
        log_error "错误信息: $connection_test_output"
        log_error "请检查："
        log_error "  1. 主机地址和端口是否正确"
        log_error "  2. 用户名和密码是否正确"
        log_error "  3. TDengine 服务是否运行"
        log_error "  4. 防火墙设置是否允许连接"
        return 1
    }
    
    if echo "$connection_test_output" | grep -q '"code":0'; then
        log_success "TDengine 连接验证成功"
        return 0
    else
        log_error "TDengine 连接验证失败: $connection_test_output"
        return 1
    fi
}

# Check if ports are available
check_ports_available() {
    local ports=("$@")
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        # Check if port is in use using multiple methods for better compatibility
        local port_in_use=false
        
        # Method 1: netstat (if available)
        if command_exists netstat; then
            if netstat -tuln 2>/dev/null | grep -q ":${port} "; then
                port_in_use=true
            fi
        # Method 2: ss (if available)
        elif command_exists ss; then
            if ss -tuln 2>/dev/null | grep -q ":${port} "; then
                port_in_use=true
            fi
        # Method 3: lsof (if available)
        elif command_exists lsof; then
            if lsof -i ":${port}" 2>/dev/null | grep -q LISTEN; then
                port_in_use=true
            fi
        # Method 4: nc (netcat) test
        elif command_exists nc; then
            if nc -z localhost "$port" 2>/dev/null; then
                port_in_use=true
            fi
        fi
        
        if [ "$port_in_use" = true ]; then
            occupied_ports+=("$port")
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_error "以下端口被占用: ${occupied_ports[*]}"
        log_error "Caddy 需要端口 80 和 443 来提供 HTTPS 服务"
        log_error "请释放这些端口或停止占用它们的服务"
        return 1
    fi
    
    log_success "端口 ${ports[*]} 可用"
    return 0
}

# Check prerequisites
check_prerequisites() {
    log_info "检查系统环境..."
    
    if ! command_exists docker; then
        log_error "Docker 未安装！请先安装 Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        if ! command_exists docker compose; then
            log_error "Docker Compose 未安装！请先安装 Docker Compose"
            exit 1
        else
            DOCKER_COMPOSE_CMD="docker compose"
        fi
    else
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
    
    if ! command_exists openssl; then
        log_error "OpenSSL 未安装！请先安装 OpenSSL"
        exit 1
    fi
    
    if ! command_exists curl; then
        log_error "curl 未安装！请先安装 curl"
        exit 1
    fi
    
    # Check required ports availability
    log_info "检查必要端口可用性..."
    local required_ports=(8080 3100 6379 5432)
    if ! check_ports_available "${required_ports[@]}"; then
        echo
        log_error "必要端口被占用！ZFC 系统需要以下端口："
        log_error "  - 8080: 前端 Web 服务"
        log_error "  - 3100: 控制器服务"
        log_error "  - 6379: Redis 数据库"
        log_error "  - 5432: PostgreSQL 数据库"
        log_error ""
        log_error "请释放被占用的端口后重新运行安装脚本"
        log_error "您可以使用以下命令查看端口占用情况："
        log_error "  - netstat -tuln | grep :端口号"
        log_error "  - lsof -i :端口号"
        echo
        read -p "按任意键退出..." -n 1
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# Database configuration functions
configure_postgres() {
    echo
    log_info "配置 PostgreSQL 数据库..."
    echo "1. 使用内置 PostgreSQL 容器（推荐）"
    echo "2. 使用外部 PostgreSQL 数据库"
    
    while true; do
        read -p "请选择 PostgreSQL 配置 [1-2]: " postgres_choice
        case $postgres_choice in
            1)
                POSTGRES_TYPE="builtin"
                log_info "将使用内置 PostgreSQL 容器"
                break
                ;;
            2)
                POSTGRES_TYPE="external"
                log_info "配置外部 PostgreSQL 连接..."
                
                while true; do
                    read -p "请输入 PostgreSQL 主机地址: " EXTERNAL_POSTGRES_HOST
                    if [[ -n "$EXTERNAL_POSTGRES_HOST" ]]; then
                        break
                    else
                        log_error "主机地址不能为空"
                    fi
                done
                
                read -p "请输入端口 [默认: 5432]: " EXTERNAL_POSTGRES_PORT
                EXTERNAL_POSTGRES_PORT=${EXTERNAL_POSTGRES_PORT:-5432}
                
                while true; do
                    read -p "请输入用户名: " EXTERNAL_POSTGRES_USER
                    if [[ -n "$EXTERNAL_POSTGRES_USER" ]]; then
                        break
                    else
                        log_error "用户名不能为空"
                    fi
                done
                
                while true; do
                    read -s -p "请输入密码: " EXTERNAL_POSTGRES_PASSWORD
                    echo
                    if [[ -n "$EXTERNAL_POSTGRES_PASSWORD" ]]; then
                        break
                    else
                        log_error "密码不能为空"
                    fi
                done
                
                while true; do
                    read -p "请输入数据库名 [默认: zfc]: " EXTERNAL_POSTGRES_DB
                    EXTERNAL_POSTGRES_DB=${EXTERNAL_POSTGRES_DB:-zfc}
                    break
                done
                
                break
                ;;
            *)
                log_error "无效选择，请输入 1 或 2"
                ;;
        esac
    done
}

configure_redis() {
    echo
    log_info "配置 Redis 数据库..."
    echo "1. 使用内置 Redis 容器（推荐）"
    echo "2. 使用外部 Redis 数据库"
    
    while true; do
        read -p "请选择 Redis 配置 [1-2]: " redis_choice
        case $redis_choice in
            1)
                REDIS_TYPE="builtin"
                log_info "将使用内置 Redis 容器"
                break
                ;;
            2)
                REDIS_TYPE="external"
                log_info "配置外部 Redis 连接..."
                
                while true; do
                    read -p "请输入 Redis 主机地址: " EXTERNAL_REDIS_HOST
                    if [[ -n "$EXTERNAL_REDIS_HOST" ]]; then
                        break
                    else
                        log_error "主机地址不能为空"
                    fi
                done
                
                read -p "请输入端口 [默认: 6379]: " EXTERNAL_REDIS_PORT
                EXTERNAL_REDIS_PORT=${EXTERNAL_REDIS_PORT:-6379}
                
                read -s -p "请输入密码（可选，直接回车跳过）: " EXTERNAL_REDIS_PASSWORD
                echo
                
                break
                ;;
            *)
                log_error "无效选择，请输入 1 或 2"
                ;;
        esac
    done
}

configure_tdengine() {
    echo
    log_info "配置 TDengine 数据库..."
    echo "1. 使用内置 TDengine 容器（推荐）"
    echo "2. 使用外部 TDengine 数据库"
    
    while true; do
        read -p "请选择 TDengine 配置 [1-2]: " tdengine_choice
        case $tdengine_choice in
            1)
                TDENGINE_TYPE="builtin"
                log_info "将使用内置 TDengine 容器"
                break
                ;;
            2)
                TDENGINE_TYPE="external"
                log_info "配置外部 TDengine 连接..."
                
                while true; do
                    read -p "请输入 TDengine 主机地址: " EXTERNAL_TDENGINE_HOST
                    if [[ -n "$EXTERNAL_TDENGINE_HOST" ]]; then
                        break
                    else
                        log_error "主机地址不能为空"
                    fi
                done
                
                read -p "请输入端口 [默认: 6030]: " EXTERNAL_TDENGINE_PORT
                EXTERNAL_TDENGINE_PORT=${EXTERNAL_TDENGINE_PORT:-6030}
                
                read -p "请输入用户名 [默认: root]: " EXTERNAL_TDENGINE_USER
                EXTERNAL_TDENGINE_USER=${EXTERNAL_TDENGINE_USER:-root}
                
                while true; do
                    read -s -p "请输入密码: " EXTERNAL_TDENGINE_PASSWORD
                    echo
                    if [[ -n "$EXTERNAL_TDENGINE_PASSWORD" ]]; then
                        break
                    else
                        log_error "密码不能为空"
                    fi
                done
                
                break
                ;;
            *)
                log_error "无效选择，请输入 1 或 2"
                ;;
        esac
    done
}

configure_caddy() {
    echo
    log_info "配置 Caddy 反向代理..."
    echo "Caddy 可以为您的域名自动配置 HTTPS 证书和反向代理"
    echo "这将需要占用端口 80 和 443"
    echo
    
    while true; do
        read -p "是否配置 Caddy 反向代理？[Y/n]: " caddy_choice
        case ${caddy_choice,,} in
            y|yes|"")
                CADDY_ENABLED="true"
                log_info "将配置 Caddy 反向代理"
                
                # Check if ports 80 and 443 are available
                log_info "检查端口 80 和 443 是否可用..."
                if ! check_ports_available 80 443; then
                    log_error "无法配置 Caddy，请先释放端口 80 和 443"
                    log_warning "您可以稍后手动配置反向代理"
                    CADDY_ENABLED="false"
                    break
                fi
                
                # Get email for TLS certificates
                while true; do
                    read -p "请输入用于 TLS 证书的邮箱地址: " CADDY_EMAIL
                    if [[ -n "$CADDY_EMAIL" && "$CADDY_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
                        break
                    else
                        log_error "邮箱格式不正确，请重新输入"
                    fi
                done
                
                log_info "将使用 Caddy Docker 容器提供反向代理服务"
                
                # Cloudflare warning for Caddy users
                echo
                log_warning "重要提醒：您已启用 Caddy 反向代理"
                log_warning "如果您使用 Cloudflare 管理域名，请确保："
                log_warning "• 前端域名和控制器域名都不要开启小黄云（代理功能）"
                log_warning "• Caddy 将处理 HTTPS 证书和反向代理，Cloudflare 代理会产生冲突"
                echo
                
                break
                ;;
            n|no)
                CADDY_ENABLED="false"
                log_info "跳过 Caddy 配置，您需要手动设置反向代理"
                break
                ;;
            *)
                log_error "请输入 y 或 n"
                ;;
        esac
    done
}

# Check domain and license preparation
check_preparation() {
    echo
    log_info "全新安装前准备检查..."
    echo
    echo -e "${YELLOW}在开始安装之前，请确认您已准备好以下内容:${NC}"
    echo
    echo "1. ${GREEN}域名准备${NC}:"
    echo "   - 前端网页域名 (例如: forward.example.com)"
    echo "   - 控制器网页域名 (例如: zf-controler.example.com)"
    echo "   - 这两个域名需要解析到当前机器的公网IP地址"
    echo "   - ${RED}重要：如果使用 Cloudflare DNS 服务：${NC}"
    echo "     ${RED}• 如果启用 Caddy 反向代理，两个域名都不能开启小黄云（代理功能）${NC}"
    echo "     ${RED}• 如果不使用 Caddy，至少控制器域名不能开启小黄云${NC}"
    echo
    echo "2. ${GREEN}License 配置${NC}:"
    echo "   - ZFC_INSTANCE_ID (实例标识符)"
    echo "   - ZFC_API_KEY (API密钥)"
    echo "   - 请确保您已从 ZFC 授权方获得有效的许可证信息"
    echo
    echo -e "${YELLOW}注意事项:${NC}"
    echo "- 域名必须指向本机公网IP，否则HTTPS证书申请将失败"
    echo "- 如果使用Caddy自动HTTPS，需要确保80和443端口对外开放"
    echo "- License信息错误将导致系统无法正常运行"
    echo
    
    while true; do
        read -p "确认已准备好上述内容？[y/N]: " preparation_confirmed
        case ${preparation_confirmed,,} in
            y|yes)
                log_success "准备检查通过，开始配置..."
                break
                ;;
            n|no|"")
                log_warning "请准备好必要的域名和License信息后重新运行安装脚本"
                exit 0
                ;;
            *)
                log_error "请输入 y 或 n"
                ;;
        esac
    done
    echo
}

# Get user input
get_user_input() {
    log_info "配置系统参数..."
    
    # Web domain
    while true; do
        read -p "请输入前端网页域名 (例如: forward.example.com): " WEB_DOMAIN
        if [[ -n "$WEB_DOMAIN" && "$WEB_DOMAIN" =~ ^[a-zA-Z0-9.-]+$ ]]; then
            break
        else
            log_error "域名格式不正确，请重新输入"
        fi
    done
    
    # Controller domain
    while true; do
        read -p "请输入控制器网页域名 (例如: zf-controler.example.com): " CONTROLER_DOMAIN
        if [[ -n "$CONTROLER_DOMAIN" && "$CONTROLER_DOMAIN" =~ ^[a-zA-Z0-9.-]+$ ]]; then
            break
        else
            log_error "域名格式不正确，请重新输入"
        fi
    done
    
    # Cloudflare warning for controller domain
    echo
    log_warning "重要提醒：如果您使用 Cloudflare 管理域名"
    log_warning "控制器域名 ${CONTROLER_DOMAIN} 不能开启小黄云（代理功能）"
    log_warning "如果稍后启用 Caddy，则前端和控制器域名都不能开启小黄云"
    
    # License info
    while true; do
        read -p "请输入 ZFC_INSTANCE_ID: " ZFC_INSTANCE_ID
        if [[ -n "$ZFC_INSTANCE_ID" ]]; then
            break
        else
            log_error "ZFC_INSTANCE_ID 不能为空"
        fi
    done
    
    while true; do
        read -p "请输入 ZFC_API_KEY: " ZFC_API_KEY
        if [[ -n "$ZFC_API_KEY" ]]; then
            break
        else
            log_error "ZFC_API_KEY 不能为空"
        fi
    done
    
    # Database configuration
    configure_postgres
    configure_redis
    configure_tdengine
    
    # Caddy configuration
    configure_caddy
    
    # Always execute Prisma migration to ensure consistency
    echo
    log_info "数据库初始化选项..."
    if [[ "$POSTGRES_TYPE" == "external" ]]; then
        echo "检测到您使用外部 PostgreSQL 数据库"
        log_info "自动执行 Prisma 数据库迁移以确保一致性"
    fi
    RUN_PRISMA_MIGRATION="true"
    
    # Docker registry configuration
    read -p "Docker 镜像仓库地址 [默认: hub.covm.net]: " DOCKER_REGISTRY
    DOCKER_REGISTRY=${DOCKER_REGISTRY:-"hub.covm.net"}
    
    read -p "镜像标签 [默认: latest]: " IMAGE_TAG
    IMAGE_TAG=${IMAGE_TAG:-"latest"}
    
    # Generate image addresses based on registry
    ZF_WEB_IMAGE="${DOCKER_REGISTRY}/zf-web:${IMAGE_TAG}"
    ZF_CONTROLER_IMAGE="${DOCKER_REGISTRY}/zf-controler:${IMAGE_TAG}"
    RRD_SERVICE_IMAGE="${DOCKER_REGISTRY}/rrd-service:${IMAGE_TAG}"
    ZFC_UTIL_IMAGE="${DOCKER_REGISTRY}/zfc-util:${IMAGE_TAG}"
    ZFC_ADMIN_IMAGE="${DOCKER_REGISTRY}/zfc-admin:${IMAGE_TAG}"
    
    log_info "使用镜像地址:"
    log_info "  ZF-Web: $ZF_WEB_IMAGE"
    log_info "  ZF-Controller: $ZF_CONTROLER_IMAGE"
    log_info "  RRD Service: $RRD_SERVICE_IMAGE"
    log_info "  ZFC-Util: $ZFC_UTIL_IMAGE"
    log_info "  ZFC-Admin: $ZFC_ADMIN_IMAGE"
    
    echo
    log_info "数据库配置总结:"
    log_info "  PostgreSQL: $POSTGRES_TYPE"
    log_info "  Redis: $REDIS_TYPE"
    log_info "  TDengine: $TDENGINE_TYPE"
    if [[ "$POSTGRES_TYPE" == "external" ]]; then
        log_info "  Prisma 迁移: $RUN_PRISMA_MIGRATION"
    fi
    
    log_success "用户输入完成"
}

# Generate configuration
generate_config() {
    log_info "生成配置文件..."
    
    # Generate passwords
    POSTGRES_PASSWORD=$(generate_password)
    REDIS_PASSWORD=$(generate_password)
    TDENGINE_ROOT_PASSWORD=$(generate_password)
    JWT_SECRET=$(generate_jwt_secret)
    
    log_info "生成数据库密码..."
    log_info "生成 JWT 密钥..."
    
    # Pre-pull required images to avoid timeout issues
    log_info "预拉取必要的镜像..."
    docker pull "$ZFC_UTIL_IMAGE" &
    docker pull "$ZFC_ADMIN_IMAGE" &
    if [[ -n "$RRD_SERVICE_IMAGE" ]]; then
        docker pull "$RRD_SERVICE_IMAGE" &
    fi
    
    # Wait for zfc-util image to be ready for key generation
    wait
    
    # Generate management key pair using zfc-util
    log_info "生成管理密钥对..."
    
    # Generate key pair
    KEY_OUTPUT=$(docker run --rm "$ZFC_UTIL_IMAGE" generate-key)
    
    MGMT_ARRANGER_PRIV_KEY=$(echo "$KEY_OUTPUT" | grep "private_key:" | awk '{print $2}')
    MGMT_PUBKEY=$(echo "$KEY_OUTPUT" | grep "public_key:" | awk '{print $2}')
    
    if [[ -z "$MGMT_ARRANGER_PRIV_KEY" || -z "$MGMT_PUBKEY" ]]; then
        log_error "密钥对生成失败"
        exit 1
    fi
    
    log_success "密钥对生成完成"
    log_success "镜像预拉取完成"
}

# Generate docker-compose configuration based on database choices
generate_docker_compose() {
    log_info "生成 Docker Compose 配置..."
    
    # Download base template first
    if [[ -f "docker-compose.template.yml" ]]; then
        log_info "使用本地模板文件"
        cp docker-compose.template.yml docker-compose.yml
    else
        log_info "从远程下载模板文件..."
        TEMPLATE_URL="https://img.coderluny.com:444/uploads/docker-compose.template.yml"
        curl -fsSL "$TEMPLATE_URL" -o docker-compose.yml
    fi
    
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "无法获取 Docker Compose 模板"
        exit 1
    fi
    
    # Create backup of original template
    cp docker-compose.yml docker-compose.full.yml
    
    # Remove services based on external database choices
    local services_to_remove=()
    
    if [[ "$POSTGRES_TYPE" == "external" ]]; then
        services_to_remove+=("postgres")
        log_info "移除 PostgreSQL 服务（使用外部数据库）"
    fi
    
    if [[ "$REDIS_TYPE" == "external" ]]; then
        services_to_remove+=("redis")
        log_info "移除 Redis 服务（使用外部数据库）"
    fi
    
    if [[ "$TDENGINE_TYPE" == "external" ]]; then
        services_to_remove+=("tdengine" "tdengine-init")
        log_info "移除 TDengine 服务（使用外部数据库）"
    fi
    
    if [[ "$CADDY_ENABLED" != "true" ]]; then
        services_to_remove+=("caddy")
        log_info "移除 Caddy 服务（用户未启用）"
    fi
    
    # Remove services from docker-compose.yml
    for service in "${services_to_remove[@]}"; do
        log_info "从 docker-compose.yml 中移除 $service 服务..."
        
        # Use sed to remove service section
        # Find the service definition and remove it completely
        if grep -q "^  $service:" docker-compose.yml; then
            # Create a temporary file to work with
            local temp_file=$(mktemp)
            
            # Use awk to remove the service section
            awk -v service="$service" '
            BEGIN { in_service = 0; indent_level = 0 }
            /^  [a-zA-Z0-9_-]+:/ { 
                if ($0 ~ "^  " service ":") {
                    in_service = 1
                    indent_level = length($0) - length(ltrim($0))
                    next
                } else {
                    in_service = 0
                }
            }
            in_service && /^    / { 
                current_indent = length($0) - length(ltrim($0))
                if (current_indent > indent_level) {
                    next
                } else {
                    in_service = 0
                }
            }
            in_service { next }
            { print }
            function ltrim(str) { gsub(/^[ \t\r\n]+/, "", str); return str }
            ' docker-compose.yml > "$temp_file"
            
            mv "$temp_file" docker-compose.yml
        fi
    done
    
    # Also remove volumes for external databases
    if [[ "$POSTGRES_TYPE" == "external" ]]; then
        sed_inplace docker-compose.yml '/postgres_data:/d' 2>/dev/null || true
    fi
    
    if [[ "$REDIS_TYPE" == "external" ]]; then
        sed_inplace docker-compose.yml '/redis_data:/d' 2>/dev/null || true
    fi
    
    if [[ "$TDENGINE_TYPE" == "external" ]]; then
        sed_inplace docker-compose.yml '/tdengine_data:/d' 2>/dev/null || true
        sed_inplace docker-compose.yml '/tdengine_log:/d' 2>/dev/null || true
    fi
    
    if [[ "$CADDY_ENABLED" != "true" ]]; then
        sed_inplace docker-compose.yml '/caddy_data:/d' 2>/dev/null || true
        sed_inplace docker-compose.yml '/caddy_config:/d' 2>/dev/null || true
    fi
    
    log_success "Docker Compose 配置生成完成"
    log_info "完整模板备份为: docker-compose.full.yml"
}

# Create environment file
create_env_file() {
    log_info "创建环境变量文件..."
    
    # Generate database connection URLs based on configuration
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        POSTGRES_URL="postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/zfc?schema=public"
    else
        POSTGRES_URL="postgresql://${EXTERNAL_POSTGRES_USER}:${EXTERNAL_POSTGRES_PASSWORD}@${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT}/${EXTERNAL_POSTGRES_DB}?schema=public"
    fi
    
    if [[ "$REDIS_TYPE" == "builtin" ]]; then
        REDIS_URL="redis://:${REDIS_PASSWORD}@redis:6379"
    else
        if [[ -n "$EXTERNAL_REDIS_PASSWORD" ]]; then
            REDIS_URL="redis://:${EXTERNAL_REDIS_PASSWORD}@${EXTERNAL_REDIS_HOST}:${EXTERNAL_REDIS_PORT}"
        else
            REDIS_URL="redis://${EXTERNAL_REDIS_HOST}:${EXTERNAL_REDIS_PORT}"
        fi
    fi
    
    if [[ "$TDENGINE_TYPE" == "builtin" ]]; then
        TDENGINE_URL="http://root:${TDENGINE_ROOT_PASSWORD}@tdengine:6030"
    else
        TDENGINE_URL="http://${EXTERNAL_TDENGINE_USER}:${EXTERNAL_TDENGINE_PASSWORD}@${EXTERNAL_TDENGINE_HOST}:${EXTERNAL_TDENGINE_PORT}"
    fi
    
    cat > .env << EOF
# Database Types (builtin/external)
POSTGRES_TYPE=$(quote_env_value "${POSTGRES_TYPE}")
REDIS_TYPE=$(quote_env_value "${REDIS_TYPE}")
TDENGINE_TYPE=$(quote_env_value "${TDENGINE_TYPE}")

# Database Configuration (for builtin databases)
POSTGRES_PASSWORD=$(quote_env_value "${POSTGRES_PASSWORD:-}")
REDIS_PASSWORD=$(quote_env_value "${REDIS_PASSWORD:-}")
TDENGINE_ROOT_PASSWORD=$(quote_env_value "${TDENGINE_ROOT_PASSWORD:-}")

# Database Connection URLs
POSTGRES_URL=$(quote_env_value "${POSTGRES_URL}")
REDIS_URL=$(quote_env_value "${REDIS_URL}")
TDENGINE_URL=$(quote_env_value "${TDENGINE_URL}")

# Legacy database environment variables (for backward compatibility)
DB_PATH=$(quote_env_value "${POSTGRES_URL}")
REDIS_PATH=$(quote_env_value "${REDIS_URL}")

# Prisma Configuration
RUN_PRISMA_MIGRATION=$(quote_env_value "${RUN_PRISMA_MIGRATION}")

# Domain Configuration
WEB_DOMAIN=$(quote_env_value "${WEB_DOMAIN}")
CONTROLER_DOMAIN=$(quote_env_value "${CONTROLER_DOMAIN}")

# License Configuration
ZFC_INSTANCE_ID=$(quote_env_value "${ZFC_INSTANCE_ID}")
ZFC_API_KEY=$(quote_env_value "${ZFC_API_KEY}")

# Management Keys
MGMT_ARRANGER_PRIV_KEY=$(quote_env_value "${MGMT_ARRANGER_PRIV_KEY}")
MGMT_PUBKEY=$(quote_env_value "${MGMT_PUBKEY}")

# JWT Secret
JWT_SECRET=$(quote_env_value "${JWT_SECRET}")

# Docker Images
ZF_WEB_IMAGE=$(quote_env_value "${ZF_WEB_IMAGE}")
ZF_CONTROLER_IMAGE=$(quote_env_value "${ZF_CONTROLER_IMAGE}")
RRD_SERVICE_IMAGE=$(quote_env_value "${RRD_SERVICE_IMAGE}")
ZFC_UTIL_IMAGE=$(quote_env_value "${ZFC_UTIL_IMAGE}")
ZFC_ADMIN_IMAGE=$(quote_env_value "${ZFC_ADMIN_IMAGE}")

# Caddy Configuration
CADDY_ENABLED=$(quote_env_value "${CADDY_ENABLED:-false}")
CADDY_EMAIL=$(quote_env_value "${CADDY_EMAIL:-}")
EOF
    
    log_success "环境文件创建完成"
    log_info "数据库连接配置:"
    log_info "  PostgreSQL: $POSTGRES_TYPE"
    log_info "  Redis: $REDIS_TYPE"
    log_info "  TDengine: $TDENGINE_TYPE"
}

# Generate Caddyfile
generate_caddyfile() {
    if [[ "$CADDY_ENABLED" != "true" ]]; then
        log_info "跳过 Caddyfile 生成"
        return 0
    fi
    
    log_info "生成 Caddyfile 配置..."
    
    cat > Caddyfile << EOF
# ZFC Caddy 配置文件
# 自动生成于 $(date)

# 前端 Web 服务
${WEB_DOMAIN} {
    tls ${CADDY_EMAIL}
    encode gzip
    reverse_proxy zf-web:3030
    
    # 错误处理
    handle_errors {
        @5xx expression {http.error.status_code} >= 500
        respond "服务暂时不可用，请稍后重试" 503
    }
    
    # 日志记录
    log {
        level INFO
        format console
    }
}

# 控制器服务
${CONTROLER_DOMAIN} {
    tls ${CADDY_EMAIL}
    encode gzip
    reverse_proxy zf-controler:3100
    
    # 错误处理
    handle_errors {
        @5xx expression {http.error.status_code} >= 500
        respond "服务暂时不可用，请稍后重试" 503
    }
    
    # 日志记录
    log {
        level INFO
        format console
    }
}
EOF
    
    log_success "Caddyfile 配置生成完成"
    log_info "配置文件位置: $(pwd)/Caddyfile"
}

# Setup Caddy service
setup_caddy() {
    if [[ "$CADDY_ENABLED" != "true" ]]; then
        return 0
    fi
    
    log_info "启动 Caddy Docker 容器..."
    
    # Start Caddy with the caddy profile
    $DOCKER_COMPOSE_CMD --profile caddy up -d caddy
    
    # Wait for Caddy to be ready
    log_info "等待 Caddy 启动..."
    local retry_count=0
    until $DOCKER_COMPOSE_CMD exec -T caddy caddy version > /dev/null 2>&1; do
        sleep 2
        retry_count=$((retry_count + 1))
        if [ $retry_count -gt 30 ]; then
            log_error "Caddy 启动超时"
            return 1
        fi
    done
    
    # Test configuration
    log_info "验证 Caddy 配置..."
    if $DOCKER_COMPOSE_CMD exec -T caddy caddy validate --config /etc/caddy/Caddyfile; then
        log_success "Caddy 配置验证通过"
    else
        log_warning "Caddy 配置验证失败，请检查配置"
    fi
    
    # Check if Caddy is healthy
    sleep 5
    if $DOCKER_COMPOSE_CMD ps caddy | grep -q "healthy\|Up"; then
        log_success "Caddy 容器启动成功"
        log_info "HTTPS 证书将自动申请和续期"
    else
        log_error "Caddy 容器启动失败"
        log_info "请检查日志: $DOCKER_COMPOSE_CMD logs caddy"
        return 1
    fi
}

# Initialize PostgreSQL
init_postgres() {
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        log_info "初始化内置 PostgreSQL..."
        
        # Clean up existing postgres volumes if needed
        local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
        if docker volume ls -q --filter "name=${project_name}_postgres" | head -1 >/dev/null 2>&1; then
            log_info "清理现有 PostgreSQL 数据卷..."
            docker volume ls -q --filter "name=${project_name}_postgres" | xargs -r docker volume rm 2>/dev/null || true
        fi
        
        # Start PostgreSQL service
        $DOCKER_COMPOSE_CMD up -d postgres
        
        # Wait for postgres to be ready
        log_info "等待 PostgreSQL 启动..."
        local retry_count=0
        until $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U postgres; do
            sleep 2
            retry_count=$((retry_count + 1))
            if [ $retry_count -gt 30 ]; then
                log_error "PostgreSQL 启动超时"
                return 1
            fi
        done
        
        # Additional wait to ensure PostgreSQL is fully initialized
        log_info "等待 PostgreSQL 完全初始化..."
        sleep 5
        
        # Test PostgreSQL readiness with actual password
        log_info "测试 PostgreSQL 密码认证..."
        local retry_count=0
        until $DOCKER_COMPOSE_CMD exec -T postgres sh -c "PGPASSWORD=\"${POSTGRES_PASSWORD}\" psql -U postgres -c 'SELECT 1;'" > /dev/null 2>&1; do
            sleep 2
            retry_count=$((retry_count + 1))
            if [ $retry_count -gt 20 ]; then
                log_error "PostgreSQL 密码认证失败"
                return 1
            fi
        done
        
        log_success "内置 PostgreSQL 初始化完成"
    else
        log_info "验证外部 PostgreSQL 连接..."
        if validate_postgres_connection "$EXTERNAL_POSTGRES_HOST" "$EXTERNAL_POSTGRES_PORT" "$EXTERNAL_POSTGRES_USER" "$EXTERNAL_POSTGRES_PASSWORD" "$EXTERNAL_POSTGRES_DB"; then
            log_success "外部 PostgreSQL 连接验证成功"
        else
            log_error "外部 PostgreSQL 连接验证失败"
            return 1
        fi
    fi
}

# Initialize Redis
init_redis() {
    if [[ "$REDIS_TYPE" == "builtin" ]]; then
        log_info "初始化内置 Redis..."
        
        # Clean up existing redis volumes if needed
        local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
        if docker volume ls -q --filter "name=${project_name}_redis" | head -1 >/dev/null 2>&1; then
            log_info "清理现有 Redis 数据卷..."
            docker volume ls -q --filter "name=${project_name}_redis" | xargs -r docker volume rm 2>/dev/null || true
        fi
        
        # Start Redis service
        $DOCKER_COMPOSE_CMD up -d redis
        
        # Wait for redis to be ready
        log_info "等待 Redis 启动..."
        until $DOCKER_COMPOSE_CMD exec -T redis redis-cli --no-auth-warning -a "$REDIS_PASSWORD" ping | grep -q PONG; do
            sleep 2
        done
        
        log_success "内置 Redis 初始化完成"
    else
        log_info "验证外部 Redis 连接..."
        if validate_redis_connection "$EXTERNAL_REDIS_HOST" "$EXTERNAL_REDIS_PORT" "$EXTERNAL_REDIS_PASSWORD"; then
            log_success "外部 Redis 连接验证成功"
        else
            log_error "外部 Redis 连接验证失败"
            return 1
        fi
    fi
}

# Initialize TDengine
init_tdengine() {
    if [[ "$TDENGINE_TYPE" == "builtin" ]]; then
        log_info "初始化内置 TDengine..."
        
        # Clean up existing tdengine volumes if needed
        local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
        if docker volume ls -q --filter "name=${project_name}_tdengine" | head -1 >/dev/null 2>&1; then
            log_info "清理现有 TDengine 数据卷..."
            docker volume ls -q --filter "name=${project_name}_tdengine" | xargs -r docker volume rm 2>/dev/null || true
        fi
        
        # Start TDengine service
        $DOCKER_COMPOSE_CMD up -d tdengine
        
        # Start TDengine password initialization
        log_info "启动 TDengine 密码初始化..."
        $DOCKER_COMPOSE_CMD --profile init up tdengine-init
        
        # Wait for TDengine initialization to complete
        log_info "等待 TDengine 密码初始化完成..."
        local retry_count=0
        while docker ps --filter "name=zfc-tdengine-init" --filter "status=running" | grep -q zfc-tdengine-init; do
            sleep 2
            retry_count=$((retry_count + 1))
            if [ $retry_count -gt 60 ]; then
                log_warning "TDengine 初始化等待超时，继续执行..."
                break
            fi
        done
        
        # Check if initialization was successful
        if docker ps -a --filter "name=zfc-tdengine-init" --filter "exited=0" | grep -q zfc-tdengine-init; then
            log_success "TDengine 密码初始化成功"
            
            # Remove the initialization service from docker-compose.yml to avoid future delays
            log_info "清理临时初始化服务..."
            if grep -q "tdengine-init:" docker-compose.yml; then
                cp docker-compose.yml docker-compose.yml.backup
                sed_inplace docker-compose.yml '/# TEMPORARY INITIALIZATION SERVICE/,/restart: "no"/d'
                log_success "已从 docker-compose.yml 中移除临时初始化服务"
            fi
            
            # Remove the container
            docker rm -f zfc-tdengine-init 2>/dev/null || true
            log_success "已清理初始化容器"
        else
            log_warning "TDengine 初始化可能失败，请检查日志: docker logs zfc-tdengine-init"
        fi
        
        log_success "内置 TDengine 初始化完成"
    else
        log_info "验证外部 TDengine 连接..."
        if validate_tdengine_connection "$EXTERNAL_TDENGINE_HOST" "$EXTERNAL_TDENGINE_PORT" "$EXTERNAL_TDENGINE_USER" "$EXTERNAL_TDENGINE_PASSWORD"; then
            log_success "外部 TDengine 连接验证成功"
        else
            log_error "外部 TDengine 连接验证失败"
            return 1
        fi
    fi
}

# Run Prisma migrations
run_prisma_migration() {
    if [[ "$RUN_PRISMA_MIGRATION" != "true" ]]; then
        log_info "跳过 Prisma 数据库迁移"
        return 0
    fi
    
    log_info "执行数据库迁移..."
    
    # Get schema from local file or download from remote
    if [[ -f "prisma/schema.prisma" ]]; then
        SCHEMA_DIR="$(pwd)/prisma"
        log_info "使用本地 Prisma schema"
    else
        log_info "未找到本地 prisma/schema.prisma 文件，从远程下载..."
        mkdir -p prisma
        SCHEMA_URL="https://img.coderluny.com:444/uploads/schema.prisma"
        curl -fsSL "$SCHEMA_URL" -o prisma/schema.prisma
        
        if [[ ! -f "prisma/schema.prisma" ]]; then
            log_error "无法下载 Prisma schema 文件"
            return 1
        fi
        
        SCHEMA_DIR="$(pwd)/prisma"
        log_success "Prisma schema 下载完成"
    fi
    
    # Determine the correct network name for migration
    local network_name network_arg db_url
    
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        # Get the actual compose network name
        network_name=$(get_compose_network)
        log_info "检测到 Docker Compose 网络: $network_name"
        
        # Verify the network exists, create if it doesn't
        if ! docker network inspect "$network_name" >/dev/null 2>&1; then
            log_info "网络不存在，创建网络: $network_name"
            docker network create "$network_name" || {
                log_error "无法创建网络 $network_name"
                return 1
            }
        fi
        
        # Use internal database URL for builtin PostgreSQL
        db_url="postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/zfc?schema=public"
        network_arg="--network $network_name"
    else
        # Use external database URL
        db_url="postgresql://${EXTERNAL_POSTGRES_USER}:${EXTERNAL_POSTGRES_PASSWORD}@${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT}/${EXTERNAL_POSTGRES_DB}?schema=public"
        network_arg=""
    fi
    
    log_info "运行 Prisma 迁移..."
    log_info "使用数据库 URL: ${db_url%:*}:****@${db_url#*@}"
    
    docker run --rm \
        $network_arg \
        -v "$SCHEMA_DIR:/app/prisma" \
        -w /app \
        -e DATABASE_URL="$db_url" \
        node:18-alpine \
        sh -c "
            echo '安装 Prisma CLI...' &&
            npm install -g prisma @prisma/client &&
            echo '修改 schema 文件以支持 npx prisma...' &&
            sed 's/provider = \"cargo prisma\"/provider = \"prisma-client-js\"/' prisma/schema.prisma > prisma/schema.prisma.tmp && mv prisma/schema.prisma.tmp prisma/schema.prisma &&
            sed 's|output.*|// output removed for npx compatibility|' prisma/schema.prisma > prisma/schema.prisma.tmp && mv prisma/schema.prisma.tmp prisma/schema.prisma &&
            echo '开始执行数据库迁移（跳过代码生成）...' &&
            npx prisma db push --schema=prisma/schema.prisma --accept-data-loss --skip-generate
        "
    
    log_success "Prisma 数据库迁移完成"
}

# Initialize database - main function
init_database() {
    log_info "初始化数据库..."
    
    # Wait for network to be created if using Docker services
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" || "$TDENGINE_TYPE" == "builtin" ]]; then
        log_info "等待 Docker 网络创建..."
        sleep 3
    fi
    
    # Initialize each database component
    init_postgres
    init_redis
    init_tdengine
    
    # Run Prisma migrations
    run_prisma_migration
    
    log_success "数据库初始化完成"
}

# Create admin user
create_admin_user() {
    log_info "创建管理员账户..."
    
    # Create temporary directory if not exists
    local TEMP_DIR="/tmp"
    
    # Create temporary admin user JSON file
    local admin_json_file="$TEMP_DIR/admin_user.json"
    cat > "$admin_json_file" << EOF
{
  "users": [
    {
      "address": "<EMAIL>",
      "tg_user": null,
      "tg_chat_id": null,
      "bandwidth": null,
      "traffic": 1099511627776,
      "activated": true,
      "ports": [],
      "max_ports_per_server": 100,
      "bill_type": {
        "OneTime": {
          "price": 0,
          "days": 365
        }
      },
      "total_days": 3650,
      "lines": [],
      "is_admin": true
    }
  ]
}
EOF
    
    # Run zfc-admin to create user
    log_info "运行 zfc-admin 创建管理员用户..."
    
    # Determine the correct network name for admin creation
    local network_name
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" ]]; then
        network_name=$(get_compose_network)
        log_info "使用 Docker Compose 网络创建管理员: $network_name"
        
        # Verify network exists
        if ! docker network inspect "$network_name" >/dev/null 2>&1; then
            log_error "Docker Compose 网络 $network_name 不存在"
            log_error "请确保 Docker Compose 服务已启动"
            return 1
        fi
    else
        # For external databases, no network needed
        network_name=""
        log_info "使用外部数据库，无需 Docker 网络"
    fi
    
    # Test database connectivity from admin container
    log_info "测试数据库连接性..."
    local db_test_output
    
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        db_test_output=$(docker run --rm \
            --network "$network_name" \
            -e PGPASSWORD="${POSTGRES_PASSWORD}" \
            postgres:15-alpine \
            psql -h postgres -U postgres -d zfc -c "SELECT 1;" 2>&1) || {
            log_error "从 admin 容器无法连接内置数据库"
            log_error "数据库连接测试输出: $db_test_output"
            return 1
        }
    else
        # For external database, test connectivity directly
        if ! validate_postgres_connection "$EXTERNAL_POSTGRES_HOST" "$EXTERNAL_POSTGRES_PORT" "$EXTERNAL_POSTGRES_USER" "$EXTERNAL_POSTGRES_PASSWORD" "$EXTERNAL_POSTGRES_DB"; then
            log_error "外部 PostgreSQL 数据库连接失败"
            return 1
        fi
    fi
    
    log_success "数据库连接测试成功"
    
    # Test zfc-admin container first
    log_info "测试 zfc-admin 容器启动..."
    local test_output
    test_output=$(timeout 30 docker run --rm "$ZFC_ADMIN_IMAGE" --help 2>&1) || {
        log_warning "zfc-admin 容器启动测试失败"
        log_info "测试输出: $test_output"
        log_info "继续尝试运行 zfc-admin..."
    }
    if [[ $test_output == *"Usage:"* ]] || [[ $test_output == *"USAGE:"* ]]; then
        log_success "zfc-admin 容器测试成功"
    fi
    
    # Add debugging - show admin JSON content
    log_info "管理员用户配置:"
    cat "$admin_json_file"
    
    # Prepare database and Redis URLs based on configuration
    local db_url redis_url network_args
    
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        db_url="postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/zfc?schema=public"
    else
        db_url="postgresql://${EXTERNAL_POSTGRES_USER}:${EXTERNAL_POSTGRES_PASSWORD}@${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT}/${EXTERNAL_POSTGRES_DB}?schema=public"
    fi
    
    if [[ "$REDIS_TYPE" == "builtin" ]]; then
        redis_url="redis://:${REDIS_PASSWORD}@redis:6379"
    else
        if [[ -n "$EXTERNAL_REDIS_PASSWORD" ]]; then
            redis_url="redis://:${EXTERNAL_REDIS_PASSWORD}@${EXTERNAL_REDIS_HOST}:${EXTERNAL_REDIS_PORT}"
        else
            redis_url="redis://${EXTERNAL_REDIS_HOST}:${EXTERNAL_REDIS_PORT}"
        fi
    fi
    
    # Set network arguments based on database type
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" ]]; then
        network_args="--network $network_name"
    else
        network_args=""
    fi
    
    log_info "运行 zfc-admin 容器..."
    log_info "容器环境变量:"
    log_info "  DB_PATH=${db_url%:*}:****@${db_url#*@}"
    log_info "  REDIS_PATH=${redis_url%:*}:****@${redis_url#*@}"
    log_info "  ARRANGER_HOSTS_URL=https://${CONTROLER_DOMAIN}"
    
    # Use timeout to prevent hanging
    log_info "启动 zfc-admin 容器（60秒超时）..."
    
    local admin_output
    local exit_code
    
    # Run container with timeout in background and monitor
    {
        timeout 60 docker run --rm \
            $network_args \
            -v "$admin_json_file:/tmp/admin_user.json" \
            -e DB_PATH="$db_url" \
            -e REDIS_PATH="$redis_url" \
            -e MGMT_ARRANGER_PRIV_KEY="${MGMT_ARRANGER_PRIV_KEY}" \
            -e ARRANGER_HOSTS_URL="https://${CONTROLER_DOMAIN}" \
            "$ZFC_ADMIN_IMAGE" \
            add-user --user-file-path /tmp/admin_user.json
    } > /tmp/admin_output.log 2>&1
    
    exit_code=$?
    admin_output=$(cat /tmp/admin_output.log 2>/dev/null || echo "无法读取输出文件")
    
    log_info "zfc-admin 容器退出码: $exit_code"
    
    case $exit_code in
        0)
            log_success "zfc-admin 容器成功完成"
            ;;
        124)
            log_error "zfc-admin 容器执行超时（60秒）"
            log_error "这可能是由于网络连接问题或容器内部错误"
            ;;
        *)
            log_error "zfc-admin 容器执行失败"
            ;;
    esac
    
    log_info "zfc-admin 完整输出:"
    echo "$admin_output"
    
    if [[ $exit_code -ne 0 ]]; then
        log_error "zfc-admin 容器运行失败 (退出码: $exit_code)"
        return 1
    fi
    
    # Extract token from output
    local admin_token
    admin_token=$(echo "$admin_output" | grep "add user:" | grep "token:" | awk '{print $NF}')
    
    if [[ -z "$admin_token" ]]; then
        log_error "无法从输出中提取管理员 token"
        log_error "尝试查找其他 token 格式..."
        admin_token=$(echo "$admin_output" | grep -i "token" | tail -1 | awk '{print $NF}')
        
        if [[ -z "$admin_token" ]]; then
            log_error "仍然无法获取 token，请检查 zfc-admin 输出格式"
            return 1
        fi
    fi
    
    # Save admin token to file
    echo "$admin_token" > .admin_token
    
    log_success "管理员账户创建完成"
    log_info "管理员 Token: $admin_token"
    
    # Clean up
    rm -f "$admin_json_file"
    rm -f /tmp/admin_output.log
    
    return 0
}

# Start services
start_services() {
    log_info "启动所有服务..."
    
    # Pull all images
    log_info "拉取 Docker 镜像..."
    $DOCKER_COMPOSE_CMD pull
    
    # Start all services
    $DOCKER_COMPOSE_CMD up -d
    
    # Wait for services to be healthy
    log_info "等待服务启动..."
    sleep 30
    
    # Check service status
    if $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
        log_success "服务启动成功"
    else
        log_warning "部分服务可能未正常启动，请检查日志"
    fi
}

# Show final information
show_final_info() {
    echo
    log_success "=== ZFC 安装完成 ==="
    echo
    echo -e "${GREEN}访问地址:${NC}"
    echo -e "  前端界面: https://${WEB_DOMAIN}"
    echo
    echo -e "${GREEN}本地端口映射（仅供调试）:${NC}"
    echo -e "  前端界面: http://localhost:8080"
    echo
    echo -e "${GREEN}管理员登录信息:${NC}"
    if [[ -f ".admin_token" ]]; then
        local admin_token=$(cat .admin_token)
        echo -e "  管理员 Token: ${GREEN}${admin_token}${NC}"
        echo -e "  请使用此 Token 登录前端界面进行管理"
    else
        echo -e "  ${RED}未找到管理员 Token 文件${NC}"
    fi
    echo
    echo -e "${GREEN}数据库信息:${NC}"
    if [[ "$POSTGRES_TYPE" == "builtin" ]]; then
        echo -e "  PostgreSQL (内置): localhost:5432"
        echo -e "    用户名: postgres"
        echo -e "    密码: ${POSTGRES_PASSWORD}"
    else
        echo -e "  PostgreSQL (外部): ${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT}"
        echo -e "    数据库: ${EXTERNAL_POSTGRES_DB}"
    fi
    echo
    if [[ "$REDIS_TYPE" == "builtin" ]]; then
        echo -e "  Redis (内置): localhost:6379"
        echo -e "    密码: ${REDIS_PASSWORD}"
    else
        echo -e "  Redis (外部): ${EXTERNAL_REDIS_HOST}:${EXTERNAL_REDIS_PORT}"
    fi
    echo
    if [[ "$TDENGINE_TYPE" == "builtin" ]]; then
        echo -e "  TDengine (内置): localhost:6030"
        echo -e "    用户名: root"
        echo -e "    密码: ${TDENGINE_ROOT_PASSWORD}"
    else
        echo -e "  TDengine (外部): ${EXTERNAL_TDENGINE_HOST}:${EXTERNAL_TDENGINE_PORT}"
        echo -e "    用户名: ${EXTERNAL_TDENGINE_USER}"
    fi
    echo
    echo -e "${GREEN}管理命令:${NC}"
    echo -e "  查看服务状态: ${DOCKER_COMPOSE_CMD} ps"
    echo -e "  查看日志: ${DOCKER_COMPOSE_CMD} logs -f [service_name]"
    echo -e "  停止服务: ${DOCKER_COMPOSE_CMD} down"
    echo -e "  重启服务: ${DOCKER_COMPOSE_CMD} restart"
    echo
    echo -e "${YELLOW}重要提醒:${NC}"
    echo -e "  - 请妥善保管 .env 和 .admin_token 文件"
    if [[ "$TDENGINE_TYPE" == "builtin" ]]; then
        echo -e "  - TDengine 初始化服务已自动清理，不会影响后续启动速度"
    fi
    if [[ "${CADDY_ENABLED}" == "true" ]]; then
        echo -e "  - ${GREEN}Caddy 反向代理已配置并启动${NC}"
        echo -e "    ${WEB_DOMAIN} -> 自动 HTTPS 代理到 zf-web 容器"
        echo -e "    ${CONTROLER_DOMAIN} -> 自动 HTTPS 代理到 zf-controler 容器"
        echo -e "    配置文件: $(pwd)/Caddyfile"
        echo -e "    HTTPS 证书自动申请和续期"
        echo -e "    ${RED}注意：如使用 Cloudflare，两个域名都不能开启小黄云${NC}"
        echo -e "  - Caddy 管理命令："
        echo -e "    重启: ${DOCKER_COMPOSE_CMD} --profile caddy restart caddy"
        echo -e "    状态: ${DOCKER_COMPOSE_CMD} ps caddy"
        echo -e "    日志: ${DOCKER_COMPOSE_CMD} logs -f caddy"
        echo -e "    停止: ${DOCKER_COMPOSE_CMD} --profile caddy stop caddy"
        echo -e "    启动: ${DOCKER_COMPOSE_CMD} --profile caddy up -d caddy"
    else
        echo -e "  - ${RED}必须配置反向代理${NC}，将以下域名代理到对应服务："
        echo -e "    ${WEB_DOMAIN} -> http://localhost:8080 (zf-web)"
        echo -e "    ${CONTROLER_DOMAIN} -> http://localhost:3100 (zf-controler)"
        echo -e "  - 系统配置使用 HTTPS，请确保反向代理启用 SSL"
        echo -e "  - ${YELLOW}推荐使用 Caddy 自动配置 HTTPS:${NC}"
        echo -e "    参考配置文件: $(pwd)/Caddyfile (如果已生成)"
    fi
    echo -e "  - 管理员 Token 仅在首次安装时显示，请妥善保存"
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" || "$TDENGINE_TYPE" == "builtin" ]]; then
        echo -e "  - 建议定期备份内置数据库数据"
    fi
    echo
}

# Update database schema function
update_schema() {
    log_info "检查数据库 schema 更新..."
    
    # Check if .env file exists
    if [[ ! -f ".env" ]]; then
        log_error "未找到 .env 文件，请先执行全新安装"
        return 1
    fi
    
    # Source environment file to get database configuration
    source .env
    
    # Only proceed if we're using Prisma migrations
    if [[ "${RUN_PRISMA_MIGRATION:-true}" != "true" ]]; then
        log_info "Prisma 迁移已禁用，跳过 schema 更新"
        return 0
    fi
    
    # Create backup directory if not exists
    mkdir -p schema_backup
    
    # Backup current schema if exists
    if [[ -f "prisma/schema.prisma" ]]; then
        local backup_name="schema_backup/schema.prisma.$(date +%Y%m%d_%H%M%S)"
        cp "prisma/schema.prisma" "$backup_name"
        log_info "当前 schema 已备份到: $backup_name"
    fi
    
    # Download latest schema
    log_info "下载最新 schema.prisma 文件..."
    mkdir -p prisma
    local SCHEMA_URL="https://img.coderluny.com:444/uploads/schema.prisma"
    local temp_schema="prisma/schema.prisma.new"
    
    if ! curl -fsSL "$SCHEMA_URL" -o "$temp_schema"; then
        log_error "无法下载最新 schema 文件"
        return 1
    fi
    
    # Compare schemas if old one exists
    local has_changes=true
    if [[ -f "prisma/schema.prisma" ]]; then
        if diff -q "prisma/schema.prisma" "$temp_schema" >/dev/null 2>&1; then
            log_info "Schema 文件无变化，跳过数据库迁移"
            rm "$temp_schema"
            return 0
        else
            log_info "检测到 schema 变化，需要执行数据库迁移"
        fi
    else
        log_info "首次下载 schema 文件"
    fi
    
    # Replace old schema with new one
    mv "$temp_schema" "prisma/schema.prisma"
    log_success "Schema 文件更新完成"
    
    # Generate and apply migration
    if ! apply_schema_migration; then
        log_error "Schema 迁移失败，尝试回滚..."
        rollback_schema_changes
        return 1
    fi
}

# Apply schema migration function
apply_schema_migration() {
    log_info "应用数据库 schema 迁移..."
    
    # Determine database URL and network settings
    local db_url network_arg
    
    if [[ "${POSTGRES_TYPE}" == "builtin" ]]; then
        # Check if postgres service is running
        if ! $DOCKER_COMPOSE_CMD ps postgres | grep -q "Up"; then
            log_info "启动 PostgreSQL 服务进行迁移..."
            $DOCKER_COMPOSE_CMD up -d postgres
            
            # Wait for postgres to be ready
            local retry_count=0
            until $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U postgres; do
                sleep 2
                retry_count=$((retry_count + 1))
                if [ $retry_count -gt 30 ]; then
                    log_error "PostgreSQL 启动超时"
                    return 1
                fi
            done
        fi
        
        # Get the actual compose network name
        local network_name
        network_name=$(get_compose_network)
        log_info "使用 Docker Compose 网络: $network_name"
        
        # Use internal database URL for builtin PostgreSQL
        db_url="postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/zfc?schema=public"
        network_arg="--network $network_name"
    else
        # Use external database URL
        db_url="postgresql://${EXTERNAL_POSTGRES_USER}:${EXTERNAL_POSTGRES_PASSWORD}@${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT}/${EXTERNAL_POSTGRES_DB}?schema=public"
        network_arg=""
        
        # Validate external database connection
        if ! validate_postgres_connection "$EXTERNAL_POSTGRES_HOST" "$EXTERNAL_POSTGRES_PORT" "$EXTERNAL_POSTGRES_USER" "$EXTERNAL_POSTGRES_PASSWORD" "$EXTERNAL_POSTGRES_DB"; then
            log_error "外部数据库连接失败，无法执行迁移"
            return 1
        fi
    fi
    
    log_info "执行 Prisma 数据库迁移..."
    log_info "使用数据库 URL: ${db_url%:*}:****@${db_url#*@}"
    
    # Create database backup before migration (for builtin PostgreSQL only)
    if [[ "${POSTGRES_TYPE}" == "builtin" ]]; then
        log_info "创建数据库备份..."
        local backup_file="schema_backup/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        if timeout 60 docker run --rm \
            $network_arg \
            -e PGPASSWORD="${POSTGRES_PASSWORD}" \
            postgres:15-alpine \
            pg_dump -h postgres -U postgres -d zfc > "$backup_file" 2>/dev/null; then
            log_success "数据库备份完成: $backup_file"
        else
            log_warning "数据库备份失败，但继续执行迁移"
        fi
    else
        log_info "外部数据库，跳过自动备份，请确保您已手动备份数据库"
    fi
    
    # Use prisma migrate deploy for production deployments
    docker run --rm \
        $network_arg \
        -v "$(pwd)/prisma:/app/prisma" \
        -w /app \
        -e DATABASE_URL="$db_url" \
        node:18-alpine \
        sh -c "
            echo '安装最新版本 Prisma CLI...' &&
            npm install -g prisma@latest @prisma/client@latest &&
            echo '修改 schema 文件以支持 npx prisma...' &&
            sed 's/provider = \\\"cargo prisma\\\"/provider = \\\"prisma-client-js\\\"/' prisma/schema.prisma > prisma/schema.prisma.tmp && mv prisma/schema.prisma.tmp prisma/schema.prisma &&
            sed 's|output.*|// output removed for npx compatibility|' prisma/schema.prisma > prisma/schema.prisma.tmp && mv prisma/schema.prisma.tmp prisma/schema.prisma &&
            echo '创建迁移目录...' &&
            mkdir -p prisma/migrations/$(date +%Y%m%d_%H%M%S)_schema_update &&
            migration_dir=\"prisma/migrations/$(date +%Y%m%d_%H%M%S)_schema_update\" &&
            echo '生成数据库迁移差异...' &&
            npx prisma migrate diff --from-schema-datasource prisma/schema.prisma --to-schema-datamodel prisma/schema.prisma --script > \"\$migration_dir/migration.sql\" &&
            if [ -s \"\$migration_dir/migration.sql\" ]; then
                echo '发现数据库结构变更，需要应用迁移:' &&
                echo '--- 迁移内容 ---' &&
                cat \"\$migration_dir/migration.sql\" &&
                echo '--- 迁移内容结束 ---' &&
                echo '执行数据库迁移...' &&
                if npx prisma db execute --file \"\$migration_dir/migration.sql\" --schema prisma/schema.prisma; then
                    echo '数据库迁移执行成功'
                else
                    echo '数据库迁移执行失败' >&2
                    exit 1
                fi
            else
                echo '数据库结构无变化，无需迁移' &&
                rm -rf \"\$migration_dir\"
            fi &&
            echo '应用 schema 更改到数据库（跳过代码生成）...' &&
            npx prisma db push --schema=prisma/schema.prisma --accept-data-loss --skip-generate
        "
    
    if [[ $? -eq 0 ]]; then
        log_success "数据库 schema 迁移完成"
        return 0
    else
        log_error "数据库 schema 迁移失败"
        return 1
    fi
}

# Rollback schema changes function
rollback_schema_changes() {
    log_info "回滚 schema 变更..."
    
    # Find the most recent backup
    local latest_backup=$(ls -t schema_backup/schema.prisma.* 2>/dev/null | head -1)
    
    if [[ -n "$latest_backup" && -f "$latest_backup" ]]; then
        log_info "恢复备份文件: $latest_backup"
        cp "$latest_backup" "prisma/schema.prisma"
        log_success "Schema 文件已回滚到之前版本"
    else
        log_warning "未找到备份文件，无法自动回滚"
        log_info "请手动检查 prisma/schema.prisma 文件"
    fi
    
    # Provide manual rollback instructions
    echo
    log_info "手动回滚说明："
    log_info "1. 检查 schema_backup/ 目录中的备份文件"
    log_info "2. 如果需要，手动恢复数据库到之前状态"
    log_info "3. 联系管理员获取进一步支持"
}

# Update images function
update_images() {
    log_info "更新镜像（保留数据）..."
    
    # Set docker-compose command
    if ! command_exists docker-compose; then
        if ! command_exists docker compose; then
            log_error "Docker Compose 未安装！请先安装 Docker Compose"
            exit 1
        else
            DOCKER_COMPOSE_CMD="docker compose"
        fi
    else
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
    
    # Check if .env file exists
    if [[ ! -f ".env" ]]; then
        log_error "未找到 .env 文件，请先执行全新安装"
        return 1
    fi
    
    # Source environment file to get database configuration
    source .env
    
    # Check if docker-compose.yml exists
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "未找到 docker-compose.yml 文件"
        return 1
    fi
    
    log_info "当前数据库配置: PostgreSQL=${POSTGRES_TYPE}, Redis=${REDIS_TYPE}, TDengine=${TDENGINE_TYPE}"
    
    # Build list of services to update (only services that exist in compose file)
    local services_to_update=()
    local potential_services=("zf-web" "zf-controler" "rrd-service" "zfc-util" "zfc-admin" "caddy")
    
    # Check which services actually exist in the compose file
    for service in "${potential_services[@]}"; do
        if grep -q "^  $service:" docker-compose.yml; then
            services_to_update+=("$service")
            log_info "发现服务: $service"
        fi
    done
    
    if [[ ${#services_to_update[@]} -eq 0 ]]; then
        log_error "未找到可更新的应用服务"
        return 1
    fi
    
    log_info "将更新以下服务: ${services_to_update[*]}"
    
    # Always update database schema to ensure consistency
    echo
    log_info "数据库 schema 更新选项..."
    log_info "自动检查并更新数据库 schema 以确保一致性"
    UPDATE_SCHEMA="true"
    
    # Update database schema if requested
    if [[ "$UPDATE_SCHEMA" == "true" ]]; then
        log_info "开始更新数据库 schema..."
        if ! update_schema; then
            log_error "数据库 schema 更新失败"
            echo
            while true; do
                read -p "是否继续镜像更新？[y/N]: " continue_choice
                case ${continue_choice,,} in
                    y|yes)
                        log_warning "继续镜像更新，但数据库 schema 可能不兼容"
                        break
                        ;;
                    n|no|"")
                        log_info "取消镜像更新"
                        return 1
                        ;;
                    *)
                        log_error "请输入 y 或 n"
                        ;;
                esac
            done
        else
            log_success "数据库 schema 更新完成"
        fi
    fi
    
    log_info "停止应用服务..."
    
    # Stop services individually to handle Caddy's profile requirement
    local caddy_in_update=false
    local other_services=()
    
    for service in "${services_to_update[@]}"; do
        if [[ "$service" == "caddy" ]]; then
            caddy_in_update=true
        else
            other_services+=("$service")
        fi
    done
    
    # Stop regular services
    if [[ ${#other_services[@]} -gt 0 ]]; then
        $DOCKER_COMPOSE_CMD stop "${other_services[@]}"
    fi
    
    # Stop Caddy with profile if needed
    if [[ "$caddy_in_update" == "true" ]]; then
        log_info "停止 Caddy 服务..."
        $DOCKER_COMPOSE_CMD --profile caddy stop caddy
    fi
    
    log_info "拉取最新应用镜像..."
    
    # Pull images individually to handle Caddy's profile requirement
    for service in "${services_to_update[@]}"; do
        if [[ "$service" == "caddy" ]]; then
            log_info "拉取 Caddy 镜像..."
            $DOCKER_COMPOSE_CMD --profile caddy pull caddy
        else
            $DOCKER_COMPOSE_CMD pull "$service"
        fi
    done
    
    log_info "重新启动应用服务..."
    
    # Start services that were actually updated
    local services_to_start=()
    local caddy_needs_start=false
    
    for service in "${services_to_update[@]}"; do
        # Only start services that should be running (exclude utility services)
        case "$service" in
            "zfc-util"|"zfc-admin")
                # These are utility services, don't restart them
                log_info "跳过工具服务: $service"
                ;;
            "caddy")
                # Caddy needs special handling with profile
                caddy_needs_start=true
                log_info "检测到 Caddy 服务需要重启"
                ;;
            *)
                services_to_start+=("$service")
                ;;
        esac
    done
    
    # Start regular services
    if [[ ${#services_to_start[@]} -gt 0 ]]; then
        log_info "启动服务: ${services_to_start[*]}"
        $DOCKER_COMPOSE_CMD up -d "${services_to_start[@]}"
    fi
    
    # Start Caddy with profile if needed
    if [[ "$caddy_needs_start" == "true" ]]; then
        log_info "启动 Caddy 反向代理服务..."
        $DOCKER_COMPOSE_CMD --profile caddy up -d caddy
    fi
    
    if [[ ${#services_to_start[@]} -eq 0 && "$caddy_needs_start" == "false" ]]; then
        log_info "没有需要启动的服务"
    fi
    
    log_success "应用镜像更新完成！"
    log_info "数据库服务保持不变，数据完整保留"
    
    # Show update summary
    echo
    log_success "=== 更新完成总结 ==="
    if [[ "$UPDATE_SCHEMA" == "true" ]]; then
        log_info "✓ 数据库 schema 已检查并更新"
        log_info "✓ Schema 备份保存在 schema_backup/ 目录"
    else
        log_info "- 数据库 schema 更新已跳过"
    fi
    log_info "✓ 应用镜像已更新到最新版本"
    log_info "✓ 所有服务已重新启动"
    echo
    log_info "建议操作："
    log_info "1. 检查服务状态: ${DOCKER_COMPOSE_CMD} ps"
    log_info "2. 查看服务日志: ${DOCKER_COMPOSE_CMD} logs -f [service_name]"
    log_info "3. 测试应用功能确保更新成功"
}

# Uninstall system function
uninstall_system() {
    log_info "卸载 ZFC 系统..."
    
    # Set docker-compose command
    if ! command_exists docker-compose; then
        if ! command_exists docker compose; then
            log_error "Docker Compose 未安装！请先安装 Docker Compose"
            exit 1
        else
            DOCKER_COMPOSE_CMD="docker compose"
        fi
    else
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
    
    # Check if system is installed
    local has_env=false
    local has_compose=false
    
    if [[ -f ".env" ]]; then
        has_env=true
        source .env
        log_info "检测到数据库配置: PostgreSQL=${POSTGRES_TYPE}, Redis=${REDIS_TYPE}, TDengine=${TDENGINE_TYPE}"
    fi
    
    if [[ -f "docker-compose.yml" ]]; then
        has_compose=true
    fi
    
    if [[ "$has_compose" == "false" && "$has_env" == "false" ]]; then
        log_warning "未找到安装文件，可能系统未安装"
        return 0
    fi
    
    if [[ "$has_compose" == "true" ]]; then
        log_info "停止所有容器化服务..."
        $DOCKER_COMPOSE_CMD down
    fi
    
    echo
    echo "卸载选项："
    echo "1. 仅停止服务，保留所有数据"
    echo "2. 删除内置数据库数据，保留外部数据库"
    echo "3. 完全卸载，删除所有配置和数据"
    
    while true; do
        read -p "请选择卸载方式 [1-3]: " uninstall_choice
        case $uninstall_choice in
            1)
                log_info "仅停止服务，保留所有数据"
                log_success "系统卸载完成（所有数据已保留）"
                break
                ;;
            2)
                log_info "删除内置数据库数据..."
                if [[ "$has_compose" == "true" ]]; then
                    $DOCKER_COMPOSE_CMD down -v
                fi
                
                # Clean up volumes for builtin databases only
                local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
                
                if [[ "${POSTGRES_TYPE:-builtin}" == "builtin" ]]; then
                    log_info "清理 PostgreSQL 数据卷..."
                    docker volume ls -q --filter "name=${project_name}_postgres" | xargs -r docker volume rm 2>/dev/null || true
                fi
                
                if [[ "${REDIS_TYPE:-builtin}" == "builtin" ]]; then
                    log_info "清理 Redis 数据卷..."
                    docker volume ls -q --filter "name=${project_name}_redis" | xargs -r docker volume rm 2>/dev/null || true
                fi
                
                if [[ "${TDENGINE_TYPE:-builtin}" == "builtin" ]]; then
                    log_info "清理 TDengine 数据卷..."
                    docker volume ls -q --filter "name=${project_name}_tdengine" | xargs -r docker volume rm 2>/dev/null || true
                fi
                
                if [[ "${CADDY_ENABLED:-false}" == "true" ]]; then
                    log_info "清理 Caddy 数据卷..."
                    docker volume ls -q --filter "name=${project_name}_caddy" | xargs -r docker volume rm 2>/dev/null || true
                fi
                
                log_success "内置数据库数据已清理，外部数据库保持不变"
                break
                ;;
            3)
                log_info "完全卸载系统..."
                if [[ "$has_compose" == "true" ]]; then
                    $DOCKER_COMPOSE_CMD down -v
                fi
                
                # Clean up all volumes
                local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
                docker volume ls -q --filter "name=${project_name}_" | xargs -r docker volume rm 2>/dev/null || true
                
                log_info "清理配置文件..."
                rm -f .env .admin_token docker-compose.yml docker-compose.yml.backup docker-compose.full.yml Caddyfile
                rm -rf prisma
                
                log_success "系统完全卸载完成"
                break
                ;;
            *)
                log_error "无效选择，请输入 1-3"
                ;;
        esac
    done
}

# Show admin token function
show_admin_token() {
    log_info "查看管理员密码..."
    
    # Set docker-compose command
    if ! command_exists docker-compose; then
        if ! command_exists docker compose; then
            log_error "Docker Compose 未安装！请先安装 Docker Compose"
            exit 1
        else
            DOCKER_COMPOSE_CMD="docker compose"
        fi
    else
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
    
    # Check if system is installed
    if [[ ! -f ".env" ]] || [[ ! -f "docker-compose.yml" ]]; then
        log_error "系统未安装，请先执行全新安装"
        return 1
    fi
    
    # Source environment variables
    source .env
    
    # Check if services are running (only for builtin databases)
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" || "$TDENGINE_TYPE" == "builtin" ]]; then
        if ! $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
            log_error "服务未运行，请先启动系统"
            return 1
        fi
    fi
    
    # Prepare database and Redis URLs from environment
    local db_url="${POSTGRES_URL:-${DB_PATH}}"
    local redis_url="${REDIS_URL:-${REDIS_PATH}}"
    local network_args
    
    # Determine network settings
    if [[ "$POSTGRES_TYPE" == "builtin" || "$REDIS_TYPE" == "builtin" ]]; then
        local network_name
        network_name=$(get_compose_network)
        log_info "使用 Docker Compose 网络: $network_name"
        
        # Verify network exists
        if ! docker network inspect "$network_name" >/dev/null 2>&1; then
            log_error "Docker Compose 网络 $network_name 不存在"
            log_error "请确保系统已安装并运行"
            return 1
        fi
        
        network_args="--network $network_name"
    else
        network_args=""
    fi
    
    log_info "从数据库查询管理员信息..."
    log_info "数据库类型: PostgreSQL=${POSTGRES_TYPE}, Redis=${REDIS_TYPE}"
    
    # Use zfc-admin to show admin token
    docker run --rm \
        $network_args \
        -e DB_PATH="$db_url" \
        -e REDIS_PATH="$redis_url" \
        -e MGMT_ARRANGER_PRIV_KEY="${MGMT_ARRANGER_PRIV_KEY}" \
        -e ARRANGER_HOSTS_URL="https://${CONTROLER_DOMAIN}" \
        "$ZFC_ADMIN_IMAGE" \
        show-admin-token
}

# Main installation process
main_install() {
    echo
    log_info "开始 ZFC 安装流程..."
    echo
    
    # Check prerequisites
    check_prerequisites
    
    # Check domain and license preparation
    check_preparation
    
    # Get user input
    get_user_input
    
    # Generate configuration
    generate_config
    
    # Generate docker-compose configuration
    generate_docker_compose
    
    # Create environment file
    create_env_file
    
    # Generate Caddyfile
    generate_caddyfile
    
    # Initialize database
    init_database
    
    # Create admin user
    create_admin_user
    
    # Start services
    start_services
    
    # Setup Caddy
    setup_caddy
    
    # Show final information
    show_final_info
    
    log_success "安装完成！"
}

# Main function with menu
main() {
    # Check if running with legacy mode (no arguments and not in interactive terminal)
    if [[ $# -eq 0 ]] && [[ -t 0 ]]; then
        # Interactive mode - show menu
        while true; do
            show_menu
            read -p "请选择 [1-5]: " choice
            echo
            
            case $choice in
                1)
                    main_install
                    break
                    ;;
                2)
                    update_images
                    read -p "按任意键继续..." -n 1
                    echo
                    ;;
                3)
                    uninstall_system
                    break
                    ;;
                4)
                    show_admin_token
                    read -p "按任意键继续..." -n 1
                    echo
                    ;;
                5)
                    log_info "退出脚本"
                    exit 0
                    ;;
                *)
                    log_error "无效选择，请输入 1-5"
                    read -p "按任意键继续..." -n 1
                    echo
                    ;;
            esac
        done
    else
        # Legacy mode - direct installation
        main_install
    fi
}

# Handle interrupts
trap 'echo -e "\n${RED}安装被中断${NC}"; exit 1' INT TERM

# Run main function
main "$@"