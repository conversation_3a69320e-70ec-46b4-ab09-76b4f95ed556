# 构建阶段
FROM rust:1.75-bookworm as builder

# 设置工作目录
WORKDIR /app

# 复制工作区配置
COPY Cargo.toml Cargo.lock ./

# 复制所有项目源代码
COPY common ./common/
COPY rrd-service ./rrd-service/
COPY rrd-load-balancer ./rrd-load-balancer/

# 构建负载均衡器
RUN cargo build --release --bin rrd-load-balancer

# 同时构建rrd-service，因为负载均衡器需要启动它们
RUN cargo build --release --bin rrd-service

# 运行时阶段
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建用户和组
RUN useradd -r -s /bin/false rrd-load-balancer

# 创建数据目录
RUN mkdir -p /data/rrd && chown rrd-load-balancer:rrd-load-balancer /data/rrd

# 复制二进制文件
COPY --from=builder /app/target/release/rrd-load-balancer /usr/local/bin/rrd-load-balancer
COPY --from=builder /app/target/release/rrd-service /usr/local/bin/rrd-service

# 设置执行权限
RUN chmod +x /usr/local/bin/rrd-load-balancer
RUN chmod +x /usr/local/bin/rrd-service

# 切换到非root用户
USER rrd-load-balancer

# 设置环境变量
ENV RRD_DATA_PATH=/data/rrd
ENV RRD_SERVICE_BINARY=/usr/local/bin/rrd-service
ENV BIND_ADDRESS=0.0.0.0
ENV PORT=8082
ENV LOG_LEVEL=info

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8082/health || exit 1

# 启动命令
CMD ["rrd-load-balancer"]