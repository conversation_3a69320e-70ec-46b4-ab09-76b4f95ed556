# ZFC Licensing - Admin Panel UI Design Document

**Version:** 1.0
**Date:** 2023-10-27
**Author:** <PERSON> Assistant

---

## 1. Introduction

This document provides the user interface (UI) and user experience (UX) design for the administrative web panel of the `zf-auth-server`. This panel is the central control hub for managing all product licenses, customer instances, and renewal codes.

The target users for this interface are system administrators responsible for customer license provisioning and support.

## 2. Goals

*   Provide a clear, at-a-glance overview of the licensing system's state.
*   Enable efficient management (Create, Read, Update, Delete) of `ControllerInstance` entities.
*   Enable efficient generation and tracking of `RenewalCode` entities.
*   Provide a simple workflow for renewing a customer's license.
*   Allow administrators to resolve common support issues, such as a customer needing to move a license to a new machine.

## 3. Page & View Designs

The admin panel will be a single-page application (SPA) consisting of the following main views.

### 3.1. Dashboard / Overview

This is the landing page after an administrator logs in. It provides a high-level summary.

*   **Layout**: A series of "stat cards" at the top, with quick links below.
*   **Key Metrics (Stat Cards)**:
    *   **Active Instances**: A count of all `ControllerInstance`s where `licenseExpiresAt` is in the future.
    *   **Expired Instances**: A count of instances where `licenseExpiresAt` is in the past.
    *   **Instances Nearing Expiration**: A count of instances expiring within the next 7 days.
    *   **Available Renewal Codes**: A count of `RenewalCode`s where `usedAt` is null.
*   **Quick Links**: Large buttons or links to "Manage Instances" and "Manage Renewal Codes".

### 3.2. Instances Management Page

This page is the primary interface for managing customer licenses.

*   **Layout**: A full-page table displaying all `ControllerInstance` records, with search/filter controls at the top and a prominent "Create New Instance" button.

*   **Table Columns**:
    *   `Name`: The human-readable name of the instance (e.g., "Customer X - Production").
    *   `Instance ID`: The unique ID (`cuid`) given to the customer. Includes a "copy to clipboard" button.
    *   `License Expires At`: The expiration date. Displayed in red if in the past.
    *   `Entitlements`: A "View/Edit" button that opens a modal with a JSON editor to view or modify the instance's entitlements.
    *   `Last Seen`: A combination of `lastSeenAt` and `lastSeenIp`.
    *   `Session ID`: The `currentSessionId`. Displayed only if a session is active.
    *   `Actions`: A dropdown menu or a set of icon buttons for each row.

*   **Row Actions**:
    1.  **Edit**: Opens a modal to change the instance `Name`. (Entitlements are edited via their own button).
    2.  **Renew**: Opens a modal to apply a renewal code (see section 4.2).
    3.  **Force Expire Session**: A critical support tool. It prompts the admin for confirmation ("Are you sure you want to kick the current session? This will allow a new machine to acquire the license."). On confirmation, it clears the `currentSessionId`, `lastSeenIp`, and `lastSeenAt` fields on the server, freeing the license lease.
    4.  **Delete**: Permanently deletes the instance record, with a confirmation dialog.

*   **Page-Level Actions**:
    *   **Create New Instance**: A button that opens a form (see section 4.1).

### 3.3. Renewal Codes Management Page

This page is for managing the codes used to extend licenses.

*   **Layout**: A full-page table displaying all `RenewalCode` records, with a "Generate New Codes" button at the top.

*   **Table Columns**:
    *   `Renewal Code`: The unique code string. Includes a "copy to clipboard" button.
    *   `Duration`: The number of days this code extends a license for.
    *   `Status`: "Available" or "Used".
    *   `Used By / Used At`: If used, shows the name of the instance that consumed it and the timestamp.

*   **Page-Level Actions**:
    *   **Generate New Codes**: A button that opens a form (see section 4.3).

*   **Row Actions**:
    *   **Delete**: Allows deletion of an **unused** renewal code.

## 4. Component & Workflow Designs

### 4.1. Workflow: Creating a New Instance

1.  Admin navigates to the "Instances Management" page and clicks "Create New Instance".
2.  A modal form appears with the following fields:
    *   `Name` (Text Input, Required): e.g., "Customer Y - Staging Server".
    *   `Initial License Duration (days)` (Number Input, Required): The number of days until the first expiration.
    *   `Entitlements` (JSON Editor, Required): Pre-populated with a default template like `{ "max_workers": 1 }`.
3.  Admin fills out the form and clicks "Create".
4.  The UI sends a request to the `POST /admin/instances` endpoint.
5.  On success, a confirmation dialog appears showing the newly generated **`Instance ID`**. It stresses that this ID should be copied and sent to the customer. The new instance appears in the table.

### 4.2. Workflow: Renewing an Instance

1.  Admin locates the target instance in the "Instances Management" table and clicks the "Renew" action.
2.  A modal form appears with a single field:
    *   `Renewal Code` (Text Input, Required).
3.  Admin pastes the customer-provided renewal code and clicks "Apply Code".
4.  The UI sends a request to `POST /admin/instances/:id/renew`.
5.  On success, the modal closes, a success notification is shown, and the `License Expires At` field for that row in the table is updated.
6.  On failure (e.g., code not found, already used), an error message is displayed within the modal.

### 4.3. Workflow: Generating Renewal Codes

1.  Admin navigates to the "Renewal Codes Management" page and clicks "Generate New Codes".
2.  A modal form appears with two fields:
    *   `Duration (days)` (Number Input, Required): e.g., 30, 90, 365.
    *   `Quantity` (Number Input, Required): The number of codes to generate.
3.  Admin clicks "Generate".
4.  The UI sends a request to `POST /admin/renewal-codes`.
5.  On success, the modal closes, and the new codes appear at the top of the table, ready to be copied and distributed.

## 5. Access Control

The entire admin panel single-page application must be protected. For the initial version, a simple static bearer token authentication scheme is sufficient. The frontend application will be required to send a pre-configured secret token in the `Authorization` header of every API request to the `/admin/*` endpoints. The server will reject any request without the correct token.

A full login system with multiple admin users can be considered a future enhancement. 