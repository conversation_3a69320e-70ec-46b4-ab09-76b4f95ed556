# ZFC Licensing & Entitlements - Product Requirements Document

**Version:** 1.0
**Date:** 2023-10-27
**Author:** Gemini Assistant

---

## 1. Introduction

This document outlines the requirements for a robust, centralized licensing and entitlements system for the ZFC product suite. The primary goal is to protect the software (e.g., `zf-controller`) from unauthorized use and to enable flexible, tiered feature offerings for different customers.

The system will be composed of three main components:
1.  A central **`zf-auth-server`** to manage all licenses and entitlements.
2.  A reusable **`zf-auth-client`** library (crate) to handle communication with the server.
3.  The **Controlled Products** (e.g., `zf-controller`) that integrate the client library to validate their operational status and capabilities.

## 2. Goals and Objectives

*   **Protect Intellectual Property**: Prevent `zf-controller` and other controlled products from running without a valid, active license.
*   **Enable Tiered-Features**: Allow the administrator to control the capabilities of a deployed product instance, such as limiting the number of workers, enabling/disabling specific features, etc.
*   **Centralized Management**: Provide a single source of truth for all customer licenses and their respective entitlements.
*   **High Security**: Ensure the licensing mechanism is resistant to tampering by end-users.
*   **Decoupled Architecture**: Keep licensing logic separate from the core product's business logic, allowing for easier maintenance and future expansion.
*   **Resilience**: The controlled product should be able to withstand temporary network issues when communicating with the auth server, but should cease to function if the license cannot be validated over a sustained period.

## 3. System Architecture

The system operates on a "heartbeat" model. Controlled products do not store their license locally. Instead, they periodically contact the `zf-auth-server` to fetch a short-lived, cryptographically signed JSON Web Token (JWT). This JWT contains the product's entitlements and serves as its proof of license.

```mermaid
sequenceDiagram
    participant CP as Controlled Product (e.g., zf-controller)
    participant AC as zf-auth-client (lib)
    participant AS as zf-auth-server

    CP->>AC: initialize_auth(config)
    AC->>AS: POST /v1/heartbeat (instance_id)
    alt License is Active
        AS-->>AC: 200 OK (signed_jwt)
        AC->>AC: Validate JWT with Public Key
        AC-->>CP: Ok(Entitlements)
    else License is Expired/Invalid
        AS-->>AC: 403 Forbidden (error)
        AC-->>CP: Err(AuthError)
        CP->>CP: Terminate Execution
    end

    loop Periodic Heartbeat (e.g., every hour)
        AC->>AS: POST /v1/heartbeat (instance_id)
        AS-->>AC: 200 OK (new_signed_jwt)
    end
```

## 4. Detailed Feature Requirements

### 4.1. `zf-auth-server` (The Licensing Authority)

This is a standalone web service responsible for all licensing decisions.

#### 4.1.1. Database Schema (`prisma/schema.prisma`)

The server will have its own database to persist instance and license information.

```prisma
model ControllerInstance {
  id               String    @id @default(cuid())
  name             String    @unique
  licenseExpiresAt DateTime
  
  /// Stores all feature flags and limits for this instance.
  /// Example: { "max_workers": 10, "enable_feature_x": true }
  entitlements     Json      @default("{}")

  // -- Fields for Concurrency Control --
  /// The IP address of the last client to successfully perform a heartbeat.
  lastSeenIp       String?
  /// The timestamp of the last successful heartbeat.
  lastSeenAt       DateTime?
  /// A unique ID for the current "active" session/lease.
  currentSessionId String?   @unique

  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  
  renewalCodes     RenewalCode[]
}

model RenewalCode {
  id                 String    @id @default(cuid())
  duration           Int       // License extension in days
  createdAt          DateTime  @default(now())
  usedAt             DateTime?
  usedByInstanceId   String?
  usedByInstance     ControllerInstance? @relation(fields: [usedByInstanceId], references: [id])
}
```

#### 4.1.2. Key Management

*   The server must generate and securely store an **Ed25519 private key**. This key is used to sign all JWTs.
*   The private key **MUST NOT** be checked into version control. It should be loaded via environment variables or a secure configuration provider.

#### 4.1.3. API Endpoints

The server must expose the following RESTful API endpoints over HTTPS.

*   **`POST /v1/heartbeat`** (For Controlled Products)
    *   **Request Header (Optional)**: `Authorization: Bearer <EXISTING_JWT>`
    *   **Request Body**: `{ "instance_id": "string" }`
    *   **Logic**:
        1.  Find the `ControllerInstance` by `instance_id`.
        2.  If not found, or if `licenseExpiresAt` is in the past, return `403 Forbidden`.
        3.  **Concurrency Check**:
            *   Extract the `sid` from the optional bearer token JWT, if present.
            *   If the instance has an active `currentSessionId` and `lastSeenAt` is recent (e.g. < 24h ago), and the request's `sid` does not match, the license is in use by another process. Return `409 Conflict`.
            *   If the check passes (i.e., the seat is free or this is a valid renewal), generate a new `currentSessionId` if one isn't present.
        4.  Update the instance's `lastSeenIp`, `lastSeenAt`, and `currentSessionId` in the database.
        5.  Create a JWT payload (see section 4.1.4), including the `currentSessionId`.
        6.  Sign the JWT with the server's private key.
    *   **Success Response (200 OK)**: `{ "token": "SIGNED_JWT" }`
    *   **Failure Response (403 Forbidden)**: `{ "error": "LICENSE_EXPIRED" | "INSTANCE_NOT_FOUND" }`
    *   **Failure Response (409 Conflict)**: `{ "error": "LICENSE_IN_USE" }`

*   **Administrative Endpoints**
    *   These endpoints should be protected (e.g., by a static bearer token, IP whitelist, or a future admin user system).
    *   `POST /admin/instances`: Create a new `ControllerInstance`.
    *   `GET /admin/instances`: List all instances.
    *   `PUT /admin/instances/:id`: Update an instance's `name` or `entitlements`.
    *   `POST /admin/renewal-codes`: Create a new `RenewalCode`.
    *   `POST /admin/instances/:id/renew`: Apply a `RenewalCode` to an instance, extending its `licenseExpiresAt`.

#### 4.1.4. JWT Structure

The JWT payload signed by the server must conform to the following structure.

```json
{
  "sub": "cuid_of_the_instance",
  "sid": "unique_session_id_generated_by_server",
  "exp": **********,
  "iat": **********,
  "entitlements": {
    "max_workers": 5,
    "max_users_per_worker": 100,
    "feature_udp_forwarding_enabled": true
  }
}
```

*   `sub` (Subject): The `id` of the `ControllerInstance`.
*   `sid` (Session ID): A unique identifier for the current license lease.
*   `exp` (Expiration Time): A Unix timestamp for when this JWT expires. Should be short-lived (e.g., 24-48 hours).
*   `iat` (Issued At): A Unix timestamp for when this JWT was created.
*   `entitlements`: The JSON object from the `ControllerInstance.entitlements` field in the database.

### 4.2. `zf-auth-client` (Client SDK)

This is a Rust library (`lib` crate) that encapsulates all client-side licensing logic.

#### 4.2.1. Key Management

*   The **Ed25519 public key** (corresponding to the server's private key) must be hardcoded into the library's source code. This allows it to verify JWTs offline.

#### 4.2.2. Public Interface (`lib.rs`)

The library will expose a simple, high-level API.

*   **Structs**:
    *   `AuthConfig`: `{ instance_id: String, server_url: String }`
    *   `Entitlements`: A struct that mirrors the `entitlements` JSON object, providing type-safe access for the consuming application. It should use `serde(default = ...)` to provide restrictive defaults if a field is missing.
    *   `AuthError`: A comprehensive error enum (`NetworkError`, `TokenInvalid`, `LicenseExpired`, `ServerRejected`).

*   **Function**:
    *   `pub async fn initialize_auth(config: AuthConfig) -> Result<Entitlements, AuthError>`
        *   This is the main entry point.
        *   It performs the initial heartbeat request.
        *   On success, it validates the token, spawns the background checking task, and returns the parsed `Entitlements`.
        *   On failure, it returns a descriptive `AuthError`. The calling application is expected to terminate upon receiving an error.

#### 4.2.3. Behavior

*   **Initial Check**: The `initialize_auth` function provides a blocking, fail-fast check on application startup.
*   **Background Task**: Upon a successful initial check, a `tokio::spawn` task is launched.
    *   This task will perform the heartbeat check periodically (e.g., every 1 to 8 hours).
    *   If the heartbeat fails for a sustained period (e.g., 3 consecutive attempts), or if it receives a `409 Conflict` (meaning another instance took its lease), the background task will terminate the entire process via `std::process::exit(1)`.
*   **Token Persistence**: The client must cache the most recently received JWT on local disk. This token must be sent as an `Authorization` header in subsequent heartbeat requests to prove it is the current lease holder.

### 4.3. Controlled Products (e.g., `zf-controller`)

This refers to any application that needs to be protected by the licensing system.

#### 4.3.1. Integration

*   Add `zf-auth-client` as a dependency.
*   Require `ZFC_INSTANCE_ID` and `ZFC_AUTH_SERVER_URL` to be configured (e.g., via environment variables).
*   Call `zf_auth_client::initialize_auth` at the very beginning of the `main` function.
*   Use the returned `Entitlements` struct to configure its own operational parameters (e.g., set the number of worker threads).

#### 4.3.2. Responsibility

*   The controlled product **must** strictly adhere to the limits defined in the `Entitlements` struct it receives. For example, if `max_workers: 5` is received, the application must not spawn more than 5 workers.

## 5. Security Considerations

*   **Transport Security**: All communication between the client and server MUST use HTTPS.
*   **Private Key Security**: The `zf-auth-server`'s private key is the root of trust. Its compromise would allow anyone to generate valid licenses. It must be stored with the highest level of security.
*   **JWT Immutability**: The use of a digital signature on the JWT prevents the end-user from tampering with their entitlements or expiration date locally.
*   **Admin Endpoint Protection**: The `/admin` endpoints on the auth server must be secured to prevent unauthorized creation or modification of licenses.
*   **Session Hijacking**: While the Session ID prevents trivial concurrent use, a sophisticated attacker could try to steal a valid JWT file from a running machine. This is a deeper security issue, and mitigating it could involve binding the session to more machine-specific hardware identifiers, adding complexity. For v1.0, IP-based checking is sufficient.

## 6. Future Considerations

*   **Admin UI**: A dedicated web interface for managing instances and renewal codes would improve usability. It could include a button to "force-expire" a session if a customer's machine dies and they need to re-license a new one immediately.
*   **Metrics**: The heartbeat endpoint could be used to collect basic, non-sensitive telemetry about running instances (e.g., software version).
*   **Advanced Entitlements**: The system could be extended to support more complex rules, such as consumption-based limits (e.g., data transfer per month). 