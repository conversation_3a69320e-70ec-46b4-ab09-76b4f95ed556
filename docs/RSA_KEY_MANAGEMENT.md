# RSA Key Management for ZFC Authentication System

## Overview

This document describes the RSA-based JWT authentication system implemented in ZFC to replace the previous shared secret approach. This change significantly enhances security by ensuring that private keys never leave the server environment.

## Security Architecture

### Before (Insecure)
```
┌─────────────────┐    ┌─────────────────┐
│   Auth Client   │    │   Auth Server   │
│                 │    │                 │
│ 🔴 Private Key  │◄──►│ 🔴 Private Key  │
│ (Hardcoded)     │    │ (Same Key)      │
└─────────────────┘    └─────────────────┘
    SECURITY RISK: Anyone with client code can forge JWTs
```

### After (Secure)
```
┌─────────────────┐    ┌─────────────────┐
│   Auth Client   │    │   Auth Server   │
│                 │    │                 │
│ ✅ Public Key   │◄───│ 🔒 Private Key  │
│ (From Server)   │    │ (Server Only)   │
└─────────────────┘    └─────────────────┘
    SECURE: Only server can sign, client can only verify
```

## Implementation Details

### Server-Side (zf-auth-server)

#### Key Generation
```rust
// Automatic 2048-bit RSA key generation
let rsa_private_key = RsaPrivateKey::new(&mut rng, 2048)?;
let rsa_public_key = RsaPublicKey::from(&rsa_private_key);
```

#### JWT Signing (RS256)
```rust
// Create JWT with RSA private key
let header = Header::new(Algorithm::RS256);
let encoding_key = EncodingKey::from_rsa_pem(private_key_pem.as_bytes())?;
encode(&header, claims, &encoding_key)
```

#### Public Key Endpoint
- **Endpoint**: `GET /public-key`
- **Purpose**: Securely distribute public key to clients
- **Format**: PEM-encoded RSA public key
- **Algorithm**: RS256

### Client-Side (zf-auth-client)

#### Public Key Fetching
```rust
// Automatic public key retrieval with caching
async fn get_public_key(&self) -> Result<String, AuthError> {
    // Check cache first
    if let Some(cached_key) = self.public_key_cache.read().await.as_ref() {
        return Ok(cached_key.clone());
    }
    
    // Fetch from server with retry logic
    self.fetch_with_retry().await
}
```

#### Retry Logic Implementation
- **Max Attempts**: 3
- **Backoff Strategy**: Exponential (1s, 2s, 3s)
- **Timeout**: 10 seconds per request
- **Error Handling**: Detailed error categorization

#### JWT Verification (RS256)
```rust
// Verify JWT with RSA public key
let validation = Validation::new(Algorithm::RS256);
let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())?;
decode::<JwtClaims>(token, &decoding_key, &validation)
```

## Security Benefits

### 1. Private Key Isolation
- **Before**: Private key exposed in client code
- **After**: Private key remains server-side only
- **Benefit**: Eliminates risk of JWT forgery from compromised clients

### 2. Stronger Cryptography
- **Before**: HMAC-SHA256 with shared secret
- **After**: RSA-SHA256 with 2048-bit keys
- **Benefit**: More robust against cryptographic attacks

### 3. Dynamic Key Distribution
- **Before**: Static hardcoded secrets
- **After**: Dynamic public key fetching via API
- **Benefit**: Enables future key rotation capabilities

### 4. Enhanced Error Handling
- **Before**: Basic error handling
- **After**: Comprehensive retry logic with categorized errors
- **Benefit**: Better resilience to network issues

## API Reference

### Public Key Endpoint

#### Request
```bash
GET /public-key HTTP/1.1
Host: auth-server:3030
```

#### Successful Response
```json
{
  "success": true,
  "data": {
    "publicKey": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----\n",
    "algorithm": "RS256"
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Public key not available",
  "message": "Server is initializing"
}
```

## Error Handling

### Client Error Categories

#### NetworkError
- **Connection timeouts**: `fetch_public_key_once()` timeout (10s)
- **Connection failures**: Server unreachable
- **DNS resolution**: Hostname resolution failures

#### ServerRejected
- **HTTP 4xx/5xx**: Server-side errors
- **Invalid JSON**: Malformed response data
- **Empty public key**: Server returned empty key

#### HttpError
- **General HTTP**: Low-level HTTP client errors
- **TLS failures**: Certificate validation issues

### Retry Behavior

```rust
const MAX_RETRIES: u32 = 3;
const RETRY_DELAY_MS: u64 = 1000;

// Exponential backoff: 1s, 2s, 3s
tokio::time::sleep(Duration::from_millis(RETRY_DELAY_MS * attempt as u64)).await
```

### Logging

#### Server Logs
```
[INFO] Generated new RSA key pair for JWT signing
[INFO] Public key endpoint available at /public-key
```

#### Client Logs
```
[INFO] Successfully fetched public key on attempt 1
[WARN] Failed to fetch public key on attempt 1 of 3: Connection failed. Retrying in 1000ms...
[ERROR] Failed to fetch public key after 3 attempts
```

## Testing

### Unit Tests

#### Server Tests (`integration_test.rs`)
- RSA key generation validation
- JWT creation and verification
- Public key endpoint functionality

#### Client Tests (`client_test.rs`)
- RSA JWT format expectations
- Public key PEM format validation
- Algorithm verification (RS256)

### Integration Testing

```bash
# Test public key endpoint
curl -X GET http://localhost:3030/public-key

# Expected: PEM-formatted RSA public key with RS256 algorithm
```

## Migration Guide

### From Previous Version

#### No Action Required
- Existing deployments automatically upgrade
- No configuration changes needed
- Client libraries handle transition seamlessly

#### Verification Steps
1. **Check server logs** for RSA key generation message
2. **Test public key endpoint** accessibility
3. **Verify JWT authentication** still works
4. **Monitor client logs** for successful public key fetching

### Troubleshooting

#### Common Issues

**Public Key Fetch Fails**
```bash
# Check endpoint accessibility
curl -v http://server:3030/public-key

# Check server logs
grep "RSA key" /var/log/auth-server.log
```

**JWT Verification Errors**
```bash
# Verify algorithm mismatch
# Client expects: RS256
# Server provides: Check /public-key response
```

**Network Connectivity**
```bash
# Test basic connectivity
telnet auth-server 3030

# Check DNS resolution
nslookup auth-server
```

## Best Practices

### Security
1. **Monitor key endpoint**: Log access to `/public-key`
2. **Rate limiting**: Implement if needed for public endpoint
3. **Network security**: Ensure TLS in production
4. **Log analysis**: Monitor for repeated fetch failures

### Performance
1. **Client caching**: Leverage built-in public key cache
2. **Server scaling**: Public key endpoint is stateless
3. **Monitoring**: Track public key fetch frequency

### Operations
1. **Health checks**: Include `/public-key` in monitoring
2. **Alerting**: Alert on RSA key generation failures
3. **Backup**: Consider logging public keys for debugging

## Future Enhancements

### Planned Features
1. **Key Rotation**: Periodic automatic key rotation
2. **JWK Support**: JSON Web Key format for standards compliance
3. **Multi-Key Support**: Support multiple active keys during rotation
4. **Key Versioning**: Track key versions for seamless rotation

### Configuration Options
```toml
[auth.rsa]
key_size = 2048              # RSA key size in bits
rotation_interval = "24h"    # Automatic rotation frequency
public_key_cache_ttl = "1h"  # Client cache duration
max_active_keys = 2          # Keys during rotation period
```

## Conclusion

The RSA-based JWT authentication system provides significantly enhanced security while maintaining backward compatibility and ease of deployment. The automatic key management and robust error handling ensure reliable operation in production environments.

For questions or issues, please refer to the troubleshooting section or check the server logs for detailed error information.