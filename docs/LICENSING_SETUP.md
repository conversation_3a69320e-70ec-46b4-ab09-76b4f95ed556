# ZFC Licensing System Setup Guide

This guide explains how to set up and configure the ZFC licensing system.

## Overview

The ZFC licensing system consists of three main components:
1. **zf-auth-server** - Central licensing authority
2. **zf-auth-client** - Client library for license validation
3. **zf-controller** - Licensed application (example)

## Quick Start

### 1. Generate Ed25519 Keys

First, generate Ed25519 keys for the auth server:

```bash
# This will generate both private and public keys
cargo run --bin zf-auth-server -- --help
```

Or use OpenSSL:
```bash
# Generate private key
openssl genpkey -algorithm Ed25519 -out private.pem

# Extract public key
openssl pkey -in private.pem -pubout -out public.pem

# Convert to hex format (for environment variables)
openssl pkey -in private.pem -noout -text | grep -A2 "priv:" | tail -n +2 | tr -d '[:space:]' | tr -d ':'
```

### 2. Set Environment Variables

```bash
# Auth Server Configuration
export ZF_AUTH_DATABASE_URL="postgresql://user:password@localhost/auth_db"
export ZF_AUTH_ED25519_PRIVATE_KEY="your_private_key_hex"

# Controller Configuration
export ZFC_INSTANCE_ID="my-controller-instance"
export ZFC_AUTH_SERVER_URL="https://auth.example.com"
```

### 3. Start the Auth Server

```bash
# Create the database first
createdb auth_db

# Run migrations (if using Prisma CLI)
cd zf-auth-server
npx prisma migrate deploy

# Start the server
cargo run --bin zf-auth-server
```
