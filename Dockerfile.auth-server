FROM rust:1.75 as builder

WORKDIR /app

# Copy workspace configuration
COPY Cargo.toml Cargo.lock ./
COPY rust-toolchain.toml ./

# Copy source code
COPY zf-auth-server ./zf-auth-server
COPY common ./common

# Build the auth server
RUN cargo build --release --bin zf-auth-server

# Runtime image
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary
COPY --from=builder /app/target/release/zf-auth-server /usr/local/bin/zf-auth-server

# Create a non-root user
RUN useradd -r -s /bin/false authuser

USER authuser

EXPOSE 8080

CMD ["zf-auth-server"]