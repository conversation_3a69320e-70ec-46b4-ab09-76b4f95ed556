/**
 * 隐私保护工具函数
 * 用于隐藏IP地址和域名中的敏感信息
 */

/**
 * 检测字符串是否为IPv4地址
 * @param {string} str 
 * @returns {boolean}
 */
export function isIPv4(str) {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipv4Regex.test(str)
}

/**
 * 检测字符串是否为IPv6地址
 * @param {string} str 
 * @returns {boolean}
 */
export function isIPv6(str) {
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$|^(?:[0-9a-fA-F]{1,4}:)*::[0-9a-fA-F]{1,4}(?::[0-9a-fA-F]{1,4})*$/
  return ipv6Regex.test(str)
}

/**
 * 检测字符串是否为域名
 * @param {string} str 
 * @returns {boolean}
 */
export function isDomain(str) {
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  return domainRegex.test(str) && str.includes('.')
}

/**
 * 隐藏IPv4地址后两段
 * @param {string} ipv4 IPv4地址
 * @returns {string} 隐藏后的IPv4地址
 */
export function maskIPv4(ipv4) {
  if (!isIPv4(ipv4)) return ipv4
  
  const parts = ipv4.split('.')
  return `${parts[0]}.${parts[1]}.xxx.xxx`
}

/**
 * 隐藏IPv6地址后六段
 * @param {string} ipv6 IPv6地址
 * @returns {string} 隐藏后的IPv6地址
 */
export function maskIPv6(ipv6) {
  if (!isIPv6(ipv6)) return ipv6
  
  const parts = ipv6.split(':')
  if (parts.length >= 2) {
    return `${parts[0]}:${parts[1]}:xxx:xxx:xxx:xxx:xxx:xxx`
  }
  return ipv6
}

/**
 * 隐藏域名中间部分
 * @param {string} domain 域名
 * @returns {string} 隐藏后的域名
 */
export function maskDomain(domain) {
  if (!isDomain(domain)) return domain
  
  const parts = domain.split('.')
  if (parts.length < 2) return domain
  
  if (parts.length === 2) {
    // 对于二级域名，隐藏主域名
    return `${parts[0]}.xxx`
  } else {
    // 对于多级域名，保留第一段和最后一段，中间用xxx替代
    const first = parts[0]
    const last = parts[parts.length - 1]
    return `${first}.xxx.${last}`
  }
}

/**
 * 统一的地址隐藏函数
 * @param {string} address IP地址或域名
 * @param {boolean} showFull 是否显示完整地址，默认为false
 * @returns {string} 处理后的地址
 */
export function maskAddress(address, showFull = false) {
  if (!address || showFull) return address
  
  if (isIPv4(address)) {
    return maskIPv4(address)
  } else if (isIPv6(address)) {
    return maskIPv6(address)
  } else if (isDomain(address)) {
    return maskDomain(address)
  }
  
  return address
}

/**
 * 格式化目标地址显示（带端口处理）
 * @param {Object} config 配置对象
 * @param {boolean} showFull 是否显示完整地址
 * @returns {string} 格式化后的地址
 */
export function formatTargetAddress(config, showFull = false) {
  if (!config || !config.target_address) return ''
  
  const maskedAddress = maskAddress(config.target_address, showFull)
  
  if (config.test_type === 'tcp' && config.target_port) {
    return `${maskedAddress}:${config.target_port}`
  }
  
  return maskedAddress
}