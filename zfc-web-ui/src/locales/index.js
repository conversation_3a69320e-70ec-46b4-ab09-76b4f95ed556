import { createI18n } from 'vue-i18n'
import enUS from './en-US.json'
import zhCN from './zh-CN.json'
import { getSiteTitle } from '../api'

// Get browser language preference
function getBrowserLanguage() {
  const language = navigator.language || navigator.userLanguage
  if (language.startsWith('zh')) {
    return 'zh-CN'
  }
  return 'en-US'
}

// Get stored language preference or use browser default
function getStoredLanguage() {
  const stored = localStorage.getItem('zfc-language')
  if (stored && ['en-US', 'zh-CN'].includes(stored)) {
    return stored
  }
  return getBrowserLanguage()
}

const messages = {
  'en-US': enUS,
  'zh-CN': zhCN
}

const i18n = createI18n({
  locale: getStoredLanguage(),
  fallbackLocale: 'en-US',
  messages,
  legacy: false,
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true
})

export default i18n

// Language switching utility  
export function setLanguage(locale) {
  if (messages[locale]) {
    i18n.global.locale.value = locale
    localStorage.setItem('zfc-language', locale)
    document.documentElement.setAttribute('lang', locale)
    
    // Update page title when language changes
    updatePageTitle(locale)
  }
}

// Update page title based on current language
async function updatePageTitle(locale) {
  try {
    const response = await getSiteTitle()
    const titles = response.data
    const langKey = locale === 'zh-CN' ? 'zh' : 'en'
    document.title = titles[langKey] || titles.zh || 'Firefly\'s World'
  } catch (error) {
    console.warn('Failed to update page title:', error)
    // Keep current title or set fallback
    if (!document.title || document.title === 'Loading...') {
      document.title = 'Firefly\'s World'
    }
  }
}

export function getCurrentLanguage() {
  return i18n.global.locale.value
}

export function getAvailableLanguages() {
  return [
    { code: 'en-US', name: 'English' },
    { code: 'zh-CN', name: '中文' }
  ]
}