<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('batchUpdate.title')"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="handleDialogClosed"
  >
    <div class="batch-update-container">
      <!-- Mode Selector -->
      <div class="mode-selector">
        <el-radio-group v-model="currentMode" class="mode-radio-group">
          <el-radio-button label="simple">{{ t('batchUpdate.simpleMode') }}</el-radio-button>
          <el-radio-button label="advanced">{{ t('batchUpdate.advancedMode') }}</el-radio-button>
        </el-radio-group>
        <div class="mode-description">
          {{ t(currentMode === 'simple' ? 'batchUpdate.simpleModeDesc' : 'batchUpdate.advancedModeDesc') }}
        </div>
      </div>

      <!-- Simple Mode Form -->
      <div v-if="currentMode === 'simple'" class="simple-mode-form">
        <el-form
          ref="simpleFormRef"
          :model="simpleForm"
          :rules="simpleRules"
          label-width="120px"
          class="batch-form"
        >
          <el-form-item :label="t('batchUpdate.replaceType')" prop="replaceType">
            <el-select 
              v-model="simpleForm.replaceType" 
              :placeholder="t('batchUpdate.selectReplaceType')"
              style="width: 100%"
              @change="handleReplaceTypeChange"
            >
              <el-option 
                v-for="type in replaceTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value"
              >
                <div class="option-item">
                  <div class="option-label">{{ type.label }}</div>
                  <div class="option-desc">{{ type.description }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- IP Address Replace -->
          <div v-if="simpleForm.replaceType === 'ip_address'" class="replace-config">
            <el-form-item :label="t('batchUpdate.originalIpAddress')" prop="originalIpAddress">
              <el-input v-model="simpleForm.originalIpAddress" :placeholder="t('batchUpdate.originalIpAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.originalIpAddressHelp') }}
              </div>
            </el-form-item>
            
            <el-form-item :label="t('batchUpdate.newIpAddress')" prop="newIpAddress">
              <el-input v-model="simpleForm.newIpAddress" :placeholder="t('batchUpdate.newIpAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.newIpAddressHelp') }}
              </div>
            </el-form-item>
          </div>

          <!-- Port Replace -->
          <div v-if="simpleForm.replaceType === 'port'" class="replace-config">
            <el-form-item :label="t('batchUpdate.originalPort')" prop="originalPort">
              <el-input v-model="simpleForm.originalPort" :placeholder="t('batchUpdate.originalPortPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.originalPortHelp') }}
              </div>
            </el-form-item>
            
            <el-form-item :label="t('batchUpdate.newPort')" prop="newPort">
              <el-input v-model="simpleForm.newPort" :placeholder="t('batchUpdate.newPortPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.newPortHelp') }}
              </div>
            </el-form-item>
          </div>

          <!-- Exact Replace -->
          <div v-if="simpleForm.replaceType === 'exact'" class="replace-config">
            <el-form-item :label="t('batchUpdate.originalAddress')" prop="originalAddress">
              <el-input v-model="simpleForm.originalAddress" :placeholder="t('batchUpdate.originalAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.originalAddressHelp') }}
              </div>
            </el-form-item>
            
            <el-form-item :label="t('batchUpdate.newAddress')" prop="newAddress">
              <el-input v-model="simpleForm.newAddress" :placeholder="t('batchUpdate.newAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.newAddressHelp') }}
              </div>
            </el-form-item>
          </div>

          <!-- Batch Add -->
          <div v-if="simpleForm.replaceType === 'batch_add'" class="replace-config">
            <el-form-item :label="t('batchUpdate.conditionAddress')" prop="conditionAddress">
              <el-input v-model="simpleForm.conditionAddress" :placeholder="t('batchUpdate.conditionAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.conditionAddressHelp') }}
              </div>
            </el-form-item>

            <el-form-item :label="t('batchUpdate.batchAddAddress')" prop="batchAddAddress">
              <el-input v-model="simpleForm.batchAddAddress" :placeholder="t('batchUpdate.batchAddAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.batchAddAddressHelp') }}
              </div>
            </el-form-item>
          </div>

          <!-- Batch Remove -->
          <div v-if="simpleForm.replaceType === 'batch_remove'" class="replace-config">
            <el-form-item :label="t('batchUpdate.removeAddress')" prop="removeAddress">
              <el-input v-model="simpleForm.removeAddress" :placeholder="t('batchUpdate.removeAddressPlaceholder')" />
              <div class="form-help-text">
                {{ t('batchUpdate.removeAddressHelp') }}
              </div>
            </el-form-item>
          </div>

          <!-- Preview Example -->
          <el-form-item v-if="simplePreviewExample">
            <div class="example-section">
              <el-text type="info" size="small">
                {{ t('batchUpdate.example') }}: {{ simplePreviewExample }}
              </el-text>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- Advanced Mode Form (Original) -->
      <div v-else class="advanced-mode-form">
        <el-form
          ref="advancedFormRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="batch-form"
        >
          <el-form-item :label="t('batchUpdate.matchRule')" prop="regexPattern">
            <el-input
              v-model="form.regexPattern"
              :placeholder="t('batchUpdate.regexPlaceholder')"
              clearable
            />
            <div class="form-help-text">
              {{ t('batchUpdate.regexHelp') }}
            </div>
          </el-form-item>
          
          <el-form-item :label="t('batchUpdate.newFormat')" prop="replacement">
            <el-input
              v-model="form.replacement"
              :placeholder="t('batchUpdate.replacementPlaceholder')"
              clearable
            />
            <div class="form-help-text">
              {{ t('batchUpdate.replacementHelp') }}
            </div>
          </el-form-item>

          <el-form-item>
            <div class="example-section">
              <el-text type="info" size="small">
                {{ t('batchUpdate.example') }}: 192\.168\.1\.(\d+) → 10.0.0.$1
              </el-text>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <el-button 
          @click="handlePreview" 
          :loading="previewing"
          :disabled="!canPreview"
        >
          {{ t('batchUpdate.preview') }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleExecute" 
          :loading="executing"
          :disabled="!canExecute"
        >
          {{ t('batchUpdate.execute') }}
        </el-button>
      </div>

      <!-- Preview Results Section -->
      <div v-if="previewResults.length > 0" class="preview-section">
        <el-divider>{{ t('batchUpdate.previewResults') }}</el-divider>
        <div class="preview-summary">
          <el-tag type="info">{{ t('batchUpdate.affectedPorts', { count: previewResults.length }) }}</el-tag>
        </div>
        <el-table
          :data="previewResults"
          style="width: 100%"
          max-height="300"
          :header-cell-style="headerCellStyle"
          size="small"
        >
          <el-table-column :label="t('batchUpdate.portName')" width="150">
            <template #default="{ row }">
              <el-text>{{ row.port_name }}</el-text>
            </template>
          </el-table-column>
          <el-table-column :label="t('batchUpdate.changes')">
            <template #default="{ row }">
              <div class="address-changes">
                <div v-for="(oldAddr, index) in row.old_addresses" :key="index" class="address-change">
                  <el-text type="danger" size="small">{{ oldAddr }}</el-text>
                  <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                  <el-text type="success" size="small">{{ row.new_addresses[index] }}</el-text>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="t('batchUpdate.status')" width="80">
            <template #default="{ row }">
              <el-tag :type="row.changes_made ? 'success' : 'info'" size="small">
                {{ row.changes_made ? t('batchUpdate.willUpdate') : t('batchUpdate.noChange') }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Execution Results Section -->
      <div v-if="executionResults" class="results-section">
        <el-divider>{{ t('batchUpdate.executionResults') }}</el-divider>
        <div class="results-summary">
          <el-tag type="success">{{ t('batchUpdate.successCount', { count: executionResults.success_count }) }}</el-tag>
          <el-tag v-if="executionResults.failed_count > 0" type="danger">
            {{ t('batchUpdate.failedCount', { count: executionResults.failed_count }) }}
          </el-tag>
        </div>

        <!-- Error Messages -->
        <div v-if="executionResults.errors && executionResults.errors.length > 0" class="error-messages">
          <el-alert
            v-for="(error, index) in executionResults.errors"
            :key="index"
            :title="error"
            type="error"
            :closable="false"
            class="error-item"
          />
        </div>

        <!-- Updated Ports Details -->
        <div v-if="executionResults.updated_ports.length > 0" class="updated-ports">
          <h4>{{ t('batchUpdate.updatedPortsDetails') }}</h4>
          <el-table
            :data="executionResults.updated_ports.filter(p => p.changes_made)"
            style="width: 100%"
            max-height="200"
            :header-cell-style="headerCellStyle"
            size="small"
          >
            <el-table-column :label="t('batchUpdate.portName')" prop="port_name" width="150" />
            <el-table-column :label="t('batchUpdate.changes')">
              <template #default="{ row }">
                <div class="address-changes">
                  <div v-for="(oldAddr, index) in row.old_addresses" :key="index" class="address-change">
                    <el-text type="danger" size="small">{{ oldAddr }}</el-text>
                    <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                    <el-text type="success" size="small">{{ row.new_addresses[index] }}</el-text>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t('actions.close') }}</el-button>
        <el-button v-if="executionResults && executionResults.success_count > 0" type="success" @click="handleCloseAndRefresh">
          {{ t('batchUpdate.closeAndRefresh') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import { batchUpdatePorts } from '../api'
import { useThemeStore } from '../stores/theme'
import { storeToRefs } from 'pinia'

// I18n setup
const { t } = useI18n()

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

// Props and Emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success', 'refresh'])

// Reactive data
const dialogVisible = ref(false)
const formRef = ref(null)
const simpleFormRef = ref(null)
const advancedFormRef = ref(null)
const previewing = ref(false)
const executing = ref(false)
const previewResults = ref([])
const executionResults = ref(null)

// Mode selection
const currentMode = ref('simple')

// Simple form data
const simpleForm = ref({
  replaceType: '',
  originalIpAddress: '',
  newIpAddress: '',
  originalPort: '',
  newPort: '',
  originalAddress: '',
  newAddress: '',
  // New fields for batch add/remove
  batchAddAddress: '',
  conditionAddress: '',
  removeAddress: ''
})

// Advanced form data
const form = ref({
  regexPattern: '',
  replacement: ''
})

// Replace types for simple mode
const replaceTypes = computed(() => [
  {
    value: 'ip_address',
    label: t('batchUpdate.types.ipAddress'),
    description: t('batchUpdate.types.ipAddressDesc')
  },
  {
    value: 'port',
    label: t('batchUpdate.types.port'),
    description: t('batchUpdate.types.portDesc')
  },
  {
    value: 'exact',
    label: t('batchUpdate.types.exact'),
    description: t('batchUpdate.types.exactDesc')
  },
  {
    value: 'batch_add',
    label: t('batchUpdate.types.batchAdd'),
    description: t('batchUpdate.types.batchAddDesc')
  },
  {
    value: 'batch_remove',
    label: t('batchUpdate.types.batchRemove'),
    description: t('batchUpdate.types.batchRemoveDesc')
  }
])

// Simple mode validation rules
const simpleRules = {
  replaceType: [
    { required: true, message: t('batchUpdate.validation.replaceTypeRequired'), trigger: 'change' }
  ],
  originalIpAddress: [
    { required: true, message: t('batchUpdate.validation.originalIpAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, message: t('batchUpdate.validation.invalidIpAddressFormat'), trigger: 'blur' }
  ],
  newIpAddress: [
    { required: true, message: t('batchUpdate.validation.newIpAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, message: t('batchUpdate.validation.invalidIpAddressFormat'), trigger: 'blur' }
  ],
  originalPort: [
    { required: true, message: t('batchUpdate.validation.originalPortRequired'), trigger: 'blur' },
    { pattern: /^\d+$/, message: t('batchUpdate.validation.invalidPortFormat'), trigger: 'blur' }
  ],
  newPort: [
    { required: true, message: t('batchUpdate.validation.newPortRequired'), trigger: 'blur' },
    { pattern: /^\d+$/, message: t('batchUpdate.validation.invalidPortFormat'), trigger: 'blur' }
  ],
  originalAddress: [
    { required: true, message: t('batchUpdate.validation.originalAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/, message: t('batchUpdate.validation.invalidAddressFormat'), trigger: 'blur' }
  ],
  newAddress: [
    { required: true, message: t('batchUpdate.validation.newAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/, message: t('batchUpdate.validation.invalidAddressFormat'), trigger: 'blur' }
  ],
  batchAddAddress: [
    { required: true, message: t('batchUpdate.validation.batchAddAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/, message: t('batchUpdate.validation.invalidAddressFormat'), trigger: 'blur' }
  ],
  conditionAddress: [
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/, message: t('batchUpdate.validation.invalidAddressFormat'), trigger: 'blur' }
  ],
  removeAddress: [
    { required: true, message: t('batchUpdate.validation.removeAddressRequired'), trigger: 'blur' },
    { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/, message: t('batchUpdate.validation.invalidAddressFormat'), trigger: 'blur' }
  ]
}

// Advanced mode validation rules
const rules = {
  regexPattern: [
    { required: true, message: t('batchUpdate.validation.regexRequired'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) return callback()
        try {
          new RegExp(value)
          callback()
        } catch (e) {
          callback(new Error(t('batchUpdate.validation.invalidRegex')))
        }
      },
      trigger: 'blur'
    }
  ],
  replacement: [
    { required: true, message: t('batchUpdate.validation.replacementRequired'), trigger: 'blur' }
  ]
}

// Simple mode preview example
const simplePreviewExample = computed(() => {
  const { replaceType, originalIpAddress, newIpAddress, originalPort, newPort, originalAddress, newAddress, batchAddAddress, conditionAddress, removeAddress } = simpleForm.value
  
  switch (replaceType) {
    case 'ip_address':
      if (originalIpAddress && newIpAddress) {
        return `${originalIpAddress}:8080 → ${newIpAddress}:8080`
      }
      break
    case 'port':
      if (originalPort && newPort) {
        return `*************:${originalPort} → *************:${newPort}`
      }
      break
    case 'exact':
      if (originalAddress && newAddress) {
        return `${originalAddress} → ${newAddress}`
      }
      break
    case 'batch_add':
      if (batchAddAddress) {
        if (conditionAddress) {
          return `对包含 ${conditionAddress} 的端口添加 ${batchAddAddress}`
        } else {
          return `对所有端口添加 ${batchAddAddress}`
        }
      }
      break
    case 'batch_remove':
      if (removeAddress) {
        return `从目标列表中删除 ${removeAddress}`
      }
      break
  }
  return ''
})

// Computed properties
const canPreview = computed(() => {
  if (currentMode.value === 'simple') {
    const { replaceType, originalIpAddress, newIpAddress, originalPort, newPort, originalAddress, newAddress, batchAddAddress, conditionAddress, removeAddress } = simpleForm.value
    switch (replaceType) {
      case 'ip_address':
        return originalIpAddress && newIpAddress && !previewing.value && !executing.value
      case 'port':
        return originalPort && newPort && !previewing.value && !executing.value
      case 'exact':
        return originalAddress && newAddress && !previewing.value && !executing.value
      case 'batch_add':
        return batchAddAddress && !previewing.value && !executing.value
      case 'batch_remove':
        return removeAddress && !previewing.value && !executing.value
      default:
        return false
    }
  } else {
    return form.value.regexPattern && form.value.replacement && !previewing.value && !executing.value
  }
})

const canExecute = computed(() => {
  return canPreview.value && previewResults.value.length > 0 && previewResults.value.some(p => p.changes_made)
})

// Watch props.visible
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const handleReplaceTypeChange = () => {
  // Clear form data when replace type changes
  simpleForm.value.originalIpAddress = ''
  simpleForm.value.newIpAddress = ''
  simpleForm.value.originalPort = ''
  simpleForm.value.newPort = ''
  simpleForm.value.originalAddress = ''
  simpleForm.value.newAddress = ''
  simpleForm.value.batchAddAddress = ''
  simpleForm.value.conditionAddress = ''
  simpleForm.value.removeAddress = ''
  previewResults.value = []
  executionResults.value = null
}

const convertSimpleToRegex = () => {
  const { replaceType, originalIpAddress, newIpAddress, originalPort, newPort, originalAddress, newAddress, batchAddAddress, conditionAddress, removeAddress } = simpleForm.value
  
  switch (replaceType) {
    case 'ip_address':
      return {
        regex_pattern: `${originalIpAddress.replace(/\./g, '\\.')}:(\\d+)`,
        replacement: `${newIpAddress}:$1`,
        operation_type: 'Replace',
        condition_address: null
      }
    case 'port':
      return {
        regex_pattern: `(\\d+\\.\\d+\\.\\d+\\.\\d+):${originalPort}`,
        replacement: `$1:${newPort}`,
        operation_type: 'Replace',
        condition_address: null
      }
    case 'exact':
      return {
        regex_pattern: originalAddress.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        replacement: newAddress,
        operation_type: 'Replace',
        condition_address: null
      }
    case 'batch_add':
      return {
        regex_pattern: '.*', // Not used in append mode
        replacement: batchAddAddress,
        operation_type: 'Append',
        condition_address: conditionAddress
      }
    case 'batch_remove':
      return {
        regex_pattern: removeAddress.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        replacement: '', // Not used in remove mode
        operation_type: 'Remove',
        condition_address: null
      }
    default:
      return { regex_pattern: '', replacement: '', operation_type: 'Replace', condition_address: null }
  }
}

const handlePreview = async () => {
  const currentFormRef = currentMode.value === 'simple' ? simpleFormRef.value : advancedFormRef.value
  if (!currentFormRef) return
  
  try {
    await currentFormRef.validate()
    
    previewing.value = true
    executionResults.value = null
    
    let requestData
    if (currentMode.value === 'simple') {
      const converted = convertSimpleToRegex()
      requestData = {
        regex_pattern: converted.regex_pattern,
        replacement: converted.replacement,
        operation_type: converted.operation_type,
        condition_address: converted.condition_address,
        preview_only: true
      }
    } else {
      requestData = {
        regex_pattern: form.value.regexPattern,
        replacement: form.value.replacement,
        operation_type: 'Replace',
        condition_address: null,
        preview_only: true
      }
    }
    
    const response = await batchUpdatePorts(requestData)
    
    previewResults.value = response.data.updated_ports || []
    
    if (previewResults.value.length === 0) {
      ElMessage.info(t('batchUpdate.noMatchingPorts'))
    } else {
      const changedPorts = previewResults.value.filter(p => p.changes_made).length
      if (changedPorts === 0) {
        ElMessage.info(t('batchUpdate.noChangesNeeded'))
      } else {
        ElMessage.success(t('batchUpdate.previewComplete', { count: changedPorts }))
      }
    }
    
  } catch (error) {
    console.error('Preview failed:', error)
    ElMessage.error(error.response?.data?.message || t('batchUpdate.previewFailed'))
  } finally {
    previewing.value = false
  }
}

const handleExecute = async () => {
  const currentFormRef = currentMode.value === 'simple' ? simpleFormRef.value : advancedFormRef.value
  if (!currentFormRef) return
  
  try {
    await currentFormRef.validate()
    
    executing.value = true
    
    let requestData
    if (currentMode.value === 'simple') {
      const converted = convertSimpleToRegex()
      requestData = {
        regex_pattern: converted.regex_pattern,
        replacement: converted.replacement,
        operation_type: converted.operation_type,
        condition_address: converted.condition_address,
        preview_only: false
      }
    } else {
      requestData = {
        regex_pattern: form.value.regexPattern,
        replacement: form.value.replacement,
        operation_type: 'Replace',
        condition_address: null,
        preview_only: false
      }
    }
    
    const response = await batchUpdatePorts(requestData)
    
    executionResults.value = response.data
    
    if (executionResults.value.success_count > 0) {
      ElMessage.success(t('batchUpdate.executeSuccess', { 
        count: executionResults.value.success_count 
      }))
      emit('success')
    } else {
      ElMessage.warning(t('batchUpdate.noPortsUpdated'))
    }
    
    if (executionResults.value.failed_count > 0) {
      ElMessage.error(t('batchUpdate.executePartialFailure', { 
        failed: executionResults.value.failed_count,
        success: executionResults.value.success_count
      }))
    }
    
  } catch (error) {
    console.error('Execution failed:', error)
    ElMessage.error(error.response?.data?.message || t('batchUpdate.executeFailed'))
  } finally {
    executing.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleCloseAndRefresh = () => {
  dialogVisible.value = false
  emit('refresh')
}

const handleDialogClosed = () => {
  // Reset all forms and results when dialog is closed
  currentMode.value = 'simple'
  
  form.value = {
    regexPattern: '',
    replacement: ''
  }
  
  simpleForm.value = {
    replaceType: '',
    originalIpAddress: '',
    newIpAddress: '',
    originalPort: '',
    newPort: '',
    originalAddress: '',
    newAddress: '',
    batchAddAddress: '',
    conditionAddress: '',
    removeAddress: ''
  }
  
  previewResults.value = []
  executionResults.value = null
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  if (simpleFormRef.value) {
    simpleFormRef.value.clearValidate()
  }
  if (advancedFormRef.value) {
    advancedFormRef.value.clearValidate()
  }
}
</script>

<style scoped>
.batch-update-container {
  padding: 20px 0;
}

.mode-selector {
  margin-bottom: 24px;
  text-align: center;
}

.mode-radio-group {
  margin-bottom: 12px;
}

.mode-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.simple-mode-form,
.advanced-mode-form {
  margin-bottom: 20px;
}

.batch-form {
  margin-bottom: 20px;
}

.replace-config {
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 3px solid var(--el-color-primary);
}

.ip-segment-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ip-wildcard {
  font-weight: 500;
  color: var(--el-color-primary);
  font-size: 14px;
}

.option-item {
  padding: 4px 0;
}

.option-label {
  font-weight: 500;
  margin-bottom: 2px;
}

.option-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
}

.form-help-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.example-section {
  margin-top: 10px;
  padding: 8px 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  border-left: 3px solid var(--el-color-primary);
}

.action-buttons {
  margin: 20px 0;
  display: flex;
  gap: 12px;
}

.preview-section, .results-section {
  margin-top: 20px;
}

.preview-summary, .results-summary {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.address-changes {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-change {
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow-icon {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.error-messages {
  margin-bottom: 16px;
}

.error-item {
  margin-bottom: 8px;
}

.error-item:last-child {
  margin-bottom: 0;
}

.updated-ports h4 {
  margin: 16px 0 12px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Dark theme adjustments */
.dark .example-section {
  background: var(--el-fill-color-dark);
  border-left-color: var(--el-color-primary);
}

.dark .replace-config {
  background: var(--el-fill-color-dark);
  border-left-color: var(--el-color-primary);
}
</style>