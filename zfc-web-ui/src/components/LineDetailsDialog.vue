<template>
  <el-dialog
    v-model="visible"
    :title="$t('dialogs.lineDetails.title')"
    width="90%"
    :before-close="handleClose"
    class="line-details-dialog"
  >
    <div v-loading="loading" class="dialog-content">
      <el-table
        :data="lineDetails"
        style="width: 100%"
        empty-text="暂无数据"
        :default-sort="{ prop: 'usedTraffic', order: 'descending' }"
      >
        <el-table-column
          prop="lineName"
          :label="$t('dialogs.lineDetails.lineName')"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="entryIp"
          :label="$t('dialogs.lineDetails.entryIp')"
          min-width="120"
        />
        <el-table-column
          prop="trafficScale"
          :label="$t('dialogs.lineDetails.trafficScale')"
          min-width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ row.trafficScale }}x
          </template>
        </el-table-column>
        <el-table-column
          prop="trafficLimit"
          :label="$t('dialogs.lineDetails.trafficLimit')"
          min-width="120"
          align="right"
        >
          <template #default="{ row }">
            {{ row.trafficLimit ? formatTraffic(row.trafficLimit) : $t('dialogs.lineDetails.followTotalLimit') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="usedTraffic"
          :label="$t('dialogs.lineDetails.usedTraffic')"
          min-width="120"
          align="right"
          sortable
        >
          <template #default="{ row }">
            {{ formatTraffic(row.usedTraffic) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="portCount"
          :label="$t('dialogs.lineDetails.portCount')"
          min-width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ row.portCount }} {{ $t('dialogs.lineDetails.ports') }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <el-button @click="handleClose">{{ $t('common.close') }}</el-button>
      <el-button type="primary" @click="refreshData">{{ $t('common.refresh') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useAuthStore } from '../stores/auth'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const authStore = useAuthStore()
const visible = ref(false)
const loading = ref(false)
const lineDetails = ref([])

// Watch for prop changes
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    fetchLineDetails()
  }
})

// Watch for dialog visibility changes
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

const fetchLineDetails = async () => {
  if (!authStore.token) {
    ElMessage.error('请先登录')
    return
  }

  loading.value = true
  try {
    const response = await fetch('/api/line_details', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    
    // Transform backend data to frontend format
    lineDetails.value = data.line_details.map(item => ({
      lineId: item.line_id,
      lineName: item.line_name,
      entryIp: item.entry_ip,
      trafficScale: item.traffic_scale,
      trafficLimit: item.traffic_limit,
      usedTraffic: item.used_traffic,
      portCount: item.port_count
    }))

  } catch (error) {
    console.error('Failed to fetch line details:', error)
    let errorMessage = '获取线路详细信息失败'
    if (error.message && error.message.includes('401')) {
      errorMessage = '登录已过期，请重新登录'
    } else if (error.message && error.message.includes('403')) {
      errorMessage = '没有权限查看线路详细信息'
    } else if (error.message && error.message.includes('500')) {
      errorMessage = '服务器内部错误，请稍后重试'
    }
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchLineDetails()
}

const formatTraffic = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  const size = bytes / Math.pow(1024, i)
  
  return `${size.toFixed(2)} ${sizes[i]}`
}
</script>

<style scoped>
.line-details-dialog {
  --el-dialog-width: 90%;
}

.dialog-content {
  min-height: 200px;
}

.el-table {
  --el-table-border-color: var(--theme-border-base);
  --el-table-text-color: var(--theme-text-regular);
  --el-table-header-text-color: var(--theme-text-primary);
  --el-table-header-bg-color: var(--theme-fill-light);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .line-details-dialog {
    --el-dialog-width: 95%;
  }
  
  :deep(.el-dialog__body) {
    padding: 10px;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .line-details-dialog {
    --el-dialog-width: 98%;
  }
  
  :deep(.el-dialog__header) {
    padding: 15px 10px 10px;
  }
  
  :deep(.el-dialog__title) {
    font-size: 14px;
  }
}
</style>