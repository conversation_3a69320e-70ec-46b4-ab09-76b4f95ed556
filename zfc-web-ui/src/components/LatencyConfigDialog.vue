<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :title="isEditing ? '编辑监控配置' : '添加监控配置'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="配置名称" prop="display_name">
        <el-input
          v-model="form.display_name"
          placeholder="请输入配置名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="目标地址" prop="target_address">
        <el-input
          v-model="form.target_address"
          placeholder="IP地址或域名，如：******* 或 google.com"
          maxlength="100"
        />
        <div class="form-hint">支持IP地址和域名</div>
      </el-form-item>

      <el-form-item label="测试类型" prop="test_type">
        <el-radio-group v-model="form.test_type">
          <el-radio label="icmp">ICMP Ping</el-radio>
          <el-radio label="tcp">TCP Ping</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        label="目标端口" 
        prop="target_port"
        v-if="form.test_type === 'tcp'"
      >
        <el-input-number
          v-model="form.target_port"
          :min="1"
          :max="65535"
          placeholder="端口号"
          style="width: 100%"
        />
        <div class="form-hint">TCP测试需要指定端口</div>
      </el-form-item>

      <el-form-item 
        label="包大小" 
        prop="packet_size"
        v-if="form.test_type === 'icmp'"
      >
        <el-input-number
          v-model="form.packet_size"
          :min="8"
          :max="1500"
          placeholder="字节"
          style="width: 100%"
        />
        <div class="form-hint">ICMP包大小，默认64字节</div>
      </el-form-item>

      <el-form-item label="超时时间" prop="timeout">
        <el-input-number
          v-model="form.timeout"
          :min="1000"
          :max="30000"
          :step="1000"
          placeholder="毫秒"
          style="width: 100%"
        />
        <div class="form-hint">测试超时时间，默认5000毫秒</div>
      </el-form-item>

      <el-form-item label="测试间隔" prop="interval">
        <el-input-number
          v-model="form.interval"
          :min="60"
          :max="3600"
          :step="10"
          placeholder="秒"
          style="width: 100%"
        />
        <div class="form-hint">延迟测试间隔，默认60秒</div>
      </el-form-item>

      <el-form-item label="告警阈值" prop="alert_threshold">
        <el-input-number
          v-model="form.alert_threshold"
          :min="1"
          :max="10"
          placeholder="次数"
          style="width: 100%"
        />
        <div class="form-hint">连续失败多少次后告警，默认3次</div>
      </el-form-item>

      <el-form-item label="可见性设置" v-if="isAdmin">
        <el-checkbox v-model="form.is_public">
          所有用户可见
        </el-checkbox>
        <div class="form-hint">勾选后，普通用户也可以查看此监控配置的数据</div>
      </el-form-item>

      <el-form-item label="启用状态">
        <el-checkbox v-model="form.is_enabled">
          启用监控
        </el-checkbox>
        <div class="form-hint">关闭后，将停止对此目标的延迟监控</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useSubscriptionStore } from '../stores/subscription'
import { createLatencyConfig, updateLatencyConfig } from '../api/index.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  serverId: {
    type: Number,
    required: true
  },
  config: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'saved'])

const subscriptionStore = useSubscriptionStore()
const formRef = ref(null)
const submitting = ref(false)

const isEditing = computed(() => !!props.config)
const isAdmin = computed(() => subscriptionStore.isAdmin)

// 表单数据
const form = reactive({
  display_name: '',
  target_address: '',
  test_type: 'icmp',
  target_port: null,
  packet_size: 64,
  timeout: 5000,
  interval: 60,
  alert_threshold: 3,
  is_public: false,
  is_enabled: true
})

// 表单验证规则
const rules = {
  display_name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2到50个字符', trigger: 'blur' }
  ],
  target_address: [
    { required: true, message: '请输入目标地址', trigger: 'blur' },
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
      message: '请输入有效的IP地址或域名', 
      trigger: 'blur' 
    }
  ],
  test_type: [
    { required: true, message: '请选择测试类型', trigger: 'change' }
  ],
  target_port: [
    {
      validator: (rule, value, callback) => {
        if (form.test_type === 'tcp' && (!value || value < 1 || value > 65535)) {
          callback(new Error('TCP测试需要输入有效的端口号(1-65535)'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  packet_size: [
    { 
      type: 'number', 
      min: 8, 
      max: 1500, 
      message: '包大小范围为8-1500字节', 
      trigger: 'blur' 
    }
  ],
  timeout: [
    { 
      type: 'number', 
      min: 1000, 
      max: 30000, 
      message: '超时时间范围为1000-30000毫秒', 
      trigger: 'blur' 
    }
  ],
  interval: [
    { 
      type: 'number', 
      min: 60, 
      max: 3600, 
      message: '测试间隔范围为60-3600秒', 
      trigger: 'blur' 
    }
  ],
  alert_threshold: [
    { 
      type: 'number', 
      min: 1, 
      max: 10, 
      message: '告警阈值范围为1-10次', 
      trigger: 'blur' 
    }
  ]
}

// 重置表单
const resetForm = () => {
  if (props.config) {
    // 编辑模式，填充现有数据
    Object.assign(form, {
      display_name: props.config.display_name || '',
      target_address: props.config.target_address || '',
      test_type: props.config.test_type || 'icmp',
      target_port: props.config.target_port || null,
      packet_size: props.config.packet_size || 64,
      timeout: props.config.timeout || 5000,
      interval: props.config.interval || 60,
      alert_threshold: props.config.alert_threshold || 3,
      is_public: props.config.is_public || false,
      is_enabled: props.config.is_enabled !== false
    })
  } else {
    // 新建模式，使用默认值
    Object.assign(form, {
      display_name: '',
      target_address: '',
      test_type: 'icmp',
      target_port: null,
      packet_size: 64,
      timeout: 5000,
      interval: 60,
      alert_threshold: 3,
      is_public: false,
      is_enabled: true
    })
  }
}

// 监听测试类型变化，清理相关字段
watch(() => form.test_type, (newType) => {
  if (newType === 'icmp') {
    form.target_port = null
  } else if (newType === 'tcp') {
    form.packet_size = null
  }
})

// 监听对话框打开
watch(() => props.modelValue, (visible) => {
  if (visible) {
    resetForm()
    // 清除验证状态
    setTimeout(() => {
      formRef.value?.clearValidate()
    }, 100)
  }
})

// 处理取消
const handleCancel = () => {
  emit('update:modelValue', false)
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 准备请求数据
    const requestData = {
      display_name: form.display_name,
      target_address: form.target_address,
      test_type: form.test_type,
      target_port: form.target_port,
      packet_size: form.packet_size,
      timeout: form.timeout,
      interval: form.interval,
      alert_threshold: form.alert_threshold,
      is_public: form.is_public,
      is_enabled: form.is_enabled,
      server_id: props.serverId
    }

    // 根据测试类型清理不需要的字段
    if (form.test_type === 'icmp') {
      delete requestData.target_port
    } else if (form.test_type === 'tcp') {
      delete requestData.packet_size
    }

    let response
    if (isEditing.value) {
      // 更新配置
      response = await updateLatencyConfig(props.config.id, requestData)
    } else {
      // 创建配置
      response = await createLatencyConfig(requestData)
    }

    ElMessage.success(isEditing.value ? '配置更新成功' : '配置创建成功')
    emit('saved')
  } catch (error) {
    console.error('Failed to save config:', error)
    ElMessage.error(
      error.response?.data?.message || 
      (isEditing.value ? '更新失败' : '创建失败')
    )
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-hint {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}
</style>