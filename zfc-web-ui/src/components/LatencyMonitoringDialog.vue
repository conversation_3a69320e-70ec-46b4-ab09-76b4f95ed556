<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="延迟监控"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="latency-monitoring-dialog">
      <!-- 实时状态面板 -->
      <el-card class="realtime-panel">
        <template #header>
          <div class="card-header">
            <span>实时延迟状态</span>
            <el-button text @click="refreshRealtimeData" :loading="realtimeLoading">
              <el-icon><refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <div class="realtime-grid">
          <div v-for="(status, configId) in realtimeStatus" :key="configId" class="status-item">
            <div class="status-header">
              <div class="status-name">{{ status.display_name }}</div>
              <div class="status-indicator" :class="{ 'success': status.success, 'error': !status.success }">
                <el-icon v-if="status.success"><check /></el-icon>
                <el-icon v-else><close /></el-icon>
              </div>
            </div>
            <div class="status-details">
              <div class="target">{{ formatTargetAddress(status, false) }}</div>
              <div class="latency" v-if="status.success && status.latency_us">
                {{ formatLatency(status.latency_us) }}
              </div>
              <div class="error" v-else-if="!status.success">
                {{ status.error_msg || '测试失败' }}
              </div>
              <div class="last-test" v-if="status.last_test_time">
                最后测试: {{ formatTime(status.last_test_time) }}
              </div>
            </div>
          </div>
          <div v-if="Object.keys(realtimeStatus).length === 0" class="empty-status">
            暂无监控配置
          </div>
        </div>
      </el-card>

      <!-- 历史数据图表 -->
      <el-card class="chart-panel">
        <template #header>
          <div class="card-header">
            <span>历史延迟数据</span>
            <div class="time-range-selector">
              <el-radio-group v-model="selectedTimeRange" @change="loadHistoryData" size="small">
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="3h">3小时</el-radio-button>
                <el-radio-button label="6h">6小时</el-radio-button>
                <el-radio-button label="12h">12小时</el-radio-button>
                <el-radio-button label="24h">24小时</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <LatencyChart 
          :configs="configs"
          :loading="configsLoading"
          :time-range="selectedTimeRange"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="openDetailPage">配置管理</el-button>
        <el-button type="primary" @click="$emit('update:modelValue', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { ElMessage } from 'element-plus'
import { Refresh, Check, Close } from '@element-plus/icons-vue'
import LatencyChart from './LatencyChart.vue'
import { formatTargetAddress } from '../utils/privacy.js'
import { 
  getLatencyHistory, 
  getLatencyRealtime,
  getLatencyConfigs
} from '../api/index.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  serverId: {
    type: Number,
    required: true
  },
  serverName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

// 权限控制
const isAdmin = computed(() => subscriptionStore.isAdmin)

// 实时状态数据
const realtimeStatus = ref({})
const realtimeLoading = ref(false)

// 配置数据
const configs = ref([])
const configsLoading = ref(false)

// 历史数据
const historyData = ref([])
const chartLoading = ref(false)
const selectedTimeRange = ref('1h')

// 定时器引用
const realtimeTimer = ref(null)

// 重置数据
const resetData = () => {
  realtimeStatus.value = {}
  configs.value = []
  historyData.value = []
  chartLoading.value = false
  realtimeLoading.value = false
  configsLoading.value = false
}

// 加载实时状态数据
const loadRealtimeData = async () => {
  if (!props.serverId) return
  
  try {
    realtimeLoading.value = true
    const response = await getLatencyRealtime(props.serverId)
    realtimeStatus.value = response.data.status || {}
  } catch (error) {
    console.error('Failed to load realtime data:', error)
    ElMessage.error('加载实时数据失败')
    realtimeStatus.value = {}
  } finally {
    realtimeLoading.value = false
  }
}

// 刷新实时数据
const refreshRealtimeData = () => {
  loadRealtimeData()
}

// 加载配置数据
const loadConfigs = async () => {
  if (!props.serverId) return
  
  try {
    configsLoading.value = true
    const response = await getLatencyConfigs({
      server_id: props.serverId,
      page: 1,
      page_size: 100  // 对话框中加载所有配置
    })
    configs.value = response.data.configs || []
  } catch (error) {
    console.error('Failed to load configs:', error)
    ElMessage.error('加载配置失败')
    configs.value = []
  } finally {
    configsLoading.value = false
  }
}

// 加载历史数据
const loadHistoryData = async () => {
  if (!props.serverId) return
  
  try {
    chartLoading.value = true
    // 重置历史数据
    historyData.value = []
    
    const response = await getLatencyHistory({
      server_id: props.serverId,
      time_range: selectedTimeRange.value
    })
    historyData.value = response.data.data_points || []
  } catch (error) {
    console.error('Failed to load history data:', error)
    ElMessage.error('加载历史数据失败')
    historyData.value = []
  } finally {
    chartLoading.value = false
  }
}

// 打开详细管理页面
const openDetailPage = () => {
  emit('update:modelValue', false)
  router.push(`/dashboard/latency-monitoring/${props.serverId}`)
}

// 格式化延迟显示
const formatLatency = (latencyUs) => {
  if (latencyUs < 1000) {
    return `${latencyUs}μs`
  } else if (latencyUs < 1000000) {
    return `${(latencyUs / 1000).toFixed(2)}ms`
  } else {
    return `${(latencyUs / 1000000).toFixed(2)}s`
  }
}

// 格式化时间显示
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}


// 监听弹窗打开/关闭
watch(() => props.modelValue, (visible) => {
  if (visible) {
    // 弹窗打开时重置并加载数据
    resetData()
    loadRealtimeData()
    loadConfigs()
    loadHistoryData()
    
    // 设置定时刷新实时数据（每30秒）
    if (realtimeTimer.value) {
      clearInterval(realtimeTimer.value)
    }
    realtimeTimer.value = setInterval(loadRealtimeData, 30000)
  } else {
    // 弹窗关闭时清理定时器和数据
    if (realtimeTimer.value) {
      clearInterval(realtimeTimer.value)
      realtimeTimer.value = null
    }
    // 延迟重置数据，避免关闭动画时闪烁
    setTimeout(() => {
      if (!props.modelValue) {
        resetData()
      }
    }, 300)
  }
})

// 监听服务器ID变化
watch(() => props.serverId, (newServerId, oldServerId) => {
  if (newServerId !== oldServerId && newServerId && props.modelValue) {
    resetData()
    loadRealtimeData()
    loadConfigs()
    loadHistoryData()
  }
})

// 清理定时器
onUnmounted(() => {
  if (realtimeTimer.value) {
    clearInterval(realtimeTimer.value)
    realtimeTimer.value = null
  }
})
</script>

<style scoped>
.latency-monitoring-dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.realtime-panel,
.chart-panel {
  margin-bottom: 0;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.status-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 12px;
  background: var(--el-bg-color-page);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.status-indicator.success {
  background-color: var(--el-color-success);
}

.status-indicator.error {
  background-color: var(--el-color-danger);
}

.status-details {
  font-size: 12px;
}

.target {
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.latency {
  font-weight: 500;
  color: var(--el-color-success);
  margin-bottom: 4px;
}

.error {
  color: var(--el-color-danger);
  margin-bottom: 4px;
}

.last-test {
  color: var(--el-text-color-placeholder);
  font-size: 11px;
}

.empty-status {
  grid-column: 1 / -1;
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 0;
}

.time-range-selector {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 确保图表在弹窗中正确显示 */
:deep(.latency-chart) {
  height: 350px;
}

:deep(.chart-container) {
  height: 300px;
}

/* 弹窗内滚动条样式 */
.realtime-grid::-webkit-scrollbar {
  width: 6px;
}

.realtime-grid::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.realtime-grid::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.realtime-grid::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}
</style>