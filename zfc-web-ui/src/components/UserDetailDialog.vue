<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('dialogs.userDetail.title') + ' - ' + (userData?.email_address || 'Unknown')"
    width="90%"
    top="5vh"
    @close="handleClose"
  >
    <div class="user-detail-content">
      <!-- User Basic Info -->
      <el-card class="user-info-card" v-if="userData">
        <template #header>
          <h3>{{ $t('dialogs.userDetail.basicInfo') }}</h3>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item :label="$t('forms.labels.id')">{{ userData.id }}</el-descriptions-item>
          <el-descriptions-item :label="$t('forms.labels.email')">{{ userData.email_address }}</el-descriptions-item>
          <el-descriptions-item :label="$t('forms.labels.tokenId')">
            <span class="token-id">{{ userData.token_id }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('dialogs.userDetail.validUntil')">
            {{ formatDate(userData.valid_until) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('dialogs.userDetail.nextReset')">
            {{ formatDate(userData.next_reset) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('dialogs.userDetail.bandwidth')">
            {{ userData.bandwidth ? `${userData.bandwidth} Mbps` : $t('dialogs.userDetail.unlimited') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('dialogs.userDetail.trafficUsage')" :span="3">
            <div class="traffic-info">
              <el-progress
                :percentage="getTrafficPercentage(userData)"
                :status="getTrafficStatus(userData)"
              />
              <div class="traffic-details">
                <span>{{ formatTrafficBytes(userData.traffic_used) }} / {{ formatTrafficBytes(userData.traffic_total) }}</span>
                <span class="traffic-percentage">{{ getTrafficPercentage(userData).toFixed(1) }}%</span>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- Historical Speed Charts -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3>{{ $t('dialogs.userDetail.historicalSpeed') }}</h3>
          </div>
        </template>

        <div class="chart-main-layout">
          <!-- Left side: Charts and Controls -->
          <div class="chart-left-panel">
            <div class="chart-controls">
              <div class="time-range-controls">
                <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
                  <el-radio-button label="1h">{{ $t('timeRange.1h') }}</el-radio-button>
                  <el-radio-button label="3h">{{ $t('timeRange.3h') }}</el-radio-button>
                  <el-radio-button label="6h">{{ $t('timeRange.6h') }}</el-radio-button>
                  <el-radio-button label="24h">{{ $t('timeRange.24h') }}</el-radio-button>
                  <el-radio-button label="7d">{{ $t('timeRange.7d') }}</el-radio-button>
                </el-radio-group>
                <el-button 
                  :icon="Refresh" 
                  @click="refreshChartData"
                  :loading="chartLoading"
                  size="small"
                >
                  {{ $t('common.refresh') }}
                </el-button>
              </div>
              
              <!-- Line Selection Controls -->
              <div class="line-selection-controls" v-if="availableLines.length > 0">
                <el-select
                  v-model="selectedLineIds"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
:placeholder="$t('dialogs.userDetail.selectLines')"
                  style="width: 300px"
                  @change="handleLineSelectionChange"
                >
                  <template #header>
                    <el-checkbox
                      v-model="selectAll"
                      :indeterminate="isIndeterminate"
                      @change="handleSelectAll"
                    >
                      {{ $t('dialogs.userDetail.selectAll') }}
                    </el-checkbox>
                  </template>
                  <el-option
                    v-for="line in availableLines"
                    :key="line.line_id"
                    :label="line.line_name || `Line ${line.line_id}`"
                    :value="line.line_id"
                  >
                    <span class="line-option">
                      <span>{{ line.line_name || `${$t('dialogs.userDetail.linePrefix')} ${line.line_id}` }}</span>
                      <span class="line-speed">{{ formatSpeed(line.latest_total_speed) }}</span>
                    </span>
                  </el-option>
                </el-select>
                
                <el-alert
                  v-if="hasMoreLines"
                  :title="$t('dialogs.userSpeedDetail.showingLines', { count: selectedLineIds.length, total: totalLinesCount })"
                  type="info"
                  show-icon
                  :closable="false"
                  style="margin-top: 8px"
                />
              </div>
            </div>

            <div v-loading="chartLoading" class="chart-container">
              <div v-if="!chartData || chartData.length === 0" class="no-chart-data">
                <el-empty :description="$t('dialogs.userDetail.noData')" />
              </div>
              <div v-else>
                <!-- Total Speed Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userDetail.totalSpeed') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="totalSpeedOption" 
                    :autoresize="true"
                    ref="totalChart"
                  />
                </div>

                <!-- Client IP Count Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userDetail.clientIPCount') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="clientIPCountOption" 
                    :autoresize="true"
                    ref="clientIPChart"
                  />
                </div>

                <!-- Connection Count Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userDetail.connectionCount') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="connectionCountOption" 
                    :autoresize="true"
                    ref="connectionChart"
                  />
                </div>

                <!-- Traffic Consumption Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userDetail.trafficConsumption') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="trafficConsumptionOption" 
                    :autoresize="true"
                    ref="trafficChart"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Right side: Real-time Line Speeds -->
          <div class="chart-right-panel" v-if="realtimeLineSpeeds.length > 0">
            <div class="realtime-speeds">
              <h4>{{ $t('dialogs.userDetail.realtimeSpeed') }}</h4>
              <div class="speed-grid">
                <div 
                  v-for="lineSpeed in realtimeLineSpeeds" 
                  :key="lineSpeed.line_id"
                  class="speed-item"
                >
                  <div class="line-name">{{ lineSpeed.line_name || `${$t('dialogs.userDetail.linePrefix')} ${lineSpeed.line_id}` }}</div>
                  <div class="speed-info">
                    <span class="total-speed">{{ formatSpeed(lineSpeed.upload_speed + lineSpeed.download_speed) }}</span>
                    <div class="connection-info">
                      <span class="ip-count">{{ $t('dialogs.userDetail.clientIPs') }}: {{ lineSpeed.client_ips_count || 0 }}</span>
                      <span class="connection-count">{{ $t('dialogs.userDetail.connections') }}: {{ lineSpeed.connection_count || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Port Details Card (Admin Only) -->
      <el-card class="chart-card" v-if="authStore.subscriptionData?.is_admin">
        <template #header>
          <div class="port-header">
            <h3>{{ $t('dialogs.userDetail.portDetails') }}</h3>
            <el-button 
              @click="togglePortDetails"
              :icon="portDetailsExpanded ? ArrowUp : ArrowDown"
              type="default"
              size="small"
              :loading="portDetailsLoading && portDetailsExpanded"
            >
              {{ portDetailsExpanded ? $t('dialogs.userDetail.hidePorts') : $t('dialogs.userDetail.showPorts') }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="portDetailsExpanded" class="port-details-container">
            <div v-loading="portDetailsLoading" class="port-table-wrapper">
              <div v-if="!portDetailsLoading && (!userPorts || userPorts.length === 0)" class="no-ports-data">
                <el-empty :description="$t('dialogs.userDetail.noPorts')" />
              </div>
              
              <!-- Port Table -->
              <el-table
                v-else
                :data="userPorts"
                style="width: 100%"
                class="port-table"
                :row-class-name="({ row }) => row.is_suspended ? 'suspended-port' : ''"
                :row-style="{ height: '60px' }"
              >
                <el-table-column prop="id" :label="$t('forms.labels.id')" width="60" />
                <el-table-column :label="$t('forms.labels.name')" min-width="180">
                  <template #default="{ row }">
                    <div class="truncated-text">
                      {{ row.display_name }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.status')" width="80">
                  <template #default="{ row }">
                    <div class="status-indicator">
                      <div
                        :class="['status-dot', row.is_suspended ? 'status-suspended' : 'status-active']"
                        :title="row.is_suspended ? $t('status.offline') : $t('status.active')"
                      ></div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.line')" min-width="120">
                  <template #default="{ row }">
                    <div class="truncated-text">
                      {{ getLineName(row) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.entryPoint')" min-width="200">
                  <template #default="{ row }">
                    <div class="truncated-text">
                      {{ row.ip_addr }}:{{ row.port_v4 }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.target')" min-width="220">
                  <template #default="{ row }">
                    <div class="target-address truncated-text">
                      {{ row.target_address_list[0] }}
                      <span v-if="row.target_address_list.length > 1" class="more-addresses">
                        (+{{ row.target_address_list.length - 1 }} more)
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.targetSelectMode')" width="110">
                  <template #default="{ row }">
                    <el-tag size="small" type="info" v-if="row.target_select_mode !== null">
                      {{ getTargetModeLabel(row.target_select_mode) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.testMethod')" width="110">
                  <template #default="{ row }">
                    <el-tag size="small" type="info" v-if="row.test_method !== null">
                      {{ getTestMethodLabel(row.test_method) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('forms.labels.trafficConsumption')" width="130">
                  <template #default="{ row }">
                    <div class="traffic-container">
                      <div class="traffic-row">
                        <span class="traffic-icon">↑</span>
                        <span class="traffic-value">{{ formatTraffic(row.traffic_out) }}</span>
                      </div>
                      <div class="traffic-row">
                        <span class="traffic-icon">↓</span>
                        <span class="traffic-value">{{ formatTraffic(row.traffic_in) }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              
              <!-- Pagination -->
              <div v-if="userPorts && userPorts.length > 0" class="pagination-container">
                <div class="pagination-info">
                  <span>{{ $t('messages.info.totalItems', { count: portTotalItems }) }}</span>
                </div>
                <el-pagination
                  v-model:current-page="portCurrentPage"
                  :page-size="portPageSize"
                  :total="portTotalItems"
                  layout="prev, pager, next, sizes, jumper"
                  :page-sizes="[10, 20, 50]"
                  @current-change="handlePortPageChange"
                  @size-change="handlePortPageSizeChange"
                  class="pagination-controls"
                  small
                />
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>

      <!-- Line Details Card (Admin Only) -->
      <el-card class="chart-card" v-if="authStore.subscriptionData?.is_admin">
        <template #header>
          <div class="port-header">
            <h3>{{ $t('dialogs.userDetail.lineDetails') }}</h3>
            <el-button 
              @click="toggleLineDetails"
              :icon="lineDetailsExpanded ? ArrowUp : ArrowDown"
              type="default"
              size="small"
              :loading="lineDetailsLoading && lineDetailsExpanded"
            >
              {{ lineDetailsExpanded ? $t('dialogs.userDetail.hideLines') : $t('dialogs.userDetail.showLines') }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="lineDetailsExpanded" class="line-details-container">
            <div v-loading="lineDetailsLoading" class="line-table-wrapper">
              <div v-if="!lineDetailsLoading && (!userLineDetails || userLineDetails.length === 0)" class="no-lines-data">
                <el-empty :description="$t('dialogs.userDetail.noLines')" />
              </div>
              
              <!-- Line Details Table -->
              <el-table
                v-else
                :data="userLineDetails"
                style="width: 100%"
                class="line-table"
                :default-sort="{ prop: 'usedTraffic', order: 'descending' }"
                :row-style="{ height: '60px' }"
              >
                <el-table-column 
                  prop="line_name" 
                  :label="$t('dialogs.lineDetails.lineName')" 
                  min-width="150"
                  show-overflow-tooltip
                />
                <el-table-column 
                  prop="entry_ip" 
                  :label="$t('dialogs.lineDetails.entryIp')" 
                  min-width="120"
                />
                <el-table-column 
                  prop="traffic_scale" 
                  :label="$t('dialogs.lineDetails.trafficScale')" 
                  min-width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    {{ row.traffic_scale }}x
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="traffic_limit" 
                  :label="$t('dialogs.lineDetails.trafficLimit')" 
                  min-width="120"
                  align="right"
                >
                  <template #default="{ row }">
                    {{ row.traffic_limit ? formatTrafficBytes(row.traffic_limit) : $t('dialogs.lineDetails.followTotalLimit') }}
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="used_traffic" 
                  :label="$t('dialogs.lineDetails.usedTraffic')" 
                  min-width="120"
                  align="right"
                  sortable
                >
                  <template #default="{ row }">
                    {{ formatTrafficBytes(row.used_traffic) }}
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="port_count" 
                  :label="$t('dialogs.lineDetails.portCount')" 
                  min-width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    {{ row.port_count }} {{ $t('dialogs.lineDetails.ports') }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components'
import dayjs from 'dayjs'
import { getUserHistoricalSpeeds, getUserLineSpeeds, getAdminPortsByUser } from '../api'
import { useAuthStore } from '../stores/auth'
import { useThemeStore } from '../stores/theme'

// Register ECharts components
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close'])

const { t } = useI18n()
const authStore = useAuthStore()
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const selectedTimeRange = ref('24h')
const chartLoading = ref(false)
const chartData = ref([])
const totalChart = ref(null)
const clientIPChart = ref(null)
const connectionChart = ref(null)
const trafficChart = ref(null)

// Line selection data
const selectedLineIds = ref([])
const availableLines = ref([])
const hasMoreLines = ref(false)
const totalLinesCount = ref(0)
const selectAll = ref(false)

// Real-time line speeds data
const realtimeLineSpeeds = ref([])
const realtimeLoading = ref(false)

// Port details data
const portDetailsExpanded = ref(false)
const portDetailsLoading = ref(false)
const userPorts = ref([])
const portCurrentPage = ref(1)
const portPageSize = ref(10)
const portTotalItems = ref(0)

// Line details data
const lineDetailsExpanded = ref(false)
const lineDetailsLoading = ref(false)
const userLineDetails = ref([])

const handleClose = () => {
  emit('close')
}

// Computed properties for line selection
const isIndeterminate = computed(() => {
  const selectedCount = selectedLineIds.value.length
  const totalCount = availableLines.value.length
  return selectedCount > 0 && selectedCount < totalCount
})

// Line selection methods
const handleSelectAll = (checked) => {
  if (checked) {
    selectedLineIds.value = availableLines.value.map(line => line.line_id)
  } else {
    selectedLineIds.value = []
  }
  loadHistoricalData(false) // Don't reset selection when manually changing selection
}

const handleLineSelectionChange = () => {
  selectAll.value = selectedLineIds.value.length === availableLines.value.length
  loadHistoricalData(false) // Don't reset selection when manually changing selection
}

const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const formatTrafficBytes = (bytes) => {
  if (bytes === 0) return '0 GB'
  const GB = 1024 * 1024 * 1024
  return `${(bytes / GB).toFixed(2)} GB`
}

const getTrafficPercentage = (userData) => {
  if (!userData?.traffic_total || !userData?.traffic_used) return 0
  const percentage = (userData.traffic_used / userData.traffic_total) * 100
  return Math.min(percentage, 100)
}

const getTrafficStatus = (userData) => {
  const percentage = getTrafficPercentage(userData)
  if (percentage > 90) return 'exception'
  if (percentage > 70) return 'warning'
  return 'success'
}

const formatSpeed = (bytesPerSecond) => {
  if (!bytesPerSecond || bytesPerSecond < 0) return '0 B/s'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  let value = bytesPerSecond
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

const getTimeRangeInMinutes = (range) => {
  const ranges = {
    '1h': 60,
    '3h': 180,
    '6h': 360,
    '24h': 1440,
    '7d': 10080
  }
  return ranges[range] || 1440
}

const getTimeFormat = (range) => {
  switch (range) {
    case '1h':
    case '3h':
    case '6h':
      return 'HH:mm'
    case '24h':
      return 'MM-DD HH:mm'
    case '7d':
      return 'MM-DD'
    default:
      return 'MM-DD HH:mm'
  }
}

const resetSelectionState = () => {
  selectedLineIds.value = []
  availableLines.value = []
  hasMoreLines.value = false
  totalLinesCount.value = 0
  selectAll.value = false
}

const loadHistoricalData = async (resetSelection = false) => {
  if (!props.userData?.id) return

  // Reset selection state if needed (e.g., when userData changes)
  if (resetSelection) {
    resetSelectionState()
  }

  chartLoading.value = true
  try {
    const timeRangeMinutes = getTimeRangeInMinutes(selectedTimeRange.value)
    const requestData = {
      subscription_id: props.userData.id,
      time_range: selectedTimeRange.value,
      minutes: timeRangeMinutes
    }
    
    // Add line IDs filter if lines are specifically selected
    if (selectedLineIds.value.length > 0 && selectedLineIds.value.length < availableLines.value.length) {
      requestData.line_ids = selectedLineIds.value
    }
    
    const response = await getUserHistoricalSpeeds(requestData)

    chartData.value = response.data?.historical_speeds || []
    availableLines.value = response.data?.available_lines || []
    hasMoreLines.value = response.data?.has_more_lines || false
    totalLinesCount.value = response.data?.total_lines_count || 0
    
    // Initialize selected line IDs when no selection exists or when reset
    if (selectedLineIds.value.length === 0 && availableLines.value.length > 0) {
      selectedLineIds.value = availableLines.value
        .filter(line => line.is_selected)
        .map(line => line.line_id)
      selectAll.value = selectedLineIds.value.length === availableLines.value.length
    }
    
  } catch (error) {
    console.error('Failed to load historical data:', error)
    ElMessage.error(t('dialogs.userSpeedDetail.failedToLoadHistorical'))
    chartData.value = []
    availableLines.value = []
    hasMoreLines.value = false
    totalLinesCount.value = 0
  } finally {
    chartLoading.value = false
  }
}

const totalSpeedOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `Line ${item.line_id}`,
        times: [],
        total_speeds: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.times.push(dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)))
    lineData.total_speeds.push(item.upload_speed + item.download_speed)
  })

  const series = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'line',
    smooth: true,
    data: lineData.total_speeds,
    symbol: 'none',
    lineStyle: { width: 2 }
  }))

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${formatSpeed(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0,
      textStyle: {
        color: 'var(--theme-text-primary)'
      }
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatSpeed(value)
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series
  }
})

const clientIPCountOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `Line ${item.line_id}`,
        times: [],
        ip_counts: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.times.push(dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)))
    lineData.ip_counts.push(item.client_ips_count || 0)
  })

  const series = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'line',
    smooth: true,
    data: lineData.ip_counts,
    symbol: 'none',
    lineStyle: { width: 2 }
  }))

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${param.value} IPs<br/>`
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0,
      textStyle: {
        color: 'var(--theme-text-primary)'
      }
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => `${Math.round(value)}`
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series
  }
})

const connectionCountOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `Line ${item.line_id}`,
        times: [],
        connection_counts: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.times.push(dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)))
    lineData.connection_counts.push(item.connection_count || 0)
  })

  const series = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'line',
    smooth: true,
    data: lineData.connection_counts,
    symbol: 'none',
    lineStyle: { width: 2 }
  }))

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${param.value} connections<br/>`
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0,
      textStyle: {
        color: 'var(--theme-text-primary)'
      }
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => `${Math.round(value)}`
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series
  }
})

const trafficConsumptionOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id and sort by timestamp
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `Line ${item.line_id}`,
        dataPoints: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.dataPoints.push({
      timestamp: item.timestamp,
      time_formatted: dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)),
      traffic_inc: item.traffic_inc || 0
    })
  })

  // Sort each line's data by timestamp
  Array.from(lineDataMap.values()).forEach(lineData => {
    lineData.dataPoints.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
  })

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  // Create series for interval traffic only
  const allSeries = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'bar',
    data: allTimes.map(time => {
      // For 7d range, aggregate all data points for the same day
      // For other ranges, find the specific time point
      const points = lineData.dataPoints.filter(p => p.time_formatted === time)
      return points.reduce((sum, point) => sum + (point.traffic_inc || 0), 0)
    }),
    barMaxWidth: 20,
    itemStyle: {
      color: 'rgba(59, 130, 246, 0.6)' // Blue with transparency
    }
  }))

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${formatTraffic(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: allSeries.map(s => s.name),
      bottom: 0,
      textStyle: {
        color: 'var(--theme-text-primary)'
      }
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatTraffic(value)
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series: allSeries
  }
})

const handleTimeRangeChange = () => {
  loadHistoricalData(false) // Don't reset selection when changing time range
}

const refreshChartData = () => {
  loadHistoricalData(false) // Don't reset selection on manual refresh
  loadRealtimeLineSpeeds()
}

const loadRealtimeLineSpeeds = async () => {
  if (!props.userData?.id) return

  realtimeLoading.value = true
  try {
    const response = await getUserLineSpeeds(props.userData.id)
    realtimeLineSpeeds.value = response.data?.line_speeds || []
  } catch (error) {
    console.error('Failed to load realtime line speeds:', error)
    ElMessage.error(t('dialogs.userSpeedDetail.failedToLoadRealtime'))
    realtimeLineSpeeds.value = []
  } finally {
    realtimeLoading.value = false
  }
}

// Port details methods
const togglePortDetails = () => {
  portDetailsExpanded.value = !portDetailsExpanded.value
  
  // Always load fresh data when expanding
  if (portDetailsExpanded.value) {
    // Reset to first page and load fresh data
    portCurrentPage.value = 1
    loadUserPorts()
  }
}

const loadUserPorts = async () => {
  if (!props.userData?.id) return
  
  portDetailsLoading.value = true
  try {
    const response = await getAdminPortsByUser(props.userData.id, {
      page: portCurrentPage.value,
      page_size: portPageSize.value
    })
    
    userPorts.value = response.data?.ports || []
    portTotalItems.value = response.data?.pagination?.total_items || 0
  } catch (error) {
    console.error('Failed to load user ports:', error)
    ElMessage.error(t('dialogs.userDetail.failedToLoadPorts'))
    userPorts.value = []
  } finally {
    portDetailsLoading.value = false
  }
}

const handlePortPageChange = (page) => {
  portCurrentPage.value = page
  loadUserPorts()
}

const handlePortPageSizeChange = (size) => {
  portPageSize.value = size
  portCurrentPage.value = 1
  loadUserPorts()
}

// Line details methods
const toggleLineDetails = () => {
  lineDetailsExpanded.value = !lineDetailsExpanded.value
  
  // Always load fresh data when expanding
  if (lineDetailsExpanded.value) {
    loadUserLineDetails()
  }
}

const loadUserLineDetails = async () => {
  if (!props.userData?.id) return
  
  lineDetailsLoading.value = true
  try {
    const response = await fetch(`/api/admin_line_details_by_user?subscription_id=${props.userData.id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    userLineDetails.value = data.line_details || []
  } catch (error) {
    console.error('Failed to load user line details:', error)
    ElMessage.error(t('dialogs.userDetail.failedToLoadLines'))
    userLineDetails.value = []
  } finally {
    lineDetailsLoading.value = false
  }
}

// Format methods for port display
const formatTraffic = (bytes) => {
  if (bytes == null) return 'N/A'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

const getTargetModeLabel = (mode) => {
  switch (mode) {
    case 0:
      return t('forms.options.bestLatency')
    case 1:
      return t('forms.options.fallback')
    case 2:
      return t('forms.options.domainFollow')
    case 3:
      return t('forms.options.balanceRoundRobin')
    case 4:
      return t('forms.options.balanceRandom')
    default:
      return t('forms.options.unknown')
  }
}

const getTestMethodLabel = (method) => {
  switch (method) {
    case 0:
      return t('forms.options.tcpPing')
    case 1:
      return t('forms.options.icmp')
    default:
      return t('forms.options.unknown')
  }
}

const getLineName = (port) => {
  // Use line_name from port data if available, otherwise fallback to lineId format
  if (port.line_name) {
    return port.line_name
  }
  return port.outbound_endpoint_id ? `Line ${port.outbound_endpoint_id}` : 'Unknown Line'
}

watch(() => props.visible, (newVal) => {
  if (newVal && props.userData) {
    // Reset selection state when dialog opens
    loadHistoricalData(true)
    loadRealtimeLineSpeeds()
    // Reset port details state when dialog opens
    portDetailsExpanded.value = false
    userPorts.value = []
    portCurrentPage.value = 1
    // Reset line details state when dialog opens
    lineDetailsExpanded.value = false
    userLineDetails.value = []
  }
})

watch(() => props.userData, (newVal, oldVal) => {
  if (newVal && props.visible) {
    // Reset selection state when userData changes to a different user
    const shouldResetSelection = !oldVal || oldVal.id !== newVal.id
    loadHistoricalData(shouldResetSelection)
    loadRealtimeLineSpeeds()
  }
  
  // Reset port details when user changes
  if (newVal && (!oldVal || oldVal.id !== newVal.id)) {
    portDetailsExpanded.value = false
    userPorts.value = []
    portCurrentPage.value = 1
    // Reset line details when user changes
    lineDetailsExpanded.value = false
    userLineDetails.value = []
  }
})
</script>

<style scoped>
.user-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-info-card,
.chart-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-header h3 {
  margin: 0;
  color: var(--theme-text-primary);
}

.chart-main-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.chart-left-panel {
  flex: 1;
  min-width: 0;
}

.chart-right-panel {
  width: 300px;
  flex-shrink: 0;
}

.chart-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.time-range-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.line-selection-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.line-option {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.line-speed {
  color: var(--theme-text-secondary);
  font-size: 12px;
}

.realtime-speeds {
  height: 100%;
}

.realtime-speeds h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  border-bottom: 2px solid var(--theme-border-light);
  padding-bottom: 8px;
}

.speed-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.speed-item {
  padding: 8px 12px;
  background: var(--theme-fill-light);
  border-radius: 6px;
  border: 1px solid var(--theme-border-lighter);
}

.line-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.speed-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
}

.total-speed {
  color: var(--theme-primary);
  font-weight: 600;
}

.connection-info {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: var(--theme-text-secondary);
}

.ip-count, .connection-count {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.chart-container {
  min-height: 400px;
}

.chart-section {
  margin-bottom: 40px;
}

.chart-section:last-child {
  margin-bottom: 0;
}

.chart-section h4 {
  margin: 0 0 16px 0;
  color: var(--theme-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.chart {
  height: 300px;
  width: 100%;
}

.no-chart-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.token-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background-color: var(--theme-fill-light);
  padding: 2px 6px;
  border-radius: 4px;
  color: var(--theme-primary);
}

.traffic-info {
  width: 100%;
}

.traffic-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  color: var(--theme-text-regular);
  font-size: 14px;
}

.traffic-percentage {
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 1200px) {
  .chart-main-layout {
    flex-direction: column;
  }
  
  .chart-right-panel {
    width: 100%;
  }
  
  .speed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chart-controls {
    align-items: flex-start;
    width: 100%;
  }
  
  .time-range-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .line-selection-controls {
    width: 100%;
  }
  
  .line-selection-controls .el-select {
    width: 100% !important;
  }
  
  .speed-grid {
    grid-template-columns: 1fr;
  }
  
  :deep(.el-radio-group) {
    flex-wrap: wrap;
  }
  
  .chart {
    height: 250px;
  }
}

/* Dark mode compatibility */
:global(.dark) .user-info-card,
:global(.dark) .chart-card {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-border-dark);
}

:global(.dark) .token-id {
  background-color: var(--theme-fill-darker);
  color: var(--theme-primary-light);
}

/* Port Details Styles */
.port-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.port-header h3 {
  margin: 0;
  color: var(--theme-text-primary);
}

.port-details-container {
  width: 100%;
}

.port-table-wrapper {
  min-height: 200px;
}

.no-ports-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.port-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.suspended-port {
  background-color: var(--theme-fill-light);
  opacity: 0.7;
}

.status-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--theme-success);
}

.status-suspended {
  background-color: var(--theme-danger);
}

.status-active {
  background-color: var(--theme-success);
}

.truncated-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.target-address {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.more-addresses {
  font-size: 12px;
  color: var(--theme-text-secondary);
  font-style: italic;
}

.traffic-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.traffic-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.traffic-icon {
  font-weight: bold;
  color: var(--theme-text-secondary);
}

.traffic-value {
  color: var(--theme-text-primary);
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 0 16px;
}

.pagination-info {
  font-size: 14px;
  color: var(--theme-text-secondary);
}

.pagination-controls {
  flex-shrink: 0;
}

/* Line Details Styles */
.line-details-container {
  width: 100%;
}

.line-table-wrapper {
  min-height: 200px;
}

.no-lines-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.line-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* Responsive design for port details */
@media (max-width: 768px) {
  .port-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .port-table {
    font-size: 12px;
  }
  
  .line-table {
    font-size: 12px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
}
</style>