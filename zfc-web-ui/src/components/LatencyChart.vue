<template>
  <div class="latency-charts-container">
    <div v-loading="loading" class="charts-wrapper">
      <div v-if="!configs || configs.length === 0" class="no-chart-data">
        <el-empty description="暂无延迟监控配置" />
      </div>
      <div v-else class="charts-grid">
        <div 
          v-for="config in configs" 
          :key="config.id" 
          class="chart-item"
        >
          <div class="chart-header">
            <h4 class="chart-title">
              {{ config.display_name }} 
              <span class="target-address">({{ formatTargetAddress(config, false) }})</span>
            </h4>
            <div class="time-range-tabs">
              <el-button-group>
                <el-button
                  v-for="range in timeRanges"
                  :key="range"
                  :type="selectedRange[config.id] === range ? 'primary' : ''"
                  size="small"
                  @click="selectTimeRange(config.id, range)"
                >
                  {{ range }}
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="chart-content">
            <div v-if="chartImages[config.id]?.loading" class="chart-loading">
              <el-loading text="生成图表中..." />
            </div>
            <div v-else-if="chartImages[config.id]?.error" class="chart-error">
              <el-alert
                :title="chartImages[config.id].error"
                type="error"
                show-icon
                :closable="false"
              />
              <el-button 
                type="primary" 
                size="small" 
                @click="refreshChart(config.id)"
                style="margin-top: 10px;"
              >
                重新加载
              </el-button>
            </div>
            <img
              v-else-if="chartImages[config.id]?.data"
              :src="chartImages[config.id].data"
              :alt="`${config.display_name} 延迟图表`"
              class="chart-image"
              @error="handleImageError(config.id)"
              @load="handleImageLoad(config.id)"
            />
            <div v-else class="chart-placeholder">
              <el-empty description="图表加载中..." />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { useAuthStore } from '../stores/auth'
import { formatTargetAddress } from '../utils/privacy.js'
import api from '../api'

const props = defineProps({
  configs: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  timeRange: {
    type: String,
    default: '1h'
  }
})

const authStore = useAuthStore()

// 时间范围选项
const timeRanges = ref(['1h', '3h', '6h', '12h', '24h', '7d'])

// 每个配置的选中时间范围
const selectedRange = ref({})

// 图表图片数据
const chartImages = ref({})

// 获取图表图片
const fetchChart = async (serverId, configId, timeRange) => {
  try {
    chartImages.value[configId] = { 
      ...chartImages.value[configId], 
      loading: true, 
      error: null 
    }

    const response = await api.get(`/latency_chart/${serverId}/${configId}/${timeRange}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    if (response.data) {
      // 检查后端是否明确表示无数据
      if (response.data.success === false) {
        chartImages.value[configId] = {
          data: null,
          loading: false,
          error: '暂无数据'
        }
        return
      }
      
      // 检查是否有有效的图表数据
      if (response.data.image_data) {
        chartImages.value[configId] = {
          data: `data:image/png;base64,${response.data.image_data}`,
          loading: false,
          error: null,
          generated_at: response.data.generated_at,
          expires_at: response.data.expires_at
        }
      } else {
        throw new Error('图表数据格式错误')
      }
    } else {
      throw new Error('图表数据格式错误')
    }
  } catch (error) {
    console.error('Failed to fetch chart:', error)
    chartImages.value[configId] = {
      data: null,
      loading: false,
      error: error.response?.data?.message || error.message || '加载图表失败'
    }
  }
}

// 初始化选中的时间范围
const initSelectedRanges = () => {
  const newSelectedRange = {}
  props.configs.forEach(config => {
    newSelectedRange[config.id] = props.timeRange || '1h'
  })
  selectedRange.value = newSelectedRange
}

// 选择时间范围
const selectTimeRange = (configId, timeRange) => {
  selectedRange.value[configId] = timeRange
  const config = props.configs.find(c => c.id === configId)
  if (config) {
    fetchChart(config.server_id, config.id, timeRange)
  }
}

// 刷新图表
const refreshChart = (configId) => {
  const config = props.configs.find(c => c.id === configId)
  if (config) {
    const timeRange = selectedRange.value[config.id] || '1h'
    fetchChart(config.server_id, config.id, timeRange)
  }
}

// 图片加载错误处理
const handleImageError = (configId) => {
  chartImages.value[configId] = {
    ...chartImages.value[configId],
    error: '图片加载失败'
  }
}

// 图片加载成功处理
const handleImageLoad = (configId) => {
  console.log(`Chart image loaded for config ${configId}`)
}


// 加载所有图表
const loadAllCharts = () => {
  props.configs.forEach(config => {
    const timeRange = selectedRange.value[config.id] || '1h'
    fetchChart(config.server_id, config.id, timeRange)
  })
}

// 监听配置变化
watch(() => props.configs, (newConfigs) => {
  if (newConfigs && newConfigs.length > 0) {
    initSelectedRanges()
    nextTick(() => {
      loadAllCharts()
    })
  }
}, { immediate: true })

// 监听时间范围变化
watch(() => props.timeRange, (newTimeRange) => {
  if (newTimeRange) {
    // 更新所有配置的选中时间范围
    Object.keys(selectedRange.value).forEach(configId => {
      selectedRange.value[configId] = newTimeRange
    })
    // 重新加载图表
    loadAllCharts()
  }
})

onMounted(() => {
  if (props.configs && props.configs.length > 0) {
    initSelectedRanges()
    nextTick(() => {
      loadAllCharts()
    })
  }
})
</script>

<style scoped>
.latency-charts-container {
  width: 100%;
  padding: 16px;
}

.charts-wrapper {
  min-height: 400px;
}

.no-chart-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(800px, 1fr));
  gap: 24px;
}

.chart-item {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.target-address {
  font-size: 14px;
  font-weight: 400;
  color: var(--el-text-color-regular);
  margin-left: 8px;
}

.time-range-tabs {
  flex-shrink: 0;
}

.chart-content {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.chart-loading {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-error {
  width: 100%;
  text-align: center;
  padding: 20px;
}

.chart-placeholder {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .latency-charts-container {
    padding: 8px;
  }
  
  .chart-item {
    padding: 12px;
  }
  
  .charts-grid {
    gap: 16px;
  }
  
  .time-range-tabs .el-button-group .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>