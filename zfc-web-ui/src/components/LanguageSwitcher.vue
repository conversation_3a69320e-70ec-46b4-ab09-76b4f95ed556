<template>
  <el-dropdown 
    class="language-switcher"
    trigger="click"
    @command="handleLanguageChange"
  >
    <el-button 
      :type="variant === 'button' ? 'default' : 'text'"
      :size="size"
      class="language-button"
    >
      <el-icon class="language-icon">
        <component :is="currentLanguageIcon" />
      </el-icon>
      <span v-if="showText" class="language-text">{{ currentLanguageName }}</span>
      <el-icon class="arrow-icon">
        <ArrowDown />
      </el-icon>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="lang in availableLanguages" 
          :key="lang.code"
          :command="lang.code"
          :class="{ 'is-active': lang.code === currentLanguage }"
        >
          <div class="language-item">
            <el-icon class="item-icon">
              <component :is="getLanguageIcon(lang.code)" />
            </el-icon>
            <span class="item-text">{{ lang.name }}</span>
            <el-icon v-if="lang.code === currentLanguage" class="check-icon">
              <Check />
            </el-icon>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrowDown, Check } from '@element-plus/icons-vue'
import { setLanguage, getCurrentLanguage, getAvailableLanguages } from '../locales'

// Define custom icons for languages
const LanguageEnIcon = {
  name: 'LanguageEn',
  render() {
    return h('span', { class: 'language-flag' }, 'EN')
  }
}

const LanguageZhIcon = {
  name: 'LanguageZh', 
  render() {
    return h('span', { class: 'language-flag' }, '中')
  }
}

const props = defineProps({
  variant: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'text'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  showText: {
    type: Boolean,
    default: true
  }
})

// const { t } = useI18n() // 暂时不需要，如果后续需要本地化可以启用

const currentLanguage = computed(() => getCurrentLanguage())
const availableLanguages = computed(() => getAvailableLanguages())

const currentLanguageName = computed(() => {
  const lang = availableLanguages.value.find(l => l.code === currentLanguage.value)
  return lang ? lang.name : 'Language'
})

const getLanguageIcon = (langCode) => {
  return langCode === 'zh-CN' ? LanguageZhIcon : LanguageEnIcon
}

const currentLanguageIcon = computed(() => {
  return getLanguageIcon(currentLanguage.value)
})

const handleLanguageChange = (langCode) => {
  if (langCode !== currentLanguage.value) {
    setLanguage(langCode)
  }
}
</script>

<style scoped>
.language-switcher {
  display: inline-block;
}

.language-button {
  display: flex;
  align-items: center;
  gap: 6px;
  border: 1px solid var(--el-border-color);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: all 0.3s ease;
}

.language-button:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.language-icon {
  font-size: 16px;
}

.language-text {
  font-size: 14px;
  font-weight: 500;
}

.arrow-icon {
  font-size: 12px;
  margin-left: 2px;
}

.language-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-width: 120px;
}

.item-icon {
  font-size: 16px;
  color: var(--theme-text-regular);
}

.item-text {
  flex: 1;
  font-size: 14px;
}

.check-icon {
  font-size: 14px;
  color: var(--theme-primary);
}

.is-active .item-icon,
.is-active .item-text {
  color: var(--theme-primary);
}

:deep(.language-flag) {
  display: inline-block;
  width: 20px;
  height: 14px;
  line-height: 14px;
  text-align: center;
  font-size: 10px;
  font-weight: bold;
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  border-radius: 2px;
  border: 1px solid var(--el-border-color-light);
}

/* Dark theme adjustments */
.dark .language-button {
  background-color: var(--theme-bg-secondary);
  border-color: var(--el-border-color);
}

.dark .language-button:hover {
  border-color: var(--theme-primary);
  background-color: var(--theme-bg-primary);
}
</style>