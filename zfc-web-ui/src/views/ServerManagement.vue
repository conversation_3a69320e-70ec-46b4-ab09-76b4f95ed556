<template>
  <div class="server-management">
    <div class="header">
      <h2>{{ t('pages.servers.title') }}</h2>
      <div class="header-actions">
        <el-button
          @click="toggleSearchExpanded"
          :icon="searchExpanded ? ArrowUp : ArrowDown"
          type="default"
          class="search-toggle-btn"
          :aria-expanded="searchExpanded"
          :aria-controls="'search-form-container'"
          :title="searchExpanded ? t('actions.hideSearchFilters') : t('actions.showSearchFilters')"
        >
          <span class="search-toggle-text">
            {{ searchExpanded ? t('actions.hideSearch') : t('actions.showSearch') }}
          </span>
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          {{ t('actions.addServer') }}
        </el-button>
      </div>
    </div>

    <!-- Collapsible Search Form -->
    <el-collapse-transition>
      <div
        v-show="searchExpanded"
        class="search-container"
        id="search-form-container"
        role="region"
:aria-label="t('search.filterOptionsLabel')"
      >
        <el-form :model="searchForm" class="search-form" :inline="true">
          <el-form-item :label="t('forms.labels.id')">
            <el-input
              v-model="searchForm.id"
              :placeholder="t('forms.placeholders.searchById')"
              clearable
              style="width: 120px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('forms.labels.name')">
            <el-input
              v-model="searchForm.name"
              :placeholder="t('forms.placeholders.searchByName')"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('forms.labels.ipAddress')">
            <el-input
              v-model="searchForm.ipAddr"
              :placeholder="t('forms.placeholders.searchByAddress')"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('pages.servers.version')">
            <el-select
              v-model="searchForm.version"
              :placeholder="t('forms.placeholders.selectVersions')"
              clearable
              filterable
              multiple
              style="width: 180px"
              @change="handleSearchChange"
            >
              <el-option
                v-for="version in availableVersions"
                :key="version"
                :label="version"
                :value="version"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('forms.labels.status')">
            <el-select
              v-model="searchForm.status"
              :placeholder="t('forms.placeholders.selectStatus')"
              clearable
              multiple
              style="width: 150px"
              @change="handleSearchChange"
            >
              <el-option
                v-for="status in availableStatuses"
                :key="status"
                :label="status"
                :value="status"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('forms.labels.tags')">
            <el-select
              v-model="searchForm.tags"
              :placeholder="t('forms.placeholders.selectTags')"
              clearable
              multiple
              filterable
              allow-create
              default-first-option
              style="width: 200px"
              @change="handleSearchChange"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              {{ t('actions.search') }}
            </el-button>
            <el-button @click="handleClearSearch">
              {{ t('actions.clear') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <el-card class="table-card">
      <div class="table-wrapper">
        <el-table
          :data="validatedServers"
          v-loading="loading"
          style="width: 100%"
          class="server-table"
          :row-style="{ height: '60px' }"
          virtual-scroll
        >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          :label="t('pages.servers.name')"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.display_name || `${t('pages.servers.serverPrefix')} ${scope.row.id}` }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ip_addr"
          :label="t('pages.servers.ipAddress')"
          min-width="140"
        />
        <el-table-column
          prop="version"
          :label="t('pages.servers.version')"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              size="small"
              :type="scope.row.version ? 'info' : 'danger'"
              effect="plain"
            >
              {{ scope.row.version || t('pages.servers.unknown') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_online"
          :label="t('pages.servers.status')"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="scope.row.is_online ? 'success' : 'danger'">
              {{ scope.row.is_online ? t('pages.servers.online') : t('pages.servers.offline') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('pages.servers.ports')"
          min-width="180"
        >
          <template #default="scope">
            <div class="ports-info">
              <el-tag
                type="info"
                class="ports-count"
                effect="plain"
              >
                {{ (scope.row.used_ports?.length || 0) + ' / ' + getPortsTotal(scope.row) + ' ' + t('pages.servers.ports') }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          :label="t('pages.servers.actions')" 
          width="200"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-tooltip 
                :content="t('actions.copyInstallationCommand')" 
                placement="top"
              >
                <el-button
                  size="small"
                  type="primary"
                  :icon="Document"
                  @click="handleCopyCommand(scope.row)"
                  circle
                />
              </el-tooltip>
              <el-tooltip
                :content="t('pages.servers.latencyMonitoring')"
                placement="top"
              >
                <el-button
                  size="small"
                  type="info"
                  :icon="Connection"
                  @click="handleLatencyMonitoring(scope.row)"
                  circle
                />
              </el-tooltip>
              <el-tooltip
                :content="t('actions.edit')"
                placement="top"
              >
                <el-button
                  size="small"
                  type="warning"
                  :icon="Edit"
                  @click="handleEdit(scope.row)"
                  circle
                />
              </el-tooltip>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="handleDelete(scope.row)"
                circle
              />
            </el-space>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- Pagination Controls -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>{{ t('messages.info.totalItems', { count: totalItems }) }}</span>
          <el-select
            v-model="pageSize"
            @change="handlePageSizeChange"
            class="page-size-selector"
            size="small"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size} / page`"
              :value="size"
            />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          :page-count="totalPages"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-controls"
          small
        />
      </div>
    </el-card>

    <!-- Add Server Dialog -->
    <el-dialog
      v-model="showAddDialog"
      :title="t('pages.servers.addServerDialog')"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
      class="server-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        size="large"
      >
        <el-form-item 
          :label="t('forms.labels.displayName')" 
          prop="display_name"
          :error="formErrors.display_name"
        >
          <el-input 
            v-model="form.display_name" 
            :placeholder="t('forms.placeholders.enterServerName')"
          />
        </el-form-item>

        <el-form-item 
          :label="t('forms.labels.ipAddress')" 
          prop="ip_addr"
          :error="formErrors.ip_addr"
        >
          <el-input 
            v-model="form.ip_addr" 
            :placeholder="t('forms.placeholders.enterIpAddress')"
          />
        </el-form-item>

        <el-form-item 
          prop="interface_name"
          :error="formErrors.interface_name"
        >
          <template #label>
            <span class="form-label-with-help">
              {{ t('forms.labels.interfaceName') }}
              <el-tooltip
                :content="t('forms.help.interfaceName')"
                placement="top"
                effect="light"
                raw-content
              >
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input 
            v-model="form.interface_name"
            :placeholder="t('forms.placeholders.enterInterfaceName')"
          />
        </el-form-item>

        <el-form-item :label="t('forms.labels.portRange')">
          <el-row :gutter="20">
            <el-col :span="11">
              <el-form-item prop="port_start">
                <el-input-number v-model="form.port_start" :min="1" :max="65535" :placeholder="t('forms.placeholders.enterStartPort')" />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">{{ t('common.to') }}</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="port_end">
                <el-input-number v-model="form.port_end" :min="1" :max="65535" :placeholder="t('forms.placeholders.enterEndPort')" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="advanced-options">
          <div class="advanced-options-header" @click="showAdvanced = !showAdvanced">
            <span>{{ t('forms.advancedOptions') }}</span>
            <el-icon class="advanced-icon" :class="{ 'is-active': showAdvanced }">
              <arrow-down />
            </el-icon>
          </div>
          <div v-show="showAdvanced" class="advanced-options-content">
                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.trafficScale') }}
                      <el-tooltip
                        :content="t('forms.help.trafficScale')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-input-number 
                    v-model="form.traffic_scale" 
                    :min="0.1"
                    :max="10"
                    :precision="2"
                    :step="0.1"
                    placeholder="1.00"
                    controls-position="right"
                    style="width: 160px"
                  />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowForward') }}
                      <el-tooltip
                        :content="t('forms.help.allowForward')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="form.allow_forward" />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowIpv6') }}
                      <el-tooltip
                        :content="t('forms.help.allowIpv6')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="form.allow_ipv6" />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowLatencyTest') }}
                      <el-tooltip
                        :content="t('forms.help.allowLatencyTest')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="form.allow_latency_test" />
                </el-form-item>

                <el-form-item :label="t('forms.labels.maxIps')">
                  <el-input-number 
                    v-model="form.allow_ip_num" 
                    :min="0"
                    :placeholder="t('forms.placeholders.leaveEmptyUnlimited')"
                    controls-position="right"
                    style="width: 160px"
                  />
                  <div class="field-tip">{{ t('messages.info.maxUniqueIps') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.maxConnectionsPerIp')">
                  <el-input-number 
                    v-model="form.allow_conn_num" 
                    :min="0"
                    :placeholder="t('forms.placeholders.leaveEmptyUnlimited')"
                    controls-position="right"
                    style="width: 160px"
                  />
                  <div class="field-tip">{{ t('messages.info.maxConnectionsDescription') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.protocolFilters')">
                  <el-checkbox-group v-model="form.protocol_filters">
                    <el-checkbox value="Http" :label="t('forms.options.http')" />
                    <el-checkbox value="Socks5" :label="t('forms.options.socks5')" />
                    <el-checkbox value="BitTorrent" :label="t('forms.options.bitTorrent')" />
                    <el-checkbox value="Tls" :label="t('forms.options.tls')" />
                  </el-checkbox-group>
                  <div class="field-tip">{{ t('messages.info.protocolFilterDescription') }}</div>
                </el-form-item>

                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.forwardEndpoints') }}
                      <el-tooltip
                        :content="t('forms.help.forwardEndpoints')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="selectedForwardEndpointIds"
                    multiple
                    filterable
                    :placeholder="t('forms.placeholders.selectForwardEndpoints')"
                    style="width: 100%"
                    @change="handleForwardEndpointsChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    >
                      <span>{{ endpoint.display_name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        {{ endpoint.ingress_address || endpoint.ip_addr || 'N/A' }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedForwardEndpoints.length > 0" :label="t('forms.labels.priority')">
                  <div class="forward-endpoints-list">
                    <draggable
                      v-model="selectedForwardEndpoints"
                      @end="handleForwardEndpointSort"
                      item-key="id"
                      :animation="150"
                      class="draggable-list"
                    >
                      <template #item="{ element }">
                        <div class="forward-endpoint-item">
                          <el-tag 
                            class="endpoint-tag" 
                            size="large"
                            closable
                            effect="plain"
                            @close="handleRemoveForwardEndpoint(element)"
                          >
                            <el-icon class="drag-handle"><Operation /></el-icon>
                            {{ element.id }}: {{ element.display_name }}
                          </el-tag>
                        </div>
                      </template>
                    </draggable>
                  </div>
                </el-form-item>

                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint && selectedForwardEndpointIds.length > 1"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.balanceStrategy') }}
                      <el-tooltip
                        :content="t('forms.help.balanceStrategy')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select v-model="form.balance_strategy" :placeholder="t('forms.placeholders.selectBalanceStrategy')">
                    <el-option :label="t('forms.options.bestLatency')" :value="0" />
                    <el-option :label="t('forms.options.fallback')" :value="1" />
                    <el-option :label="t('forms.options.domainFollow')" :value="2" />
                    <el-option :label="t('forms.options.roundRobin')" :value="3" />
                    <el-option :label="t('forms.options.random')" :value="4" />
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint && selectedForwardEndpointIds.length > 0"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.useForwardAsTransport') }}
                      <el-tooltip
                        :content="t('forms.help.useForwardAsTransport')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="form.use_forward_as_tun" />
                </el-form-item>


                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServers') }}
                      <el-tooltip
                        :content="t('forms.help.totServers')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="selectedTotServerIds"
                    multiple
                    filterable
                    :placeholder="t('forms.placeholders.selectTotServers')"
                    style="width: 100%"
                    @change="handleTotServerChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    >
                      <span>{{ endpoint.display_name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        {{ endpoint.ingress_address || endpoint.ip_addr || 'N/A' }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedTotServers.length > 0" :label="t('forms.labels.priority')">
                  <div class="forward-endpoints-list">
                    <draggable
                      v-model="selectedTotServers"
                      @end="handleTotServerSort"
                      item-key="id"
                      :animation="150"
                      class="draggable-list"
                    >
                      <template #item="{ element }">
                        <div class="forward-endpoint-item">
                          <el-tag 
                            class="endpoint-tag" 
                            size="large"
                            closable
                            effect="plain"
                            @close="handleRemoveTotServer(element)"
                          >
                            <el-icon class="drag-handle"><Operation /></el-icon>
                            {{ element.id }}: {{ element.display_name }}
                          </el-tag>
                        </div>
                      </template>
                    </draggable>
                  </div>
                </el-form-item>

                <el-form-item v-if="selectedTotServers.length > 1">
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServerSelectMode') }}
                      <el-tooltip
                        :content="t('forms.help.totServerSelectMode')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="form.tot_server_select_mode"
                    :placeholder="t('forms.placeholders.selectBalanceStrategy')"
                    style="width: 100%"
                  >
                    <el-option :label="t('forms.options.bestLatency')" :value="0" />
                    <el-option :label="t('forms.options.fallback')" :value="1" />
                    <el-option :label="t('forms.options.domainFollow')" :value="2" />
                    <el-option :label="t('forms.options.roundRobin')" :value="3" />
                    <el-option :label="t('forms.options.random')" :value="4" />
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedTotServers.length > 0">
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServerTestMethod') }}
                      <el-tooltip
                        :content="t('forms.help.totServerTestMethod')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select v-model="form.tot_server_test_method" :placeholder="t('forms.placeholders.selectProtocol')" style="width: 100%">
                    <el-option :label="t('forms.options.tcpPing')" :value="0" />
                    <el-option :label="t('forms.options.icmp')" :value="1" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="t('forms.labels.tags')">
                  <el-select
                    v-model="form.tags"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    :placeholder="t('forms.placeholders.enterTags')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tag in availableTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                    />
                  </el-select>
                  <div class="field-tip">{{ t('messages.info.tagsDescription') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.customConfig')">
                  <el-input
                    v-model="form.custom_config"
                    type="textarea"
                    :rows="4"
                    :placeholder="t('forms.placeholders.enterCustomConfig')"
                    style="width: 100%"
                  />
                  <div class="field-tip">{{ t('messages.info.customConfigDescription') }}</div>
                </el-form-item>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="showAddDialog = false"
            class="cancel-button"
          >
            {{ t('actions.cancel') }}
          </el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="submitting"
            class="submit-button"
          >
            {{ t('actions.add') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Edit Server Dialog -->
    <el-dialog
      v-model="showEditDialog"
      :title="t('pages.servers.editServerDialog')"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
      class="server-dialog"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="rules"
        label-position="top"
        size="large"
      >
        <el-form-item 
          :label="t('forms.labels.displayName')" 
          prop="display_name"
          :error="formErrors.display_name"
        >
          <el-input 
            v-model="editForm.display_name" 
            :placeholder="t('forms.placeholders.enterDisplayName')"
          />
        </el-form-item>

        <el-form-item 
          :label="t('forms.labels.ipAddress')" 
          prop="ip_addr"
          :error="formErrors.ip_addr"
        >
          <el-input 
            v-model="editForm.ip_addr" 
            :placeholder="t('forms.placeholders.enterIpAddress')"
          />
        </el-form-item>

        <el-form-item 
          prop="interface_name"
          :error="formErrors.interface_name"
        >
          <template #label>
            <span class="form-label-with-help">
              {{ t('forms.labels.interfaceName') }}
              <el-tooltip
                :content="t('forms.help.interfaceName')"
                placement="top"
                effect="light"
                raw-content
              >
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input 
            v-model="editForm.interface_name"
            :placeholder="t('forms.placeholders.enterInterfaceName')"
          />
        </el-form-item>

        <el-form-item :label="t('forms.labels.portRange')">
          <el-row :gutter="20">
            <el-col :span="11">
              <el-form-item prop="port_start">
                <el-input-number v-model="editForm.port_start" :min="1" :max="65535" :placeholder="t('forms.placeholders.enterStartPort')" />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">{{ t('common.to') }}</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="port_end">
                <el-input-number v-model="editForm.port_end" :min="1" :max="65535" :placeholder="t('forms.placeholders.enterEndPort')" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="advanced-options">
          <div class="advanced-options-header" @click="showEditAdvanced = !showEditAdvanced">
            <span>{{ t('forms.labels.advancedOptions') }}</span>
            <el-icon class="advanced-icon" :class="{ 'is-active': showEditAdvanced }">
              <arrow-down />
            </el-icon>
          </div>
          <div v-show="showEditAdvanced" class="advanced-options-content">
                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.trafficScale') }}
                      <el-tooltip
                        :content="t('forms.help.trafficScale')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-input-number 
                    v-model="editForm.traffic_scale" 
                    :min="0.1"
                    :max="10"
                    :precision="2"
                    :step="0.1"
                    placeholder="1.00"
                    controls-position="right"
                    style="width: 160px"
                  />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowForward') }}
                      <el-tooltip
                        :content="t('forms.help.allowForward')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="editForm.allow_forward" />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowIpv6') }}
                      <el-tooltip
                        :content="t('forms.help.allowIpv6')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="editForm.allow_ipv6" />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.allowLatencyTest') }}
                      <el-tooltip
                        :content="t('forms.help.allowLatencyTest')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="editForm.allow_latency_test" />
                </el-form-item>

                <el-form-item :label="t('forms.labels.maxIps')">
                  <el-input-number 
                    v-model="editForm.allow_ip_num" 
                    :min="0"
                    :placeholder="t('forms.placeholders.leaveEmptyUnlimited')"
                    controls-position="right"
                    style="width: 160px"
                  />
                  <div class="field-tip">{{ t('messages.info.maxUniqueIps') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.maxConnectionsPerIp')">
                  <el-input-number 
                    v-model="editForm.allow_conn_num" 
                    :min="0"
                    :placeholder="t('forms.placeholders.leaveEmptyUnlimited')"
                    controls-position="right"
                    style="width: 160px"
                  />
                  <div class="field-tip">{{ t('messages.info.maxConnectionsDescription') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.protocolFilters')">
                  <el-checkbox-group v-model="editForm.protocol_filters">
                    <el-checkbox value="Http" :label="t('forms.options.http')" />
                    <el-checkbox value="Socks5" :label="t('forms.options.socks5')" />
                    <el-checkbox value="BitTorrent" :label="t('forms.options.bitTorrent')" />
                    <el-checkbox value="Tls" :label="t('forms.options.tls')" />
                  </el-checkbox-group>
                  <div class="field-tip">{{ t('messages.info.protocolFilterDescription') }}</div>
                </el-form-item>

                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.forwardEndpoints') }}
                      <el-tooltip
                        :content="t('forms.help.forwardEndpoints')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="selectedEditForwardEndpointIds"
                    multiple
                    filterable
                    :placeholder="t('forms.placeholders.selectForwardEndpoints')"
                    style="width: 100%"
                    @change="handleEditForwardEndpointsChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    >
                      <span>{{ endpoint.display_name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        {{ endpoint.ingress_address || endpoint.ip_addr || 'N/A' }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedEditForwardEndpoints.length > 0" :label="t('forms.labels.priorityOrder')">
                  <div class="forward-endpoints-list">
                    <draggable
                      v-model="selectedEditForwardEndpoints"
                      @end="handleEditForwardEndpointSort"
                      item-key="id"
                      :animation="150"
                      class="draggable-list"
                    >
                      <template #item="{ element }">
                        <div class="forward-endpoint-item">
                          <el-tag 
                            class="endpoint-tag" 
                            size="large"
                            closable
                            effect="plain"
                            @close="handleEditRemoveForwardEndpoint(element)"
                          >
                            <el-icon class="drag-handle"><Operation /></el-icon>
                            {{ element.id }}: {{ element.display_name }}
                          </el-tag>
                        </div>
                      </template>
                    </draggable>
                  </div>
                </el-form-item>

                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint && selectedEditForwardEndpointIds.length > 1"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.balanceStrategy') }}
                      <el-tooltip
                        :content="t('forms.help.balanceStrategy')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select v-model="editForm.balance_strategy" :placeholder="t('forms.placeholders.selectBalanceStrategy')">
                    <el-option :label="t('forms.options.bestLatency')" :value="0" />
                    <el-option :label="t('forms.options.fallback')" :value="1" />
                    <el-option :label="t('forms.options.domainFollow')" :value="2" />
                    <el-option :label="t('forms.options.roundRobin')" :value="3" />
                    <el-option :label="t('forms.options.random')" :value="4" />
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint && selectedEditForwardEndpointIds.length > 0"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.useForwardAsTransport') }}
                      <el-tooltip
                        :content="t('forms.help.useForwardAsTransport')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-switch v-model="editForm.use_forward_as_tun" />
                </el-form-item>


                <el-form-item
                  v-if="subscriptionStore.allowForwardEndpoint"
                >
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServers') }}
                      <el-tooltip
                        :content="t('forms.help.totServers')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="selectedEditTotServerIds"
                    multiple
                    filterable
                    :placeholder="t('forms.placeholders.selectTotServers')"
                    style="width: 100%"
                    @change="handleEditTotServerChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    >
                      <span>{{ endpoint.display_name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        {{ endpoint.ingress_address || endpoint.ip_addr || 'N/A' }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedEditTotServers.length > 0" :label="t('forms.labels.priorityOrder')">
                  <div class="forward-endpoints-list">
                    <draggable
                      v-model="selectedEditTotServers"
                      @end="handleEditTotServerSort"
                      item-key="id"
                      :animation="150"
                      class="draggable-list"
                    >
                      <template #item="{ element }">
                        <div class="forward-endpoint-item">
                          <el-tag 
                            class="endpoint-tag" 
                            size="large"
                            closable
                            effect="plain"
                            @close="handleEditRemoveTotServer(element)"
                          >
                            <el-icon class="drag-handle"><Operation /></el-icon>
                            {{ element.id }}: {{ element.display_name }}
                          </el-tag>
                        </div>
                      </template>
                    </draggable>
                  </div>
                </el-form-item>

                <el-form-item v-if="selectedEditTotServers.length > 1">
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServerSelectMode') }}
                      <el-tooltip
                        :content="t('forms.help.totServerSelectMode')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select
                    v-model="editForm.tot_server_select_mode"
                    :placeholder="t('forms.placeholders.selectBalanceStrategy')"
                    style="width: 100%"
                  >
                    <el-option :label="t('forms.options.bestLatency')" :value="0" />
                    <el-option :label="t('forms.options.fallback')" :value="1" />
                    <el-option :label="t('forms.options.domainFollow')" :value="2" />
                    <el-option :label="t('forms.options.roundRobin')" :value="3" />
                    <el-option :label="t('forms.options.random')" :value="4" />
                  </el-select>
                </el-form-item>

                <el-form-item v-if="selectedEditTotServers.length > 0">
                  <template #label>
                    <span class="form-label-with-help">
                      {{ t('forms.labels.totServerTestMethod') }}
                      <el-tooltip
                        :content="t('forms.help.totServerTestMethod')"
                        placement="top"
                        effect="light"
                        raw-content
                      >
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-select v-model="editForm.tot_server_test_method" :placeholder="t('forms.placeholders.selectTestMethod')" style="width: 100%">
                    <el-option :label="t('forms.options.tcpPing')" :value="0" />
                    <el-option :label="t('forms.options.icmp')" :value="1" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="t('forms.labels.tags')">
                  <el-select
                    v-model="editForm.tags"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    :placeholder="t('forms.placeholders.enterTags')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tag in availableTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                    />
                  </el-select>
                  <div class="field-tip">{{ t('messages.info.tagsDescription') }}</div>
                </el-form-item>

                <el-form-item :label="t('forms.labels.customConfig')">
                  <el-input
                    v-model="editForm.custom_config"
                    type="textarea"
                    :rows="4"
                    :placeholder="t('forms.placeholders.enterCustomConfig')"
                    style="width: 100%"
                  />
                  <div class="field-tip">{{ t('messages.info.customConfigDescription') }}</div>
                </el-form-item>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="showEditDialog = false"
            class="cancel-button"
          >
            {{ t('actions.cancel') }}
          </el-button>
          <el-button 
            type="primary" 
            @click="handleEditSubmit" 
            :loading="submitting"
            class="submit-button"
          >
            {{ t('actions.saveChanges') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getServerList, addServer, removeServer, modifyServer, getForwardEndpoints } from '../api'
import { ArrowDown, ArrowUp, Delete, Document, Edit, Operation, Connection, QuestionFilled } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { API_HOST } from '../config'
import { useThemeStore } from '../stores/theme'
import { useSubscriptionStore } from '../stores/subscription'

// i18n setup
const { t } = useI18n()

// Router setup
const router = useRouter()

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Subscription store for permission checking
const subscriptionStore = useSubscriptionStore()

// Removed reactive headerCellStyle for performance - using static CSS instead

const servers = ref([])
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showAdvanced = ref(false)
const showEditAdvanced = ref(false)
const formRef = ref(null)

// Computed property to validate and filter server data
const validatedServers = computed(() => {
  return servers.value.filter(server => {
    if (!server || typeof server !== 'object') {
      console.warn('Invalid server object:', server)
      return false
    }
    if (!server.id) {
      console.warn('Server missing ID:', server)
      return false
    }
    // Ensure display_name exists, provide fallback if missing
    if (!server.display_name) {
      console.warn('Server missing display_name, using fallback:', server)
      server.display_name = `Server ${server.id}`
    }
    return true
  })
})

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]
const editFormRef = ref(null)
const formErrors = ref({})

// Search state
const searchForm = ref({
  id: '',
  name: '',
  ipAddr: '',
  version: [],
  status: [],
  tags: []
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

// Available options for multi-select filters
const availableVersions = ref([])
const availableStatuses = ref(['Online', 'Offline'])
const availableTags = ref([])

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('server-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

// Save search expanded state to localStorage
const saveSearchExpandedState = () => {
  localStorage.setItem('server-search-expanded', JSON.stringify(searchExpanded.value))
}

// Forward endpoints management
const availableEndpoints = ref([])
const selectedForwardEndpointIds = ref([])
const selectedForwardEndpoints = ref([])
const selectedEditForwardEndpointIds = ref([])
const selectedEditForwardEndpoints = ref([])

// ToT server management
const selectedTotServerIds = ref([])
const selectedTotServers = ref([])
const selectedEditTotServerIds = ref([])
const selectedEditTotServers = ref([])

const form = ref({
  display_name: '',
  ip_addr: '',
  interface_name: 'eth0',
  port_start: 30000,
  port_end: 31000,
  traffic_scale: 1.0,
  allow_forward: false,
  allow_latency_test: true,
  allow_ipv6: false,
  balance_strategy: 0,
  use_forward_as_tun: false,
  forward_endpoints: [],
  allow_ip_num: null,
  allow_conn_num: null,
  protocol_filters: [],
  tot_server_list: [],
  tot_server_select_mode: 0,
  tot_server_test_method: 0,
  custom_config: '',
  tags: []
})

const editForm = ref({
  server_id: null,
  display_name: '',
  ip_addr: '',
  interface_name: 'eth0',
  port_start: 30000,
  port_end: 31000,
  traffic_scale: 1.0,
  allow_forward: false,
  allow_latency_test: false,
  allow_ipv6: false,
  balance_strategy: 0,
  use_forward_as_tun: false,
  forward_endpoints: [],
  allow_ip_num: null,
  allow_conn_num: null,
  protocol_filters: [],
  tot_server_list: [],
  tot_server_select_mode: 0,
  tot_server_test_method: 0,
  custom_config: '',
  tags: []
})

const rules = computed(() => ({
  display_name: [
    { required: true, message: t('forms.validation.pleaseEnterDisplayName'), trigger: 'blur' }
  ],
  ip_addr: [
    { required: true, message: t('forms.validation.pleaseEnterIpAddress'), trigger: 'blur' }
  ],
  port_start: [
    { required: true, message: t('forms.validation.pleaseEnterStartPort'), trigger: 'blur' },
    { type: 'number', message: t('forms.validation.portMustBeNumber'), trigger: 'blur' }
  ],
  port_end: [
    { required: true, message: t('forms.validation.pleaseEnterEndPort'), trigger: 'blur' },
    { type: 'number', message: t('forms.validation.portMustBeNumber'), trigger: 'blur' }
  ],
  tot_server_test_method: [
    {
      validator(rule, value, callback) {
        // Only validate if ToT servers are selected (for both add and edit forms)
        const totServerCount = selectedTotServers.value.length || selectedEditTotServers.value.length
        if (totServerCount > 0 && (value === null || value === undefined)) {
          callback(new Error(t('forms.validation.selectTotTestMethod')))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  tot_server_select_mode: [
    {
      validator(rule, value, callback) {
        // Only validate if multiple ToT servers are selected (for both add and edit forms)
        const totServerCount = selectedTotServers.value.length || selectedEditTotServers.value.length
        if (totServerCount > 1 && (value === null || value === undefined)) {
          callback(new Error(t('forms.validation.selectTotBalanceStrategy')))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}))

const handleForwardEndpointsChange = (selectedIds) => {
  selectedForwardEndpointIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedForwardEndpoints.value = selectedIds
    .map(id => {
      const endpoint = endpointMap.get(id)
      return endpoint ? { id: endpoint.id, display_name: endpoint.display_name } : null
    })
    .filter(Boolean)
  form.value.forward_endpoints = selectedIds
  
  if (selectedIds.length <= 1) {
    form.value.balance_strategy = 0
  }
}

const handleEditForwardEndpointsChange = (selectedIds) => {
  selectedEditForwardEndpointIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedEditForwardEndpoints.value = selectedIds
    .map(id => {
      const endpoint = endpointMap.get(id)
      return endpoint ? { id: endpoint.id, display_name: endpoint.display_name } : null
    })
    .filter(Boolean)
  editForm.value.forward_endpoints = selectedIds
  
  if (selectedIds.length <= 1) {
    editForm.value.balance_strategy = 0
  }
}

const handleForwardEndpointSort = () => {
  selectedForwardEndpointIds.value = selectedForwardEndpoints.value.map(ep => ep.id)
  form.value.forward_endpoints = selectedForwardEndpointIds.value
}

const handleEditForwardEndpointSort = () => {
  selectedEditForwardEndpointIds.value = selectedEditForwardEndpoints.value.map(ep => ep.id)
  editForm.value.forward_endpoints = selectedEditForwardEndpointIds.value
}

const handleRemoveForwardEndpoint = (endpoint) => {
  const index = selectedForwardEndpoints.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedForwardEndpoints.value.splice(index, 1)
    selectedForwardEndpointIds.value = selectedForwardEndpoints.value.map(ep => ep.id)
    form.value.forward_endpoints = selectedForwardEndpointIds.value
  }
  
  if (selectedForwardEndpoints.value.length <= 1) {
    form.value.balance_strategy = 0
  }
}

const handleEditRemoveForwardEndpoint = (endpoint) => {
  const index = selectedEditForwardEndpoints.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedEditForwardEndpoints.value.splice(index, 1)
    selectedEditForwardEndpointIds.value = selectedEditForwardEndpoints.value.map(ep => ep.id)
    editForm.value.forward_endpoints = selectedEditForwardEndpointIds.value
  }
  
  if (selectedEditForwardEndpoints.value.length <= 1) {
    editForm.value.balance_strategy = 0
  }
}

const handleTotServerChange = (selectedIds) => {
  selectedTotServerIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedTotServers.value = selectedIds
    .map(id => endpointMap.get(id))
    .filter(Boolean)
  form.value.tot_server_list = selectedIds
  
  if (selectedIds.length <= 1) {
    form.value.tot_server_select_mode = 0
  }
}

const handleEditTotServerChange = (selectedIds) => {
  selectedEditTotServerIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedEditTotServers.value = selectedIds
    .map(id => endpointMap.get(id))
    .filter(Boolean)
  editForm.value.tot_server_list = selectedIds
  
  if (selectedIds.length <= 1) {
    editForm.value.tot_server_select_mode = 0
  }
}

const handleTotServerSort = () => {
  selectedTotServerIds.value = selectedTotServers.value.map(ep => ep.id)
  form.value.tot_server_list = selectedTotServerIds.value
}

const handleEditTotServerSort = () => {
  selectedEditTotServerIds.value = selectedEditTotServers.value.map(ep => ep.id)
  editForm.value.tot_server_list = selectedEditTotServerIds.value
}

const handleRemoveTotServer = (endpoint) => {
  const index = selectedTotServers.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedTotServers.value.splice(index, 1)
    selectedTotServerIds.value = selectedTotServers.value.map(ep => ep.id)
    form.value.tot_server_list = selectedTotServerIds.value
  }
  
  if (selectedTotServers.value.length <= 1) {
    form.value.tot_server_select_mode = 0
  }
}

const handleEditRemoveTotServer = (endpoint) => {
  const index = selectedEditTotServers.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedEditTotServers.value.splice(index, 1)
    selectedEditTotServerIds.value = selectedEditTotServers.value.map(ep => ep.id)
    editForm.value.tot_server_list = selectedEditTotServerIds.value
  }
  
  if (selectedEditTotServers.value.length <= 1) {
    editForm.value.tot_server_select_mode = 0
  }
}

const fetchForwardEndpoints = async () => {
  // Only fetch if user has forward endpoint permission
  if (!subscriptionStore.allowForwardEndpoint) {
    console.log('User does not have forward endpoint permission, skipping forward endpoints fetch')
    availableEndpoints.value = []
    return
  }

  try {
    // Fetch all forward endpoints without pagination to get complete list for server creation
    console.log('Fetching complete forward endpoints list with fetch_all=true')
    const { data } = await getForwardEndpoints({ fetch_all: true })
    if (data && Array.isArray(data.forward_endpoints)) {
      availableEndpoints.value = data.forward_endpoints.map(item => ({
        id: item.id,
        display_name: item.display_name || item.name,
        // Store any other properties that might be needed
        ...item
      }))
      console.log(`Loaded ${availableEndpoints.value.length} forward endpoints for server management`)
    } else {
      console.warn('No forward endpoints data in response:', data)
      availableEndpoints.value = []
    }
  } catch (error) {
    console.error('Failed to fetch forward endpoints:', error)
    ElMessage.error(error?.response?.data?.message || 'Failed to fetch forward endpoints')
    availableEndpoints.value = []
  }
}

const getPortsTotal = (server) => {
  if (!server.port_start || !server.port_end) return 0
  return server.port_end - server.port_start + 1
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    display_name: '',
    ip_addr: '',
    interface_name: 'eth0',
    port_start: 30000,
    port_end: 31000,
    traffic_scale: 1.0,
    allow_forward: false,
    allow_latency_test: true,
    allow_ipv6: false,
    balance_strategy: 0,
    use_forward_as_tun: false,
    forward_endpoints: [],
    allow_ip_num: null,
    allow_conn_num: null,
    protocol_filters: [],
    tot_server_list: [],
    tot_server_select_mode: 0,
    tot_server_test_method: 0,
    custom_config: '',
    tags: []
  }
  selectedTotServerIds.value = []
  selectedTotServers.value = []
  selectedForwardEndpointIds.value = []
  selectedForwardEndpoints.value = []
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true
      try {
        const submitData = { ...form.value }
        
        if (!submitData.forward_endpoints || submitData.forward_endpoints.length <= 1) {
          delete submitData.balance_strategy
        }

        if (!submitData.forward_endpoints) {
          delete submitData.use_forward_as_tun
        }

        // Handle ToT server data
        if (selectedTotServers.value.length > 0) {
          submitData.tot_server_list = selectedTotServerIds.value
          if (selectedTotServers.value.length > 1) {
            submitData.tot_server_select_mode = form.value.tot_server_select_mode
          } else {
            delete submitData.tot_server_select_mode
          }
          submitData.tot_server_test_method = form.value.tot_server_test_method
        } else {
          delete submitData.tot_server_list
          delete submitData.tot_server_select_mode
          delete submitData.tot_server_test_method
        }
        
        // Handle custom config - only include if not empty
        if (!submitData.custom_config || submitData.custom_config.trim() === '') {
          delete submitData.custom_config
        }
        
        await addServer(submitData)
        ElMessage.success(t('messages.success.serverAdded'))
        showAddDialog.value = false
        resetForm()
        loadServers(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Error submitting form:', error)
        ElMessage.error(error.message || t('messages.error.failedToAddServer'))
      } finally {
        submitting.value = false
      }
    }
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirm.deleteServer'),
      t('common.warning'),
      {
        confirmButtonText: t('actions.ok'),
        cancelButtonText: t('actions.cancel'),
        type: 'warning',
      }
    )
    
    loading.value = true
    await removeServer(row.id)
    ElMessage.success(t('messages.success.serverDeleted'))
    await loadServers(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting server:', error)
      ElMessage.error(error.message || t('messages.error.failedToDeleteServer'))
    }
  } finally {
    loading.value = false
  }
}

const handleLatencyMonitoring = (row) => {
  router.push(`/dashboard/latency-monitoring/${row.id}`)
}

const handleCopyCommand = async (row) => {
  if (!row.server_pubkey || !row.interface_name) {
    ElMessage.error(t('messages.error.missingServerData'))
    return
  }

  const command = `curl -s ${API_HOST}/worker_setup_script/${row.server_pubkey} | bash -s ${row.interface_name}`
  
  try {
    // 尝试使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(command)
    } else {
      // 回退方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = command
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        textArea.remove()
      } catch (err) {
        console.error('Failed to copy text:', err)
        textArea.remove()
        throw new Error('Failed to copy text')
      }
    }
    ElMessage.success(t('messages.success.commandCopied'))
  } catch (err) {
    console.error('Failed to copy:', err)
    ElMessage.error(t('messages.error.failedToCopyCommand'))
  }
}

const handleEdit = (row) => {
  // Validate that row exists and has required properties
  if (!row) {
    console.error('handleEdit: Server data is missing')
    ElMessage.error(t('messages.error.invalidServerData'))
    return
  }

  if (!row.id) {
    console.error('handleEdit: Server ID is missing')
    ElMessage.error(t('messages.error.missingServerId'))
    return
  }

  // Safely extract data with fallbacks for missing fields
  editForm.value = {
    server_id: row.id,
    display_name: row.display_name || `Server ${row.id}`,
    ip_addr: row.ip_addr || '',
    interface_name: row.interface_name || 'eth0',
    port_start: row.port_start || 30000,
    port_end: row.port_end || 31000,
    traffic_scale: row.traffic_scale || 1.0,
    allow_forward: row.allow_forward || false,
    allow_latency_test: row.allow_latency_test || false,
    allow_ipv6: row.allow_ipv6 || false,
    balance_strategy: row.balance_strategy || 0,
    forward_endpoints: row.forward_endpoints || [],
    use_forward_as_tun: row.use_forward_as_tun || false,
    allow_ip_num: row.allow_ip_num,
    allow_conn_num: row.allow_conn_num,
    protocol_filters: row.protocol_filters || [],
    tot_server_list: row.tot_server_list || [],
    tot_server_select_mode: row.tot_server_select_mode || 0,
    tot_server_test_method: row.tot_server_test_method || 0,
    custom_config: row.custom_config || '',
    tags: row.tags || [],
  }

  // Safely handle forward endpoints
  const forwardEndpoints = row.forward_endpoints || []
  selectedEditForwardEndpointIds.value = forwardEndpoints

  // Safely map forward endpoint names
  selectedEditForwardEndpoints.value = forwardEndpoints
    .map(id => {
      const endpoint = availableEndpoints.value.find(item => item.id == id)
      return endpoint ? { id: endpoint.id, display_name: endpoint.display_name } : { id, display_name: `${id}: Endpoint ${id}` }
    })
    .filter(Boolean)

  // Safely handle ToT servers
  const totServers = row.tot_server_list || []
  selectedEditTotServerIds.value = totServers

  // Safely map ToT server names
  selectedEditTotServers.value = totServers
    .map(id => {
      const endpoint = availableEndpoints.value.find(item => item.id == id)
      return endpoint ? { id: endpoint.id, display_name: endpoint.display_name } : { id, display_name: `${id}: Server ${id}` }
    })
    .filter(Boolean)

  showEditDialog.value = true
}

const handleEditSubmit = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          server_id: editForm.value.server_id
        }

        if (editForm.value.display_name !== undefined) {
          submitData.display_name = editForm.value.display_name
        }
        if (editForm.value.ip_addr !== undefined) {
          submitData.ip_addr = editForm.value.ip_addr
        }
        if (editForm.value.interface_name !== undefined) {
          submitData.interface_name = editForm.value.interface_name
        }
        if (editForm.value.port_start !== undefined) {
          submitData.port_start = editForm.value.port_start
        }
        if (editForm.value.port_end !== undefined) {
          submitData.port_end = editForm.value.port_end
        }
        if (editForm.value.traffic_scale !== undefined) {
          submitData.traffic_scale = editForm.value.traffic_scale
        }
        if (editForm.value.allow_forward !== undefined) {
          submitData.allow_forward = editForm.value.allow_forward
        }
        if (editForm.value.allow_latency_test !== undefined) {
          submitData.allow_latency_test = editForm.value.allow_latency_test
        }
        if (editForm.value.allow_ipv6 !== undefined) {
          submitData.allow_ipv6 = editForm.value.allow_ipv6
        }
        if (editForm.value.forward_endpoints !== undefined) {
          submitData.forward_endpoints = editForm.value.forward_endpoints
        }
        if (editForm.value.balance_strategy !== undefined && editForm.value.forward_endpoints?.length > 1) {
          submitData.balance_strategy = editForm.value.balance_strategy
        }
        if (editForm.value.use_forward_as_tun !== undefined && editForm.value.forward_endpoints?.length >= 1) {
          submitData.use_forward_as_tun = editForm.value.use_forward_as_tun
        }
        if (editForm.value.allow_ip_num !== undefined) {
          submitData.allow_ip_num = editForm.value.allow_ip_num
        }
        if (editForm.value.allow_conn_num !== undefined) {
          submitData.allow_conn_num = editForm.value.allow_conn_num
        }
        if (editForm.value.protocol_filters !== undefined) {
          submitData.protocol_filters = editForm.value.protocol_filters
        }
        // Handle ToT server data
        if (editForm.value.tot_server_list !== undefined) {
          submitData.tot_server_list = editForm.value.tot_server_list
        }
        if (editForm.value.tot_server_select_mode !== undefined && editForm.value.tot_server_list?.length > 1) {
          submitData.tot_server_select_mode = editForm.value.tot_server_select_mode
        }
        if (editForm.value.tot_server_test_method !== undefined && editForm.value.tot_server_list?.length > 0) {
          submitData.tot_server_test_method = editForm.value.tot_server_test_method
        }
        if (editForm.value.custom_config !== undefined && editForm.value.custom_config.trim() !== '') {
          submitData.custom_config = editForm.value.custom_config
        }
        if (editForm.value.tags !== undefined) {
          submitData.tags = editForm.value.tags
        }
        await modifyServer(submitData)
        ElMessage.success(t('messages.success.serverModified'))
        showEditDialog.value = false
        loadServers(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Error modifying server:', error)
        ElMessage.error(error.message || t('messages.error.failedToModifyServer'))
      } finally {
        submitting.value = false
      }
    }
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const loadServers = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
    }
    if (searchForm.value.name?.trim()) {
      searchParams.name = searchForm.value.name.trim()
    }
    if (searchForm.value.ipAddr?.trim()) {
      searchParams.ip_addr = searchForm.value.ipAddr.trim()
    }
    if (searchForm.value.version && searchForm.value.version.length > 0) {
      searchParams.version = searchForm.value.version
    }
    if (searchForm.value.status && searchForm.value.status.length > 0) {
      searchParams.status = searchForm.value.status
    }
    if (searchForm.value.tags && searchForm.value.tags.length > 0) {
      searchParams.tags = searchForm.value.tags
    }

    const { data } = await getServerList(searchParams)

    // Handle paginated response
    if (data.servers && data.pagination) {
      // New paginated response format
      servers.value = data.servers
      currentPage.value = data.pagination.current_page
      pageSize.value = data.pagination.page_size
      totalItems.value = data.pagination.total_items
      totalPages.value = data.pagination.total_pages
      
      // Use available_versions from API response if available (contains all versions from all servers)
      if (data.available_versions && Array.isArray(data.available_versions)) {
        availableVersions.value = data.available_versions
      } else {
        // Fallback: extract versions from current page data
        const versions = new Set()
        servers.value.forEach(server => {
          const version = server.version || 'Unknown'
          versions.add(version)
        })
        availableVersions.value = Array.from(versions).sort()
      }
      
      // Use available_tags from API response if available (contains all tags from all servers)
      if (data.available_tags && Array.isArray(data.available_tags)) {
        availableTags.value = data.available_tags
      } else {
        // Fallback: extract tags from current page data
        const tags = new Set()
        servers.value.forEach(server => {
          if (server.tags && Array.isArray(server.tags)) {
            server.tags.forEach(tag => tags.add(tag))
          }
        })
        availableTags.value = Array.from(tags).sort()
      }
    } else if (data?.servers) {
      // Fallback for old response format (if backend doesn't support pagination yet)
      servers.value = data.servers
      totalItems.value = data.servers.length
      totalPages.value = 1
      
      // Extract versions from current data
      const versions = new Set()
      servers.value.forEach(server => {
        const version = server.version || 'Unknown'
        versions.add(version)
      })
      availableVersions.value = Array.from(versions).sort()
      
      // Extract tags from current data (fallback for old API format)
      const tags = new Set()
      servers.value.forEach(server => {
        if (server.tags && Array.isArray(server.tags)) {
          server.tags.forEach(tag => tags.add(tag))
        }
      })
      availableTags.value = Array.from(tags).sort()
    } else {
      servers.value = []
      totalItems.value = 0
      totalPages.value = 1
      availableVersions.value = []
      availableTags.value = []
    }
  } catch (error) {
    console.error('loadServers: API error', error)
    ElMessage.error(`Failed to load servers: ${error.message || 'Unknown error'}`)
    servers.value = []
    totalItems.value = 0
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  loadServers(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  loadServers(1, size)
}

// Search handlers
const handleSearchInput = () => {
  // Debounce search input to avoid too many API calls
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  // Immediate search for dropdown changes
  handleSearch()
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  loadServers(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    name: '',
    ipAddr: '',
    version: [],
    status: [],
    tags: []
  }
  currentPage.value = 1
  loadServers(1, pageSize.value)
}

// Search UI handlers
const toggleSearchExpanded = () => {
  searchExpanded.value = !searchExpanded.value
  saveSearchExpandedState()
}

onMounted(async () => {
  loadSearchExpandedState()

  // Ensure subscription data is loaded for permission checking
  await subscriptionStore.fetchData()

  // Load servers and forward endpoints
  loadServers()
  await fetchForwardEndpoints()
})
</script>

<style scoped>
.server-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-toggle-btn {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* Dark mode compatibility for search form */
:root.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:root.dark .search-toggle-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive design for search form */
@media (max-width: 768px) {
  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

/* Mobile Table Wrapper */
.table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
}

.ports-info {
  display: flex;
  align-items: center;
}

.ports-count {
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-regular) !important;
  border: 1px solid var(--theme-border-base) !important;
}

/* Dark mode ports count styling */
:global(.dark) .ports-count {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}


/* Clean borderless table styling with performance optimizations */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

/* Fixed row height for performance */
:deep(.el-table .el-table__row) {
  height: 60px !important;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0 !important;
  vertical-align: middle;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
  transition: none;
}

:deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Dark mode specific table styling */
:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

:global(.dark) :deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}

:deep(.el-card__body) {
  padding: 12px;
}

.server-dialog {
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid var(--theme-border-base);
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-dialog__title) {
    font-size: 20px;
    font-weight: 500;
    color: var(--theme-text-primary);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid var(--theme-border-base);
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 8px;
    line-height: 1.2;

    &::before {
      color: var(--theme-danger);
      margin-right: 4px;
    }
  }

  :deep(.el-input__wrapper),
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    border-radius: 6px;
  }

  :deep(.el-input__wrapper) {
    box-shadow: var(--theme-shadow-light);
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-base);
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 2px var(--theme-primary-light);
    border-color: var(--theme-primary);
  }

  :deep(.el-input__wrapper:hover) {
    border-color: var(--theme-border-hover);
  }
}

.port-range {
  display: flex;
  gap: 12px;
}

.port-input {
  flex: 1;
  :deep(.el-input-number) {
    width: 100%;
  }
}

.advanced-options {
  margin-top: 16px;
}

.advanced-options-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  user-select: none;
  color: var(--theme-text-primary);
  font-weight: 500;
  font-size: 16px;
  transition: color 0.3s ease;

  &:hover {
    color: var(--theme-primary);
  }
}

.advanced-icon {
  font-size: 16px;
  color: var(--theme-text-secondary);
  transition: transform 0.2s ease, color 0.3s ease;

  &.is-active {
    transform: rotate(180deg);
    color: var(--theme-primary);
  }
}

.advanced-options-content {
  padding-top: 8px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.custom-checkbox {
  :deep(.el-checkbox__label) {
    color: var(--theme-text-primary);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
  }

  :deep(.el-checkbox__inner) {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-base);
  }

  :deep(.el-checkbox__inner:hover) {
    border-color: var(--theme-primary);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  border-radius: 6px;
  border: 1px solid var(--theme-border-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-regular);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--theme-border-hover);
    background-color: var(--theme-fill-light);
    color: var(--theme-text-primary);
  }
}

.submit-button {
  border-radius: 6px;
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--theme-shadow-base);
  }
}

:deep(.el-form-item__error) {
  color: var(--theme-danger);
  font-size: 13px;
  margin-top: 4px;
}

/* Additional dark mode enhancements */
.server-management {
  background-color: var(--theme-bg-secondary);
  transition: background-color 0.3s ease;
}

/* Enhanced status tags - optimized for performance */
:deep(.el-tag) {
  font-weight: 500;
}

/* Enhanced buttons - optimized for performance */
:deep(.el-button.is-circle:hover) {
  box-shadow: var(--theme-shadow-base);
}

/* Enhanced dialog */
:deep(.el-dialog) {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-modal);
}

/* Enhanced scrollbar */
:deep(.el-scrollbar__thumb) {
  background-color: var(--theme-border-base);
}

:deep(.el-scrollbar__thumb:hover) {
  background-color: var(--theme-border-hover);
}

/* Enhanced loading - now handled globally in themes.css */
:deep(.el-loading-mask) {
  background-color: var(--loading-background-overlay) !important;
  backdrop-filter: blur(2px);
}

:deep(.el-loading-spinner .path) {
  stroke: var(--loading-spinner-color) !important;
  stroke-width: var(--loading-spinner-border-width) !important;
}

:deep(.el-loading-text) {
  color: var(--loading-text-color) !important;
}

/* Enhanced select dropdown */
:deep(.el-select-dropdown) {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-dropdown);
}

:deep(.el-select-dropdown__item) {
  color: var(--theme-text-regular);
}

:deep(.el-select-dropdown__item:hover) {
  background-color: var(--theme-fill-light);
}

:deep(.el-select-dropdown__item.selected) {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }

  /* Mobile Table Optimizations */
  .table-wrapper {
    margin: 0 -16px;
    border-radius: 0;
  }

  .table-card {
    margin: 0 -16px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .server-table {
    min-width: 700px; /* Ensure horizontal scroll */
  }

  .el-table .el-table__row {
    height: 70px !important; /* Increase row height for mobile */
  }

  /* Mobile-friendly action buttons */
  .el-space {
    gap: 6px !important;
  }

  .el-space .el-button {
    width: 36px;
    height: 36px;
    padding: 0;
  }
}

@media (max-width: 480px) {
  .server-management-container {
    padding: 12px;
  }

  .table-wrapper {
    margin: 0 -12px;
  }

  .table-card {
    margin: 0 -12px;
  }

  .server-table {
    min-width: 750px; /* Increase min-width for smaller screens */
  }

  .el-table .el-table__row {
    height: 80px !important; /* Further increase row height */
  }

  /* Larger touch targets */
  .el-space .el-button {
    width: 40px;
    height: 40px;
  }
}

.field-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
  line-height: 1.4;
}

/* ToT Server Draggable Styles */
.forward-endpoints-list {
  margin-top: 8px;
}

.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.forward-endpoint-item {
  display: flex;
  align-items: center;
}

.endpoint-tag {
  cursor: move;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.endpoint-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.drag-handle {
  cursor: grab;
  color: var(--el-text-color-secondary);
  transition: color 0.2s ease;
}

.drag-handle:hover {
  color: var(--el-color-primary);
}

.endpoint-tag:active .drag-handle {
  cursor: grabbing;
}

/* Help tooltip styles */
.form-label-with-help {
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  color: var(--el-color-info);
  cursor: help;
  font-size: 14px;
  transition: color 0.2s ease;
}

.help-icon:hover {
  color: var(--el-color-primary);
}
</style>
