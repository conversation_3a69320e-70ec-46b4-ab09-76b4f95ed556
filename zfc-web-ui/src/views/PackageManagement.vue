<template>
  <div class="package-management">
    <div class="header">
      <h2>{{ t('pages.packageManagement.title') }}</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          {{ t('pages.packageManagement.createPackage') }}
        </el-button>
      </div>
    </div>

    <!-- Search Form -->
    <el-card class="search-card">
      <el-form :model="searchForm" class="search-form" :inline="true">
        <el-form-item :label="t('pages.packageManagement.packageName')">
          <el-input
            v-model="searchForm.name"
            :placeholder="t('pages.packageManagement.searchByName')"
            clearable
            style="width: 200px"
            @input="handleSearchInput"
          />
        </el-form-item>
        <el-form-item :label="t('pages.packageManagement.status')">
          <el-select
            v-model="searchForm.status_filter"
            :placeholder="t('pages.packageManagement.filterByStatus')"
            clearable
            style="width: 160px"
            @change="handleSearchChange"
          >
            <el-option
              :label="t('pages.packageManagement.allStatuses')"
              value=""
            />
            <el-option
              :label="t('pages.packageManagement.onlyActive')"
              value="active"
            />
            <el-option
              :label="t('pages.packageManagement.onlyInactive')"
              value="inactive"
            />
            <el-option
              :label="t('pages.packageManagement.onlyDefault')"
              value="default"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            {{ t('pages.packageManagement.search') }}
          </el-button>
          <el-button @click="handleClearSearch">
            {{ t('pages.packageManagement.clear') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Packages Table -->
    <el-card class="table-card">
      <div class="table-wrapper">
        <el-table
          :data="packages"
          v-loading="loading"
          style="width: 100%"
          class="package-table"
          :row-style="{ height: '60px' }"
        >
          <el-table-column
            prop="id"
            label="ID"
            width="80"
          />
          <el-table-column
            prop="name"
            :label="t('pages.packageManagement.packageName')"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="display_name"
            :label="t('pages.packageManagement.displayName')"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="description"
            :label="t('pages.packageManagement.description')"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            :label="t('pages.packageManagement.totalTraffic')"
            width="120"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.total_traffic">
                {{ scope.row.total_traffic }} GB
              </span>
              <span v-else class="unlimited-text">
                {{ t('pages.packageManagement.leaveEmptyForUnlimited') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('pages.packageManagement.bandwidth')"
            width="120"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.bandwidth">
                {{ scope.row.bandwidth }} Mbps
              </span>
              <span v-else class="unlimited-text">
                {{ t('pages.packageManagement.leaveEmptyForUnlimited') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('pages.packageManagement.lineCount')"
            width="100"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.package_lines ? scope.row.package_lines.length : 0 }}
            </template>
          </el-table-column>
          <el-table-column
            :label="t('pages.packageManagement.status')"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-tag
                :type="getStatusTagType(scope.row)"
                size="small"
              >
                {{ getStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('pages.packageManagement.actions')"
            width="200"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="handleView(scope.row)"
              >
                {{ t('pages.packageManagement.view') }}
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                @click="handleEdit(scope.row)"
              >
                {{ t('pages.packageManagement.edit') }}
              </el-button>
              <el-button
                type="danger"
                link
                size="small"
                @click="handleDelete(scope.row)"
                :disabled="scope.row.is_default"
              >
                {{ t('pages.packageManagement.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- Pagination -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Package Dialog -->
    <PackageDialog
      v-model:visible="showCreateDialog"
      :package-data="null"
      @success="handlePackageSuccess"
    />

    <PackageDialog
      v-model:visible="showEditDialog"
      :package-data="selectedPackage"
      @success="handlePackageSuccess"
    />

    <!-- Package Detail Dialog -->
    <el-dialog
      v-model="showDetailDialog"
      :title="t('pages.packageManagement.view')"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedPackage" class="package-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('pages.packageManagement.packageName')">
            {{ selectedPackage.name }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.displayName')">
            {{ selectedPackage.display_name }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.description')" :span="2">
            {{ selectedPackage.description || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.totalTraffic')">
            <span v-if="selectedPackage.total_traffic">
              {{ selectedPackage.total_traffic }} GB
            </span>
            <span v-else>{{ t('pages.packageManagement.leaveEmptyForUnlimited') }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.bandwidth')">
            <span v-if="selectedPackage.bandwidth">
              {{ selectedPackage.bandwidth }} Mbps
            </span>
            <span v-else>{{ t('pages.packageManagement.leaveEmptyForUnlimited') }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.maxPortsPerServer')">
            {{ selectedPackage.max_ports_per_server || t('pages.packageManagement.leaveEmptyForUnlimited') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.allowForwardEndpoint')">
            <el-tag :type="selectedPackage.allow_forward_endpoint ? 'success' : 'danger'" size="small">
              {{ selectedPackage.allow_forward_endpoint ? t('pages.subscriptionManagement.enabled') : t('pages.subscriptionManagement.disabled') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.allowIpNum')">
            {{ selectedPackage.allow_ip_num || t('pages.packageManagement.leaveEmptyForUnlimited') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.allowConnNum')">
            {{ selectedPackage.allow_conn_num || t('pages.packageManagement.leaveEmptyForUnlimited') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.isActive')">
            <el-tag :type="selectedPackage.is_active ? 'success' : 'danger'" size="small">
              {{ selectedPackage.is_active ? t('pages.packageManagement.active') : t('pages.packageManagement.inactive') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.packageManagement.isDefault')">
            <el-tag :type="selectedPackage.is_default ? 'success' : 'info'" size="small">
              {{ selectedPackage.is_default ? t('pages.packageManagement.default') : '-' }}
            </el-tag>
          </el-descriptions-item>
          <!-- 计费信息 -->
          <el-descriptions-item :label="t('pages.subscriptionManagement.billingType')" v-if="selectedPackage.bill_type">
            <el-tag :type="getBillingTypeTagType(selectedPackage.bill_type)" size="small">
              {{ getBillingTypeText(selectedPackage.bill_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.subscriptionManagement.cycleDays')" v-if="selectedPackage.bill_type && selectedPackage.bill_type.Cycle">
            {{ selectedPackage.bill_type.Cycle.days }} {{ t('common.days') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.subscriptionManagement.cyclePrice')" v-if="selectedPackage.bill_type && selectedPackage.bill_type.Cycle">
            {{ selectedPackage.bill_type.Cycle.price }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.subscriptionManagement.onetimePrice')" v-if="selectedPackage.bill_type && selectedPackage.bill_type.OneTime">
            {{ selectedPackage.bill_type.OneTime.price }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.subscriptionManagement.totalDays')" v-if="selectedPackage.total_days">
            {{ selectedPackage.total_days }} {{ t('common.days') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.subscriptionManagement.trafficResetCycleLabel')" v-if="selectedPackage.traffic_reset_days">
            {{ selectedPackage.traffic_reset_days }} {{ t('common.days') }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- Package Lines -->
        <div v-if="selectedPackage.package_lines && selectedPackage.package_lines.length > 0" class="package-lines-section">
          <h4>{{ t('pages.packageManagement.packageLines') }}</h4>
          <el-table :data="selectedPackage.package_lines" style="width: 100%" size="small">
            <el-table-column prop="line_name" :label="t('forms.labels.line')" />
            <el-table-column :label="t('pages.packageManagement.bandwidthLimit')">
              <template #default="scope">
                <span v-if="scope.row.bandwidth_limit">
                  {{ scope.row.bandwidth_limit }} Mbps
                </span>
                <span v-else>{{ t('pages.packageManagement.leaveEmptyForUnlimited') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="traffic_scale" :label="t('pages.packageManagement.trafficScale')" />
            <el-table-column :label="t('pages.packageManagement.lineTraffic')">
              <template #default="scope">
                <span v-if="scope.row.line_traffic">
                  {{ scope.row.line_traffic }} GB
                </span>
                <span v-else>{{ t('pages.packageManagement.leaveEmptyForUnlimited') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">
            {{ t('common.close') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPackageList, deletePackage } from '../api'
import PackageDialog from '../components/PackageDialog.vue'

const { t } = useI18n()

// Reactive data
const packages = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// Search form
const searchForm = reactive({
  name: '',
  status_filter: ''
})

// Dialog states
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const selectedPackage = ref(null)

// Search functionality
let searchTimeout = null

const handleSearchInput = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  handleSearch()
}

const handleSearch = async () => {
  currentPage.value = 1
  await fetchPackages()
}

const handleClearSearch = () => {
  searchForm.name = ''
  searchForm.status_filter = ''
  handleSearch()
}

// Status helpers
const getStatusTagType = (pkg) => {
  if (pkg.is_default) return 'warning'
  return pkg.is_active ? 'success' : 'danger'
}

const getStatusText = (pkg) => {
  if (pkg.is_default) return t('pages.packageManagement.default')
  return pkg.is_active ? t('pages.packageManagement.active') : t('pages.packageManagement.inactive')
}

// Billing helpers
const getBillingTypeTagType = (billType) => {
  if (billType.Cycle) return 'primary'
  if (billType.OneTime) return 'success'
  return 'info'
}

const getBillingTypeText = (billType) => {
  if (billType.Cycle) return t('pages.subscriptionManagement.cycleBilling')  
  if (billType.OneTime) return t('pages.subscriptionManagement.onetimeBilling')
  return '-'
}

// Table actions
const handleView = (pkg) => {
  selectedPackage.value = pkg
  showDetailDialog.value = true
}

const handleEdit = (pkg) => {
  selectedPackage.value = pkg
  showEditDialog.value = true
}

const handleDelete = async (pkg) => {
  try {
    await ElMessageBox.confirm(
      t('pages.packageManagement.confirmDeletePackage'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    )

    await deletePackage(pkg.id)
    ElMessage.success(t('pages.packageManagement.packageDeletedSuccessfully'))
    await fetchPackages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete package:', error)
      ElMessage.error(error.message || t('pages.packageManagement.failedToDeletePackage'))
    }
  }
}

// Pagination
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchPackages()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchPackages()
}

// Package success handler
const handlePackageSuccess = () => {
  showCreateDialog.value = false
  showEditDialog.value = false
  selectedPackage.value = null
  fetchPackages()
}

// Fetch packages
const fetchPackages = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    if (searchForm.name.trim()) {
      params.name = searchForm.name.trim()
    }

    if (searchForm.status_filter) {
      if (searchForm.status_filter === 'active') {
        params.is_active = true
      } else if (searchForm.status_filter === 'inactive') {
        params.is_active = false
      } else if (searchForm.status_filter === 'default') {
        params.is_default = true
      }
    }

    const response = await getPackageList(params)
    packages.value = response.data.packages
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to fetch packages:', error)
    ElMessage.error(error.message || t('pages.packageManagement.failedToLoadPackages'))
  } finally {
    loading.value = false
  }
}

// Initialize
onMounted(() => {
  fetchPackages()
})
</script>

<style scoped>
.package-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-wrapper {
  margin-bottom: 20px;
}

.package-table {
  --el-table-border-color: var(--el-border-color-lighter);
}

.package-table :deep(.el-table__cell) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.unlimited-text {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.package-detail {
  padding: 0;
}

.package-lines-section {
  margin-top: 24px;
}

.package-lines-section h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
  .package-management {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>