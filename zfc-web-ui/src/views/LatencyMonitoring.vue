<template>
  <div class="latency-monitoring">
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="goBack" 
          class="back-button"
        >
          <el-icon><arrow-left /></el-icon>
          返回
        </el-button>
        <h2>延迟监控</h2>
        <p v-if="serverName">服务器: {{ serverName }}</p>
        <p v-else>服务器ID: {{ serverId }}</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showConfigDialog = true">
          <el-icon><plus /></el-icon>
          添加监控配置
        </el-button>
      </div>
    </div>

    <!-- 实时状态面板 -->
    <el-card class="realtime-panel">
      <template #header>
        <div class="card-header">
          <span>实时延迟状态</span>
          <el-button text @click="refreshRealtimeData">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div class="realtime-grid">
        <div v-for="(status, configId) in realtimeStatus" :key="configId" class="status-item">
          <div class="status-header">
            <div class="status-name">{{ status.display_name }}</div>
            <div class="status-indicator" :class="{ 'success': status.success, 'error': !status.success }">
              <el-icon v-if="status.success"><check /></el-icon>
              <el-icon v-else><close /></el-icon>
            </div>
          </div>
          <div class="status-details">
            <div class="target">{{ formatTargetAddress(status, false) }}</div>
            <div class="latency" v-if="status.success && status.latency_us">
              {{ formatLatency(status.latency_us) }}
            </div>
            <div class="error" v-else-if="!status.success">
              {{ status.error_msg || '测试失败' }}
            </div>
            <div class="last-test" v-if="status.last_test_time">
              最后测试: {{ formatTime(status.last_test_time) }}
            </div>
          </div>
        </div>
        <div v-if="Object.keys(realtimeStatus).length === 0" class="empty-status">
          暂无监控配置
        </div>
      </div>
    </el-card>

    <!-- 历史数据图表 -->
    <el-card class="chart-panel">
      <template #header>
        <div class="card-header">
          <span>历史延迟数据</span>
          <div class="time-range-selector">
            <el-radio-group v-model="selectedTimeRange" @change="loadHistoryData">
              <el-radio-button label="1h">1小时</el-radio-button>
              <el-radio-button label="3h">3小时</el-radio-button>
              <el-radio-button label="6h">6小时</el-radio-button>
              <el-radio-button label="12h">12小时</el-radio-button>
              <el-radio-button label="24h">24小时</el-radio-button>
              <el-radio-button label="7d">7天</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <LatencyChart 
        :configs="configs"
        :loading="configLoading"
        :time-range="selectedTimeRange"
      />
    </el-card>

    <!-- 配置管理表格 -->
    <el-card class="config-panel">
      <template #header>
        <div class="card-header">
          <span>监控配置</span>
          <div class="config-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索配置..."
              style="width: 200px; margin-right: 16px"
              clearable
            />
            <el-button @click="loadConfigs">
              <el-icon><refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      <el-table :data="configs" style="width: 100%" v-loading="configLoading">
        <el-table-column prop="display_name" label="名称" />
        <el-table-column prop="target_address" label="目标地址">
          <template #default="scope">
            {{ formatTargetAddress(scope.row, true) }}
          </template>
        </el-table-column>
        <el-table-column prop="test_type" label="测试类型">
          <template #default="scope">
            <el-tag :type="scope.row.test_type === 'tcp' ? 'primary' : 'success'">
              {{ scope.row.test_type.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="interval" label="间隔(秒)" />
        <el-table-column prop="is_enabled" label="状态">
          <template #default="scope">
            <el-switch
              v-model="scope.row.is_enabled"
              :disabled="!(isAdmin || scope.row.created_by === subscriptionStore.subscription?.id)"
              @change="toggleConfig(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button 
              v-if="isAdmin || scope.row.created_by === subscriptionStore.subscription?.id"
              size="small" 
              @click="editConfig(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="isAdmin || scope.row.created_by === subscriptionStore.subscription?.id"
              size="small" 
              type="danger" 
              @click="deleteConfig(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100]"
          :total="totalConfigs"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadConfigs"
          @current-change="loadConfigs"
        />
      </div>
    </el-card>

    <!-- 配置对话框 -->
    <LatencyConfigDialog
      v-model="showConfigDialog"
      :server-id="serverId"
      :config="editingConfig"
      @saved="onConfigSaved"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Check, Close, ArrowLeft } from '@element-plus/icons-vue'
import LatencyChart from '../components/LatencyChart.vue'
import LatencyConfigDialog from '../components/LatencyConfigDialog.vue'
import { formatTargetAddress } from '../utils/privacy.js'
import { 
  getLatencyConfigs, 
  getLatencyHistory, 
  getLatencyRealtime,
  updateLatencyConfig,
  deleteLatencyConfig
} from '../api/index.js'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

// 路由参数
const serverId = ref(parseInt(route.params.serverId))
const serverName = ref('')

// 权限控制
const isAdmin = computed(() => subscriptionStore.isAdmin)

// 实时状态数据
const realtimeStatus = ref({})
const realtimeLoading = ref(false)

// 历史数据
const historyData = ref([])
const chartLoading = ref(false)
const selectedTimeRange = ref('1h')

// 配置管理
const configs = ref([])
const configLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalConfigs = ref(0)
const searchKeyword = ref('')

// 重置配置数据
const resetConfigData = () => {
  configs.value = []
  currentPage.value = 1
  totalConfigs.value = 0
  configLoading.value = false
}

// 对话框控制
const showConfigDialog = ref(false)
const editingConfig = ref(null)

// 刷新所有数据
const refreshAllData = () => {
  loadRealtimeData()
  loadHistoryData()
  loadConfigs()
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  currentPage.value = 1
  loadConfigs()
}, { debounce: 500 })

// 监听时间范围变化
watch(selectedTimeRange, () => {
  loadHistoryData()
})

// 重置数据
const resetData = () => {
  realtimeStatus.value = {}
  historyData.value = []
  chartLoading.value = false
  realtimeLoading.value = false
}

// 加载实时状态数据
const loadRealtimeData = async () => {
  if (!serverId.value) return
  
  try {
    realtimeLoading.value = true
    const response = await getLatencyRealtime(serverId.value)
    realtimeStatus.value = response.data.status || {}
  } catch (error) {
    console.error('Failed to load realtime data:', error)
    ElMessage.error('加载实时数据失败')
    realtimeStatus.value = {}
  } finally {
    realtimeLoading.value = false
  }
}

// 刷新实时数据
const refreshRealtimeData = () => {
  loadRealtimeData()
}

// 加载历史数据
const loadHistoryData = async () => {
  if (!serverId.value) {
    console.log('serverId 为空，跳过加载历史数据')
    return
  }
  
  console.log('=== 开始加载历史数据 ===', {
    serverId: serverId.value,
    timeRange: selectedTimeRange.value
  })
  
  try {
    chartLoading.value = true
    // 重置历史数据
    historyData.value = []
    
    const response = await getLatencyHistory({
      server_id: serverId.value,
      time_range: selectedTimeRange.value
    })
    
    console.log('历史数据 API 响应:', response.data)
    
    const dataPoints = response.data.data_points || []
    historyData.value = dataPoints
    
    console.log('设置历史数据:', {
      dataPointsLength: dataPoints.length,
      firstPoint: dataPoints[0],
      historyDataLength: historyData.value.length
    })
  } catch (error) {
    console.error('Failed to load history data:', error)
    ElMessage.error('加载历史数据失败')
    historyData.value = []
  } finally {
    chartLoading.value = false
    console.log('历史数据加载完成')
  }
}

// 加载配置列表
const loadConfigs = async () => {
  if (!serverId.value) return
  
  try {
    configLoading.value = true
    // 在新请求开始时清空旧数据
    if (currentPage.value === 1) {
      configs.value = []
    }
    
    const response = await getLatencyConfigs({
      server_id: serverId.value,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchKeyword.value || undefined
    })
    configs.value = response.data.configs || []
    totalConfigs.value = response.data.total || 0
  } catch (error) {
    console.error('Failed to load configs:', error)
    ElMessage.error('加载配置失败')
    configs.value = []
    totalConfigs.value = 0
  } finally {
    configLoading.value = false
  }
}

// 切换配置启用状态
const toggleConfig = async (config) => {
  try {
    await updateLatencyConfig(config.id, {
      is_enabled: config.is_enabled
    })
    ElMessage.success('配置状态更新成功')
  } catch (error) {
    console.error('Failed to toggle config:', error)
    ElMessage.error('更新配置状态失败')
    // 回滚状态
    config.is_enabled = !config.is_enabled
  }
}

// 编辑配置
const editConfig = (config) => {
  editingConfig.value = { ...config }
  showConfigDialog.value = true
}

// 删除配置
const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除监控配置 "${config.display_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await deleteLatencyConfig(config.id)
    ElMessage.success('删除成功')
    loadConfigs()
    loadRealtimeData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete config:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 配置保存回调
const onConfigSaved = () => {
  showConfigDialog.value = false
  editingConfig.value = null
  loadConfigs()
  loadRealtimeData()
}

// 格式化延迟显示
const formatLatency = (latencyUs) => {
  if (latencyUs < 1000) {
    return `${latencyUs}μs`
  } else if (latencyUs < 1000000) {
    return `${(latencyUs / 1000).toFixed(2)}ms`
  } else {
    return `${(latencyUs / 1000000).toFixed(2)}s`
  }
}

// 格式化时间显示
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}


// 定时器引用
const realtimeTimer = ref(null)

// 页面初始化
onMounted(async () => {
  try {
    // 确保订阅数据已加载
    await subscriptionStore.fetchData()
    
    // 初始化数据
    resetData()
    resetConfigData()
    
    // 加载数据
    refreshAllData()
    
    // 设置定时刷新实时数据（每30秒）
    realtimeTimer.value = setInterval(loadRealtimeData, 30000)
  } catch (error) {
    console.error('Failed to initialize page:', error)
    ElMessage.error('页面初始化失败')
  }
})

// 清理定时器
onUnmounted(() => {
  if (realtimeTimer.value) {
    clearInterval(realtimeTimer.value)
    realtimeTimer.value = null
  }
})
</script>

<style scoped>
.latency-monitoring {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.back-button:hover {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: var(--el-text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.realtime-panel,
.chart-panel,
.config-panel {
  margin-bottom: 20px;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.status-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 16px;
  background: var(--el-bg-color-page);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.status-indicator.success {
  background-color: var(--el-color-success);
}

.status-indicator.error {
  background-color: var(--el-color-danger);
}

.status-details {
  font-size: 14px;
}

.target {
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.latency {
  font-weight: 500;
  color: var(--el-color-success);
  margin-bottom: 4px;
}

.error {
  color: var(--el-color-danger);
  margin-bottom: 4px;
}

.last-test {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

.empty-status {
  grid-column: 1 / -1;
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 0;
}

.time-range-selector {
  display: flex;
  align-items: center;
}

.config-actions {
  display: flex;
  align-items: center;
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>