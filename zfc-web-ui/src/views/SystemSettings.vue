<template>
  <div class="system-settings">
    <div class="page-header">
      <h1>{{ $t('systemSettings.title') }}</h1>
      <p class="page-description">{{ $t('systemSettings.description') }}</p>
    </div>

    <el-form
      ref="settingsForm"
      :model="formData"
      :rules="formRules"
      label-width="200px"
      class="settings-form"
      v-loading="loading"
    >
      <!-- Website Configuration Section -->
      <el-card class="settings-section" :header="$t('systemSettings.sections.website')">
        <el-form-item :label="$t('systemSettings.siteTitle.label')" prop="siteTitle">
          <div class="multilang-input">
            <el-input
              v-model="formData.siteTitle.zh"
              :placeholder="$t('systemSettings.siteTitle.zhPlaceholder')"
              class="lang-input"
            >
              <template #prepend>{{ $t('systemSettings.siteTitle.zh') }}</template>
            </el-input>
            <el-input
              v-model="formData.siteTitle.en"
              :placeholder="$t('systemSettings.siteTitle.enPlaceholder')"
              class="lang-input"
              style="margin-top: 12px"
            >
              <template #prepend>{{ $t('systemSettings.siteTitle.en') }}</template>
            </el-input>
          </div>
          <div class="form-item-description">
            {{ $t('systemSettings.siteTitle.description') }}
          </div>
        </el-form-item>
      </el-card>

      <!-- User Permissions Section -->
      <el-card class="settings-section" :header="$t('systemSettings.sections.userPermissions')">
        <el-form-item :label="$t('systemSettings.allowLatencyMonitoring.label')" prop="allowUserLatencyMonitoring">
          <el-switch
            v-model="formData.allowUserLatencyMonitoring"
            :active-text="$t('common.enabled')"
            :inactive-text="$t('common.disabled')"
          />
          <div class="form-item-description">
            {{ $t('systemSettings.allowLatencyMonitoring.description') }}
          </div>
        </el-form-item>
      </el-card>

      <!-- Save Button -->
      <div class="form-actions">
        <el-button type="primary" @click="saveSettings" :loading="saving" size="large">
          <el-icon><Check /></el-icon>
          {{ $t('common.save') }}
        </el-button>
        <el-button @click="resetForm" :disabled="saving" size="large">
          <el-icon><RefreshLeft /></el-icon>
          {{ $t('common.reset') }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, RefreshLeft } from '@element-plus/icons-vue'
import { getSystemSettings, updateSystemSettings } from '../api'

const loading = ref(false)
const saving = ref(false)
const settingsForm = ref(null)

const formData = reactive({
  siteTitle: {
    zh: '',
    en: ''
  },
  allowUserLatencyMonitoring: true
})

const originalData = reactive({
  siteTitle: {
    zh: '',
    en: ''
  },
  allowUserLatencyMonitoring: true
})

const formRules = {
  siteTitle: [
    {
      validator: (rule, value, callback) => {
        if (!value.zh || !value.zh.trim()) {
          callback(new Error('请输入中文网站标题'))
        } else if (!value.en || !value.en.trim()) {
          callback(new Error('请输入英文网站标题'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

const loadSettings = async () => {
  loading.value = true
  try {
    const response = await getSystemSettings()
    const settings = response.data.settings
    
    // Parse site title
    if (settings.site_title) {
      try {
        const siteTitle = JSON.parse(settings.site_title)
        formData.siteTitle.zh = siteTitle.zh || '莹火虫的世界'
        formData.siteTitle.en = siteTitle.en || "Firefly's World"
      } catch (error) {
        console.error('Error parsing site title:', error)
        formData.siteTitle.zh = '莹火虫的世界'
        formData.siteTitle.en = "Firefly's World"
      }
    }
    
    // Parse user permissions
    if (settings.allow_user_latency_monitoring !== undefined) {
      formData.allowUserLatencyMonitoring = settings.allow_user_latency_monitoring === 'true'
    }
    
    // Store original data for reset
    Object.assign(originalData, JSON.parse(JSON.stringify(formData)))
    
  } catch (error) {
    console.error('Failed to load system settings:', error)
    ElMessage.error('加载系统设置失败')
  } finally {
    loading.value = false
  }
}

const saveSettings = async () => {
  if (!settingsForm.value) return
  
  try {
    await settingsForm.value.validate()
  } catch (error) {
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确认保存这些系统设置吗？',
      '确认保存',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
  } catch {
    return
  }
  
  saving.value = true
  try {
    const settingsToSave = {
      site_title: JSON.stringify({
        zh: formData.siteTitle.zh.trim(),
        en: formData.siteTitle.en.trim()
      }),
      allow_user_latency_monitoring: formData.allowUserLatencyMonitoring.toString()
    }
    
    await updateSystemSettings({ settings: settingsToSave })
    
    // Update original data
    Object.assign(originalData, JSON.parse(JSON.stringify(formData)))
    
    ElMessage.success('系统设置保存成功')
  } catch (error) {
    console.error('Failed to save system settings:', error)
    ElMessage.error('保存系统设置失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, JSON.parse(JSON.stringify(originalData)))
  settingsForm.value?.clearValidate()
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.system-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  color: var(--theme-text-primary);
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  color: var(--theme-text-regular);
  font-size: 14px;
  margin: 0;
}

.settings-form {
  background: transparent;
}

.settings-section {
  margin-bottom: 24px;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-base);
  background: var(--theme-bg-primary);
}

.settings-section :deep(.el-card__header) {
  background: var(--theme-fill-lighter);
  border-bottom: 1px solid var(--theme-border-light);
  font-weight: 600;
  color: var(--theme-text-primary);
}

.settings-section :deep(.el-card__body) {
  padding: 24px;
}

.multilang-input {
  width: 100%;
}

.lang-input {
  width: 100%;
}

.lang-input :deep(.el-input-group__prepend) {
  background-color: var(--theme-fill-light);
  color: var(--theme-text-regular);
  border-color: var(--theme-border-base);
  min-width: 60px;
  text-align: center;
}

.form-item-description {
  margin-top: 8px;
  font-size: 12px;
  color: var(--theme-text-placeholder);
  line-height: 1.4;
}

.form-actions {
  text-align: center;
  margin-top: 40px;
  padding: 24px;
  background: var(--theme-fill-lighter);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--theme-border-light);
}

.form-actions .el-button {
  margin: 0 8px;
  min-width: 120px;
}

/* Dark theme adjustments */
:global(.dark) .settings-section {
  background: var(--theme-bg-secondary);
  border-color: var(--theme-border-base);
}

:global(.dark) .settings-section :deep(.el-card__header) {
  background: var(--theme-fill-dark);
  border-color: var(--theme-border-base);
  color: var(--theme-text-primary);
}

:global(.dark) .lang-input :deep(.el-input-group__prepend) {
  background-color: var(--theme-fill-dark);
  color: var(--theme-text-regular);
  border-color: var(--theme-border-base);
}

:global(.dark) .form-actions {
  background: var(--theme-fill-dark);
  border-color: var(--theme-border-base);
}

/* Responsive */
@media (max-width: 768px) {
  .system-settings {
    padding: 16px;
  }
  
  .settings-form :deep(.el-form-item__label) {
    width: 100% !important;
    text-align: left;
    margin-bottom: 8px;
  }
  
  .settings-form :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
  
  .form-actions .el-button {
    margin: 4px;
    min-width: 100px;
  }
}
</style>