<template>
  <div class="license-renewal">
    <div class="header">
      <h2>{{ t('pages.license.title') }}</h2>
      <el-button 
        type="primary" 
        :icon="Refresh" 
        @click="refreshLicenseStatus"
        :loading="refreshing"
      >
        {{ t('pages.license.refreshStatus') }}
      </el-button>
    </div>

    <!-- License Status Card -->
    <el-card class="license-status-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h3>{{ t('pages.license.currentLicenseStatus') }}</h3>
          <el-tag 
            :type="licenseStatusType" 
            size="large"
            :effect="isExpired ? 'dark' : 'light'"
          >
            {{ licenseStatusText }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border v-if="softwareLicenseData">
        <el-descriptions-item :label="t('pages.license.licenseExpires')">
          <span :class="{ 'text-danger': isExpired, 'text-warning': isExpiringSoon }">
            {{ formatDate(softwareLicenseData.valid_until) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.daysRemaining')">
          <span :class="{ 'text-danger': isExpired, 'text-warning': isExpiringSoon }">
            {{ daysRemaining }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.lastRenewal')">
          {{ lastRenewalDate || 'N/A' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.licenseId')">
          {{ softwareLicenseData.license_id || 'N/A' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.monthlyRate')" v-if="softwareLicenseData.monthly_rate">
          ¥{{ softwareLicenseData.monthly_rate }}
        </el-descriptions-item>
        <el-descriptions-item v-if="softwareLicenseData.entitlements">
          <template #label>
            <el-tooltip :content="t('pages.license.maxWorkersTooltip')" placement="top">
              <span>{{ t('pages.license.maxWorkers') }} <el-icon><QuestionFilled /></el-icon></span>
            </el-tooltip>
          </template>
          {{ softwareLicenseData.entitlements.max_workers || 'N/A' }}
        </el-descriptions-item>
        <el-descriptions-item v-if="softwareLicenseData.entitlements">
          <template #label>
            <el-tooltip :content="t('pages.license.maxSubscriptionNumberTooltip')" placement="top">
              <span>{{ t('pages.license.maxSubscriptionNumber') }} <el-icon><QuestionFilled /></el-icon></span>
            </el-tooltip>
          </template>
          {{ softwareLicenseData.entitlements.max_subscription_number || 'N/A' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- Expiration Warning -->
      <el-alert
        v-if="isExpired"
        :title="t('pages.license.licenseExpired')"
        type="error"
        :description="t('pages.license.licenseExpiredDescription')"
        show-icon
        :closable="false"
        class="mt-4"
      />
      <el-alert
        v-else-if="isExpiringSoon"
        :title="t('pages.license.licenseExpiringSoon')"
        type="warning"
        :description="t('pages.license.licenseExpiringSoonDescription', { days: daysRemaining })"
        show-icon
        :closable="false"
        class="mt-4"
      />
    </el-card>

    <!-- Renewal Code Input Card -->
    <el-card class="renewal-card">
      <template #header>
        <h3>{{ t('pages.license.applyRenewalCode') }}</h3>
      </template>

      <el-form
        ref="renewalForm"
        :model="renewalData"
        :rules="renewalRules"
        label-width="120px"
        @submit.prevent="handleRenewal"
      >
        <el-form-item :label="t('forms.labels.renewalCode')" prop="renewalCode">
          <el-input
            v-model="renewalData.renewalCode"
            :placeholder="t('forms.placeholders.enterRenewalCode')"
            maxlength="100"
            show-word-limit
            :disabled="applying"
          />
          <div class="form-hint">
            {{ t('pages.license.renewalCodeHint') }}
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleRenewal"
            :loading="applying"
            :disabled="!renewalData.renewalCode.trim()"
          >
            {{ t('actions.applyRenewalCode') }}
          </el-button>
          <el-button @click="resetForm">
            {{ t('actions.clear') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Renewal History Card -->
    <el-card class="history-card" v-if="renewalHistory.length > 0">
      <template #header>
        <h3>{{ t('pages.license.renewalHistory') }}</h3>
      </template>

      <el-table :data="renewalHistory" style="width: 100%">
        <el-table-column prop="date" :label="t('forms.labels.applicationTime')" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.date) }}
          </template>
        </el-table-column>
        <el-table-column prop="code_id" :label="t('forms.labels.codeId')" width="250" />
        <el-table-column prop="extended_days" :label="t('forms.labels.extendedDays')" width="120" />
        <el-table-column prop="amount" :label="t('forms.labels.renewalAmount')" width="120">
          <template #default="scope">
            <span v-if="scope.row.amount">{{ formatCurrency(scope.row.amount) }}</span>
            <span v-else class="text-muted">N/A</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="t('forms.labels.status')">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Renewal Request Card -->
    <el-card class="renewal-request-card" v-if="showRenewalRequestsForTesting" v-loading="subscriptionLoading">
      <template #header>
        <div class="card-header">
          <h3>{{ t('pages.license.renewalRequests') }}</h3>
          <el-button
            type="primary"
            @click="openCreateRequestDialog"
            :disabled="safeRenewalRequests.some(req => ['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(req.status))"
          >
            {{ t('pages.license.submitRenewalRequest') }}
          </el-button>
        </div>
      </template>

      <div v-if="safeRenewalRequests.length === 0" class="empty-state">
        <p>{{ t('pages.license.noRenewalRequests') }}</p>
      </div>

      <el-table v-else :data="safeRenewalRequests" style="width: 100%">
        <el-table-column prop="id" :label="t('pages.license.requestId')" width="120">
          <template #default="scope">
            {{ scope.row.id.substring(0, 8) }}...
          </template>
        </el-table-column>
        <el-table-column prop="requestedDuration" :label="t('pages.license.renewalDuration')" width="100">
          <template #default="scope">
            {{ scope.row.requestedDuration }}{{ t('pages.license.renewalRequestForm.months') }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentAmount" :label="t('pages.license.paymentAmount')" width="120">
          <template #default="scope">
            ¥{{ scope.row.paymentAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" :label="t('pages.license.paymentMethod')" width="120">
          <template #default="scope">
            {{ scope.row.paymentMethod === 'alipay_hongbao' ? t('pages.license.alipayHongbao') : t('pages.license.cryptocurrency') }}
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="t('forms.labels.status')" width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" :label="t('forms.labels.applicationTime')" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('forms.labels.actions')" width="160">
          <template #default="scope">
            <el-button 
              type="info" 
              size="small" 
              @click="showRequestDetail(scope.row)"
            >
              {{ t('pages.license.viewDetails') }}
            </el-button>
            <el-button
              v-if="['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(scope.row.status)"
              type="danger"
              size="small"
              @click="handleCancelRequest(scope.row.id)"
            >
              {{ t('actions.cancel') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Renewal Request Permission Notice -->
    <el-card class="renewal-request-notice" v-else-if="!subscriptionLoading && !showRenewalRequestsForTesting">
      <template #header>
        <h3>{{ t('pages.license.renewalRequests') }}</h3>
      </template>

      <el-alert
        :title="t('pages.license.notificationMessages.featureNotAvailable')"
        type="info"
        :description="t('pages.license.notificationMessages.permissionRequired')"
        show-icon
        :closable="false"
      />
    </el-card>

    <!-- Loading placeholder for subscription data -->
    <el-card class="renewal-request-loading" v-else-if="subscriptionLoading">
      <template #header>
        <h3>{{ t('pages.license.renewalRequests') }}</h3>
      </template>

      <div class="loading-placeholder">
        <el-skeleton :rows="3" animated />
      </div>
    </el-card>

    <!-- Create Renewal Request Dialog -->
    <el-dialog
      v-model="showCreateRequestDialog"
      :title="t('pages.license.renewalRequestForm.title')"
      width="600px"
    >
      <el-form
        ref="renewalRequestForm"
        :model="renewalRequestData"
        :rules="renewalRequestRules"
        label-width="120px"
      >
        <el-form-item :label="t('pages.license.renewalDuration')" prop="requestedDuration">
          <el-select
            v-model="renewalRequestData.requestedDuration"
            @change="calculatePrice"
            :placeholder="t('pages.license.renewalRequestValidation.selectDurationPlaceholder')"
          >
            <el-option :label="'1' + t('pages.license.renewalRequestForm.months')" :value="1" />
            <el-option :label="'3' + t('pages.license.renewalRequestForm.months')" :value="3" />
            <el-option :label="'6' + t('pages.license.renewalRequestForm.months')" :value="6" />
            <el-option :label="'12' + t('pages.license.renewalRequestForm.months')" :value="12" />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('pages.license.renewalRequestForm.price')">
          <div class="price-display">
            <span v-if="currentPrice">
              {{ t('pages.license.renewalRequestForm.monthlyRate') }}: ¥{{ currentPrice.monthlyRate }} × {{ renewalRequestData.requestedDuration }}{{ t('pages.license.renewalRequestForm.months') }} =
              <strong>¥{{ currentPrice.totalPrice }}</strong>
            </span>
            <span v-else>{{ t('pages.license.calculating') }}</span>
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.license.paymentMethod')" prop="paymentMethod">
          <el-radio-group v-model="renewalRequestData.paymentMethod">
            <el-radio value="alipay_hongbao">{{ t('pages.license.alipayHongbao') }}</el-radio>
            <el-radio value="cryptocurrency">{{ t('pages.license.cryptocurrency') }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :label="t('pages.license.paymentProof')" prop="paymentProof">
          <el-input
            v-model="renewalRequestData.paymentProof"
            type="textarea"
            :rows="3"
            :placeholder="renewalRequestData.paymentMethod === 'alipay_hongbao' ?
              t('pages.license.renewalRequestForm.alipayPlaceholder') :
              t('pages.license.renewalRequestForm.cryptoPlaceholder')"
            maxlength="1000"
            show-word-limit
          />
          <div class="form-hint">
            {{ renewalRequestData.paymentMethod === 'alipay_hongbao' ?
                t('pages.license.renewalRequestForm.alipayHint') :
                t('pages.license.renewalRequestForm.cryptoHint') }}
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.license.customerMessage')">
          <el-input
            v-model="renewalRequestData.customerMessage"
            type="textarea"
            :rows="2"
            :placeholder="t('pages.license.renewalRequestForm.optionalMessage')"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateRequestDialog = false">{{ t('actions.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleCreateRenewalRequest"
          :loading="creatingRequest"
          :disabled="!isRenewalRequestFormValid"
        >
          {{ t('pages.license.renewalRequestForm.submitRequest') }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Renewal Request Detail Dialog -->
    <el-dialog 
      v-model="showRequestDetailDialog" 
      :title="t('pages.license.renewalRequestDetail.title')" 
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRequest">
        <!-- Request Information -->
        <el-descriptions :column="2" border class="mb-4">
          <el-descriptions-item :label="t('pages.license.requestId')">
            {{ selectedRequest.id }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('forms.labels.status')">
            <el-tag
              :type="getStatusType(selectedRequest.status)"
            >
              {{ getStatusText(selectedRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.license.renewalDuration')">
            {{ selectedRequest.requestedDuration }}{{ t('pages.license.renewalRequestForm.months') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.license.paymentAmount')">
            ¥{{ selectedRequest.paymentAmount }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.license.paymentMethod')">
            {{ selectedRequest.paymentMethod === 'alipay_hongbao' ? t('pages.license.alipayHongbao') : t('pages.license.cryptocurrency') }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.license.renewalRequestDetail.submissionTime')">
            {{ formatDate(selectedRequest.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('pages.license.paymentProof')" span="2">
            <pre class="payment-proof">{{ selectedRequest.paymentProof }}</pre>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.customerMessage" :label="t('pages.license.customerMessage')" span="2">
            {{ selectedRequest.customerMessage }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.processedAt" :label="t('pages.license.processedAt')" span="2">
            {{ formatDate(selectedRequest.processedAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.adminNotes" :label="t('pages.license.adminNotes')" span="2">
            <div class="admin-notes">{{ selectedRequest.adminNotes }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- Conversation History -->
        <div class="conversation-section">
          <h4>{{ t('pages.license.conversationHistory') }}</h4>
          
          <div class="conversation-container" v-loading="loadingMessages">
            <div v-if="requestMessages.length === 0" class="no-messages">
              <el-empty 
                :description="t('pages.license.noMessagesYet')" 
                :image-size="60"
              />
            </div>
            
            <div v-else class="messages-list">
              <div 
                v-for="message in requestMessages" 
                :key="message.id"
                :class="['message-item', { 'customer-message': message.messageType === 'CUSTOMER', 'admin-message': message.messageType === 'ADMIN' }]"
              >
                <div class="message-header">
                  <span class="message-sender">
                    {{ message.messageType === 'CUSTOMER' ? t('pages.license.customer') : t('pages.license.admin') }}
                  </span>
                  <span class="message-time">{{ formatDate(message.createdAt) }}</span>
                </div>
                <div class="message-content">{{ message.content }}</div>
              </div>
            </div>
          </div>

          <!-- Send Message (only if request is still active) -->
          <div v-if="['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(selectedRequest.status)" class="send-message-section">
            <el-form label-position="top">
              <el-form-item :label="t('pages.license.sendMessage')">
                <el-input
                  v-model="newMessage"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('pages.license.enterMessage')"
                  maxlength="500"
                  show-word-limit
                  :disabled="sendingMessage"
                  class="message-textarea"
                />
              </el-form-item>
              <el-form-item>
                <div class="message-actions">
                  <el-button
                    type="primary"
                    @click="handleSendMessage"
                    :loading="sendingMessage"
                    :disabled="!newMessage.trim()"
                    class="send-button"
                  >
                    {{ t('pages.license.sendMessage') }}
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
          
          <el-alert
            v-else
            :title="t('pages.license.requestClosed')"
            :description="t('pages.license.requestClosedDescription')"
            type="info"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="showRequestDetailDialog = false">{{ t('pages.license.renewalRequestForm.close') }}</el-button>
        <el-button
          v-if="selectedRequest && ['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(selectedRequest.status)"
          type="danger"
          @click="handleCancelRequest(selectedRequest.id); showRequestDetailDialog = false"
        >
          {{ t('pages.license.cancelRequest') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSubscriptionStore } from '../stores/subscription'
import { storeToRefs } from 'pinia'
import { Refresh, QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  applyRenewalCode, 
  getRenewalHistory, 
  getSoftwareLicenseInfo,
  calculateRenewalPrice,
  createRenewalRequest,
  getRenewalRequests,
  cancelRenewalRequest,
  sendRenewalRequestMessage,
  getRenewalRequestMessages
} from '../api'

const { t } = useI18n()

const subscriptionStore = useSubscriptionStore()
const { allowForwardEndpoint, loading: subscriptionLoading } = storeToRefs(subscriptionStore)

// State
const loading = ref(false)
const refreshing = ref(false)
const applying = ref(false)
const renewalHistory = ref([])
const renewalForm = ref(null)
const renewalRequestForm = ref(null)
const lastRenewalDate = ref(null)
const softwareLicenseData = ref(null)

// 续费请求相关状态
const renewalRequests = ref([])
const showCreateRequestDialog = ref(false)
const showRequestDetailDialog = ref(false)
const selectedRequest = ref(null)
const creatingRequest = ref(false)
const calculatingPrice = ref(false)
const currentPrice = ref(null)

// 消息相关状态
const requestMessages = ref([])
const loadingMessages = ref(false)
const sendingMessage = ref(false)
const newMessage = ref('')

// Form data
const renewalData = reactive({
  renewalCode: ''
})

// 续费请求表单数据
const renewalRequestData = reactive({
  requestedDuration: 1, // 默认1个月
  paymentMethod: 'alipay_hongbao', // 默认支付宝口令红包
  paymentAmount: '',
  paymentProof: '',
  customerMessage: ''
})

// Form validation rules
const renewalRules = {
  renewalCode: [
    { required: true, message: () => t('forms.validation.pleaseEnterRenewalCode'), trigger: 'blur' },
    { min: 5, message: () => t('forms.validation.renewalCodeMinLength'), trigger: 'blur' }
  ]
}

// 续费请求表单验证规则
const renewalRequestRules = {
  requestedDuration: [
    { required: true, message: () => t('pages.license.renewalRequestValidation.selectDuration'), trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: () => t('pages.license.renewalRequestValidation.selectPaymentMethod'), trigger: 'change' }
  ],
  paymentProof: [
    { required: true, message: () => t('pages.license.renewalRequestValidation.enterPaymentProof'), trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (!value) return callback()

        if (renewalRequestData.paymentMethod === 'cryptocurrency') {
          // 加密货币凭证验证：应包含交易哈希或地址
          if (value.length < 20) {
            callback(new Error(t('pages.license.paymentProofValidation.cryptocurrencyIncomplete')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// Computed properties
const isExpired = computed(() => {
  if (!softwareLicenseData.value?.valid_until) return false
  return new Date() > new Date(softwareLicenseData.value.valid_until)
})

const isExpiringSoon = computed(() => {
  if (isExpired.value) return false
  return daysRemaining.value <= 15
})

const daysRemaining = computed(() => {
  if (!softwareLicenseData.value?.valid_until) return 0
  const now = new Date()
  const expiry = new Date(softwareLicenseData.value.valid_until)
  const diffTime = expiry.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

const licenseStatusType = computed(() => {
  if (isExpired.value) return 'danger'
  if (isExpiringSoon.value) return 'warning'
  return 'success'
})

const licenseStatusText = computed(() => {
  if (isExpired.value) return t('status.expired')
  if (isExpiringSoon.value) return t('status.expiringSoon')
  return t('status.active')
})

// 检查续费请求表单是否有效
const isRenewalRequestFormValid = computed(() => {
  return renewalRequestData.requestedDuration &&
         renewalRequestData.paymentMethod &&
         renewalRequestData.paymentProof &&
         renewalRequestData.paymentProof.trim().length > 0 &&
         renewalRequestData.paymentAmount &&
         currentPrice.value
})

// Safe computed property for renewal requests array
const safeRenewalRequests = computed(() => {
  const requests = renewalRequests.value
  // Ensure we always return an array, even if requests is null, undefined, or not an array
  if (!requests || !Array.isArray(requests)) {
    return []
  }
  return requests
})

// Whether to show renewal requests based on subscription permission
const showRenewalRequestsForTesting = computed(() => {
  return allowForwardEndpoint.value
})

// Methods
const getStatusType = (status) => {
  switch (status) {
    case 'PENDING':
      return 'warning'
    case 'CUSTOMER_REPLIED':
      return 'info'
    case 'ADMIN_REPLIED':
      return 'primary'
    case 'PROCESSED':
      return 'success'
    case 'CANCELLED':
    case 'CLOSED_BY_ADMIN':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'PENDING':
      return t('pages.license.statusPending')
    case 'CUSTOMER_REPLIED':
      return t('pages.license.statusCustomerReplied')
    case 'ADMIN_REPLIED':
      return t('pages.license.statusAdminReplied')
    case 'PROCESSED':
      return t('pages.license.statusProcessed')
    case 'CANCELLED':
      return t('pages.license.statusCancelled')
    case 'CLOSED_BY_ADMIN':
      return t('pages.license.statusClosedByAdmin')
    default:
      return status
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString()
}

const formatCurrency = (amount) => {
  if (!amount) return 'N/A'
  // Convert to number and format with 2 decimal places
  const numericAmount = parseFloat(amount)
  if (isNaN(numericAmount)) return amount
  return '¥' + numericAmount.toFixed(2)
}

const refreshLicenseStatus = async () => {
  refreshing.value = true
  try {
    await loadSoftwareLicenseInfo()
    await loadRenewalHistory()
    ElMessage.success(t('messages.success.licenseRefreshed'))
  } catch (error) {
    ElMessage.error(t('messages.error.failedToRefreshLicense'))
  } finally {
    refreshing.value = false
  }
}

const handleRenewal = async () => {
  if (!renewalForm.value) return
  
  await renewalForm.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      await ElMessageBox.confirm(
        t('pages.license.confirmRenewal'),
        t('pages.license.confirmRenewalTitle'),
        {
          confirmButtonText: t('pages.license.applyCode'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
      
      applying.value = true
      
      await applyRenewalCode(renewalData.renewalCode)
      
      ElMessage.success(t('messages.success.renewalApplied'))
      
      // Refresh data
      await refreshLicenseStatus()
      
      // Reset form
      resetForm()
      
    } catch (error) {
      if (error === 'cancel') return
      ElMessage.error(error.message || t('messages.error.failedToApplyRenewal'))
    } finally {
      applying.value = false
    }
  })
}

const resetForm = () => {
  renewalData.renewalCode = ''
  if (renewalForm.value) {
    renewalForm.value.resetFields()
  }
}

const loadSoftwareLicenseInfo = async () => {
  try {
    const { data } = await getSoftwareLicenseInfo()
    softwareLicenseData.value = data
  } catch (error) {
    throw error
  }
}

const loadRenewalHistory = async () => {
  try {
    const { data } = await getRenewalHistory()
    // Backend returns { history: [...] }
    renewalHistory.value = data.history || []
    
    // Set last renewal date
    if (renewalHistory.value.length > 0) {
      lastRenewalDate.value = renewalHistory.value[0].date
    }
  } catch (error) {
    // ignore
  }
}

// 续费请求相关方法
const loadRenewalRequests = async () => {
  try {
    const { data } = await getRenewalRequests()
    // Ensure we always set an array, even if API returns unexpected data
    const requestsData = data?.data?.data
    renewalRequests.value = Array.isArray(requestsData) ? requestsData : []
    console.log('Loaded renewal requests:', renewalRequests.value)
  } catch (error) {
    // Always ensure renewalRequests is an empty array on error
    renewalRequests.value = []
    // Don't show error message if it's a permission issue (403)
    if (error.response?.status !== 403) {
      ElMessage.error(t('pages.license.notificationMessages.loadRenewalRequestsFailed') + ': ' + (error.message || t('common.unknownError')))
    }
  }
}

const calculatePrice = async () => {
  calculatingPrice.value = true
  try {
    const { data } = await calculateRenewalPrice(renewalRequestData.requestedDuration)
    currentPrice.value = data
    renewalRequestData.paymentAmount = data.totalPrice
  } catch (error) {
    ElMessage.error(t('pages.license.failedToCalculatePrice'))
  } finally {
    calculatingPrice.value = false
  }
}

const handleCreateRenewalRequest = async () => {
  if (!renewalRequestForm.value) return

  // 验证表单
  await renewalRequestForm.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning(t('pages.license.renewalRequestForm.formValidationFailed'))
      return
    }

    // 检查是否已有活跃请求
    const hasActiveRequest = safeRenewalRequests.value.some(req => ['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(req.status))
    if (hasActiveRequest) {
      ElMessage.warning(t('pages.license.hasActiveRequest'))
      return
    }

    // 验证支付金额是否匹配
    if (currentPrice.value && renewalRequestData.paymentAmount !== currentPrice.value.totalPrice) {
      ElMessage.warning(t('pages.license.renewalRequestForm.paymentAmountMismatch'))
      return
    }

    creatingRequest.value = true
    try {
      await createRenewalRequest({
        paymentMethod: renewalRequestData.paymentMethod,
        paymentAmount: renewalRequestData.paymentAmount,
        paymentProof: renewalRequestData.paymentProof,
        customerMessage: renewalRequestData.customerMessage,
        requestedDuration: renewalRequestData.requestedDuration
      })

      ElMessage.success(t('pages.license.renewalRequestCreated'))
      showCreateRequestDialog.value = false
      await loadRenewalRequests()

      // 重置表单
      resetRenewalRequestForm()
    } catch (error) {
      ElMessage.error(error.message || t('pages.license.failedToCreateRequest'))
    } finally {
      creatingRequest.value = false
    }
  })
}

const handleCancelRequest = async (requestId) => {
  try {
    await ElMessageBox.confirm(
      t('pages.license.confirmCancelRequest'),
      t('pages.license.confirmCancel'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    await cancelRenewalRequest(requestId)
    ElMessage.success(t('pages.license.renewalRequestCancelled'))
    await loadRenewalRequests()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || t('pages.license.failedToCancelRequest'))
  }
}

const showRequestDetail = async (request) => {
  selectedRequest.value = request
  showRequestDetailDialog.value = true
  newMessage.value = ''
  requestMessages.value = []
  
  // 加载消息历史
  await loadRequestMessages(request.id)
}

// 消息相关方法
const loadRequestMessages = async (requestId) => {
  loadingMessages.value = true
  try {
    const { data } = await getRenewalRequestMessages(requestId)
    requestMessages.value = data?.data || []
  } catch (error) {
    console.error('Failed to load messages:', error)
    // 不显示错误消息，因为可能是权限问题
    requestMessages.value = []
  } finally {
    loadingMessages.value = false
  }
}

const handleSendMessage = async () => {
  if (!newMessage.value.trim() || !selectedRequest.value) return
  
  sendingMessage.value = true
  try {
    await sendRenewalRequestMessage(selectedRequest.value.id, newMessage.value.trim())
    ElMessage.success(t('pages.license.messageSent'))
    newMessage.value = ''
    
    // 重新加载消息历史
    await loadRequestMessages(selectedRequest.value.id)
  } catch (error) {
    ElMessage.error(error.message || t('pages.license.failedToSendMessage'))
  } finally {
    sendingMessage.value = false
  }
}

const resetRenewalRequestForm = () => {
  renewalRequestData.paymentProof = ''
  renewalRequestData.customerMessage = ''
  renewalRequestData.requestedDuration = 1
  renewalRequestData.paymentMethod = 'alipay_hongbao'
  renewalRequestData.paymentAmount = ''
  currentPrice.value = null

  if (renewalRequestForm.value) {
    renewalRequestForm.value.resetFields()
  }
}

const openCreateRequestDialog = async () => {
  // 检查是否已有活跃请求
  const hasActiveRequest = safeRenewalRequests.value.some(req => ['PENDING', 'CUSTOMER_REPLIED', 'ADMIN_REPLIED'].includes(req.status))
  if (hasActiveRequest) {
    ElMessage.warning(t('pages.license.hasActiveRequest'))
    return
  }

  // 重置表单到默认值
  resetRenewalRequestForm()
  showCreateRequestDialog.value = true
  await calculatePrice()
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  try {
    // Ensure subscription data is loaded first
    await subscriptionStore.fetchData()

    await loadSoftwareLicenseInfo()
    await loadRenewalHistory()

    // Only load renewal requests if user has permission (using debug permission for testing)
    if (showRenewalRequestsForTesting.value) {
      await loadRenewalRequests()
    }
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.license-renewal {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
}

.license-status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.renewal-card {
  margin-bottom: 20px;
}

.history-card {
  margin-bottom: 20px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

.mt-4 {
  margin-top: 16px;
}

/* Renewal Request Card Styles */
.renewal-request-card,
.renewal-request-notice,
.renewal-request-loading {
  margin-bottom: 20px;
}

.loading-placeholder {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #909399;
  font-size: 14px;
}

.price-display {
  font-size: 14px;
  color: #606266;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.price-display strong {
  color: #e6a23c;
  font-size: 16px;
}

.payment-proof {
  background: #f9f9f9;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 120px;
  overflow-y: auto;
}

.admin-notes {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 8px;
  font-size: 13px;
  line-height: 1.5;
  color: #1e40af;
}

/* Dialog Styles */
.el-dialog .el-form-item {
  margin-bottom: 18px;
}

.el-dialog .el-form-item__label {
  font-weight: 500;
}

/* Conversation Styles */
.conversation-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.conversation-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.conversation-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid transparent;
  max-width: 70%;
  width: fit-content;
  word-wrap: break-word;
}

.message-item.customer-message {
  background: #e7f3ff;
  border-color: #b3d8ff;
  align-self: flex-start;
}

.message-item.admin-message {
  background: #f0f9ff;
  border-color: #bfdbfe;
  align-self: flex-end;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.message-sender {
  font-weight: 600;
  color: #409eff;
}

.message-time {
  color: #909399;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

.send-message-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.send-message-section .el-form-item {
  margin-bottom: 16px;
}

.send-message-section .el-form-item:last-child {
  margin-bottom: 0;
}

.message-textarea {
  width: 100%;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.send-button {
  min-width: 120px;
}

.mb-4 {
  margin-bottom: 16px;
}

/* Table responsive styles */
@media (max-width: 1200px) {
  .renewal-request-card .el-table-column:nth-child(n+4) {
    display: none;
  }
}

@media (max-width: 768px) {
  .license-renewal {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .renewal-request-card .el-table {
    font-size: 12px;
  }

  .empty-state {
    padding: 20px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5px auto !important;
  }

  .payment-proof {
    font-size: 10px;
    max-height: 80px;
  }

  .message-actions {
    justify-content: stretch;
  }

  .send-button {
    width: 100%;
    height: 40px;
  }

  .message-item {
    max-width: 85%;
  }
}
</style>