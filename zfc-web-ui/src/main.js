import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/themes.css'
import router from './router'
import App from './App.vue'
import { useThemeStore } from './stores/theme'
import i18n from './locales'
import { getSiteTitle } from './api'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.use(i18n)

// Initialize theme after pinia is available
const themeStore = useThemeStore()
themeStore.initializeTheme()

// Set default title and load dynamic title
document.title = 'Loading...'

// Load site title asynchronously
getSiteTitle()
  .then(response => {
    const titles = response.data
    const currentLang = i18n.global.locale.value || 'zh'
    document.title = titles[currentLang] || titles.zh || 'Firefly\'s World'
  })
  .catch(error => {
    console.warn('Failed to load site title:', error)
    // Keep default title or set fallback
    document.title = 'Firefly\'s World'
  })

app.mount('#app')
