/* Theme CSS Custom Properties */

/* Base theme variables that work with Element Plus */
:root {
  /* Transition for smooth theme changes */
  --theme-transition: all 0.3s ease;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-modal: 2000;
  --z-notification: 3000;
  
  /* Border radius */
  --border-radius-small: 4px;
  --border-radius-base: 6px;
  --border-radius-large: 8px;
  
  /* Shadows */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.2);

  /* Loading animation variables */
  --loading-spinner-color: #409eff;
  --loading-spinner-color-light: #79bbff;
  --loading-background: rgba(255, 255, 255, 0.9);
  --loading-background-overlay: rgba(255, 255, 255, 0.8);
  --loading-text-color: #606266;
  --loading-mask-opacity: 0.9;
  --loading-spinner-size: 42px;
  --loading-spinner-border-width: 4px;
}

/* Light Theme */
:root,
:root.light-theme,
[data-theme="light"] {
  /* Primary colors (keeping Element Plus primary) */
  --theme-primary: #409eff;
  --theme-primary-light: #79bbff;
  --theme-primary-lighter: #a0cfff;
  --theme-primary-dark: #337ecc;
  
  /* Background colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f7fa;
  --theme-bg-tertiary: #fafbfc;
  --theme-bg-overlay: rgba(255, 255, 255, 0.9);
  
  /* Text colors */
  --theme-text-primary: #303133;
  --theme-text-regular: #606266;
  --theme-text-secondary: #909399;
  --theme-text-placeholder: #c0c4cc;
  --theme-text-disabled: #c0c4cc;
  
  /* Border colors */
  --theme-border-light: #ebeef5;
  --theme-border-base: #dcdfe6;
  --theme-border-dark: #d4d7de;
  --theme-border-darker: #cdd0d6;
  
  /* Fill colors */
  --theme-fill-extra-light: #fafcff;
  --theme-fill-light: #f0f2f5;
  --theme-fill-base: #e4e7ed;
  --theme-fill-dark: #d3d4d6;
  --theme-fill-darker: #bcbec2;
  
  /* Status colors */
  --theme-success: #67c23a;
  --theme-warning: #e6a23c;
  --theme-danger: #f56c6c;
  --theme-info: #909399;
  
  /* Component specific */
  --theme-header-bg: #ffffff;
  --theme-sidebar-bg: #ffffff;
  --theme-card-bg: #ffffff;
  --theme-input-bg: #ffffff;
  --theme-button-bg: #ffffff;
  
  /* Shadows for light theme */
  --theme-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --theme-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --theme-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Dark Theme */
:root.dark-theme,
[data-theme="dark"] {
  /* Primary colors (adjusted for dark theme) */
  --theme-primary: #409eff;
  --theme-primary-light: #529eff;
  --theme-primary-lighter: #66b1ff;
  --theme-primary-dark: #3375b9;
  
  /* Background colors */
  --theme-bg-primary: #1a1a1a;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #0f0f0f;
  --theme-bg-overlay: rgba(26, 26, 26, 0.9);
  
  /* Text colors */
  --theme-text-primary: #e5eaf3;
  --theme-text-regular: #cfd3dc;
  --theme-text-secondary: #a3a6ad;
  --theme-text-placeholder: #6c6e72;
  --theme-text-disabled: #6c6e72;
  
  /* Border colors */
  --theme-border-light: #2c2c2c;
  --theme-border-base: #414243;
  --theme-border-dark: #58585b;
  --theme-border-darker: #6c6e72;
  
  /* Fill colors */
  --theme-fill-extra-light: #262727;
  --theme-fill-light: #2d2d2d;
  --theme-fill-base: #34353a;
  --theme-fill-dark: #3b3c40;
  --theme-fill-darker: #424243;
  
  /* Status colors (adjusted for dark theme) */
  --theme-success: #67c23a;
  --theme-warning: #e6a23c;
  --theme-danger: #f56c6c;
  --theme-info: #909399;
  
  /* Component specific */
  --theme-header-bg: #1f1f1f;
  --theme-sidebar-bg: #1a1a1a;
  --theme-card-bg: #1f1f1f;
  --theme-input-bg: #2d2d2d;
  --theme-button-bg: #2d2d2d;
  
  /* Shadows for dark theme */
  --theme-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --theme-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.4);
  --theme-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.5);

  /* Dark mode loading variables */
  --loading-spinner-color: #79bbff;
  --loading-spinner-color-light: #a0cfff;
  --loading-background: rgba(31, 34, 37, 0.85);
  --loading-background-overlay: rgba(31, 34, 37, 0.75);
  --loading-text-color: #e5eaf3;
  --loading-mask-opacity: 0.85;
  --loading-spinner-size: 42px;
  --loading-spinner-border-width: 3px;
}

/* Element Plus CSS Variable Overrides for Dark Theme */
:root.dark-theme,
[data-theme="dark"] {
  /* Element Plus color overrides */
  --el-bg-color: var(--theme-bg-primary);
  --el-bg-color-page: var(--theme-bg-secondary);
  --el-bg-color-overlay: var(--theme-bg-overlay);
  
  --el-text-color-primary: var(--theme-text-primary);
  --el-text-color-regular: var(--theme-text-regular);
  --el-text-color-secondary: var(--theme-text-secondary);
  --el-text-color-placeholder: var(--theme-text-placeholder);
  --el-text-color-disabled: var(--theme-text-disabled);
  
  --el-border-color: var(--theme-border-base);
  --el-border-color-light: var(--theme-border-light);
  --el-border-color-lighter: var(--theme-border-light);
  --el-border-color-extra-light: var(--theme-border-light);
  --el-border-color-dark: var(--theme-border-dark);
  --el-border-color-darker: var(--theme-border-darker);
  
  --el-fill-color: var(--theme-fill-base);
  --el-fill-color-light: var(--theme-fill-light);
  --el-fill-color-lighter: var(--theme-fill-extra-light);
  --el-fill-color-extra-light: var(--theme-fill-extra-light);
  --el-fill-color-dark: var(--theme-fill-dark);
  --el-fill-color-darker: var(--theme-fill-darker);
  --el-fill-color-blank: transparent;
  
  /* Component specific overrides */
  --el-menu-bg-color: var(--theme-sidebar-bg);
  --el-menu-text-color: var(--theme-text-regular);
  --el-menu-hover-bg-color: var(--theme-fill-light);
  --el-menu-item-hover-fill: var(--theme-fill-light);
  
  --el-card-bg-color: var(--theme-card-bg);
  --el-card-border-color: var(--theme-border-base);
  
  --el-input-bg-color: var(--theme-input-bg);
  --el-input-border-color: var(--theme-border-base);
  
  --el-button-bg-color: var(--theme-button-bg);
  --el-button-border-color: var(--theme-border-base);
  
  /* Box shadows */
  --el-box-shadow: var(--theme-shadow-base);
  --el-box-shadow-light: var(--theme-shadow-light);
  --el-box-shadow-dark: var(--theme-shadow-dark);

  /* Loading component overrides */
  --el-loading-spinner-size: var(--loading-spinner-size);
  --el-loading-background-color: var(--loading-background);
  --el-loading-text-color: var(--loading-text-color);
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease, 
              box-shadow 0.3s ease;
}

/* Disable transitions during theme initialization to prevent flash */
.theme-transition-disabled * {
  transition: none !important;
}

/* ===== GLOBAL LOADING OPTIMIZATIONS ===== */

/* Element Plus Loading Mask Optimization */
.el-loading-mask {
  background-color: var(--loading-background) !important;
  backdrop-filter: blur(2px);
  transition: all 0.3s ease;
}

/* Element Plus Loading Spinner Optimization */
.el-loading-spinner {
  color: var(--loading-spinner-color) !important;
}

.el-loading-spinner .circular {
  width: var(--loading-spinner-size) !important;
  height: var(--loading-spinner-size) !important;
  animation: loading-rotate 2s linear infinite;
}

.el-loading-spinner .path {
  stroke: var(--loading-spinner-color) !important;
  stroke-width: var(--loading-spinner-border-width) !important;
  stroke-linecap: round;
  animation: loading-dash 1.5s ease-in-out infinite;
  opacity: 0.8;
}

/* Loading Text Optimization */
.el-loading-text {
  color: var(--loading-text-color) !important;
  font-weight: 500;
  margin-top: 12px;
  font-size: 14px;
}

/* Custom Loading Animations */
@keyframes loading-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -124px;
  }
}

/* Table Loading Optimization */
.el-table .el-loading-mask {
  background-color: var(--loading-background-overlay) !important;
  border-radius: 8px;
}

/* Card Loading Optimization */
.el-card .el-loading-mask {
  background-color: var(--loading-background-overlay) !important;
  border-radius: 8px;
}

/* Button Loading State Optimization */
.el-button.is-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.el-button.is-loading .el-icon {
  color: var(--loading-spinner-color) !important;
  animation: loading-rotate 1s linear infinite;
}

/* Primary Button Loading */
.el-button--primary.is-loading {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.el-button--primary.is-loading .el-icon {
  color: #ffffff !important;
}

/* Secondary Button Loading */
.el-button--default.is-loading .el-icon {
  color: var(--loading-spinner-color) !important;
}

/* Danger Button Loading */
.el-button--danger.is-loading .el-icon {
  color: #ffffff !important;
}

/* Warning Button Loading */
.el-button--warning.is-loading .el-icon {
  color: #ffffff !important;
}

/* Success Button Loading */
.el-button--success.is-loading .el-icon {
  color: #ffffff !important;
}

/* Info Button Loading */
.el-button--info.is-loading .el-icon {
  color: #ffffff !important;
}

/* Small Button Loading */
.el-button--small.is-loading .el-icon {
  font-size: 12px;
}

/* Large Button Loading */
.el-button--large.is-loading .el-icon {
  font-size: 16px;
}

/* Dialog Loading Optimization */
.el-dialog .el-loading-mask {
  background-color: var(--loading-background) !important;
  border-radius: 8px;
  backdrop-filter: blur(3px);
}

.el-dialog .el-loading-spinner {
  margin-top: -20px;
}

.el-dialog .el-loading-text {
  margin-top: 16px;
  font-size: 14px;
}

/* Modal Loading States */
.el-overlay .el-loading-mask {
  background-color: var(--loading-background) !important;
  backdrop-filter: blur(4px);
}

/* Form Loading States */
.el-form .el-loading-mask {
  background-color: var(--loading-background-overlay) !important;
  border-radius: 6px;
  backdrop-filter: blur(2px);
}

.el-form .el-loading-spinner {
  margin-top: -10px;
}

.el-form .el-loading-text {
  margin-top: 12px;
  font-size: 13px;
}

/* Page Level Loading */
.loading-container .el-loading-mask {
  background-color: var(--theme-bg-secondary) !important;
}

/* Drawer Loading */
.el-drawer .el-loading-mask {
  background-color: var(--loading-background) !important;
  border-radius: 0;
  backdrop-filter: blur(3px);
}

/* Popover Loading */
.el-popover .el-loading-mask {
  background-color: var(--loading-background-overlay) !important;
  border-radius: 6px;
  backdrop-filter: blur(2px);
}

/* Message Box Loading */
.el-message-box .el-loading-mask {
  background-color: var(--loading-background) !important;
  border-radius: 8px;
  backdrop-filter: blur(3px);
}

/* Enhanced Loading for Dark Mode */
:root.dark-theme .el-loading-spinner .path,
[data-theme="dark"] .el-loading-spinner .path {
  stroke: var(--loading-spinner-color) !important;
  opacity: 0.9;
  filter: drop-shadow(0 0 4px rgba(121, 187, 255, 0.3));
}

:root.dark-theme .el-loading-mask,
[data-theme="dark"] .el-loading-mask {
  background-color: var(--loading-background) !important;
  backdrop-filter: blur(3px);
}

/* Responsive Loading Spinner Sizes */
@media (max-width: 768px) {
  :root {
    --loading-spinner-size: 36px;
    --loading-spinner-border-width: 3px;
  }

  .el-loading-text {
    font-size: 13px;
  }
}

/* Mobile-First Form Responsive Styles */
@media (max-width: 768px) {
  /* Touch-friendly form inputs */
  .el-input__inner {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 16px !important; /* Prevents iOS zoom on input focus */
  }

  .el-select {
    min-height: 44px !important;
  }

  .el-select .el-input__inner {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
  }

  .el-button {
    min-height: 44px !important;
    padding: 12px 20px !important;
    font-size: 16px !important;
  }

  .el-button--small {
    min-height: 36px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
  }

  /* Dialog mobile optimizations */
  .el-dialog {
    margin: 16px !important;
    width: calc(100vw - 32px) !important;
    max-width: none !important;
  }

  .el-dialog__header {
    padding: 20px 16px 16px !important;
  }

  .el-dialog__body {
    padding: 16px !important;
  }

  .el-dialog__footer {
    padding: 16px !important;
  }

  /* Form mobile optimization */
  .el-form-item {
    margin-bottom: 20px !important;
  }

  .el-form-item__label {
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  /* Table mobile scroll indicators */
  .table-wrapper {
    position: relative;
  }

  .table-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .table-wrapper:hover::after {
    opacity: 1;
  }

  /* Mobile-specific interaction improvements */
  .el-tooltip__popper {
    font-size: 14px !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    max-width: 250px !important;
  }

  /* Mobile pagination improvements */
  .el-pagination {
    justify-content: center !important;
  }

  .el-pagination .el-pager li {
    min-width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    width: 32px !important;
    height: 32px !important;
  }

  /* Mobile drawer improvements */
  .el-drawer {
    border-radius: 0 !important;
  }

  .el-drawer__header {
    padding: 16px !important;
    margin-bottom: 0 !important;
  }

  .el-drawer__body {
    padding: 16px !important;
  }

  /* Touch-friendly scrollbars */
  .table-wrapper::-webkit-scrollbar {
    height: 8px !important;
  }

  .table-wrapper::-webkit-scrollbar-track {
    background: var(--theme-fill-light) !important;
    border-radius: 4px !important;
  }

  .table-wrapper::-webkit-scrollbar-thumb {
    background: var(--theme-primary) !important;
    border-radius: 4px !important;
  }

  .table-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--theme-primary-dark) !important;
  }
}

@media (max-width: 480px) {
  .el-dialog {
    margin: 8px !important;
    width: calc(100vw - 16px) !important;
  }

  .el-button {
    min-height: 48px !important;
    padding: 14px 24px !important;
  }

  .el-input__inner {
    min-height: 48px !important;
    padding: 14px 16px !important;
  }

  .el-select .el-input__inner {
    min-height: 48px !important;
    padding: 14px 16px !important;
  }
}

/* Loading State Transitions */
.el-loading-fade-enter-active,
.el-loading-fade-leave-active {
  transition: opacity 0.3s ease, backdrop-filter 0.3s ease;
}

.el-loading-fade-enter-from,
.el-loading-fade-leave-to {
  opacity: 0;
  backdrop-filter: blur(0px);
}
