import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getSiteTitle } from '../api'
import { getCurrentLanguage } from '../locales'

export const useTitlesStore = defineStore('titles', () => {
  // Site title from system settings
  const siteTitle = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Load site title from API
  const loadSiteTitle = async () => {
    if (isLoading.value) return
    
    isLoading.value = true
    error.value = null
    
    try {
      const response = await getSiteTitle()
      siteTitle.value = response.data
    } catch (err) {
      console.warn('Failed to load site title:', err)
      error.value = err
      // Set default fallback
      siteTitle.value = {
        zh: '莹火虫的世界',
        en: 'Firefly\'s World'
      }
    } finally {
      isLoading.value = false
    }
  }

  // Get current site title based on language
  const getCurrentSiteTitle = () => {
    if (!siteTitle.value) return 'Firefly\'s World'
    
    const currentLang = getCurrentLanguage()
    const langKey = currentLang === 'zh-CN' ? 'zh' : 'en'
    return siteTitle.value[langKey] || siteTitle.value.zh || 'Firefly\'s World'
  }


  // Initialize store
  const initialize = async () => {
    await loadSiteTitle()
  }

  return {
    siteTitle,
    isLoading,
    error,
    loadSiteTitle,
    getCurrentSiteTitle,
    initialize
  }
})