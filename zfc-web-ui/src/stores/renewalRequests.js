import { defineStore } from 'pinia'
import { 
  getRenewalRequests, 
  createRenewalRequest, 
  cancelRenewalRequest,
  getRenewalRequestDetail,
  calculateRenewalPrice
} from '../api'

export const useRenewalRequestsStore = defineStore('renewalRequests', {
  state: () => ({
    requests: [],
    loading: false,
    error: null,
    lastFetchTime: null,
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    // Current price calculation
    currentPriceCalculation: null,
    calculatingPrice: false,
    // Request creation state
    creatingRequest: false,
    createError: null,
    // Request cancellation state
    cancellingRequest: false,
    cancelError: null
  }),

  getters: {
    // Get pending requests count
    pendingRequestsCount: (state) => {
      return state.requests.filter(request => request.status === 'pending').length
    },
    
    // Check if user has pending request
    hasPendingRequest: (state) => {
      return state.requests.some(request => request.status === 'pending')
    },
    
    // Get requests by status
    requestsByStatus: (state) => (status) => {
      if (!status) return state.requests
      return state.requests.filter(request => request.status === status)
    },
    
    // Get most recent request
    mostRecentRequest: (state) => {
      return state.requests.length > 0 ? state.requests[0] : null
    }
  },

  actions: {
    // Fetch renewal requests
    async fetchRenewalRequests(params = {}) {
      // Avoid unnecessary requests if data is fresh
      if (this.requests.length > 0 && !params.force && 
          Date.now() - this.lastFetchTime < 30000) {
        return
      }

      this.loading = true
      this.error = null

      try {
        const requestParams = {
          page: params.page || this.pagination.page,
          pageSize: params.pageSize || this.pagination.pageSize,
          ...params
        }

        const response = await getRenewalRequests(requestParams)
        
        this.requests = response.data?.data || []
        this.pagination = {
          page: response.data?.page || 1,
          pageSize: response.data?.pageSize || 20,
          total: response.data?.total || 0
        }
        this.lastFetchTime = Date.now()
      } catch (error) {
        console.error('Failed to fetch renewal requests:', error)
        this.error = error
        this.requests = []
      } finally {
        this.loading = false
      }
    },

    // Get specific renewal request details
    async getRenewalRequestDetail(requestId) {
      try {
        const response = await getRenewalRequestDetail(requestId)
        return response.data
      } catch (error) {
        console.error('Failed to get renewal request detail:', error)
        throw error
      }
    },

    // Calculate renewal price
    async calculateRenewalPrice(requestedDuration) {
      this.calculatingPrice = true
      this.currentPriceCalculation = null

      try {
        const response = await calculateRenewalPrice(requestedDuration)
        this.currentPriceCalculation = response.data
        return response.data
      } catch (error) {
        console.error('Failed to calculate renewal price:', error)
        this.currentPriceCalculation = null
        throw error
      } finally {
        this.calculatingPrice = false
      }
    },

    // Create new renewal request
    async createRenewalRequest(requestData) {
      // Check if user already has pending request
      if (this.hasPendingRequest) {
        throw new Error('You already have a pending renewal request')
      }

      this.creatingRequest = true
      this.createError = null

      try {
        const response = await createRenewalRequest(requestData)
        
        // Add the new request to the beginning of the list
        if (response.data) {
          this.requests.unshift(response.data)
          this.pagination.total += 1
        }

        return response.data
      } catch (error) {
        console.error('Failed to create renewal request:', error)
        this.createError = error
        throw error
      } finally {
        this.creatingRequest = false
      }
    },

    // Cancel renewal request
    async cancelRenewalRequest(requestId) {
      this.cancellingRequest = true
      this.cancelError = null

      try {
        await cancelRenewalRequest(requestId)
        
        // Update the request status in the local state
        const requestIndex = this.requests.findIndex(request => request.id === requestId)
        if (requestIndex !== -1) {
          this.requests[requestIndex].status = 'cancelled'
        }

        return true
      } catch (error) {
        console.error('Failed to cancel renewal request:', error)
        this.cancelError = error
        throw error
      } finally {
        this.cancellingRequest = false
      }
    },

    // Update pagination settings
    updatePagination(page, pageSize) {
      this.pagination.page = page
      this.pagination.pageSize = pageSize
    },

    // Clear price calculation
    clearPriceCalculation() {
      this.currentPriceCalculation = null
      this.calculatingPrice = false
    },

    // Clear all data
    clearData() {
      this.requests = []
      this.loading = false
      this.error = null
      this.lastFetchTime = null
      this.pagination = {
        page: 1,
        pageSize: 20,
        total: 0
      }
      this.currentPriceCalculation = null
      this.calculatingPrice = false
      this.creatingRequest = false
      this.createError = null
      this.cancellingRequest = false
      this.cancelError = null
    },

    // Refresh data
    async refresh() {
      await this.fetchRenewalRequests({ force: true })
    }
  }
})