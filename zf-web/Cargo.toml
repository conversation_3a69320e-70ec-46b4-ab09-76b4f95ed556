[package]
name = "zf-web"
version = "0.1.0"
edition = "2021"

[features]
default = ["embed"]
embed = ["warp-embed", "rust-embed"]

[dependencies]
warp = "0.3"
tokio = { workspace = true}
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "2"
tracing = "0.1"
tracing-subscriber = "0.3"
jsonwebtoken = "9"
chrono = { version = "0.4", features = ["serde"] }
dotenv = "0.15"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0"
bb8-redis = "0.13.1"
redis = { version = "0.23.3", features = ["tokio-comp", "tls", "serde_json", "serde", "tokio-native-tls-comp", "json"] }
clap = { version = "4.1", features = ["derive", "env"] }
reqwest = { version = "0.12", default-features=false, features = ["json", "rustls-tls"] }
futures = "0.3"
private_tun = { workspace = true, default-features=false, features=["cache_udp"] }
rand = "0.9"

uuid = { version = "1.8.0", features = ["v4", "fast-rng"] }
warp-embed = { version = "0.5", optional = true }
rust-embed = { version = "8.0", optional = true }
mime_guess = "2.0"

common = { path = "../common" }
# Prisma client dependencies
prisma-client-rust = { git = "https://github.com/Brendonovich/prisma-client-rust", tag = "0.6.11", no-default-features = true, features = ["postgresql"] }
sql-query-connector = { git = "https://github.com/Brendonovich/prisma-engines", tag = "pcr-0.6.10", features = ["vendored-openssl"] }
taos = { version = "0.12"}
surge-ping = "0.8.0"
base64 = "0.22"
brotli = "8.0.1"
itertools = "0.10"
regex = "1.10"