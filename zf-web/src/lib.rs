#![recursion_limit = "256"]
pub mod error;
pub mod handlers;

pub mod add_machine;
pub mod add_user;
pub mod forwarder;
pub mod message;
pub mod stats;
pub mod tot;

pub use common::{
    prisma,
    reset_traffic_task::{ArcResetTrafficContext, ResetTrafficContext},
    retry_queue::RetryQueue,
    tdengine_init::{setup_tdengine, TaosClient},
    RedisLineLock,
};

pub use chrono::Utc;

pub use anyhow::anyhow;
pub use bb8_redis::{bb8::Pool, RedisConnectionManager};
pub use common::license_message::{Entitlements, EntitlementsData, GetEntitlementsResponse};
pub use common::{cache::Cache, redis_lock::DistributedLock};
pub use futures::future::BoxFuture;
pub use handlers::latency_monitoring::{handle_get_latency_chart, handle_trigger_chart_generation};
pub use handlers::{
    handle_add_forward_endpoint, handle_add_server, handle_add_user,
    handle_admin_get_line_details_by_user, handle_admin_get_ports_by_user,
    handle_apply_renewal_code, handle_batch_update_ports, handle_copy_forward_endpoint,
    handle_create_latency_config, handle_create_package, handle_create_port,
    handle_delete_latency_config, handle_delete_package, handle_edit_user,
    handle_extend_subscription_time, handle_get_all_server_list, handle_get_connection_stats,
    handle_get_detailed_package_list, handle_get_entitlements, handle_get_forward_endpoint,
    handle_get_forward_endpoints_with_search, handle_get_latency_configs,
    handle_get_latency_history, handle_get_latency_realtime, handle_get_line_details,
    handle_get_line_stats, handle_get_package_detail, handle_get_package_list,
    handle_get_ports_with_search, handle_get_renewal_history, handle_get_server_list,
    handle_get_server_list_with_search, handle_get_setup_script, handle_get_site_title,
    handle_get_software_license_info, handle_get_subscription_list_with_search,
    handle_get_system_setting, handle_get_system_settings, handle_get_user_historical_speeds,
    handle_get_user_line_speeds, handle_get_user_speeds, handle_get_worker_setup_script,
    handle_login, handle_modify_forward_endpoint, handle_modify_port, handle_modify_server,
    handle_reset_user_traffic, handle_resume_port, handle_rmv_forward_endpoint, handle_rmv_port,
    handle_rmv_server, handle_rmv_user, handle_subscription_info, handle_suspend_port,
    handle_test_latency, handle_update_latency_config, handle_update_package,
    handle_update_system_setting, handle_update_system_settings,
    handle_send_renewal_request_message, handle_get_renewal_request_messages,
};
pub use jsonwebtoken::{decode, DecodingKey, Validation};
pub use message::{
    AdminLineDetailsByUserRequest, AdminPortsByUserRequest, Claims, CreateLatencyConfigRequest,
    GetConnectionStatsRequest, GetLatencyConfigsRequest, GetLatencyHistoryRequest,
    RmvServerRequest, RmvUserRequest, TestLatencyRequest, UpdateLatencyConfigRequest,
};
pub use prisma::{subscription, PrismaClient};
pub use reqwest::Url;
pub use std::{
    collections::{HashMap, HashSet},
    convert::Infallible,
    net::IpAddr,
    sync::Arc,
};
pub use tokio::sync::Mutex;
pub use tracing::info;
pub use warp::{Filter, Rejection};

#[cfg(feature = "embed")]
pub use rust_embed::RustEmbed;

#[cfg(feature = "embed")]
pub use warp::Reply;

pub use clap::Parser;

#[derive(Debug, Parser, Clone)]
pub struct Opt {
    #[clap(short = 'b', long, env, default_value = "0.0.0.0")]
    pub bind_address: String,
    #[clap(short = 'p', long, env, default_value_t = 3030)]
    pub backend_port: u16,
    #[clap(
        short,
        long,
        env,
        default_value = "postgres://postgres:postgres@127.0.0.1/datium"
    )]
    pub db_path: String,
    #[clap(short, long, env, default_value = "redis://127.0.0.1:6379")]
    pub redis_path: String,
    #[clap(short, long, env, default_value = "taos+ws://127.0.0.1:6030")]
    pub tdengine_url: String,
    #[clap(long, env, default_value = "zfc")]
    pub tdengine_db: String,
    #[clap(long, env)]
    pub host_url: String,
    #[clap(long, env, default_value = "./downloads")]
    pub app_download_dir: String,
    #[clap(long, env, default_value = "http://127.0.0.1:3100")]
    pub mgmt_url: Url,
    #[clap(long, env)]
    pub mgmt_pubkey: String,
    #[clap(long, env, default_value_t = 5)]
    pub max_monitors_per_server: u32,
}

pub type RedisPool = Pool<RedisConnectionManager>;

pub async fn setup_redis(opt: &Opt) -> anyhow::Result<RedisPool> {
    let manager = RedisConnectionManager::new(opt.redis_path.as_str())?;
    Ok(Pool::builder().build(manager).await?)
}

#[derive(Clone)]
pub struct AppState {
    pub db: Arc<PrismaClient>,
    pub redis_pool: RedisPool,
    pub redis_lock: Arc<DistributedLock>,
    pub redis_line_lock: RedisLineLock,
    pub tdengine_client: Arc<TaosClient>,
    pub cache_sub_lines: Arc<Cache<String, Vec<i32>>>, // token_id -> lines
    pub cache_outbound_endpoint_data: Arc<Cache<i32, prisma::outbound_endpoint::Data>>,
    pub cache_forward_endpoint_data: Arc<Cache<i32, prisma::forward_endpoint::Data>>,
    pub cache_system_settings: Arc<Cache<String, String>>, // key -> value
    pub admin_tokens: Arc<HashSet<String>>,
    pub opt: Opt,
    pub web_client: Arc<reqwest::Client>,
}
