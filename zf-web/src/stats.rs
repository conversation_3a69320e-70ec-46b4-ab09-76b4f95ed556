use std::collections::HashMap;
use taos::{AsyncFetchable, AsyncQueryable, StreamExt, Taos, Value};
use redis::AsyncCommands;
use common::{
    reset_traffic_task::RedisPool,
    stats::{CachedNetSpeedItem, CachedSystemStatsItem, CachedUserLineSpeedItem},
};
use serde_json;

use crate::message::{
    LineSpeedSummary, NetSpeedItem, SystemStatsItem, UserHistoricalSpeedItem, UserLineSpeedItem,
};

pub async fn get_line_netcard_speed(
    line_id: &[i32],
    client: &Taos,
    redis_pool: &RedisPool,
) -> Result<HashMap<i32, NetSpeedItem>, anyhow::Error> {
    if line_id.is_empty() {
        return Ok(HashMap::new());
    }

    let mut result = HashMap::new();
    let mut missing_ids = Vec::new();

    // 先尝试从Redis获取数据
    if let Ok(mut conn) = redis_pool.get().await {
        for &agent_id in line_id {
            let cache_key = format!("netcard_speed:{}", agent_id);
            if let Ok(cached_data) = conn.get::<_, String>(&cache_key).await {
                if let Ok(cached_item) = serde_json::from_str::<CachedNetSpeedItem>(&cached_data) {
                    let net_speed = NetSpeedItem {
                        rx: cached_item.rx,
                        tx: cached_item.tx,
                        total_rx: cached_item.total_rx,
                        total_tx: cached_item.total_tx,
                    };
                    result.insert(agent_id, net_speed);
                    continue;
                }
            }
            missing_ids.push(agent_id);
        }
    } else {
        missing_ids = line_id.to_vec();
    }

    // 如果Redis中没有所有数据，从TDengine查询缺失的数据
    if !missing_ids.is_empty() {
        let lines_id_str = missing_ids
            .iter()
            .map(|id| id.to_string())
            .collect::<Vec<_>>()
            .join(",");

        let query_str = format!(
            "SELECT agent_id, LAST(rx) as rx, LAST(tx) as tx, LAST(total_rx) as total_rx, LAST(total_tx) as total_tx FROM netcard_speed WHERE agent_id IN ({}) AND ts > NOW() - 1m GROUP BY agent_id",
            lines_id_str
        );
        log::debug!("query_str: {:?}", query_str);

        let mut query_result = client.query(&query_str).await?;

        // Process TDengine result set
        let mut result_stream = query_result.rows();
        while let Some(Ok(row)) = result_stream.next().await {
            let row = row.into_values();
            if row.len() >= 5 {
                let agent_id = match row.get(0) {
                    Some(Value::Int(v)) => *v,
                    _ => continue,
                };
                let rx = match row.get(1) {
                    Some(Value::Double(v)) => *v,
                    _ => 0.0,
                };
                let tx = match row.get(2) {
                    Some(Value::Double(v)) => *v,
                    _ => 0.0,
                };
                let total_rx = match row.get(3) {
                    Some(Value::BigInt(v)) => *v as u64,
                    _ => 0,
                };
                let total_tx = match row.get(4) {
                    Some(Value::BigInt(v)) => *v as u64,
                    _ => 0,
                };

                result.insert(
                    agent_id,
                    NetSpeedItem {
                        rx: rx as u64,
                        tx: tx as u64,
                        total_rx,
                        total_tx,
                    },
                );
            }
        }
    }
    Ok(result)
}

pub async fn get_line_system_stats(
    line_id: &[i32],
    client: &Taos,
    redis_pool: &RedisPool,
) -> Result<HashMap<i32, SystemStatsItem>, anyhow::Error> {
    if line_id.is_empty() {
        return Ok(HashMap::new());
    }

    let mut result = HashMap::new();
    let mut missing_ids = Vec::new();

    // 先尝试从Redis获取数据
    if let Ok(mut conn) = redis_pool.get().await {
        for &agent_id in line_id {
            let cache_key = format!("system_stats:{}", agent_id);
            if let Ok(cached_data) = conn.get::<_, String>(&cache_key).await {
                if let Ok(cached_item) = serde_json::from_str::<CachedSystemStatsItem>(&cached_data) {
                    let system_stats = SystemStatsItem {
                        cpu_usage: cached_item.cpu_usage,
                        memory_total: cached_item.memory_total,
                        memory_used: cached_item.memory_used,
                        uptime: cached_item.uptime,
                        tcp_connections: cached_item.tcp_connections,
                        udp_connections: cached_item.udp_connections,
                    };
                    result.insert(agent_id, system_stats);
                    continue;
                }
            }
            missing_ids.push(agent_id);
        }
    } else {
        missing_ids = line_id.to_vec();
    }

    // 如果Redis中没有所有数据，从TDengine查询缺失的数据
    if !missing_ids.is_empty() {
        let lines_id_str = missing_ids
            .iter()
            .map(|id| id.to_string())
            .collect::<Vec<_>>()
            .join(",");

        let query_str = format!(
            "SELECT agent_id, LAST(cpu_usage) as cpu_usage, LAST(memory_total) as memory_total, LAST(memory_used) as memory_used, LAST(uptime) as uptime, LAST(tcp_connections) as tcp_connections, LAST(udp_connections) as udp_connections FROM system_stats WHERE agent_id IN ({}) AND ts > NOW() - 1m GROUP BY agent_id",
            lines_id_str
        );
        log::debug!("query_str: {:?}", query_str);

        let mut query_result = client.query(&query_str).await?;

        // Process TDengine result set
        let mut result_stream = query_result.rows();
        while let Some(Ok(row)) = result_stream.next().await {
            let row = row.into_values();
            if row.len() >= 7 {
                let agent_id = match row.get(0) {
                    Some(Value::Int(v)) => *v,
                    _ => continue,
                };
                let cpu_usage = match row.get(1) {
                    Some(Value::Float(v)) => *v,
                    _ => 0.0,
                };
                let memory_total = match row.get(2) {
                    Some(Value::BigInt(v)) => *v as u64,
                    _ => 0,
                };
                let memory_used = match row.get(3) {
                    Some(Value::BigInt(v)) => *v as u64,
                    _ => 0,
                };
                let uptime = match row.get(4) {
                    Some(Value::BigInt(v)) => *v as u64,
                    _ => 0,
                };
                let tcp_connections = match row.get(5) {
                    Some(Value::Int(v)) => *v as u32,
                    _ => 0,
                };
                let udp_connections = match row.get(6) {
                    Some(Value::Int(v)) => *v as u32,
                    _ => 0,
                };

                result.insert(
                    agent_id,
                    SystemStatsItem {
                        cpu_usage,
                        memory_total,
                        memory_used,
                        uptime,
                        tcp_connections,
                        udp_connections,
                    },
                );
            }
        }
    }
    Ok(result)
}

pub async fn get_user_line_speeds(
    subscription_ids: &[i32],
    client: &Taos,
    redis_pool: &RedisPool,
) -> Result<Vec<UserLineSpeedItem>, anyhow::Error> {
    if subscription_ids.is_empty() {
        return Ok(Vec::new());
    }

    let mut result = Vec::new();
    let mut cache_miss_subs = Vec::new();

    // 先尝试从Redis获取数据
    if let Ok(mut conn) = redis_pool.get().await {
        for &subscription_id in subscription_ids {
            let pattern = format!("user_line_info:{}:*", subscription_id);
            if let Ok(keys) = conn.keys::<_, Vec<String>>(&pattern).await {
                let mut sub_has_data = false;
                for key in keys {
                    if let Ok(cached_data) = conn.get::<_, String>(&key).await {
                        if let Ok(cached_item) = serde_json::from_str::<CachedUserLineSpeedItem>(&cached_data) {
                            let user_line_speed = UserLineSpeedItem {
                                subscription_id: cached_item.subscription_id,
                                line_id: cached_item.line_id,
                                line_name: cached_item.line_name,
                                upload_speed: cached_item.upload_speed,
                                download_speed: cached_item.download_speed,
                                client_ips_count: cached_item.client_ips_count,
                                connection_count: cached_item.connection_count,
                            };
                            result.push(user_line_speed);
                            sub_has_data = true;
                        }
                    }
                }
                if !sub_has_data {
                    cache_miss_subs.push(subscription_id);
                }
            } else {
                cache_miss_subs.push(subscription_id);
            }
        }
    } else {
        cache_miss_subs = subscription_ids.to_vec();
    }

    // 如果Redis中没有所有数据，从TDengine查询缺失的数据
    if !cache_miss_subs.is_empty() {
        let sub_ids_str = cache_miss_subs
            .iter()
            .map(|id| id.to_string())
            .collect::<Vec<_>>()
            .join(",");

        let query_str = format!(
            "SELECT subscription_id, line_id, LAST(total_speed) as total_speed, LAST(client_ips_count) as client_ips_count, LAST(connection_count) as connection_count FROM user_line_info WHERE subscription_id IN ({}) AND ts > NOW() - 15s GROUP BY subscription_id, line_id",
            sub_ids_str
        );

        log::debug!("get_user_line_speeds query: {}", query_str);
        let mut query_result = client.query(&query_str).await?;

        let mut result_stream = query_result.rows();
        while let Some(Ok(row)) = result_stream.next().await {
            let row = row.into_values();
            if row.len() >= 5 {
                let subscription_id = match row.get(0) {
                    Some(Value::Int(v)) => *v,
                    _ => continue,
                };
                let line_id = match row.get(1) {
                    Some(Value::Int(v)) => *v,
                    _ => continue,
                };
                let total_speed = match row.get(2) {
                    Some(Value::Double(v)) => *v,
                    _ => 0.0,
                };
                let client_ips_count = match row.get(3) {
                    Some(Value::Int(v)) => *v,
                    _ => 0,
                };
                let connection_count = match row.get(4) {
                    Some(Value::Int(v)) => *v,
                    _ => 0,
                };

                result.push(UserLineSpeedItem {
                    subscription_id,
                    line_id,
                    line_name: None,
                    upload_speed: 0.0,
                    download_speed: total_speed,
                    client_ips_count,
                    connection_count,
                });
            }
        }
    }

    Ok(result)
}

pub async fn get_user_historical_speeds(
    subscription_id: i32,
    time_range_minutes: i32,
    client: &Taos,
    line_ids_filter: Option<&[i32]>,
) -> Result<Vec<UserHistoricalSpeedItem>, anyhow::Error> {
    let time_filter = format!("ts > NOW() - {}m", time_range_minutes);

    // Calculate appropriate grouping interval based on time range
    let group_interval = match time_range_minutes {
        1..=60 => "1m",      // 1 hour or less: 1 minute intervals
        61..=180 => "2m",    // 3 hours: 2 minute intervals
        181..=360 => "5m",   // 6 hours: 5 minute intervals
        361..=1440 => "15m", // 24 hours: 15 minute intervals
        _ => "1h",           // 7 days: 1 hour intervals
    };

    // Build additional filter for specific line IDs if provided
    let line_filter = if let Some(line_ids) = line_ids_filter {
        if !line_ids.is_empty() {
            let line_ids_str = line_ids
                .iter()
                .map(|id| id.to_string())
                .collect::<Vec<_>>()
                .join(",");
            format!(" AND line_id IN ({})", line_ids_str)
        } else {
            String::new()
        }
    } else {
        String::new()
    };

    let query_str = format!(
        "SELECT _wstart, line_id, AVG(total_speed) as total_speed, SUM(traffic_inc) as traffic_inc, MAX(client_ips_count) as client_ips_count, MAX(connection_count) as connection_count FROM user_line_info WHERE subscription_id = {} AND {} {} PARTITION BY line_id INTERVAL({})",
        subscription_id, time_filter, line_filter, group_interval
    );

    log::debug!("get_user_historical_speeds query: {}", query_str);
    let mut result = client.query(&query_str).await?;

    let mut historical_speeds = Vec::new();
    let mut result_stream = result.rows();
    while let Some(Ok(row)) = result_stream.next().await {
        let row = row.into_values();
        if row.len() >= 6 {
            let timestamp = match row.get(0) {
                Some(Value::Timestamp(ts)) => {
                    // Convert TDengine timestamp to chrono DateTime
                    ts.to_naive_datetime().and_utc()
                }
                _ => continue,
            };
            let line_id = match row.get(1) {
                Some(Value::Int(v)) => *v,
                _ => continue,
            };
            let total_speed = match row.get(2) {
                Some(Value::Double(v)) => Some(*v),
                _ => None,
            };
            let traffic_inc = match row.get(3) {
                Some(Value::BigInt(v)) => Some(*v as u64),
                _ => None,
            };
            let client_ips_count = match row.get(4) {
                Some(Value::Int(v)) => Some(*v),
                _ => None,
            };
            let connection_count = match row.get(5) {
                Some(Value::Int(v)) => Some(*v),
                _ => None,
            };

            // Only include data points where we have at least one valid speed measurement
            if let Some(total_speed) = total_speed {
                historical_speeds.push(UserHistoricalSpeedItem {
                    subscription_id,
                    line_id,
                    line_name: None, // Will be populated by the handler
                    timestamp,
                    upload_speed: 0.0, // keep compatible with frontend
                    download_speed: total_speed,
                    traffic_inc: traffic_inc.unwrap_or(0),
                    client_ips_count: client_ips_count.unwrap_or(0),
                    connection_count: connection_count.unwrap_or(0),
                });
            }
        }
    }

    // Sort by timestamp for better chart display
    historical_speeds.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

    Ok(historical_speeds)
}

pub async fn get_latest_line_speeds_summary(
    subscription_id: i32,
    user_line_ids: &[i32],
    client: &Taos,
    redis_pool: &RedisPool,
) -> Result<Vec<LineSpeedSummary>, anyhow::Error> {
    if user_line_ids.is_empty() {
        return Ok(Vec::new());
    }

    let mut result = Vec::new();
    let mut missing_line_ids = Vec::new();

    // 先尝试从Redis获取数据
    if let Ok(mut conn) = redis_pool.get().await {
        for &line_id in user_line_ids {
            let cache_key = format!("user_line_info:{}:{}", subscription_id, line_id);
            if let Ok(cached_data) = conn.get::<_, String>(&cache_key).await {
                if let Ok(cached_item) = serde_json::from_str::<CachedUserLineSpeedItem>(&cached_data) {
                    result.push(LineSpeedSummary {
                        line_id: cached_item.line_id,
                        line_name: cached_item.line_name,
                        latest_total_speed: cached_item.download_speed, // download_speed is total_speed
                        is_selected: false, // Will be set by the handler
                    });
                    continue;
                }
            }
            missing_line_ids.push(line_id);
        }
    } else {
        missing_line_ids = user_line_ids.to_vec();
    }

    // 如果Redis中没有所有数据，从TDengine查询缺失的数据
    if !missing_line_ids.is_empty() {
        // Build the line IDs filter for the query
        let line_ids_str = missing_line_ids
            .iter()
            .map(|id| id.to_string())
            .collect::<Vec<_>>()
            .join(",");

        let query_str = format!(
            "SELECT line_id, LAST(total_speed) as total_speed FROM user_line_info WHERE subscription_id = {} AND line_id IN ({}) AND ts > NOW() - 5m GROUP BY line_id",
            subscription_id, line_ids_str
        );

        log::debug!("get_latest_line_speeds_summary query: {}", query_str);
        let mut query_result = client.query(&query_str).await?;

        let mut result_stream = query_result.rows();
        while let Some(Ok(row)) = result_stream.next().await {
            let row = row.into_values();
            if row.len() >= 2 {
                let line_id = match row.get(0) {
                    Some(Value::Int(v)) => *v,
                    _ => continue,
                };
                let total_speed = match row.get(1) {
                    Some(Value::Double(v)) => Some(*v),
                    _ => None,
                };

                let latest_total_speed = total_speed.unwrap_or(0.0);

                result.push(LineSpeedSummary {
                    line_id,
                    line_name: None, // Will be populated by the handler
                    latest_total_speed,
                    is_selected: false, // Will be set by the handler
                });
            }
        }
    }

    // Sort by total speed (descending) for easy selection of fastest lines
    result.sort_by(|a, b| {
        b.latest_total_speed
            .partial_cmp(&a.latest_total_speed)
            .unwrap_or(std::cmp::Ordering::Equal)
    });

    Ok(result)
}
