use anyhow::anyhow;
use base64::Engine;
use common::{
    crypto::{random_key, stringify_public_key_from_secret_key, stringify_secret_key},
    replace_placeholders,
};
use reqwest::Url;
use std::{collections::HashMap, sync::Arc};

use super::prisma;
use crate::{
    handlers::latency_monitoring::{
        create_latency_config_internal, notify_controler_config_update,
    },
    prisma::PrismaClient,
    AppState,
};
const WORKER_SCRIPT_TEMPLATE: &str = include_str!("install_zfc_worker.sh");

pub async fn add_machine(
    app_state: &AppState,
    name: &str,
    ip: &str,
    port_start: i32,
    port_end: i32,
    traffic_scale: f32,
    admin_token_id: &str,
    interface_name: &str,
    allow_forward: bool,
    allow_latency_test: bool,
    port_forward_config: Option<(String, (Vec<i32>, Option<Vec<i32>>))>,
    use_forward_as_tun: bool,
    allow_ip_num: Option<i32>,
    allow_conn_num: Option<i32>,
    protocol_filters: Option<Vec<crate::message::FilterType>>,
    custom_config: Option<String>,
    tags: Option<Vec<String>>,
) -> anyhow::Result<()> {
    let db = &app_state.db;
    let key = random_key();
    let pub_key_str = stringify_public_key_from_secret_key(&key);
    let priv_key_str = stringify_secret_key(&key);

    let protocol_filters_strings = protocol_filters
        .map(|filters| {
            filters
                .iter()
                .map(|f| f.as_str().to_string())
                .collect::<Vec<String>>()
        })
        .unwrap_or_default();

    let mut set_args = vec![
        prisma::outbound_endpoint::port_start::set(Some(port_start)),
        prisma::outbound_endpoint::port_end::set(Some(port_end)),
        prisma::outbound_endpoint::traffic_scale::set(Some(traffic_scale as f64)),
        prisma::outbound_endpoint::interface_name::set(Some(interface_name.to_string())),
        prisma::outbound_endpoint::allow_forward::set(Some(allow_forward)),
        prisma::outbound_endpoint::allow_latency_test::set(Some(allow_latency_test)),
        prisma::outbound_endpoint::private_key::set(Some(priv_key_str.clone())),
        prisma::outbound_endpoint::allow_ip_num::set(allow_ip_num),
        prisma::outbound_endpoint::allow_conn_num::set(allow_conn_num),
        prisma::outbound_endpoint::protocol_filters::set(protocol_filters_strings),
        prisma::outbound_endpoint::custom_config::set(custom_config),
        prisma::outbound_endpoint::tags::set(tags.unwrap_or_default()),
    ];

    if let Some((proxy_config, (related_forward_endpoint_ids, related_tot_endpoint_ids))) =
        port_forward_config
    {
        set_args.push(prisma::outbound_endpoint::proxy_config::set(Some(
            base64::engine::general_purpose::STANDARD.encode(proxy_config),
        )));
        set_args.push(
            prisma::outbound_endpoint::related_forward_endpoint_ids::set(
                related_forward_endpoint_ids,
            ),
        );
        if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
            set_args.push(prisma::outbound_endpoint::related_tot_server_ids::set(
                related_tot_endpoint_ids,
            ));
        }
        set_args.push(prisma::outbound_endpoint::use_forward_as_tun::set(Some(
            use_forward_as_tun,
        )));
    }

    let data = match db
        .outbound_endpoint()
        .create(
            name.to_string(),
            pub_key_str.clone(),
            name.to_string(),
            ip.to_string(),
            set_args,
        )
        .exec()
        .await
    {
        Ok(v) => v,
        Err(e) => {
            log::error!("add machine: {} failed: {}", name, e);
            return Err(anyhow!("add machine: {} failed: {}", name, e));
        }
    };
    // add this machine to admin's line
    db.subscription()
        .update(
            prisma::subscription::token_id::equals(admin_token_id.to_string()),
            vec![prisma::subscription::lines::push(vec![data.id])],
        )
        .exec()
        .await?;

    // Add default ICMP latency test configs if latency testing is enabled
    if allow_latency_test {
        if let Err(e) = create_default_latency_configs(app_state, data.id, admin_token_id).await {
            log::warn!(
                "Failed to create default latency configs for machine {}: {}",
                name,
                e
            );
            // Don't fail the machine addition if latency config creation fails
        }
    }

    Ok(())
}

/// Create default ICMP latency test configurations for a new machine
async fn create_default_latency_configs(
    app_state: &AppState,
    server_id: i32,
    admin_token_id: &str,
) -> anyhow::Result<()> {
    // Get the admin's subscription ID
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(
            admin_token_id.to_string(),
        ))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await?
        .ok_or_else(|| anyhow!("Admin subscription not found"))?;

    let admin_id = subscription.id;

    // Default ICMP test configurations
    let default_configs = vec![
        ("Cloudflare DNS (*******)", "*******"),
        ("Google DNS (*******)", "*******"),
    ];

    let configs_count = default_configs.len();

    for (display_name, target_address) in default_configs {
        match create_latency_config_internal(
            &app_state.db,
            server_id,
            display_name.to_string(),
            target_address.to_string(),
            None, // ICMP doesn't use ports
            "icmp".to_string(),
            Some(64),   // Standard ping packet size
            Some(5000), // 5 second timeout
            Some(60),   // Test every minute
            Some(3),    // Alert after 3 failures
            true,       // Public so all users can see
            true,       // Enabled by default
            admin_id,
        )
        .await
        {
            Ok(config_id) => {
                log::info!(
                    "Created default latency config: {} -> {} (id: {})",
                    display_name,
                    target_address,
                    config_id
                );
            }
            Err(e) => {
                log::error!(
                    "Failed to create latency config {} -> {}: {}",
                    display_name,
                    target_address,
                    e
                );
                return Err(anyhow!(
                    "Failed to create latency config {}: {}",
                    display_name,
                    e
                ));
            }
        }
    }

    log::info!(
        "Successfully created {} default latency configs for server {}",
        configs_count,
        server_id
    );

    // 通知zf-controler更新延迟监控配置
    notify_controler_config_update(app_state, Some(server_id)).await;

    Ok(())
}

pub async fn generate_worker_setup_script(
    db: Arc<PrismaClient>,
    worker_pub_key: &str,
    mgmt_pubkey: &str,
    mgmt_url: &str,
) -> anyhow::Result<String> {
    let data = db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::pubkey::equals(
            worker_pub_key.to_owned(),
        ))
        .exec()
        .await?
        .ok_or(anyhow!("machine not found"))?;
    let script_content = WORKER_SCRIPT_TEMPLATE;
    let port_start = data.port_start.unwrap_or(30000).to_string();
    let port_end = data.port_end.unwrap_or(31000).to_string();
    let private_key = data.private_key.unwrap_or("".to_string());
    let mgmt_hammer_proxy = data.proxy_config.unwrap_or("".to_string());
    let outbound_proxy_addr = if data.use_forward_as_tun.unwrap_or(false) {
        "use_proxy".to_string()
    } else {
        "direct".to_string()
    };

    // Convert protocol filters to JSON string for the worker config
    let protocol_filters_json = if data.protocol_filters.is_empty() {
        "null".to_string()
    } else {
        let filter_strings: Vec<String> = data
            .protocol_filters
            .iter()
            .map(|f| match f.as_str() {
                "Http" => "\"Http\"".to_string(),
                "Socks5" => "\"Socks5\"".to_string(),
                "BitTorrent" => "\"BitTorrent\"".to_string(),
                "Tls" => "\"Tls\"".to_string(),
                _ => format!("\"{}\"", f),
            })
            .collect();
        format!("[{}]", filter_strings.join(", "))
    };
    let url = Url::parse(mgmt_url).unwrap();
    let mgmt_port = url.port().unwrap_or(3100).to_string();
    let replacements = HashMap::from([
        ("MGMT_ARRANGER_PUBLIC_KEY_STR", mgmt_pubkey),
        ("ARRANGER_URL_STR", mgmt_url),
        ("ARRANGER_PORT_STR", &mgmt_port),
        ("WORKER_PUBKEY", worker_pub_key),
        ("PORT_START_STR", &port_start),
        ("PORT_END_STR", &port_end),
        ("PRIVATE_KEY_STR", &private_key),
        ("MGMT_HAMMER_PROXY_STR", &mgmt_hammer_proxy),
        ("OUTBOUND_PROXY_ADDR_STR", &outbound_proxy_addr),
        ("PROTOCOL_FILTERS_JSON", &protocol_filters_json),
    ]);
    let script = replace_placeholders(&script_content, &replacements);
    Ok(script)
}

pub async fn remove_line(db: Arc<PrismaClient>, line_id: i32) -> anyhow::Result<()> {
    db._transaction()
        .run(|tx| async move {
            let line = tx
                .outbound_endpoint()
                .find_unique(prisma::outbound_endpoint::id::equals(line_id))
                .select(prisma::outbound_endpoint::select!({ id }))
                .exec()
                .await?
                .ok_or_else(|| anyhow!("line not found"))?;
            // delete all ports for this line
            let _ = tx
                .port()
                .delete_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                    line_id,
                ))])
                .exec()
                .await?;

            // deleted this line for subscription who has this line
            let subs = tx
                .subscription()
                .find_many(vec![prisma::subscription::lines::has_some(vec![line.id])])
                .select(prisma::subscription::select!({
                    id
                    lines
                }))
                .exec()
                .await?;
            for s in subs.iter() {
                let new_lines = s
                    .lines
                    .iter()
                    .filter(|x| *x != &line.id)
                    .map(|x| *x)
                    .collect::<Vec<_>>();
                let _ = tx
                    .subscription()
                    .update(
                        prisma::subscription::id::equals(s.id),
                        vec![prisma::subscription::lines::set(new_lines)],
                    )
                    .exec()
                    .await?;
            }

            // remove this line from package
            let _ = tx
                .package_line()
                .delete_many(vec![prisma::package_line::line_id::equals(line.id)])
                .exec()
                .await?;

            // delete this line
            tx.outbound_endpoint()
                .delete(prisma::outbound_endpoint::id::equals(line.id))
                .exec()
                .await?;
            Ok::<(), anyhow::Error>(())
        })
        .await?;
    Ok(())
}
