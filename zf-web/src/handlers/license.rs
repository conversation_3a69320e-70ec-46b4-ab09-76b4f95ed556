use anyhow::anyhow;
use chrono::Utc;
use common::{
    ApplyRenewalCodeRequest, ApplyRenewalCodeResponse, ControlerLicenseInfoResponse,
    ControlerRenewalHistoryResponse, RenewalHistoryItem, RenewalHistoryResponse,
};
use log::{error, info};
use warp::{reject::Rejection, reply::Reply};

use crate::error::Error;
use crate::{
    message::{CalculateRenewalPriceResponse, SoftwareLicenseInfo},
    AppState,
};

pub async fn handle_apply_renewal_code(
    _token_id: String,
    request: ApplyRenewalCodeRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Apply renewal code request for zf-controler software license");

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/apply-renewal",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Calling zf-controler at: {}", controler_url);

    let response = client
        .post(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .json(&request)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: ApplyRenewalCodeResponse = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!("Software license renewal code applied successfully");
        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Software license renewal failed: {}",
            error_text
        ))))
    }
}

pub async fn handle_get_renewal_history(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get renewal history request for token_id: {}", token_id);

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-history",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let controler_response: ControlerRenewalHistoryResponse =
            response.json().await.map_err(|e| {
                error!("Failed to parse zf-controler response: {}", e);
                Error::InternalError(anyhow!("Failed to parse zf-controler response"))
            })?;

        let history = RenewalHistoryResponse {
            history: controler_response
                .data
                .into_iter()
                .filter_map(|item| {
                    Some(RenewalHistoryItem {
                        date: chrono::DateTime::parse_from_rfc3339(&item.date)
                            .ok()
                            .map(|dt| dt.with_timezone(&Utc))?,
                        code_id: item.code_id,
                        extended_days: item.extended_days as i32,
                        new_expiry_date: chrono::DateTime::parse_from_rfc3339(
                            &item.new_expiry_date,
                        )
                        .ok()
                        .map(|dt| dt.with_timezone(&Utc))?,
                        status: item.status,
                        amount: item.amount,
                    })
                })
                .collect(),
        };

        Ok(warp::reply::json(&history))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        // Return empty history on error instead of failing
        let history = RenewalHistoryResponse { history: vec![] };
        Ok(warp::reply::json(&history))
    }
}

pub async fn handle_get_software_license_info(
    _token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get software license info request");

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/info",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Fetching software license info from: {}", controler_url);

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let controler_response: ControlerLicenseInfoResponse =
            response.json().await.map_err(|e| {
                error!("Failed to parse zf-controler response: {}", e);
                Error::InternalError(anyhow!("Failed to parse zf-controler response"))
            })?;

        // Extract license information from the zf-controler response
        let instance = &controler_response.data;

        let license_expires_at = chrono::DateTime::parse_from_rfc3339(&instance.license_expires_at)
            .map_err(|e| {
                error!(
                    "Failed to parse license expiration date '{}': {}",
                    instance.license_expires_at, e
                );
                Error::InternalError(anyhow!("Invalid license expiration date format"))
            })?
            .with_timezone(&Utc);

        let license_id = &instance.id;

        let now = Utc::now();
        let is_expired = license_expires_at < now;
        let days_remaining = if is_expired {
            0
        } else {
            (license_expires_at - now).num_days() as i32
        };

        let license_info = SoftwareLicenseInfo {
            valid_until: license_expires_at,
            is_expired,
            days_remaining,
            license_id: license_id.clone(),
            entitlements: instance.entitlements.clone(),
            monthly_rate: instance.monthly_rate.clone(),
        };

        info!("Successfully retrieved software license info");
        Ok(warp::reply::json(&license_info))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to fetch software license info: {}",
            error_text
        ))))
    }
}

// 续费请求相关handler

pub async fn handle_create_renewal_request(
    _token_id: String,
    request: serde_json::Value,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Create renewal request for zf-controler software license");

    // Call zf-controler API
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-requests",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Calling zf-controler at: {}", controler_url);

    let response = client
        .post(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .json(&request)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!("Renewal request created successfully");
        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to create renewal request: {}",
            error_text
        ))))
    }
}

pub async fn handle_get_renewal_requests(
    _token_id: String,
    page: Option<u32>,
    page_size: Option<u32>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get renewal requests for token_id: {}", _token_id);

    // Call zf-controler API
    let client = reqwest::Client::new();
    let mut controler_url = format!(
        "{}/api/license/renewal-requests",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    // Add query parameters if provided
    let mut query_params = vec![];
    if let Some(p) = page {
        query_params.push(format!("page={}", p));
    }
    if let Some(ps) = page_size {
        query_params.push(format!("page_size={}", ps));
    }
    if !query_params.is_empty() {
        controler_url.push_str(&format!("?{}", query_params.join("&")));
    }

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        // Return empty response on error instead of failing
        let empty_response = serde_json::json!({
            "success": false,
            "error": error_text
        });
        Ok(warp::reply::json(&empty_response))
    }
}

pub async fn handle_cancel_renewal_request(
    _token_id: String,
    request_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Cancel renewal request {} for token_id: {}",
        request_id, _token_id
    );

    // Call zf-controler API
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-requests/{}",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/'),
        request_id
    );

    let response = client
        .delete(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!("Renewal request cancelled successfully");
        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to cancel renewal request: {}",
            error_text
        ))))
    }
}

pub async fn handle_calculate_renewal_price(
    _token_id: String,
    requested_duration: i32,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Calculate renewal price request for {} months",
        requested_duration
    );

    // Input validation
    if requested_duration < 1 || requested_duration > 12 {
        return Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Requested duration must be between 1 and 12 months"
        ))));
    }

    // Call zf-controler API for consistency with other license operations
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/calculate-renewal-price?requested_duration={}",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/'),
        requested_duration
    );

    info!("Calling zf-controler at: {}", controler_url);

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    let response_text = response.text().await.map_err(|e| {
        error!("Failed to read zf-controler response: {}", e);
        Error::InternalError(anyhow!("Failed to read zf-controler response"))
    })?;

    // Try to parse as JSON to check if it's an error response
    match serde_json::from_str::<serde_json::Value>(&response_text) {
        Ok(json_value) => {
            // Check if this is an error response
            if let Some(success) = json_value.get("success").and_then(|v| v.as_bool()) {
                if !success {
                    let error_msg = json_value
                        .get("error")
                        .and_then(|v| v.as_str())
                        .unwrap_or("Unknown error from zf-controler");
                    error!("zf-controler returned error: {}", error_msg);
                    return Err(warp::reject::custom(Error::InternalError(anyhow!(
                        "Failed to calculate renewal price: {}",
                        error_msg
                    ))));
                }
            }

            // Try to parse as CalculateRenewalPriceResponse
            match serde_json::from_value::<CalculateRenewalPriceResponse>(json_value) {
                Ok(response_data) => {
                    info!(
                        "Calculated renewal price: {} CNY for {} months",
                        response_data.total_price, requested_duration
                    );
                    Ok(warp::reply::json(&response_data))
                }
                Err(e) => {
                    error!("Failed to parse zf-controler response as CalculateRenewalPriceResponse: {}", e);
                    error!("Response was: {}", response_text);
                    Err(warp::reject::custom(Error::InternalError(anyhow!(
                        "Failed to parse zf-controler response"
                    ))))
                }
            }
        }
        Err(e) => {
            error!("zf-controler returned invalid JSON: {}", e);
            error!("Response was: {}", response_text);
            Err(warp::reject::custom(Error::InternalError(anyhow!(
                "zf-controler returned invalid response format"
            ))))
        }
    }
}

pub async fn handle_get_entitlements(
    _token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get entitlements request");

    // Call zf-controler API to get entitlements
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/entitlements",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Calling zf-controler at: {}", controler_url);

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: crate::GetEntitlementsResponse = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!("Successfully retrieved entitlements from zf-controler");
        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);

        // Return error response but don't fail completely
        let error_response = crate::GetEntitlementsResponse {
            success: false,
            data: None,
            error: Some(error_text),
        };
        Ok(warp::reply::json(&error_response))
    }
}

// 发送续费请求消息
pub async fn handle_send_renewal_request_message(
    _token_id: String,
    request_id: String,
    message: serde_json::Value,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Send message to renewal request {} for token_id: {}",
        request_id, _token_id
    );

    // Call zf-controler API
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-requests/{}/messages",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/'),
        request_id
    );
    
    info!("Constructed controler URL: {}", controler_url);
    info!("Request ID: '{}'", request_id);
    info!("Message payload: {}", serde_json::to_string(&message).unwrap_or_else(|_| "invalid json".to_string()));

    let response = client
        .post(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .json(&message)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!(
            "Message sent successfully to renewal request {}",
            request_id
        );
        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to send message: {}",
            error_text
        ))))
    }
}

// 获取续费请求消息历史
pub async fn handle_get_renewal_request_messages(
    _token_id: String,
    request_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Get messages for renewal request {} for token_id: {}",
        request_id, _token_id
    );

    // Call zf-controler API
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-requests/{}/messages",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/'),
        request_id
    );

    let response = client
        .get(&controler_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_data: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        Ok(warp::reply::json(&response_data))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        // Return empty messages on error instead of failing
        let empty_response = serde_json::json!({
            "success": false,
            "error": error_text,
            "data": []
        });
        Ok(warp::reply::json(&empty_response))
    }
}
