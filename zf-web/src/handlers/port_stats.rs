use std::collections::HashMap;

use anyhow::anyhow;
use common::{
    app_message::{ConnectionStatsInfo, LatencyTestMethod, WorkerGetConnectionStatsResponse},
    prisma::subscription,
};
use warp::{reject::Rejection, reply::Reply};

use crate::{
    error::Error,
    forwarder::{test_fwd_and_remote_latency, test_remote_latency, TestFwdAndRemoteLatencyResult},
    message::{GetConnectionStatsResponse, TestLatencyResponse},
    prisma, AppState, GetConnectionStatsRequest, TestLatencyRequest,
};

pub async fn handle_get_connection_stats(
    token_id: String,
    request: GetConnectionStatsRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Get connection stats for token_id: '{}' port_id: '{}'",
        token_id,
        request.port_id
    );
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({
            id
            is_admin
        }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let port = app_state
        .db
        .port()
        .find_first(vec![
            prisma::port::id::equals(request.port_id),
            prisma::port::subscription_id::equals(Some(subscription.id)),
        ])
        .select(prisma::port::select!(
            {
                id
                outbound_endpoint_id
                port_v_4
                display_name
                related_forward_endpoint_ids
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Port not found".to_string()))?;

    let outbound_endpoint_id =
        port.outbound_endpoint_id
            .ok_or(warp::reject::custom(Error::NotFound(format!(
                "Outbound data not found for port: {}",
                port.id
            ))))?;

    // Make HTTP request to controller to get connection stats
    let url = format!(
        "{}/get_connection_stats",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    let request_body = serde_json::json!({
        "worker_id": outbound_endpoint_id,
        "port": port.port_v_4
    });

    let response = app_state
        .web_client
        .post(&url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .json(&request_body)
        .send()
        .await
        .map_err(|e| {
            warp::reject::custom(Error::InternalError(anyhow!(
                "Failed to request connection stats: {}",
                e
            )))
        })?;

    if !response.status().is_success() {
        let error_text = response.text().await.unwrap_or_default();
        return Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Controller returned error: {}",
            error_text
        ))));
    }

    let controller_response: WorkerGetConnectionStatsResponse =
        response.json().await.map_err(|e| {
            warp::reject::custom(Error::InternalError(anyhow!(
                "Failed to parse controller response: {}",
                e
            )))
        })?;

    // Parse connections from controller response
    let raw_connections = controller_response.connections;

    let mut processed_connections = Vec::new();

    for raw_conn in raw_connections {
        // Convert forwarder IDs to display names with validation
        let mut forwarder_names = Vec::new();
        // 管理员可以查看所有转发端点, 或者端点关联了转发端点
        if subscription.is_admin || !port.related_forward_endpoint_ids.is_empty() {
            for id_str in &raw_conn.forwarder_names {
                match id_str.parse::<i32>() {
                    Ok(id) if id > 0 => {
                        let Ok(forward_endpoint) =
                            app_state.cache_forward_endpoint_data.get(&id).await
                        else {
                            continue;
                        };
                        forwarder_names.push(forward_endpoint.display_name);
                    }
                    Ok(_) => {
                        log::warn!("Invalid forwarder IDs found: {}", id_str);
                    }
                    Err(e) => {
                        log::warn!("Invalid forwarder IDs found: {} error: {}", id_str, e);
                    }
                }
            }
        }

        let connection_info = ConnectionStatsInfo {
            id: raw_conn.id,
            start_time: raw_conn.start_time,
            client_addr: raw_conn.client_addr,
            target_addr: raw_conn.target_addr,
            forwarder_names,
            conn_type: raw_conn.conn_type,
        };

        processed_connections.push(connection_info);
    }

    Ok(warp::reply::json(&GetConnectionStatsResponse {
        connections: processed_connections,
    }))
}

fn micros_to_string(micros: u64) -> String {
    format!("{:.2}ms", micros as f32 / 1000.0)
}
pub async fn handle_test_latency(
    token_id: String,
    request: TestLatencyRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Test latency for token_id: \'{}\' port_id: \'{}\'",
        token_id,
        request.port_id
    );
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let port = app_state
        .db
        .port()
        .find_first(vec![
            prisma::port::id::equals(request.port_id),
            prisma::port::subscription_id::equals(Some(subscription.id)),
        ])
        .select(prisma::port::select!(
            {
                id
                outbound_endpoint_id
                related_forward_endpoint_ids
                related_tot_server_ids
                target_addr_list
                target_address_v_4
                target_port_v_4
                test_method
                forward_config
                display_name
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Port not found".to_string()))?;

    let outbound_endpoint_id =
        port.outbound_endpoint_id
            .ok_or(warp::reject::custom(Error::NotFound(format!(
                "Outbound data not found for port: {}",
                port.id
            ))))?;
    let outbound_endpoint = app_state
        .db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::id::equals(outbound_endpoint_id))
        .select(prisma::outbound_endpoint::select!({ allow_latency_test
            use_forward_as_tun }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Outbound endpoint not found".to_string()))?;

    if port.related_forward_endpoint_ids.len() == 0
        || outbound_endpoint.use_forward_as_tun.unwrap_or(false)
        || port.related_tot_server_ids.len() > 0
    {
        // 只有一台机器
        // 直接测试ping延迟

        if outbound_endpoint.allow_latency_test.unwrap_or(false) {
            let mut remote = vec![];
            for target in port.target_addr_list.iter() {
                let target_addr = tokio::net::lookup_host(target)
                    .await
                    .map_err(|e| {
                        Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                    })?
                    .next()
                    .ok_or(Error::InternalError(anyhow::anyhow!(
                        "socket address is empty for target: {}",
                        target
                    )))?;
                remote.push(target_addr);
            }
            if remote.is_empty() {
                let target_addr = tokio::net::lookup_host((
                    port.target_address_v_4.as_str(),
                    port.target_port_v_4 as u16,
                ))
                .await
                .map_err(|e| {
                    Error::InternalError(anyhow!(
                        "Failed to resolve target: {} address: {}",
                        port.target_address_v_4,
                        e
                    ))
                })?
                .next()
                .ok_or(Error::InternalError(anyhow::anyhow!(
                    "socket address is empty for target: {}",
                    port.target_address_v_4
                )))?;
                remote.push(target_addr);
            }
            let latency = test_remote_latency(
                &app_state.web_client,
                &app_state.opt.mgmt_pubkey,
                app_state.opt.mgmt_url.clone(),
                remote,
                outbound_endpoint_id,
                port.test_method
                    .as_ref()
                    .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok())
                    .unwrap_or(LatencyTestMethod::Icmp),
                port.forward_config.clone(),
            )
            .await
            .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
            let mut result_html = format!("Test Result: \n");
            let mut remote_latency: HashMap<String, (Option<String>, String)> = HashMap::new();
            for (idx, latency) in latency.iter().enumerate() {
                result_html += &format!(
                    "    Entry -> {}: {}\n",
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4),
                    micros_to_string(*latency)
                );
                remote_latency.insert(
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4)
                        .clone(),
                    (None, micros_to_string(*latency)),
                );
            }
            Ok(warp::reply::json(&TestLatencyResponse {
                content: result_html,
            }))
        } else {
            return Err(warp::reject::custom(Error::NotFound(
                "Outbound endpoint not allow latency test".to_string(),
            )));
        }
    } else {
        // 多台机器测试反馈
        // 测试worker到forwarder的延迟
        let forward_config = port
            .forward_config
            .ok_or(warp::reject::custom(Error::NotFound(format!(
                "Forward config not found for port: {}",
                port.display_name
            ))))?;
        let mut remote = vec![];
        let forward_endpoint = app_state
            .db
            .forward_endpoint()
            .find_many(vec![prisma::forward_endpoint::id::in_vec(
                port.related_forward_endpoint_ids,
            )])
            .select(prisma::forward_endpoint::select!(
                {
                    id
                    display_name
                }
            ))
            .exec()
            .await
            .map_err(Error::Database)?;
        let id_to_name = forward_endpoint
            .iter()
            .map(|f| (f.id.to_string(), f.display_name.clone()))
            .collect::<HashMap<_, _>>();
        for target in port.target_addr_list.iter() {
            let target_addr = tokio::net::lookup_host(target)
                .await
                .map_err(|e| {
                    Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                })?
                .next()
                .ok_or(anyhow::anyhow!(
                    "socket address is empty for target: {}",
                    target
                ))
                .map_err(|e| {
                    Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                })?;
            remote.push(target_addr);
        }
        if remote.is_empty() {
            let target_addr = tokio::net::lookup_host((
                port.target_address_v_4.as_str(),
                port.target_port_v_4 as u16,
            ))
            .await
            .map_err(|e| {
                Error::InternalError(anyhow!(
                    "Failed to resolve target: {} address: {}",
                    port.target_address_v_4,
                    e
                ))
            })?
            .next()
            .ok_or(Error::InternalError(anyhow::anyhow!(
                "socket address is empty for target: {}",
                port.target_address_v_4
            )))?;
            remote.push(target_addr);
        }
        let test_result = test_fwd_and_remote_latency(
            &app_state.web_client,
            &app_state.opt.mgmt_pubkey,
            app_state.opt.mgmt_url.clone(),
            forward_config,
            outbound_endpoint_id,
            remote,
            port.test_method
                .as_ref()
                .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok())
                .unwrap_or(LatencyTestMethod::Icmp),
        )
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

        let mut result_html = format!("Test Result: \n");
        let mut fwd_server_latency = HashMap::new();
        let mut remote_latency = HashMap::new();
        for TestFwdAndRemoteLatencyResult {
            fwd_name,
            to_fwd_latency,
            fwd_to_remote_latency,
        } in test_result.iter()
        {
            fwd_server_latency.insert(
                id_to_name.get(fwd_name).unwrap_or(fwd_name).clone(),
                micros_to_string(*to_fwd_latency),
            );
            result_html += &format!(
                "    Entry -> {} Latency: {}\n",
                id_to_name.get(fwd_name).unwrap_or(fwd_name),
                micros_to_string(*to_fwd_latency)
            );
            for (idx, latency) in fwd_to_remote_latency.iter().enumerate() {
                result_html += &format!(
                    "        {} -> {}: {}\n",
                    id_to_name.get(fwd_name).unwrap_or(fwd_name),
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4),
                    micros_to_string(*latency)
                );
                remote_latency.insert(
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4)
                        .clone(),
                    (
                        Some(id_to_name.get(fwd_name).unwrap_or(fwd_name).clone()),
                        micros_to_string(*latency),
                    ),
                );
            }
        }
        Ok(warp::reply::json(&TestLatencyResponse {
            content: result_html,
        }))
    }
}
