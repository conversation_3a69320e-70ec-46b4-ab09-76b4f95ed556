use std::collections::BTreeMap;
use std::net::IpAddr;
use std::io::Read;

use anyhow::anyhow;
use chrono::{DateTime, Duration, Utc};
use common::{
    app_message::UpdateLatencyMonitoringConfigRequest,
    stats::{CachedChart, CachedLatencyRealtimeStatus},
    tdengine_init::TaosClient,
};
use log::{debug, error, info, warn};
use redis::AsyncCommands;
use serde::Serialize;
use taos::{AsyncFetchable, AsyncQueryable, StreamExt};
use warp::{reject::Rejection, Reply};

use crate::{
    error::Error,
    message::{
        CreateLatencyConfigRequest, GetLatencyConfigsRequest, GetLatencyConfigsResponse,
        GetLatencyHistoryRequest, GetLatencyHistoryResponse, GetLatencyRealtimeResponse,
        LatencyConfigInfo, LatencyHistoryPoint, LatencyRealtimeStatus, UpdateLatencyConfigRequest,
    },
    prisma::{self, latency_test_config, subscription},
    AppState,
};

/// 创建延迟测试配置的内部函数（供HTTP handler和机器添加时复用）
pub async fn create_latency_config_internal(
    db: &std::sync::Arc<crate::prisma::PrismaClient>,
    server_id: i32,
    display_name: String,
    target_address: String,
    target_port: Option<i32>,
    test_type: String,
    packet_size: Option<i32>,
    timeout: Option<i32>,
    interval: Option<i32>,
    alert_threshold: Option<i32>,
    is_public: bool,
    is_enabled: bool,
    created_by: i32,
) -> anyhow::Result<i32> {
    let config = db
        .latency_test_config()
        .create_unchecked(
            server_id,
            display_name.clone(),
            target_address.clone(),
            test_type.clone(),
            created_by,
            vec![
                latency_test_config::target_port::set(target_port),
                latency_test_config::packet_size::set(packet_size),
                latency_test_config::timeout::set(timeout.unwrap_or(5000)),
                latency_test_config::interval::set(interval.unwrap_or(60)),
                latency_test_config::alert_threshold::set(alert_threshold.unwrap_or(3)),
                latency_test_config::is_public::set(is_public),
                latency_test_config::is_enabled::set(is_enabled),
            ],
        )
        .exec()
        .await
        .map_err(|e| anyhow!("Failed to create latency config: {}", e))?;

    log::info!(
        "Created latency config: {} -> {} (id: {})",
        display_name,
        target_address,
        config.id
    );

    Ok(config.id)
}

/// 通知zf-controler更新延迟监控配置
pub async fn notify_controler_config_update(app_state: &AppState, server_id: Option<i32>) {
    let update_request = UpdateLatencyMonitoringConfigRequest { server_id };

    let update_url = format!(
        "{}/update_latency_monitoring_config",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    match app_state
        .web_client
        .post(&update_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .json(&update_request)
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                info!(
                    "Successfully notified controler to update latency configs for server: {:?}",
                    server_id
                );
            } else {
                warn!(
                    "Failed to notify controler, status: {} for server: {:?}",
                    response.status(),
                    server_id
                );
            }
        }
        Err(e) => {
            warn!(
                "Failed to notify controler about latency config update for server {:?}: {}",
                server_id, e
            );
        }
    }
}

/// 获取延迟配置列表
pub async fn handle_get_latency_configs(
    token_id: String,
    query: GetLatencyConfigsRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get latency configs for token_id: '{}'", token_id);

    // 检查用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    let is_admin = subscription.is_admin;

    // 构建查询条件
    let mut where_conditions = vec![];

    // 非管理员只能看到公开的配置或自己创建的配置
    if !is_admin {
        where_conditions.push(prisma::latency_test_config::WhereParam::Or(vec![
            latency_test_config::is_public::equals(true),
            latency_test_config::created_by::equals(subscription.id),
        ]));
    }

    // 添加筛选条件
    if let Some(server_id) = query.server_id {
        where_conditions.push(latency_test_config::server_id::equals(server_id));
    }

    if let Some(is_enabled) = query.is_enabled {
        where_conditions.push(latency_test_config::is_enabled::equals(is_enabled));
    }

    if let Some(test_type) = &query.test_type {
        where_conditions.push(latency_test_config::test_type::equals(test_type.clone()));
    }

    // 分页参数
    let page = query.page.unwrap_or(1).max(1);
    let page_size = query.page_size.unwrap_or(20).min(100);
    let skip = ((page - 1) * page_size) as i64;

    // 查询配置列表
    let configs = app_state
        .db
        .latency_test_config()
        .find_many(where_conditions.clone())
        .skip(skip)
        .take(page_size as i64)
        .order_by(latency_test_config::created_at::order(
            prisma_client_rust::Direction::Desc,
        ))
        .include(latency_test_config::include!({
            server: select {
                id
                display_name
                ingress_ipv_4
            }
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // 查询总数
    let total = app_state
        .db
        .latency_test_config()
        .count(where_conditions)
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    // 转换为响应格式
    let configs_info: Vec<LatencyConfigInfo> = configs
        .into_iter()
        .map(|config| LatencyConfigInfo {
            id: config.id,
            server_id: config.server_id,
            server_name: Some(config.server.display_name.clone()),
            server_ip: Some(config.server.ingress_ipv_4.clone()),
            display_name: config.display_name,
            target_address: config.target_address,
            target_port: config.target_port,
            test_type: config.test_type,
            packet_size: config.packet_size,
            timeout: config.timeout,
            interval: config.interval,
            alert_threshold: config.alert_threshold,
            is_public: config.is_public,
            is_enabled: config.is_enabled,
            created_at: config.created_at.into(),
            updated_at: config.updated_at.into(),
        })
        .collect();

    let response = GetLatencyConfigsResponse {
        configs: configs_info,
        total,
        page,
        page_size,
    };

    Ok(warp::reply::json(&response))
}

/// 创建延迟配置
pub async fn handle_create_latency_config(
    token_id: String,
    request: CreateLatencyConfigRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Create latency config for token_id: '{}'", token_id);

    // 检查用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 如果不是管理员，先检查是否允许普通用户添加延迟监控
    if !subscription.is_admin {
        // 检查系统设置是否允许普通用户添加延迟监控
        let allow_user_latency_monitoring = app_state
            .cache_system_settings
            .get(&"allow_user_latency_monitoring".to_string())
            .await
            .unwrap_or_else(|_| "true".to_string());
            
        if allow_user_latency_monitoring.to_lowercase() != "true" {
            return Err(warp::reject::custom(Error::BadRequest(
                "系统管理员已禁用普通用户添加延迟监控功能".to_string(),
            )));
        }
        
        // 检查数量限制
        let existing_count = app_state
            .db
            .latency_test_config()
            .count(vec![
                latency_test_config::server_id::equals(request.server_id),
                latency_test_config::created_by::equals(subscription.id),
            ])
            .exec()
            .await
            .map_err(Error::Database)?;

        if existing_count >= app_state.opt.max_monitors_per_server as i64 {
            return Err(warp::reject::custom(Error::BadRequest(format!(
                "Maximum {} monitors per server allowed",
                app_state.opt.max_monitors_per_server
            ))));
        }
    }

    // 验证测试类型
    if !matches!(request.test_type.as_str(), "tcp" | "icmp") {
        return Err(warp::reject::custom(Error::BadRequest(
            "Invalid test_type, must be 'tcp' or 'icmp'".to_string(),
        )));
    }

    // TCP测试必须提供端口
    if request.test_type == "tcp" && request.target_port.is_none() {
        return Err(warp::reject::custom(Error::BadRequest(
            "TCP test requires target_port".to_string(),
        )));
    }

    // 验证端口范围
    if let Some(port) = request.target_port {
        if port < 1 || port > 65535 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Port must be between 1 and 65535".to_string(),
            )));
        }
    }

    // 验证超时和间隔参数
    if let Some(timeout) = request.timeout {
        if timeout < 1000 || timeout > 30000 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Timeout must be between 1000 and 30000 milliseconds".to_string(),
            )));
        }
    }

    if let Some(interval) = request.interval {
        if interval < 60 || interval > 3600 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Interval must be between 10 and 3600 seconds".to_string(),
            )));
        }
    }

    // 验证告警阈值
    if let Some(threshold) = request.alert_threshold {
        if threshold < 1 || threshold > 10 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Alert threshold must be between 1 and 10".to_string(),
            )));
        }
    }

    // 验证目标地址格式（防止内网地址和恶意输入）
    if !is_valid_target_address(&request.target_address) {
        return Err(warp::reject::custom(Error::BadRequest(
            "Invalid target address format".to_string(),
        )));
    }

    // 创建配置
    let config_id = create_latency_config_internal(
        &app_state.db,
        request.server_id,
        request.display_name.clone(),
        request.target_address.clone(),
        request.target_port,
        request.test_type.clone(),
        request.packet_size,
        request.timeout,
        request.interval,
        request.alert_threshold,
        if subscription.is_admin {
            request.is_public.unwrap_or(false)
        } else {
            false // 非管理员只能创建私有配置
        },
        request.is_enabled.unwrap_or(true),
        subscription.id,
    )
    .await
    .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

    // 获取创建的配置以便返回
    let config = app_state
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Created config not found".to_string()))?;

    // 通知zf-controler更新配置
    notify_controler_config_update(&app_state, Some(request.server_id)).await;

    Ok(warp::reply::json(&config))
}

/// 更新延迟配置
pub async fn handle_update_latency_config(
    token_id: String,
    config_id: i32,
    request: UpdateLatencyConfigRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Update latency config {} for token_id: '{}'",
        config_id, token_id
    );

    // 检查用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 检查配置是否存在并验证权限
    let existing_config = app_state
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .select(latency_test_config::select!({ created_by server_id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Latency config not found".to_string()))?;

    // 非管理员只能编辑自己创建的配置
    if !subscription.is_admin && existing_config.created_by != subscription.id {
        return Err(warp::reject::custom(Error::Forbidden(
            "You can only update your own latency configs".to_string(),
        )));
    }

    // 构建更新参数
    let mut update_params = vec![];

    if let Some(display_name) = request.display_name {
        if display_name.is_empty() || display_name.len() > 50 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Display name must be between 1 and 50 characters".to_string(),
            )));
        }
        update_params.push(latency_test_config::display_name::set(display_name));
    }

    if let Some(target_address) = request.target_address {
        if !is_valid_target_address(&target_address) {
            return Err(warp::reject::custom(Error::BadRequest(
                "Invalid target address format".to_string(),
            )));
        }
        update_params.push(latency_test_config::target_address::set(target_address));
    }

    if let Some(test_type) = request.test_type {
        if test_type != "tcp" && test_type != "icmp" {
            return Err(warp::reject::custom(Error::BadRequest(
                "Invalid test_type, must be 'tcp' or 'icmp'".to_string(),
            )));
        }
        update_params.push(latency_test_config::test_type::set(test_type));
    }

    if let Some(Some(target_port)) = request.target_port {
        if target_port < 1 || target_port > 65535 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Port must be between 1 and 65535".to_string(),
            )));
        }
        update_params.push(latency_test_config::target_port::set(Some(target_port)));
    }

    if let Some(Some(packet_size)) = request.packet_size {
        if packet_size < 8 || packet_size > 1400 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Packet size must be between 8 and 1400 bytes".to_string(),
            )));
        }
        update_params.push(latency_test_config::packet_size::set(Some(packet_size)));
    }

    if let Some(timeout) = request.timeout {
        if timeout < 1000 || timeout > 30000 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Timeout must be between 1000 and 30000 milliseconds".to_string(),
            )));
        }
        update_params.push(latency_test_config::timeout::set(timeout));
    }

    if let Some(interval) = request.interval {
        if interval < 10 || interval > 3600 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Interval must be between 10 and 3600 seconds".to_string(),
            )));
        }
        update_params.push(latency_test_config::interval::set(interval));
    }

    if let Some(alert_threshold) = request.alert_threshold {
        if alert_threshold < 1 || alert_threshold > 10 {
            return Err(warp::reject::custom(Error::BadRequest(
                "Alert threshold must be between 1 and 10".to_string(),
            )));
        }
        update_params.push(latency_test_config::alert_threshold::set(alert_threshold));
    }

    if let Some(is_public) = request.is_public {
        // 非管理员不能设置为公开
        let allowed_public = if subscription.is_admin {
            is_public
        } else {
            false
        };
        update_params.push(latency_test_config::is_public::set(allowed_public));
    }

    if let Some(is_enabled) = request.is_enabled {
        update_params.push(latency_test_config::is_enabled::set(is_enabled));
    }

    // 执行更新
    let updated_config = app_state
        .db
        .latency_test_config()
        .update(latency_test_config::id::equals(config_id), update_params)
        .exec()
        .await
        .map_err(Error::Database)?;

    info!("Updated latency config with id: {}", config_id);

    // 通知zf-controler更新配置
    notify_controler_config_update(&app_state, Some(existing_config.server_id)).await;

    Ok(warp::reply::json(&updated_config))
}

/// 删除延迟配置
pub async fn handle_delete_latency_config(
    token_id: String,
    config_id: i32,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Delete latency config {} for token_id: '{}'",
        config_id, token_id
    );

    // 检查用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 获取配置信息以便验证权限和通知controler
    let config_to_delete = app_state
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .select(latency_test_config::select!({ server_id created_by }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Latency config not found".to_string()))?;

    // 非管理员只能删除自己创建的配置
    if !subscription.is_admin && config_to_delete.created_by != subscription.id {
        return Err(warp::reject::custom(Error::Forbidden(
            "You can only delete your own latency configs".to_string(),
        )));
    }

    let server_id = config_to_delete.server_id;

    // 删除配置
    app_state
        .db
        .latency_test_config()
        .delete(latency_test_config::id::equals(config_id))
        .exec()
        .await
        .map_err(Error::Database)?;

    info!("Deleted latency config with id: {}", config_id);

    // 通知zf-controler更新配置
    notify_controler_config_update(&app_state, Some(server_id)).await;

    #[derive(Serialize)]
    struct DeleteResponse {
        success: bool,
    }

    Ok(warp::reply::json(&DeleteResponse { success: true }))
}

/// 获取延迟历史数据
pub async fn handle_get_latency_history(
    token_id: String,
    query: GetLatencyHistoryRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get latency history for token_id: '{}'", token_id);

    let server_id = query.server_id;
    let config_id = query.config_id;

    // 检查用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 如果指定了config_id，验证用户是否有权限访问该配置
    if let Some(cid) = config_id {
        let config = app_state
            .db
            .latency_test_config()
            .find_unique(latency_test_config::id::equals(cid))
            .select(latency_test_config::select!({ created_by is_public }))
            .exec()
            .await
            .map_err(Error::Database)?
            .ok_or_else(|| Error::NotFound("Latency config not found".to_string()))?;

        // 非管理员只能访问公开的配置或自己创建的配置
        if !subscription.is_admin && !config.is_public && config.created_by != subscription.id {
            return Err(warp::reject::custom(Error::Forbidden(
                "You can only access public configs or your own configs".to_string(),
            )));
        }
    } else {
        // 如果没有指定config_id但指定了server_id，需要验证用户对该服务器的配置访问权限
        // 这种情况下我们将在查询时过滤掉用户无权访问的配置
    }

    // 解析时间范围
    let (start_time, end_time) = parse_time_range(query.time_range.as_deref().unwrap_or("1h"))?;

    // 查询TDengine数据
    let mut history_points = query_latency_history_from_tdengine(
        &app_state.tdengine_client,
        server_id,
        config_id,
        start_time,
        end_time,
    )
    .await
    .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

    // 如果没有指定config_id且不是管理员，需要过滤掉用户无权访问的配置数据
    if config_id.is_none() && !subscription.is_admin {
        // 获取用户有权限访问的配置ID列表
        let mut where_conditions = vec![latency_test_config::server_id::equals(server_id)];
        where_conditions.push(prisma::latency_test_config::WhereParam::Or(vec![
            latency_test_config::is_public::equals(true),
            latency_test_config::created_by::equals(subscription.id),
        ]));

        let accessible_configs = app_state
            .db
            .latency_test_config()
            .find_many(where_conditions)
            .select(latency_test_config::select!({ id }))
            .exec()
            .await
            .map_err(Error::Database)?;

        let accessible_config_ids: std::collections::HashSet<i32> =
            accessible_configs.into_iter().map(|c| c.id).collect();

        // 过滤历史数据，只保留用户有权限访问的配置
        history_points.retain(|point| accessible_config_ids.contains(&point.config_id));
    }

    let response = GetLatencyHistoryResponse {
        server_id,
        config_id,
        time_range: query.time_range.unwrap_or_else(|| "1h".to_string()),
        start_time,
        end_time,
        data_points: history_points,
    };

    Ok(warp::reply::json(&response))
}

/// 获取服务器实时延迟状态
pub async fn handle_get_latency_realtime(
    token_id: String,
    server_id: i32,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Get realtime latency for server {} by token_id: '{}'",
        server_id, token_id
    );

    // 验证用户存在
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 查询服务器的所有延迟配置
    let mut where_conditions = vec![latency_test_config::server_id::equals(server_id)];

    // 非管理员只能看到公开的配置或自己创建的配置
    if !subscription.is_admin {
        where_conditions.push(prisma::latency_test_config::WhereParam::Or(vec![
            latency_test_config::is_public::equals(true),
            latency_test_config::created_by::equals(subscription.id),
        ]));
    }

    let configs = app_state
        .db
        .latency_test_config()
        .find_many(where_conditions)
        .exec()
        .await
        .map_err(Error::Database)?;

    // 查询每个配置的最新延迟数据，优先从Redis获取
    let mut status_map = BTreeMap::new();
    let mut missing_config_ids = Vec::new();

    // 先尝试从Redis获取数据
    if let Ok(mut conn) = app_state.redis_pool.get().await {
        for config in &configs {
            let cache_key = format!("latency_realtime:{}:{}", server_id, config.id);
            if let Ok(cached_data) = conn.get::<_, String>(&cache_key).await {
                if let Ok(cached_item) =
                    serde_json::from_str::<CachedLatencyRealtimeStatus>(&cached_data)
                {
                    let status = LatencyRealtimeStatus {
                        config_id: cached_item.config_id,
                        target_address: cached_item.target_address.clone(),
                        display_name: cached_item.display_name.clone(),
                        test_type: cached_item.test_type.clone(),
                        latency_us: cached_item.latency_us(),
                        success: cached_item.success(),
                        error_msg: cached_item.error_msg.clone(),
                        last_test_time: cached_item.last_test_time,
                    };
                    status_map.insert(config.id, status);
                    continue;
                }
            }
            missing_config_ids.push(config.id);
        }
    } else {
        // Redis连接失败，所有配置都需要从TDengine查询
        missing_config_ids = configs.iter().map(|c| c.id).collect();
    }

    // 对于Redis中没有的数据，从TDengine查询
    for config in configs {
        if missing_config_ids.contains(&config.id) {
            match query_latest_latency_from_tdengine(
                &app_state.tdengine_client,
                &app_state.db,
                server_id,
                config.id,
            )
            .await
            {
                Ok(Some(status)) => {
                    status_map.insert(config.id, status);
                }
                Ok(None) => {
                    // 没有数据的情况
                    status_map.insert(
                        config.id,
                        LatencyRealtimeStatus {
                            config_id: config.id,
                            target_address: config.target_address,
                            display_name: config.display_name,
                            test_type: config.test_type,
                            latency_us: None,
                            success: false,
                            error_msg: Some("No data available".to_string()),
                            last_test_time: None,
                        },
                    );
                }
                Err(e) => {
                    error!(
                        "Failed to query latest latency for config {}: {}",
                        config.id, e
                    );
                }
            }
        }
    }

    let response = GetLatencyRealtimeResponse {
        server_id,
        status: status_map,
    };

    Ok(warp::reply::json(&response))
}

/// 解析时间范围字符串
fn parse_time_range(time_range: &str) -> Result<(DateTime<Utc>, DateTime<Utc>), Error> {
    let end_time = Utc::now();
    let start_time = match time_range {
        "1h" => end_time - Duration::hours(1),
        "3h" => end_time - Duration::hours(3),
        "6h" => end_time - Duration::hours(6),
        "12h" => end_time - Duration::hours(12),
        "24h" | "1d" => end_time - Duration::hours(24),
        "7d" => end_time - Duration::days(7),
        _ => {
            return Err(Error::InvalidInput(format!(
                "Invalid time_range: {}. Supported values: 1h, 3h, 6h, 12h, 24h, 7d",
                time_range
            )));
        }
    };

    Ok((start_time, end_time))
}

/// 从TDengine查询延迟历史数据（使用聚合查询）
async fn query_latency_history_from_tdengine(
    tdengine_client: &TaosClient,
    server_id: i32,
    config_id: Option<i32>,
    start_time: DateTime<Utc>,
    end_time: DateTime<Utc>,
) -> anyhow::Result<Vec<LatencyHistoryPoint>> {
    // 验证和清理输入参数
    if server_id <= 0 {
        return Err(anyhow!("Invalid server_id: {}", server_id));
    }

    if let Some(id) = config_id {
        if id <= 0 {
            return Err(anyhow!("Invalid config_id: {}", id));
        }
    }

    // 计算时间范围和聚合间隔
    let duration_minutes = (end_time - start_time).num_minutes();
    let group_interval = match duration_minutes {
        1..=60 => "1m",      // 1 hour or less: 1 minute intervals
        61..=180 => "2m",    // 3 hours: 2 minute intervals
        181..=360 => "5m",   // 6 hours: 5 minute intervals
        361..=1440 => "15m", // 24 hours: 15 minute intervals
        _ => "1h",           // 7 days: 1 hour intervals
    };

    // 安全的参数验证和查询构建
    let start_time_str =
        sanitize_timestamp_string(&start_time.format("%Y-%m-%d %H:%M:%S%.3f").to_string())?;
    let end_time_str =
        sanitize_timestamp_string(&end_time.format("%Y-%m-%d %H:%M:%S%.3f").to_string())?;

    let query_sql = match config_id {
        Some(cid) => build_aggregated_history_query_with_config(
            server_id,
            cid,
            &start_time_str,
            &end_time_str,
            group_interval,
        )?,
        None => build_aggregated_history_query_without_config(
            server_id,
            &start_time_str,
            &end_time_str,
            group_interval,
        )?,
    };

    let mut data_points = Vec::new();
    let taos = tdengine_client.get().await?;
    match taos.query(&query_sql).await {
        Ok(mut result_set) => {
            let mut rows_stream = result_set.rows();
            while let Some(Ok(row)) = rows_stream.next().await {
                let row_values = row.into_values();
                if row_values.len() < 6 {
                    continue;
                }

                let timestamp = match row_values.get(0) {
                    Some(taos::Value::Timestamp(ts)) => {
                        // Convert TDengine timestamp to chrono DateTime
                        ts.to_naive_datetime().and_utc()
                    }
                    _ => continue,
                };
                let latency_us = match row_values.get(1) {
                    Some(taos::Value::Double(l)) => Some((*l as i64) as u64), // AVG returns double
                    _ => None,
                };
                let success_rate = match row_values.get(2) {
                    Some(taos::Value::Double(rate)) => *rate,
                    _ => 0.0,
                };
                let _error_msg = match row_values.get(3) {
                    Some(taos::Value::NChar(s)) => Some(s.clone()),
                    _ => None,
                };
                let config_id = match row_values.get(4) {
                    Some(taos::Value::Int(id)) => id.clone(),
                    _ => continue,
                };
                let target_address = match row_values.get(5) {
                    Some(taos::Value::NChar(addr)) => addr.clone(),
                    _ => String::new(),
                };

                // SmokePing统计字段（新增）
                let test_round = match row_values.get(6) {
                    Some(taos::Value::Int(r)) => Some(*r),
                    _ => None,
                };
                let samples_count = match row_values.get(7) {
                    Some(taos::Value::Double(s)) => Some(*s as i32), // AVG返回Double，转为Int
                    Some(taos::Value::Int(s)) => Some(*s),           // 兼容可能的Int返回
                    _ => None,
                };
                let min_latency_us = match row_values.get(8) {
                    Some(taos::Value::BigInt(l)) => Some(*l as u64),
                    _ => None,
                };
                let max_latency_us = match row_values.get(9) {
                    Some(taos::Value::BigInt(l)) => Some(*l as u64),
                    _ => None,
                };
                let median_latency_us = match row_values.get(10) {
                    Some(taos::Value::BigInt(l)) => Some(*l as u64),
                    _ => None,
                };
                let stddev_latency_us = match row_values.get(11) {
                    Some(taos::Value::Double(s)) => Some(*s),
                    _ => None,
                };
                let packet_loss_rate = match row_values.get(12) {
                    Some(taos::Value::Float(r)) => Some(*r),
                    _ => None,
                };

                // 包含所有数据点，包括完全失败的情况
                data_points.push(LatencyHistoryPoint {
                    timestamp,
                    config_id,
                    target_address,
                    latency_us: if success_rate > 0.0 { latency_us } else { None }, // 失败时不显示延迟
                    success: success_rate >= 0.5, // 如果成功率>=50%认为整体成功
                    error_msg: if success_rate < 1.0 {
                        Some(format!("Success rate: {:.1}%", success_rate * 100.0))
                    } else {
                        None
                    },
                    // SmokePing统计数据
                    test_round,
                    samples_count,
                    min_latency_us,
                    max_latency_us,
                    median_latency_us,
                    stddev_latency_us,
                    packet_loss_rate,
                });
            }
        }
        Err(e) => {
            error!("Failed to query latency history: {}", e);
            return Err(anyhow!("Failed to query latency history: {}", e));
        }
    }

    Ok(data_points)
}

/// 从TDengine查询最新延迟数据
async fn query_latest_latency_from_tdengine(
    tdengine_client: &TaosClient,
    db: &std::sync::Arc<prisma::PrismaClient>,
    server_id: i32,
    config_id: i32,
) -> anyhow::Result<Option<LatencyRealtimeStatus>> {
    // 输入验证
    if server_id <= 0 || config_id <= 0 {
        return Err(anyhow!(
            "Invalid server_id or config_id: {}, {}",
            server_id,
            config_id
        ));
    }

    let query_sql = build_latest_query(server_id, config_id)?;

    let taos = tdengine_client.get().await?;
    match taos.query(&query_sql).await {
        Ok(mut result_set) => {
            let mut rows_stream = result_set.rows();
            if let Some(Ok(row)) = rows_stream.next().await {
                let row_values = row.into_values();
                if row_values.len() < 5 {
                    return Ok(None);
                }

                let timestamp = match row_values.get(0) {
                    Some(taos::Value::Timestamp(ts)) => Some(
                        DateTime::<Utc>::from_timestamp(
                            ts.as_raw_i64() / 1000,
                            ((ts.as_raw_i64() % 1000) * 1_000_000) as u32,
                        )
                        .unwrap_or_default(),
                    ),
                    _ => None,
                };
                let latency_us = match row_values.get(1) {
                    Some(taos::Value::BigInt(l)) => Some(l.clone()),
                    _ => None,
                };
                let success = match row_values.get(2) {
                    Some(taos::Value::Bool(b)) => b.clone(),
                    _ => false,
                };
                let error_msg = match row_values.get(3) {
                    Some(taos::Value::NChar(s)) => Some(s.clone()),
                    _ => None,
                };
                let target_address = match row_values.get(4) {
                    Some(taos::Value::NChar(addr)) => addr.clone(),
                    _ => String::new(),
                };

                // 获取配置信息
                let config = get_latency_config_info(db, config_id).await?;

                return Ok(Some(LatencyRealtimeStatus {
                    config_id,
                    target_address,
                    display_name: config.0,
                    test_type: config.1,
                    latency_us: latency_us.map(|l| l as u64),
                    success,
                    error_msg,
                    last_test_time: timestamp,
                }));
            }
        }
        Err(e) => {
            error!("Failed to query latest latency: {}", e);
            return Err(anyhow!("Failed to query latest latency: {}", e));
        }
    }

    Ok(None)
}

/// 验证和清理时间戳字符串
fn sanitize_timestamp_string(timestamp: &str) -> anyhow::Result<String> {
    // 验证基本格式（长度和字符组成）
    if timestamp.len() != 23 {
        return Err(anyhow!(
            "Invalid timestamp length: expected 23 characters, got {}",
            timestamp.len()
        ));
    }

    // 验证格式：YYYY-MM-DD HH:MM:SS.fff
    let parts: Vec<&str> = timestamp.split(' ').collect();
    if parts.len() != 2 {
        return Err(anyhow!("Invalid timestamp format: missing space separator"));
    }

    // 验证日期部分
    let date_parts: Vec<&str> = parts[0].split('-').collect();
    if date_parts.len() != 3
        || date_parts[0].len() != 4
        || date_parts[1].len() != 2
        || date_parts[2].len() != 2
    {
        return Err(anyhow!("Invalid date format"));
    }

    // 验证时间部分
    let time_parts: Vec<&str> = parts[1].split(':').collect();
    if time_parts.len() != 3 {
        return Err(anyhow!("Invalid time format"));
    }

    let seconds_parts: Vec<&str> = time_parts[2].split('.').collect();
    if seconds_parts.len() != 2 || seconds_parts[0].len() != 2 || seconds_parts[1].len() != 3 {
        return Err(anyhow!("Invalid seconds format"));
    }

    // 验证所有部分都是数字
    for part in [
        date_parts[0],
        date_parts[1],
        date_parts[2],
        time_parts[0],
        time_parts[1],
        seconds_parts[0],
        seconds_parts[1],
    ] {
        if part.parse::<u32>().is_err() {
            return Err(anyhow!("Non-numeric value in timestamp: {}", part));
        }
    }

    // 额外验证：不允许SQL关键字或特殊字符
    let dangerous_patterns = [
        "'", "\"", ";", "--", "/*", "*/", "DROP", "DELETE", "INSERT", "UPDATE",
    ];
    for pattern in &dangerous_patterns {
        if timestamp.to_uppercase().contains(&pattern.to_uppercase()) {
            return Err(anyhow!(
                "Potentially dangerous pattern in timestamp: {}",
                pattern
            ));
        }
    }

    Ok(timestamp.to_string())
}

/// 构建聚合历史查询（带配置ID）
fn build_aggregated_history_query_with_config(
    server_id: i32,
    config_id: i32,
    start_time: &str,
    end_time: &str,
    group_interval: &str,
) -> anyhow::Result<String> {
    Ok(format!(
        "SELECT _wstart, AVG(latency_us) as avg_latency_us, AVG(CAST(success AS DOUBLE)) as success_rate, 
         LAST(error_msg) as error_msg, config_id, LAST(target_address) as target_address,
         LAST(test_round) as test_round, AVG(samples_count) as samples_count,
         MIN(min_latency_us) as min_latency_us, MAX(max_latency_us) as max_latency_us,
         AVG(median_latency_us) as median_latency_us, AVG(stddev_latency_us) as stddev_latency_us,
         AVG(packet_loss_rate) as packet_loss_rate
         FROM server_latency_tests 
         WHERE server_id = {} AND config_id = {} AND ts >= '{}' AND ts <= '{}' 
         PARTITION BY config_id, target_address INTERVAL({})",
        server_id, config_id, start_time, end_time, group_interval
    ))
}

/// 构建聚合历史查询（不带配置ID）
fn build_aggregated_history_query_without_config(
    server_id: i32,
    start_time: &str,
    end_time: &str,
    group_interval: &str,
) -> anyhow::Result<String> {
    Ok(format!(
        "SELECT _wstart, AVG(latency_us) as avg_latency_us, AVG(CAST(success AS DOUBLE)) as success_rate,
         LAST(error_msg) as error_msg, config_id, LAST(target_address) as target_address,
         LAST(test_round) as test_round, AVG(samples_count) as samples_count,
         MIN(min_latency_us) as min_latency_us, MAX(max_latency_us) as max_latency_us,
         AVG(median_latency_us) as median_latency_us, AVG(stddev_latency_us) as stddev_latency_us,
         AVG(packet_loss_rate) as packet_loss_rate
         FROM server_latency_tests 
         WHERE server_id = {} AND ts >= '{}' AND ts <= '{}' 
         PARTITION BY config_id, target_address INTERVAL({})",
        server_id, start_time, end_time, group_interval
    ))
}

/// 构建最新数据查询
fn build_latest_query(server_id: i32, config_id: i32) -> anyhow::Result<String> {
    Ok(format!(
        "SELECT LAST(ts), LAST(latency_us), LAST(success), LAST(error_msg), LAST(target_address)
         FROM server_latency_tests 
         WHERE server_id = {} AND config_id = {} AND ts > NOW() - 5m
         GROUP BY server_id, config_id",
        server_id, config_id
    ))
}

/// 获取延迟配置信息（辅助函数）
async fn get_latency_config_info(
    db: &std::sync::Arc<prisma::PrismaClient>,
    config_id: i32,
) -> anyhow::Result<(String, String)> {
    let config = db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .select(latency_test_config::select!({ display_name test_type }))
        .exec()
        .await
        .map_err(|e| anyhow!("Failed to fetch config info: {}", e))?
        .ok_or_else(|| anyhow!("Config {} not found", config_id))?;

    Ok((config.display_name, config.test_type))
}

/// 验证目标地址是否合法（防止内网地址和恶意输入）
fn is_valid_target_address(address: &str) -> bool {
    // 基本长度检查
    if address.is_empty() || address.len() > 253 {
        return false;
    }

    // 检查是否为IP地址
    if let Ok(ip) = address.parse::<IpAddr>() {
        match ip {
            IpAddr::V4(ipv4) => {
                // 禁止私有网段和特殊地址
                let octets = ipv4.octets();
                match octets {
                    // 私有网段
                    [10, _, _, _] => false,
                    [172, b, _, _] if b >= 16 && b <= 31 => false,
                    [192, 168, _, _] => false,
                    // 本地回环
                    [127, _, _, _] => false,
                    // 链路本地
                    [169, 254, _, _] => false,
                    // 多播和其他特殊地址
                    [_, _, _, _] if octets[0] >= 224 => false,
                    // 0.0.0.0 网段
                    [0, _, _, _] => false,
                    // 允许的公网IP
                    _ => true,
                }
            }
            IpAddr::V6(_) => {
                // 暂时不支持IPv6，可以根据需要开放
                false
            }
        }
    } else {
        // 域名验证
        is_valid_domain_name(address)
    }
}

/// 解压缩brotli压缩的图片数据
fn decompress_brotli_image(compressed_base64: &str) -> anyhow::Result<String> {
    // Base64解码
    let compressed_data = base64::Engine::decode(&base64::engine::general_purpose::STANDARD, compressed_base64)
        .map_err(|e| anyhow!("Failed to decode base64 data: {}", e))?;
    
    // Brotli解压缩
    let mut decompressor = brotli::Decompressor::new(compressed_data.as_slice(), 4096);
    let mut decompressed_data = Vec::new();
    decompressor.read_to_end(&mut decompressed_data)
        .map_err(|e| anyhow!("Failed to decompress brotli data: {}", e))?;
    
    // 重新编码为base64
    let reencoded_data = base64::Engine::encode(&base64::engine::general_purpose::STANDARD, &decompressed_data);
    Ok(reencoded_data)
}

/// 验证域名格式是否合法
fn is_valid_domain_name(domain: &str) -> bool {
    // 基本格式检查
    if domain.is_empty() || domain.len() > 253 {
        return false;
    }

    // 不能以点开始或结束
    if domain.starts_with('.') || domain.ends_with('.') {
        return false;
    }

    // 检查每个标签
    for label in domain.split('.') {
        if label.is_empty() || label.len() > 63 {
            return false;
        }

        // 标签不能以连字符开始或结束
        if label.starts_with('-') || label.ends_with('-') {
            return false;
        }

        // 标签只能包含字母、数字和连字符
        if !label.chars().all(|c| c.is_alphanumeric() || c == '-') {
            return false;
        }
    }

    // 至少要有一个点（即至少是二级域名）
    domain.contains('.')
}

/// 获取延迟图表
pub async fn handle_get_latency_chart(
    token_id: String,
    server_id: i32,
    config_id: i32,
    time_range: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Get latency chart for server {} config {} range {} by token_id: '{}'",
        server_id, config_id, time_range, token_id
    );

    // 验证用户权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ id is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    // 验证配置权限
    let config = app_state
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .select(latency_test_config::select!({ server_id created_by is_public }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Latency config not found".to_string()))?;

    // 验证服务器ID匹配
    if config.server_id != server_id {
        return Err(warp::reject::custom(Error::BadRequest(
            "Server ID does not match config".to_string(),
        )));
    }

    // 非管理员只能访问公开的配置或自己创建的配置
    if !subscription.is_admin && !config.is_public && config.created_by != subscription.id {
        return Err(warp::reject::custom(Error::Forbidden(
            "You can only access public configs or your own configs".to_string(),
        )));
    }

    // 验证时间范围
    let valid_ranges = ["1h", "3h", "6h", "12h", "24h", "7d"];
    if !valid_ranges.contains(&time_range.as_str()) {
        return Err(warp::reject::custom(Error::BadRequest(format!(
            "Invalid time range: {}. Supported: {}",
            time_range,
            valid_ranges.join(", ")
        ))));
    }

    // 首先尝试从Redis缓存获取图表
    let cache_key = format!("latency_chart:{}:{}:{}", server_id, config_id, time_range);

    match app_state.redis_pool.get().await {
        Ok(mut conn) => {
            match conn.get::<_, String>(&cache_key).await {
                Ok(cached_data) => {
                    match serde_json::from_str::<CachedChart>(&cached_data) {
                        Ok(mut chart) => {
                            // 检查是否过期
                            if chart.expires_at > Utc::now() {
                                // 解压缩缓存的图片数据
                                match decompress_brotli_image(&chart.image_data) {
                                    Ok(decompressed_image) => {
                                        chart.image_data = decompressed_image;
                                        info!("Serving decompressed cached chart from Redis for server {} config {} range {}", 
                                              server_id, config_id, time_range);
                                    }
                                    Err(e) => {
                                        warn!("Failed to decompress cached image data, using original: {}", e);
                                        info!("Serving cached chart from Redis for server {} config {} range {}", 
                                              server_id, config_id, time_range);
                                    }
                                }
                                return Ok(warp::reply::json(&chart));
                            } else {
                                info!(
                                    "Cached chart expired for server {} config {} range {}",
                                    server_id, config_id, time_range
                                );
                                // 删除过期缓存
                                let _ = conn.del::<_, ()>(&cache_key).await;
                            }
                        }
                        Err(e) => {
                            warn!("Failed to deserialize cached chart {}: {}", cache_key, e);
                        }
                    }
                }
                Err(e) => {
                    debug!("Chart not found in Redis cache: {}", e);
                }
            }
        }
        Err(e) => {
            warn!("Failed to get Redis connection for chart cache: {}", e);
        }
    }

    // 如果Redis中没有找到，向zf-controler请求生成图表
    let controller_url = format!(
        "{}/api/chart/generate/{}/{}/{}",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/'),
        server_id,
        config_id,
        time_range
    );

    match app_state
        .web_client
        .get(&controller_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(mut chart_data) => {
                        // 如果chart_data包含image_data字段，则解压缩
                        if let Some(image_data_value) = chart_data.get_mut("image_data") {
                            if let Some(compressed_image_str) = image_data_value.as_str() {
                                match decompress_brotli_image(compressed_image_str) {
                                    Ok(decompressed_image) => {
                                        *image_data_value = serde_json::Value::String(decompressed_image);
                                        info!(
                                            "Successfully decompressed and reprocessed chart for server {} config {} range {}",
                                            server_id, config_id, time_range
                                        );
                                    }
                                    Err(e) => {
                                        warn!("Failed to decompress image data, using original: {}", e);
                                        // 继续使用原始数据，不抛出错误
                                    }
                                }
                            }
                        }
                        
                        info!(
                            "Successfully generated chart via controler for server {} config {} range {}",
                            server_id, config_id, time_range
                        );
                        return Ok(warp::reply::json(&chart_data));
                    }
                    Err(e) => {
                        error!("Failed to parse chart response from controler: {}", e);
                    }
                }
            } else {
                error!(
                    "Controler returned error status {} for chart generation",
                    response.status()
                );
            }
        }
        Err(e) => {
            error!("Failed to request chart from controler: {}", e);
        }
    }

    // 如果都失败了，返回暂时无数据而不是服务不可用
    #[derive(Serialize)]
    struct NoDataResponse {
        success: bool,
        message: String,
        server_id: i32,
        config_id: i32,
        time_range: String,
        image_data: Option<String>,
        generated_at: DateTime<Utc>,
    }

    let response = NoDataResponse {
        success: false,
        message: "Temporarily no data available".to_string(),
        server_id,
        config_id,
        time_range,
        image_data: None,
        generated_at: Utc::now(),
    };

    Ok(warp::reply::json(&response))
}

/// 手动触发图表生成
pub async fn handle_trigger_chart_generation(
    token_id: String,
    server_id: Option<i32>,
    config_id: Option<i32>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Trigger chart generation by token_id: '{}'", token_id);

    // 验证管理员权限
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(subscription::select!({ is_admin }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Subscription not found".to_string()))?;

    if !subscription.is_admin {
        return Err(warp::reject::custom(Error::Forbidden(
            "Only admin can trigger chart generation".to_string(),
        )));
    }

    // 构建请求URL
    let mut trigger_url = format!(
        "{}/api/chart/trigger",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    // 添加查询参数
    let mut params = vec![];
    if let Some(sid) = server_id {
        params.push(format!("server_id={}", sid));
    }
    if let Some(cid) = config_id {
        params.push(format!("config_id={}", cid));
    }
    if !params.is_empty() {
        trigger_url.push('?');
        trigger_url.push_str(&params.join("&"));
    }

    // 向zf-controler发送触发请求
    match app_state
        .web_client
        .post(&trigger_url)
        .header("zfc-pubkey", &app_state.opt.mgmt_pubkey)
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(result) => {
                        info!(
                            "Successfully triggered chart generation via controler: {:?}",
                            result
                        );
                        Ok(warp::reply::json(&result))
                    }
                    Err(e) => {
                        error!("Failed to parse response from controler: {}", e);
                        Err(warp::reject::custom(Error::InternalError(anyhow!(
                            "Failed to parse response from controler: {}",
                            e
                        ))))
                    }
                }
            } else {
                error!(
                    "Controler returned error status {} for chart trigger",
                    response.status()
                );
                Err(warp::reject::custom(Error::InternalError(anyhow!(
                    "Controler returned error status: {}",
                    response.status()
                ))))
            }
        }
        Err(e) => {
            error!("Failed to request chart trigger from controler: {}", e);
            Err(warp::reject::custom(Error::InternalError(anyhow!(
                "Failed to request chart trigger from controler: {}",
                e
            ))))
        }
    }
}
