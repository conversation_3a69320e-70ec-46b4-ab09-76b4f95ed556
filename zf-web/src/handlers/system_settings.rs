use crate::error::Error;
use crate::{prisma, AppState};
use log::info;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use warp::{reject::Rejection, Reply};

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemSettingItem {
    pub key: String,
    pub value: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetSystemSettingsResponse {
    pub settings: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateSystemSettingsRequest {
    pub settings: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateSystemSettingRequest {
    pub value: String,
}

// 获取所有系统设置
pub async fn handle_get_system_settings(
    _token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get system settings request");

    let settings = app_state
        .db
        .system_settings()
        .find_many(vec![])
        .exec()
        .await
        .map_err(Error::Database)?;

    let mut settings_map = HashMap::new();
    for setting in settings {
        settings_map.insert(setting.key, setting.value);
    }

    // 如果没有设置，初始化默认值
    if settings_map.is_empty() {
        init_default_settings(&app_state).await?;
        
        // 重新获取设置
        let settings = app_state
            .db
            .system_settings()
            .find_many(vec![])
            .exec()
            .await
            .map_err(Error::Database)?;

        for setting in settings {
            settings_map.insert(setting.key, setting.value);
        }
    }

    let response = GetSystemSettingsResponse {
        settings: settings_map,
    };

    Ok(warp::reply::json(&response))
}

// 批量更新系统设置
pub async fn handle_update_system_settings(
    _token_id: String,
    request: UpdateSystemSettingsRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Update system settings request: {:?}", request.settings);

    for (key, value) in request.settings {
        app_state
            .db
            .system_settings()
            .upsert(
                prisma::system_settings::key::equals(key.clone()),
                prisma::system_settings::create(key.clone(), value.clone(), vec![]),
                vec![prisma::system_settings::value::set(value)],
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    Ok(warp::reply::json(&serde_json::json!({
        "success": true,
        "message": "Settings updated successfully"
    })))
}

// 获取单个系统设置
pub async fn handle_get_system_setting(
    _token_id: String,
    key: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get system setting request for key: {}", key);

    let setting = app_state
        .db
        .system_settings()
        .find_unique(prisma::system_settings::key::equals(key.clone()))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound(format!("Setting not found: {}", key)))?;

    Ok(warp::reply::json(&SystemSettingItem {
        key: setting.key,
        value: setting.value,
        description: setting.description,
    }))
}

// 更新单个系统设置
pub async fn handle_update_system_setting(
    _token_id: String,
    key: String,
    request: UpdateSystemSettingRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Update system setting request for key: {}, value: {}", key, request.value);

    app_state
        .db
        .system_settings()
        .upsert(
            prisma::system_settings::key::equals(key.clone()),
            prisma::system_settings::create(key, request.value.clone(), vec![]),
            vec![prisma::system_settings::value::set(request.value)],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    Ok(warp::reply::json(&serde_json::json!({
        "success": true,
        "message": "Setting updated successfully"
    })))
}

// 初始化默认设置
async fn init_default_settings(app_state: &AppState) -> Result<(), Rejection> {
    info!("Initializing default system settings");

    let default_settings = vec![
        (
            "site_title".to_string(),
            r#"{"zh": "莹火虫的世界", "en": "Firefly's World"}"#.to_string(),
            Some("网站标题 (多语言)".to_string()),
        ),
        (
            "allow_user_latency_monitoring".to_string(),
            "true".to_string(),
            Some("用户是否可以添加服务器延迟监控".to_string()),
        ),
    ];

    for (key, value, description) in default_settings {
        app_state
            .db
            .system_settings()
            .create(
                key,
                value,
                description.map(|d| vec![prisma::system_settings::description::set(Some(d))]).unwrap_or_default(),
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    Ok(())
}

// 获取网站标题 (无权限验证，公开接口)
pub async fn handle_get_site_title(
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get site title request");

    // 使用缓存获取网站标题
    let title = match app_state.cache_system_settings.get(&"site_title".to_string()).await {
        Ok(title_json) => {
            // 尝试解析JSON格式的标题
            match serde_json::from_str::<serde_json::Value>(&title_json) {
                Ok(parsed) => parsed,
                Err(_) => {
                    // 如果解析失败，使用默认标题
                    serde_json::json!({
                        "zh": "莹火虫的世界",
                        "en": "Firefly's World"
                    })
                }
            }
        }
        Err(_) => {
            // 如果缓存中没有找到，使用默认标题
            serde_json::json!({
                "zh": "莹火虫的世界", 
                "en": "Firefly's World"
            })
        }
    };

    Ok(warp::reply::json(&title))
}