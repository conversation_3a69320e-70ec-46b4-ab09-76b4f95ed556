use log::info;
use prisma_client_rust::Direction;
use warp::{reject::Rejection, reply::Reply};

use crate::{
    add_user::update_subscription_by_package_update,
    error::Error,
    message::{
        BillingType, CreatePackageRequest, CreatePackageResponse, DetailedPackageInfo,
        DetailedPackageListResponse, PackageInfo, PackageLineInfo, PackageOperationResponse,
        PackageSearchRequest, PaginatedPackageResponse, PaginationInfo, UpdatePackageRequest,
    },
    prisma, AppState,
};

pub async fn handle_get_package_list(
    search_request: Option<PackageSearchRequest>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get package list with search: {:?}", search_request);

    // Extract pagination parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20,
    };

    // Calculate skip and take for pagination
    let skip = ((page - 1) * page_size) as i64;
    let take = page_size as i64;

    // Build where conditions
    let mut where_conditions = vec![];

    if let Some(search) = &search_request {
        if let Some(name) = &search.name {
            if !name.trim().is_empty() {
                where_conditions.push(prisma::package::name::contains(name.trim().to_string()));
            }
        }
        if let Some(is_active) = search.is_active {
            where_conditions.push(prisma::package::is_active::equals(is_active));
        }
        if let Some(is_default) = search.is_default {
            where_conditions.push(prisma::package::is_default::equals(is_default));
        }
    }

    // Get total count
    let total_count = app_state
        .db
        .package()
        .count(where_conditions.clone())
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    // Query packages with search filters and pagination
    let packages = app_state
        .db
        .package()
        .find_many(where_conditions)
        .order_by(prisma::package::created_at::order(Direction::Desc))
        .skip(skip)
        .take(take)
        .include(prisma::package::include!({ package_lines }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // Get all unique line IDs to fetch line names
    let mut all_line_ids: Vec<i32> = packages
        .iter()
        .flat_map(|p| p.package_lines.iter().map(|l| l.line_id))
        .collect();
    all_line_ids.sort();
    all_line_ids.dedup();

    // Fetch line names
    let lines = if !all_line_ids.is_empty() {
        app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(all_line_ids)])
            .select(prisma::outbound_endpoint::select!({
                id
                display_name
            }))
            .exec()
            .await
            .map_err(Error::Database)?
    } else {
        vec![]
    };

    // Create a map of line_id -> display_name
    let line_names: std::collections::HashMap<i32, String> = lines
        .into_iter()
        .map(|line| (line.id, line.display_name))
        .collect();

    let package_infos: Vec<PackageInfo> = packages
        .into_iter()
        .map(|package| {
            let package_lines: Vec<PackageLineInfo> = package
                .package_lines
                .into_iter()
                .map(|line| PackageLineInfo {
                    id: line.id,
                    line_id: line.line_id,
                    line_name: line_names
                        .get(&line.line_id)
                        .cloned()
                        .unwrap_or_else(|| format!("Line {}", line.line_id)),
                    bandwidth_limit: line.bandwidth_limit,
                    traffic_scale: line.traffic_scale.map(|v| v as f32),
                    line_traffic: line.line_traffic.map(|v| v / 1024 / 1024 / 1024),
                })
                .collect();

            PackageInfo {
                id: package.id,
                name: package.name,
                display_name: package.display_name,
                description: package.description,
                bandwidth: package.bandwidth,
                total_traffic: package.total_traffic / 1024 / 1024 / 1024,
                max_ports_per_server: package.max_ports_per_server,
                allow_forward_endpoint: package.allow_forward_endpoint,
                allow_ip_num: package.allow_ip_num,
                allow_conn_num: package.allow_conn_num,
                is_active: package.is_active,
                is_default: package.is_default,
                package_lines,
                bill_type: match (
                    package.billing_type,
                    package.reset_days,
                    package.base_price,
                    package.recurring_price,
                ) {
                    (Some(0), Some(days), Some(_base), Some(recurring)) => {
                        Some(crate::message::BillingType::Cycle {
                            days: days as usize,
                            price: recurring as usize,
                        })
                    }
                    (Some(1), _, Some(base), _) => Some(crate::message::BillingType::OneTime {
                        price: base as usize,
                        days: package.reset_days.unwrap_or(365) as usize,
                    }),
                    _ => None,
                },
                total_days: package.reset_days.map(|d| d as u32),
                traffic_reset_days: package.traffic_reset_days,
                created_at: package.created_at.into(),
                updated_at: package.updated_at.into(),
            }
        })
        .collect();

    // Calculate pagination info
    let total_pages = (total_count + page_size - 1) / page_size;

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_count,
        total_pages,
    };

    let response = PaginatedPackageResponse {
        packages: package_infos,
        pagination: pagination_info,
    };

    Ok(warp::reply::json(&response))
}

pub async fn handle_get_package_detail(
    package_id: i32,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get package detail for id: {}", package_id);

    let package = app_state
        .db
        .package()
        .find_unique(prisma::package::id::equals(package_id))
        .include(prisma::package::include!({ package_lines }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Package not found".to_string()))?;

    // Get line names
    let line_ids: Vec<i32> = package.package_lines.iter().map(|l| l.line_id).collect();
    let lines = if !line_ids.is_empty() {
        app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(line_ids)])
            .select(prisma::outbound_endpoint::select!({
                id
                display_name
            }))
            .exec()
            .await
            .map_err(Error::Database)?
    } else {
        vec![]
    };

    // Create a map of line_id -> display_name
    let line_names: std::collections::HashMap<i32, String> = lines
        .into_iter()
        .map(|line| (line.id, line.display_name))
        .collect();

    let package_lines: Vec<PackageLineInfo> = package
        .package_lines
        .into_iter()
        .map(|line| PackageLineInfo {
            id: line.id,
            line_id: line.line_id,
            line_name: line_names
                .get(&line.line_id)
                .cloned()
                .unwrap_or_else(|| format!("Line {}", line.line_id)),
            bandwidth_limit: line.bandwidth_limit,
            traffic_scale: line.traffic_scale.map(|v| v as f32),
            line_traffic: line.line_traffic.map(|v| v / 1024 / 1024 / 1024),
        })
        .collect();

    let package_info = PackageInfo {
        id: package.id,
        name: package.name,
        display_name: package.display_name,
        description: package.description,
        bandwidth: package.bandwidth,
        total_traffic: package.total_traffic / 1024 / 1024 / 1024,
        max_ports_per_server: package.max_ports_per_server,
        allow_forward_endpoint: package.allow_forward_endpoint,
        allow_ip_num: package.allow_ip_num,
        allow_conn_num: package.allow_conn_num,
        is_active: package.is_active,
        is_default: package.is_default,
        package_lines,
        bill_type: Some(crate::message::BillingType::Cycle { days: 30, price: 0 }),
        total_days: Some(30),
        traffic_reset_days: package.traffic_reset_days,
        created_at: package.created_at.into(),
        updated_at: package.updated_at.into(),
    };

    Ok(warp::reply::json(&package_info))
}

pub async fn handle_create_package(
    request: CreatePackageRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Create package: {:?}", request);

    // Validate request
    if request.name.trim().is_empty() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package name cannot be empty".to_string(),
        )));
    }

    if request.display_name.trim().is_empty() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package display name cannot be empty".to_string(),
        )));
    }

    if request.max_ports_per_server < 1 {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Max ports per server must be at least 1".to_string(),
        )));
    }

    // Check if package name already exists
    let existing_package = app_state
        .db
        .package()
        .find_unique(prisma::package::name::equals(request.name.clone()))
        .exec()
        .await
        .map_err(Error::Database)?;

    if existing_package.is_some() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package name already exists".to_string(),
        )));
    }

    // If this package is set as default, unset other default packages
    if request.is_default {
        app_state
            .db
            .package()
            .update_many(
                vec![prisma::package::is_default::equals(true)],
                vec![prisma::package::is_default::set(false)],
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    // Convert traffic from GB to bytes
    let total_traffic_bytes = request.total_traffic as i64 * 1024 * 1024 * 1024;

    // Prepare billing fields
    let (billing_type, base_price, recurring_price, reset_days) = match &request.bill_type {
        Some(BillingType::Cycle { days, price }) => (
            Some(0),
            Some(*price as i32),
            Some(*price as i32),
            Some(*days as i32),
        ),
        Some(BillingType::OneTime { price, days }) => (
            Some(1),
            Some(*price as i32),
            Some(*price as i32),
            Some(*days as i32),
        ),
        None => (None, None, None, None),
    };

    // Create package
    let package = app_state
        .db
        .package()
        .create(
            request.name.clone(),
            request.display_name.clone(),
            total_traffic_bytes,
            request.max_ports_per_server,
            vec![
                prisma::package::description::set(request.description.clone()),
                prisma::package::bandwidth::set(request.bandwidth),
                prisma::package::allow_forward_endpoint::set(request.allow_forward_endpoint),
                prisma::package::allow_ip_num::set(request.allow_ip_num),
                prisma::package::allow_conn_num::set(request.allow_conn_num),
                prisma::package::is_active::set(request.is_active),
                prisma::package::is_default::set(request.is_default),
                // 计费字段
                prisma::package::billing_type::set(billing_type),
                prisma::package::base_price::set(base_price),
                prisma::package::recurring_price::set(recurring_price),
                prisma::package::reset_days::set(reset_days),
                prisma::package::traffic_reset_days::set(request.traffic_reset_days),
            ],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    // Create package lines
    for line_request in request.lines {
        let line_traffic_bytes = line_request
            .line_traffic
            .map(|gb| gb as i64 * 1024 * 1024 * 1024);

        app_state
            .db
            .package_line()
            .create(
                prisma::package::id::equals(package.id),
                line_request.line_id,
                vec![
                    prisma::package_line::bandwidth_limit::set(line_request.bandwidth_limit),
                    prisma::package_line::traffic_scale::set(
                        line_request.traffic_scale.map(|v| v as f64),
                    ),
                    prisma::package_line::line_traffic::set(line_traffic_bytes),
                ],
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    info!("Package created successfully: {}", package.id);
    let response = CreatePackageResponse {
        id: package.id,
        message: "Package created successfully".to_string(),
    };
    Ok(warp::reply::json(&response))
}

pub async fn handle_update_package(
    request: UpdatePackageRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Update package: {:?}", request);

    // Validate request
    if request.name.trim().is_empty() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package name cannot be empty".to_string(),
        )));
    }

    if request.display_name.trim().is_empty() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package display name cannot be empty".to_string(),
        )));
    }

    // Check if package exists
    let existing_package = app_state
        .db
        .package()
        .find_unique(prisma::package::id::equals(request.id))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Package not found".to_string()))?;

    // Check if name conflicts with other packages
    let name_conflict = app_state
        .db
        .package()
        .find_first(vec![
            prisma::package::name::equals(request.name.clone()),
            prisma::package::id::not(request.id),
        ])
        .exec()
        .await
        .map_err(Error::Database)?;

    if name_conflict.is_some() {
        return Err(warp::reject::custom(Error::InvalidInput(
            "Package name already exists".to_string(),
        )));
    }

    // If this package is set as default, unset other default packages
    if request.is_default && !existing_package.is_default {
        app_state
            .db
            .package()
            .update_many(
                vec![prisma::package::is_default::equals(true)],
                vec![prisma::package::is_default::set(false)],
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    // Convert traffic from GB to bytes
    let total_traffic_bytes = request.total_traffic as i64 * 1024 * 1024 * 1024;

    // Prepare billing fields
    let (billing_type, base_price, recurring_price, reset_days) = match &request.bill_type {
        Some(BillingType::Cycle { days, price }) => (
            0,
            Some(*price as i32),
            Some(*price as i32),
            Some(*days as i32),
        ),
        Some(BillingType::OneTime { price, days }) => (
            1,
            Some(*price as i32),
            Some(*price as i32),
            Some(*days as i32),
        ),
        None => {
            return Err(warp::reject::custom(Error::InvalidInput(
                "Billing type is required".to_string(),
            )))
        }
    };

    // Update package
    app_state
        .db
        .package()
        .update(
            prisma::package::id::equals(request.id),
            vec![
                prisma::package::name::set(request.name),
                prisma::package::display_name::set(request.display_name),
                prisma::package::description::set(request.description),
                prisma::package::bandwidth::set(request.bandwidth),
                prisma::package::total_traffic::set(total_traffic_bytes),
                prisma::package::max_ports_per_server::set(request.max_ports_per_server),
                prisma::package::allow_forward_endpoint::set(request.allow_forward_endpoint),
                prisma::package::allow_ip_num::set(request.allow_ip_num),
                prisma::package::allow_conn_num::set(request.allow_conn_num),
                prisma::package::is_active::set(request.is_active),
                prisma::package::is_default::set(request.is_default),
                // 计费字段
                prisma::package::billing_type::set(Some(billing_type)),
                prisma::package::base_price::set(base_price),
                prisma::package::recurring_price::set(recurring_price),
                prisma::package::reset_days::set(reset_days),
                prisma::package::traffic_reset_days::set(request.traffic_reset_days),
            ],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    // Delete existing package lines
    app_state
        .db
        .package_line()
        .delete_many(vec![prisma::package_line::package_id::equals(request.id)])
        .exec()
        .await
        .map_err(Error::Database)?;

    let line_ids: Vec<i32> = request.lines.iter().map(|x| x.line_id).collect();
    // Create new package lines
    for line_request in request.lines {
        let line_traffic_bytes = line_request
            .line_traffic
            .map(|gb| gb as i64 * 1024 * 1024 * 1024);

        app_state
            .db
            .package_line()
            .create(
                prisma::package::id::equals(request.id),
                line_request.line_id,
                vec![
                    prisma::package_line::bandwidth_limit::set(line_request.bandwidth_limit),
                    prisma::package_line::traffic_scale::set(
                        line_request.traffic_scale.map(|v| v as f64),
                    ),
                    prisma::package_line::line_traffic::set(line_traffic_bytes),
                ],
            )
            .exec()
            .await
            .map_err(Error::Database)?;
    }

    update_subscription_by_package_update(
        request.id,
        &app_state.db,
        request.bandwidth.map(|x| x as u32),
        request.total_traffic,
        request.max_ports_per_server as u32,
        request.bill_type.unwrap(),
        line_ids,
        request.allow_forward_endpoint,
        request.allow_ip_num,
        request.allow_conn_num,
    )
    .await
    .map_err(Error::InternalError)?;

    info!("Package updated successfully: {}", request.id);
    let response = PackageOperationResponse {
        message: "Package updated successfully".to_string(),
    };
    Ok(warp::reply::json(&response))
}

pub async fn handle_delete_package(
    package_id: i32,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Delete package: {}", package_id);

    // Check if package exists
    let _package = app_state
        .db
        .package()
        .find_unique(prisma::package::id::equals(package_id))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or_else(|| Error::NotFound("Package not found".to_string()))?;

    // Check if package is used by any subscriptions
    let subscription_count = app_state
        .db
        .subscription()
        .count(vec![prisma::subscription::package_id::equals(Some(
            package_id,
        ))])
        .exec()
        .await
        .map_err(Error::Database)?;

    if subscription_count > 0 {
        return Err(warp::reject::custom(Error::InvalidInput(format!(
            "Cannot delete package: {} subscription(s) are using this package",
            subscription_count
        ))));
    }

    // Delete package (package lines will be deleted automatically due to cascade)
    app_state
        .db
        .package()
        .delete(prisma::package::id::equals(package_id))
        .exec()
        .await
        .map_err(Error::Database)?;

    info!("Package deleted successfully: {}", package_id);
    let response = PackageOperationResponse {
        message: "Package deleted successfully".to_string(),
    };
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_detailed_package_list(
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get detailed package list for selection");

    let packages = app_state
        .db
        .package()
        .find_many(vec![prisma::package::is_active::equals(true)])
        .order_by(prisma::package::is_default::order(Direction::Desc))
        .include(prisma::package::include!({ package_lines }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // Get all unique line IDs to fetch line details
    let mut all_line_ids: Vec<i32> = packages
        .iter()
        .flat_map(|p| p.package_lines.iter().map(|l| l.line_id))
        .collect();
    all_line_ids.sort();
    all_line_ids.dedup();

    // Fetch line details
    let lines = if !all_line_ids.is_empty() {
        app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(all_line_ids)])
            .exec()
            .await
            .map_err(Error::Database)?
    } else {
        vec![]
    };

    // Create a map of line_id -> line details
    let line_details: std::collections::HashMap<i32, _> =
        lines.into_iter().map(|line| (line.id, line)).collect();

    let detailed_packages: Vec<DetailedPackageInfo> = packages
        .into_iter()
        .map(|package| {
            let lines: Vec<crate::message::LineInfo> = package
                .package_lines
                .iter()
                .filter_map(|pl| {
                    line_details.get(&pl.line_id).map(|line| {
                        crate::message::LineInfo {
                            id: line.id,
                            display_name: line.display_name.clone(),
                            ip_addr: line.ingress_ipv_4.clone(),
                            is_online: None, // outbound_endpoint doesn't have is_online field
                            port_start: line.port_start,
                            port_end: line.port_end,
                            allow_forward: line.allow_forward,
                            is_in_package: Some(true), // This line is in the package
                            traffic_scale: pl.traffic_scale.map(|x| x as f32), // Convert f64 to f32
                        }
                    })
                })
                .collect();

            DetailedPackageInfo {
                id: package.id,
                name: package.name,
                display_name: package.display_name,
                is_default: package.is_default,
                bandwidth: package.bandwidth,
                total_traffic: package.total_traffic,
                max_ports_per_server: package.max_ports_per_server,
                allow_forward_endpoint: package.allow_forward_endpoint,
                allow_ip_num: package.allow_ip_num,
                allow_conn_num: package.allow_conn_num,
                lines,
                // TODO: Add billing fields to package database schema and populate from DB
                // For now, use default cycle billing with 30 days
                bill_type: match (
                    package.billing_type,
                    package.reset_days,
                    package.base_price,
                    package.recurring_price,
                ) {
                    (Some(0), Some(days), Some(_base), Some(recurring)) => {
                        Some(crate::message::BillingType::Cycle {
                            days: days as usize,
                            price: recurring as usize,
                        })
                    }
                    (Some(1), _, Some(base), _) => Some(crate::message::BillingType::OneTime {
                        price: base as usize,
                        days: package.reset_days.unwrap_or(365) as usize,
                    }),
                    _ => None,
                },
                total_days: package.reset_days.map(|d| d as u32),
                traffic_reset_days: package.traffic_reset_days,
            }
        })
        .collect();

    let response = DetailedPackageListResponse {
        packages: detailed_packages,
    };
    Ok(warp::reply::json(&response))
}
