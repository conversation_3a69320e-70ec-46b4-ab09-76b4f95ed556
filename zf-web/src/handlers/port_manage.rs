use common::get_line_lock;
use log::{error, info, warn};
use prisma_client_rust::Direction;
use redis::AsyncCommands;
use warp::{reject::Rejection, reply::Reply};

use crate::{
    error::Error,
    forwarder::get_blance_strategy_from_proxy_config,
    handlers::{
        calculate_match_score, check_addr_list_with_admin_check, generate_tot_config_or_forward_config,
        matches_regex_or_exact, SubscriptionWithAdminInfo,
    },
    message::{
        AdminPortsByUserRequest, BatchUpdateOperation, BatchUpdatePortsRequest, BatchUpdatePortsResponse, CreatePortRequest, 
        ModifyPortRequest, PaginatedPortsResponse, PaginationInfo, PortInfo, PortSearchRequest, 
        PortUpdateDetail, ResumePortRequest, RmvPortRequest, SuspendPortRequest,
    },
    prisma,
    tot::get_tot_config_from_str,
    AppState,
};

pub async fn handle_create_port(
    token_id: String,
    port_request: CreatePortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Creating new port for token_id: {} with name: {}",
        token_id, port_request.display_name
    );
    // check target select mode
    if let Some(mode) = port_request.target_select_mode {
        let _mode = TryInto::<common::app_message::Mode>::try_into(mode)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    if let Some(test_method) = port_request.test_method {
        let _test_method = TryInto::<common::app_message::LatencyTestMethod>::try_into(test_method)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    // Validate port bandwidth
    if let Some(port_bandwidth) = port_request.bandwidth {
        if port_bandwidth == 0 {
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port bandwidth cannot be zero".to_string(),
            )));
        }
    }

    let token_id_clone = token_id.clone();

    let rst = match app_state
        .db
        ._transaction()
        .run(move |tx| {
            async move {
                let subscription = match tx
                    .subscription()
                    .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
                    .select(SubscriptionWithAdminInfo::select())
                    .exec()
                    .await
                {
                    Ok(Some(sub)) => sub,
                    Ok(None) => {
                        warn!("Subscription not found for token_id: {}", token_id);
                        return Err(Error::NotFound("Subscription not found".to_string()));
                    }
                    Err(e) => {
                        error!("Database error while fetching subscription: {}", e);
                        return Err(Error::Database(e));
                    }
                };
                
                // Validate target addresses with admin privilege check
                check_addr_list_with_admin_check(&port_request.target_address_list, subscription.is_admin)
                    .map_err(|e| Error::InvalidInput(e.to_string()))?;

                // Validate port bandwidth against subscription bandwidth
                if let Some(port_bandwidth) = port_request.bandwidth {
                    if let Some(subscription_bandwidth) = subscription.bandwidth {
                        if port_bandwidth > subscription_bandwidth as u32 {
                            error!(
                                "Port bandwidth {} exceeds subscription bandwidth {} for subscription: {}",
                                port_bandwidth, subscription_bandwidth, subscription.id
                            );
                            return Err(Error::InvalidInput(format!(
                                "Port bandwidth ({} Mbps) cannot exceed subscription bandwidth ({} Mbps)",
                                port_bandwidth, subscription_bandwidth
                            )));
                        }
                    }
                    // If subscription has unlimited bandwidth (None), allow any port bandwidth
                }

                let line_id = port_request.outbound_endpoint_id;
                // check if line_id is in subscription.lines
                if !subscription.lines.iter().any(|line| *line == line_id) {
                    error!("Line not found for line_id: {}", line_id);
                    return Err(Error::InvalidInput("Line not found".to_string()));
                }
                let Ok(Some(line_info)) = tx
                    .outbound_endpoint()
                    .find_unique(prisma::outbound_endpoint::id::equals(line_id))
                    .select(prisma::outbound_endpoint::select!(
                        {
                            ingress_ipv_4
                            port_start
                            port_end
                        }
                    ))
                    .exec()
                    .await
                else {
                    error!("Line not found for line_id: {}", line_id);
                    return Err(Error::NotFound("Line not found".to_string()));
                };
                let current_ports_this_user_count = tx
                    .port()
                    .count(vec![
                        prisma::port::outbound_endpoint_id::equals(Some(line_id)),
                        prisma::port::subscription_id::equals(Some(subscription.id)),
                    ])
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                if current_ports_this_user_count >= subscription.max_port_num_per_server as i64 {
                    error!(
                        "Add port failed, current user exceed the max port number: {}",
                        subscription.max_port_num_per_server
                    );
                    return Err(Error::InvalidInput(format!(
                        "Add port failed, current user exceed the max port number: {}",
                        subscription.max_port_num_per_server
                    )));
                }
                // check ports available
                let current_ports: Vec<_> = tx
                    .port()
                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                        line_id,
                    ))])
                    .select(prisma::port::select!({ port_v_4 }))
                    .exec()
                    .await
                    .map_err(Error::Database)?
                    .into_iter()
                    .map(|x| x.port_v_4 as u16)
                    .collect();

                // add port
                // todo support limit port number for each subscription
                let port_start = line_info.port_start.map(|x| x as u16).unwrap_or(31000);
                let port_end = line_info.port_end.map(|x| x as u16).unwrap_or(34000);
                let selected_port = port_request
                    .expected_port
                    .or_else(|| (port_start..port_end).find(|x| !current_ports.contains(x)));
                let Some(selected_port) = selected_port else {
                    error!(
                        "Add port failed, can't find available port on server: {}",
                        line_id
                    );
                    return Err(Error::InvalidInput(format!(
                        "Add port failed, can't find available port on server: {}",
                        line_id
                    )));
                };
                // check port is in range
                if selected_port < port_start || selected_port > port_end {
                    error!(
                        "Port out of range for selected port: {} on server: {}",
                        selected_port, line_id
                    );
                    return Err(Error::InvalidInput(format!(
                        "expected port: {} out of range of port_start: {}, port_end: {}",
                        selected_port, port_start, port_end
                    )));
                }
                if current_ports.contains(&selected_port) {
                    error!("Port already used for selected port: {}", selected_port);
                    return Err(Error::InvalidInput(format!(
                        "Port already used for selected port: {}",
                        selected_port
                    )));
                }
                {
                    let _line_lock =
                        get_line_lock(&app_state.redis_pool, &app_state.redis_line_lock, line_id)
                            .await
                            .map_err(|e| {
                                Error::InternalError(anyhow::anyhow!(
                                    "Please try again later: {}",
                                    e
                                ))
                            })?;
                    // clear port usage
                    app_state
                        .redis_pool
                        .get()
                        .await
                        .map_err(Error::Redis)?
                        .set::<_, _, ()>(
                            format!(
                                "sub:line:port:used:{}:{}:{}",
                                subscription.id, line_id, selected_port
                            ),
                            0,
                        )
                        .await
                        .map_err(|e| {
                            Error::InternalError(anyhow::anyhow!("set port usage failed: {}", e))
                        })?;
                }
                let (first_target, port_str) = port_request
                    .target_address_list
                    .first()
                    .unwrap()
                    .rsplit_once(':')
                    .unwrap();
                let port = port_str.parse::<u16>().unwrap();
                let set_forward_config = generate_tot_config_or_forward_config(
                    &token_id,
                    port_request.forward_endpoints,
                    port_request.balance_strategy,
                    port_request.tot_server_list,
                    port_request.tot_server_select_mode,
                    port_request.tot_server_test_method,
                    &subscription,
                    &tx,
                    &None,
                )
                .await?;
                let mut set_list = vec![
                    prisma::port::subscription_id::set(Some(subscription.id)),
                    prisma::port::outbound_endpoint_id::set(Some(line_id)),
                    prisma::port::target_addr_list::set(port_request.target_address_list.clone()),
                    prisma::port::select_mode::set(
                        port_request.target_select_mode.map(|x| x as i32),
                    ),
                    prisma::port::test_method::set(port_request.test_method.map(|x| x as i32)),
                    prisma::port::bandwidth::set(port_request.bandwidth.map(|x| x as i32)),
                    prisma::port::accept_proxy_protocol::set(port_request.accept_proxy_protocol.unwrap_or(false)),
                    prisma::port::send_proxy_protocol_version::set(port_request.send_proxy_protocol_version),
                ];
                if let Some((
                    forward_config,
                    (related_forward_endpoint_ids, related_tot_endpoint_ids),
                )) = set_forward_config
                {
                    set_list.push(prisma::port::forward_config::set(Some(forward_config)));
                    set_list.push(prisma::port::related_forward_endpoint_ids::set(
                        related_forward_endpoint_ids,
                    ));
                    // TODO: current only support hammer

                    if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
                        set_list.push(prisma::port::related_tot_server_ids::set(
                            related_tot_endpoint_ids,
                        ));
                        set_list.push(prisma::port::forward_protocol::set(Some("Tot".to_string())));
                    } else {
                        set_list.push(prisma::port::forward_protocol::set(Some(
                            "Hammer".to_string(),
                        )));
                    }
                }

                let port = tx
                    .port()
                    .create(
                        port_request.display_name,
                        selected_port as i32,
                        first_target.to_string(),
                        port as i32,
                        0,
                        0,
                        set_list,
                    )
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                let list = if port.target_addr_list.is_empty() {
                    vec![format!(
                        "{}:{}",
                        port.target_address_v_4, port.target_port_v_4
                    )]
                } else {
                    port.target_addr_list
                };
                let tot_config = port
                    .forward_config
                    .as_ref()
                    .map(|x| get_tot_config_from_str(x.as_str()))
                    .flatten();
                let port_info = PortInfo {
                    id: port.id,
                    ip_addr: line_info.ingress_ipv_4,
                    display_name: port.display_name,
                    port_v4: port.port_v_4,
                    target_address_list: list,
                    target_select_mode: port.select_mode.map(|x| x as u32),
                    test_method: port.test_method.map(|x| x as u32),
                    traffic_in: port.traffic_in,
                    traffic_out: port.traffic_out,
                    outbound_endpoint_id: port.outbound_endpoint_id,
                    line_name: None, // line_name not available in create port context
                    is_suspended: port.is_suspended,
                    bandwidth: port.bandwidth,
                    balance_strategy: get_blance_strategy_from_proxy_config(
                        port.forward_config.as_ref().map(|x| x.as_str()),
                    )
                    .map_or(None, |x| x),
                    forward_endpoints: Some(port.related_forward_endpoint_ids),
                    tot_server_list: Some(port.related_tot_server_ids),
                    tot_server_select_mode: tot_config
                        .as_ref()
                        .map(|x| x.tot_server_select_mode.into()),
                    tot_server_test_method: tot_config
                        .as_ref()
                        .map(|x| x.tot_server_test_method.into()),
                    accept_proxy_protocol: Some(port.accept_proxy_protocol),
                    send_proxy_protocol_version: port.send_proxy_protocol_version,
                };
                Ok(port_info)
            }
        })
        .await
    {
        Ok(rst) => rst,
        Err(e) => return Err(warp::reject::custom(e)),
    };

    info!(
        "Successfully created new port with id: {} for token_id: {}",
        rst.id, token_id_clone
    );
    Ok(warp::reply::json(&rst))
}

pub async fn handle_rmv_port(
    token_id: String,
    port_request: RmvPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Creating new port for token_id: {} with name: {}",
        token_id, port_request.id
    );

    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };
    let ports = &subscription.ports;
    if !ports.iter().any(|x| x.id == port_request.id) {
        error!("Port not found for id: {}", port_request.id);
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }
    let _port = app_state
        .db
        .port()
        .delete(prisma::port::id::equals(port_request.id))
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully removed port with id: {} for token_id: {}",
        port_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_modify_port(
    token_id: String,
    port_request: ModifyPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Modifying port for token_id: {} with id: {}, request: {:?}",
        token_id, port_request.id, port_request
    );
    // check target select mode
    if let Some(mode) = port_request.target_select_mode {
        let _mode = TryInto::<common::app_message::Mode>::try_into(mode)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    if let Some(test_method) = port_request.test_method {
        let _test_method = TryInto::<common::app_message::LatencyTestMethod>::try_into(test_method)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    // Validate port bandwidth
    if let Some(port_bandwidth) = port_request.bandwidth {
        if port_bandwidth == 0 {
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port bandwidth cannot be zero".to_string(),
            )));
        }
    }

    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionWithAdminInfo::select())
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Validate target addresses with admin privilege check
    check_addr_list_with_admin_check(&port_request.target_address_list, subscription.is_admin)
        .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;

    // Validate port bandwidth against subscription bandwidth
    if let Some(port_bandwidth) = port_request.bandwidth {
        if let Some(subscription_bandwidth) = subscription.bandwidth {
            if port_bandwidth > subscription_bandwidth as u32 {
                error!(
                    "Port bandwidth {} exceeds subscription bandwidth {} for subscription: {}",
                    port_bandwidth, subscription_bandwidth, subscription.id
                );
                return Err(warp::reject::custom(Error::InvalidInput(format!(
                    "Port bandwidth ({} Mbps) cannot exceed subscription bandwidth ({} Mbps)",
                    port_bandwidth, subscription_bandwidth
                ))));
            }
        }
        // If subscription has unlimited bandwidth (None), allow any port bandwidth
    }

    let ports = &subscription.ports;
    let Some(port) = ports.iter().find(|x| x.id == port_request.id) else {
        error!("Port not found for id: {}", port_request.id);
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    };
    let Some(outbound_endpoint_id) = port.outbound_endpoint_id else {
        error!("Port has no outbound endpoint id");
        return Err(warp::reject::custom(Error::InvalidInput(
            "Port has no outbound endpoint id".to_string(),
        )));
    };
    // check if the port is already used
    let current_ports: Vec<_> = app_state
        .db
        .port()
        .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
            outbound_endpoint_id,
        ))])
        .select(prisma::port::select!({ port_v_4 }))
        .exec()
        .await
        .map_err(Error::Database)?;
    if let Some(expected_port) = port_request.expected_port {
        if current_ports
            .iter()
            .any(|x| x.port_v_4 == expected_port as i32)
        {
            error!("Port already used for expected port: {}", expected_port);
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port already used".to_string(),
            )));
        }
        let Ok(Some(end_point)) = app_state
            .db
            .outbound_endpoint()
            .find_unique(prisma::outbound_endpoint::id::equals(outbound_endpoint_id))
            .select(prisma::outbound_endpoint::select!(
                {
                    port_start
                    port_end
                }
            ))
            .exec()
            .await
        else {
            error!(
                "Outbound endpoint not found for id: {}",
                outbound_endpoint_id
            );
            return Err(warp::reject::custom(Error::NotFound(
                "Outbound endpoint not found".to_string(),
            )));
        };
        let port_start = end_point.port_start.map(|x| x as u16).unwrap_or(31000);
        let port_end = end_point.port_end.map(|x| x as u16).unwrap_or(34000);
        if expected_port < port_start || expected_port > port_end {
            error!("Port out of range for expected port: {}", expected_port);
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port out of range".to_string(),
            )));
        }
    }
    let (first_target, port_str) = port_request
        .target_address_list
        .first()
        .unwrap()
        .rsplit_once(':')
        .unwrap();
    let set_tot_config = generate_tot_config_or_forward_config(
        &token_id,
        port_request.forward_endpoints,
        port_request.balance_strategy,
        port_request.tot_server_list,
        port_request.tot_server_select_mode,
        port_request.tot_server_test_method,
        &subscription,
        &app_state.db,
        &None,
    )
    .await
    .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let mut set_list = vec![
        prisma::port::display_name::set(port_request.display_name),
        prisma::port::port_v_4::set(
            port_request.expected_port.unwrap_or(port.port_v_4 as u16) as i32
        ),
        prisma::port::target_address_v_4::set(first_target.to_string()),
        prisma::port::target_port_v_4::set(port_str.parse::<u16>().unwrap() as i32),
        prisma::port::target_addr_list::set(port_request.target_address_list.clone()),
        prisma::port::select_mode::set(port_request.target_select_mode.map(|x| x as i32)),
        prisma::port::test_method::set(port_request.test_method.map(|x| x as i32)),
        prisma::port::bandwidth::set(port_request.bandwidth.map(|x| x as i32)),
        prisma::port::accept_proxy_protocol::set(port_request.accept_proxy_protocol.unwrap_or(false)),
        prisma::port::send_proxy_protocol_version::set(port_request.send_proxy_protocol_version),
    ];
    if let Some((forward_config, (related_forward_endpoint_ids, related_tot_endpoint_ids))) =
        set_tot_config
    {
        set_list.push(prisma::port::forward_config::set(Some(forward_config)));
        set_list.push(prisma::port::related_forward_endpoint_ids::set(
            related_forward_endpoint_ids,
        ));
        if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
            set_list.push(prisma::port::related_tot_server_ids::set(
                related_tot_endpoint_ids,
            ));
            set_list.push(prisma::port::forward_protocol::set(Some("Tot".to_string())));
        } else {
            set_list.push(prisma::port::forward_protocol::set(Some(
                "Hammer".to_string(),
            )));
            // don't forget to clear the related tot server ids
            set_list.push(prisma::port::related_tot_server_ids::set(vec![]));
        }
    } else {
        set_list.push(prisma::port::forward_protocol::set(None));
        set_list.push(prisma::port::forward_config::set(None));
        set_list.push(prisma::port::related_forward_endpoint_ids::set(vec![]));
        set_list.push(prisma::port::related_tot_server_ids::set(vec![]));
    }
    let _port = app_state
        .db
        .port()
        .update(prisma::port::id::equals(port_request.id), set_list)
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully modified port with id: {} for token_id: {}",
        port_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_ports_with_search(
    token_id: String,
    search_request: Option<PortSearchRequest>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let line_filter = search_request.as_ref().and_then(|s| s.line);
    let entry_point_search = search_request.as_ref().and_then(|s| s.entry_point.as_ref());
    let target_search = search_request.as_ref().and_then(|s| s.target.as_ref());

    info!(
        "Get ports with search for token_id: '{}', page: {}, page_size: {}, id: {:?}, name: {:?}, line: {:?}, entry_point: {:?}, target: {:?}",
        token_id, page, page_size, id_search, name_search, line_filter, entry_point_search, target_search
    );

    // Get the subscription to get the subscription ID
    let subscription_basic = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Build database query conditions
    let mut conditions = vec![prisma::port::subscription_id::equals(Some(
        subscription_basic.id,
    ))];

    // Add line filter if specified
    if let Some(line_id) = line_filter {
        conditions.push(prisma::port::outbound_endpoint_id::equals(Some(line_id)));
    }

    // For search functionality, we need to get all ports first and then filter
    // This is because regex filtering happens in application code, not database
    let ports_data = app_state
        .db
        .port()
        .find_many(conditions)
        .order_by(prisma::port::id::order(Direction::Desc))
        .select(prisma::port::select!({
            id
            display_name
            target_addr_list
            target_port_v_4
            target_address_v_4
            port_v_4
            outbound_endpoint_id
            select_mode
            test_method
            traffic_in
            forward_config
            related_forward_endpoint_ids
            related_tot_server_ids
            is_suspended
            bandwidth
            accept_proxy_protocol
            send_proxy_protocol_version
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let ports_data = if let Some(search_id) = id_search {
        ports_data
            .into_iter()
            .filter(|port| port.id == search_id)
            .collect()
    } else {
        ports_data
    };

    let mut ports: Vec<PortInfo> = vec![];
    for port in ports_data.into_iter() {
        let Some(outbound_endpoint_id) = port.outbound_endpoint_id else {
            continue;
        };
        let outbound_endpoint = match app_state
            .cache_outbound_endpoint_data
            .get(&outbound_endpoint_id)
            .await
        {
            Ok(outbound_endpoint) => outbound_endpoint,
            Err(e) => {
                log::error!(
                    "token_id: {} get ports but outbound_endpoint_id: {} not found, error: {}",
                    token_id,
                    outbound_endpoint_id,
                    e
                );
                continue;
            }
        };

        let list = if port.target_addr_list.is_empty() {
            vec![format!(
                "{}:{}",
                port.target_address_v_4, port.target_port_v_4
            )]
        } else {
            port.target_addr_list
        };

        // Apply regex search filters on the processed data (skip if ID search is used)
        let entry_point = format!("{}:{}", outbound_endpoint.ingress_ipv_4, port.port_v_4);
        let target_addresses = list.join(", ");

        // If ID search is specified, skip other filters since exact ID match takes priority
        if id_search.is_none() {
            // Check name filter (regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&port.display_name, name_pattern) {
                    continue;
                }
            }

            // Check entry point filter (regex)
            if let Some(entry_pattern) = entry_point_search {
                if !matches_regex_or_exact(&entry_point, entry_pattern) {
                    continue;
                }
            }

            // Check target filter (regex)
            if let Some(target_pattern) = target_search {
                if !matches_regex_or_exact(&target_addresses, target_pattern) {
                    continue;
                }
            }
        }
        let tot_config = port
            .forward_config
            .as_ref()
            .map(|x| get_tot_config_from_str(x.as_str()))
            .flatten();

        let port_info = PortInfo {
            id: port.id,
            display_name: port.display_name,
            ip_addr: outbound_endpoint.ingress_ipv_4,
            port_v4: port.port_v_4,
            traffic_in: port.traffic_in,
            traffic_out: 0,
            outbound_endpoint_id: Some(outbound_endpoint_id),
            line_name: Some(outbound_endpoint.display_name),
            is_suspended: port.is_suspended,
            bandwidth: port.bandwidth,
            balance_strategy: get_blance_strategy_from_proxy_config(
                port.forward_config.as_ref().map(|x| x.as_str()),
            )
            .map_or(None, |x| x),
            forward_endpoints: Some(port.related_forward_endpoint_ids),
            target_address_list: list,
            target_select_mode: port.select_mode.map(|x| x as u32),
            test_method: port.test_method.map(|x| x as u32),
            tot_server_list: Some(port.related_tot_server_ids),
            tot_server_select_mode: tot_config.as_ref().map(|x| x.tot_server_select_mode.into()),
            tot_server_test_method: tot_config.as_ref().map(|x| x.tot_server_test_method.into()),
            accept_proxy_protocol: Some(port.accept_proxy_protocol),
            send_proxy_protocol_version: port.send_proxy_protocol_version,
        };
        ports.push(port_info);
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none()
        && (name_search.is_some() || entry_point_search.is_some() || target_search.is_some())
    {
        ports.sort_by(|a, b| {
            let a_score = calculate_match_score(a, name_search, entry_point_search, target_search);
            let b_score = calculate_match_score(b, name_search, entry_point_search, target_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Calculate pagination info based on filtered results
    let total_filtered = ports.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        ((total_filtered as f64) / (page_size as f64)).ceil() as u32
    };

    // Apply pagination to filtered results
    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(ports.len());
    let paginated_ports = if start_index < ports.len() {
        ports[start_index..end_index].to_vec()
    } else {
        vec![]
    };

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedPortsResponse {
        ports: paginated_ports,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} filtered ports for token_id: {} (page {}/{}, total: {})",
        response.ports.len(),
        token_id,
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_suspend_port(
    token_id: String,
    suspend_request: SuspendPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Suspending port for token_id: {} with id: {}",
        token_id, suspend_request.id
    );

    // Verify the port belongs to the user's subscription
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if the port belongs to this subscription
    if !subscription
        .ports
        .iter()
        .any(|p| p.id == suspend_request.id)
    {
        warn!(
            "Port {} does not belong to subscription for token_id: {}",
            suspend_request.id, token_id
        );
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }

    // Update the port to suspended status
    app_state
        .db
        .port()
        .update(
            prisma::port::id::equals(suspend_request.id),
            vec![prisma::port::is_suspended::set(true)],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully suspended port with id: {} for token_id: {}",
        suspend_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_resume_port(
    token_id: String,
    resume_request: ResumePortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Resuming port for token_id: {} with id: {}",
        token_id, resume_request.id
    );

    // Verify the port belongs to the user's subscription
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if the port belongs to this subscription
    if !subscription.ports.iter().any(|p| p.id == resume_request.id) {
        warn!(
            "Port {} does not belong to subscription for token_id: {}",
            resume_request.id, token_id
        );
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }

    // Update the port to active status
    app_state
        .db
        .port()
        .update(
            prisma::port::id::equals(resume_request.id),
            vec![prisma::port::is_suspended::set(false)],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully resumed port with id: {} for token_id: {}",
        resume_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_admin_get_ports_by_user(
    admin_request: AdminPortsByUserRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let page = admin_request.page.unwrap_or(1);
    let page_size = admin_request.page_size.unwrap_or(10);
    let subscription_id = admin_request.subscription_id;

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        10 | 20 | 50 | 100 => page_size,
        _ => 10, // Default to 10 if invalid page size
    };

    info!(
        "Admin get ports by user, subscription_id: {}, page: {}, page_size: {}",
        subscription_id, page, page_size
    );

    // Verify subscription exists
    let subscription_exists = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::id::equals(subscription_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
    {
        Ok(Some(_)) => true,
        Ok(None) => {
            warn!("Subscription not found for id: {}", subscription_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    if !subscription_exists {
        return Err(warp::reject::custom(Error::NotFound(
            "Subscription not found".to_string(),
        )));
    }

    // Build database query conditions
    let conditions = vec![prisma::port::subscription_id::equals(Some(subscription_id))];

    // Get ports data
    let ports_data = app_state
        .db
        .port()
        .find_many(conditions)
        .order_by(prisma::port::id::order(Direction::Desc))
        .select(prisma::port::select!({
            id
            display_name
            target_addr_list
            target_port_v_4
            target_address_v_4
            port_v_4
            outbound_endpoint_id
            select_mode
            test_method
            traffic_in
            traffic_out
            forward_config
            related_forward_endpoint_ids
            related_tot_server_ids
            is_suspended
            bandwidth
            accept_proxy_protocol
            send_proxy_protocol_version
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    let mut ports: Vec<PortInfo> = vec![];
    for port in ports_data.into_iter() {
        let Some(outbound_endpoint_id) = port.outbound_endpoint_id else {
            continue;
        };
        let Ok(outbound_endpoint) = app_state
            .cache_outbound_endpoint_data
            .get(&outbound_endpoint_id)
            .await
        else {
            log::error!(
                "Admin get ports by user but outbound_endpoint_id: {} not found",
                outbound_endpoint_id
            );
            continue;
        };

        let list = if port.target_addr_list.is_empty() {
            vec![format!(
                "{}:{}",
                port.target_address_v_4, port.target_port_v_4
            )]
        } else {
            port.target_addr_list
        };

        let tot_config = port
            .forward_config
            .as_ref()
            .map(|x| get_tot_config_from_str(x.as_str()))
            .flatten();

        let port_info = PortInfo {
            id: port.id,
            display_name: port.display_name,
            ip_addr: outbound_endpoint.ingress_ipv_4,
            port_v4: port.port_v_4,
            traffic_in: port.traffic_in,
            traffic_out: port.traffic_out,
            outbound_endpoint_id: Some(outbound_endpoint_id),
            line_name: Some(outbound_endpoint.display_name),
            is_suspended: port.is_suspended,
            bandwidth: port.bandwidth,
            balance_strategy: get_blance_strategy_from_proxy_config(
                port.forward_config.as_ref().map(|x| x.as_str()),
            )
            .map_or(None, |x| x),
            forward_endpoints: Some(port.related_forward_endpoint_ids),
            target_address_list: list,
            target_select_mode: port.select_mode.map(|x| x as u32),
            test_method: port.test_method.map(|x| x as u32),
            tot_server_list: Some(port.related_tot_server_ids),
            tot_server_select_mode: tot_config.as_ref().map(|x| x.tot_server_select_mode.into()),
            tot_server_test_method: tot_config.as_ref().map(|x| x.tot_server_test_method.into()),
            accept_proxy_protocol: Some(port.accept_proxy_protocol),
            send_proxy_protocol_version: port.send_proxy_protocol_version,
        };
        ports.push(port_info);
    }

    // Calculate pagination info based on filtered results
    let total_filtered = ports.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        ((total_filtered as f64) / (page_size as f64)).ceil() as u32
    };

    // Apply pagination to filtered results
    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(ports.len());
    let paginated_ports = if start_index < ports.len() {
        ports[start_index..end_index].to_vec()
    } else {
        vec![]
    };

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedPortsResponse {
        ports: paginated_ports,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} ports for subscription_id: {} (page {}/{}, total: {})",
        response.ports.len(),
        subscription_id,
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_batch_update_ports(
    token_id: String,
    batch_request: BatchUpdatePortsRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Batch updating ports for token_id: {} with pattern: '{}', replacement: '{}', preview_only: {}",
        token_id, batch_request.regex_pattern, batch_request.replacement, batch_request.preview_only
    );

    // Validate regex pattern
    let regex = match regex::Regex::new(&batch_request.regex_pattern) {
        Ok(r) => r,
        Err(e) => {
            error!("Invalid regex pattern '{}': {}", batch_request.regex_pattern, e);
            return Err(warp::reject::custom(Error::InvalidInput(format!(
                "Invalid regex pattern: {}",
                e
            ))));
        }
    };

    let token_id_clone = token_id.clone();

    let result = match app_state
        .db
        ._transaction()
        .run(move |tx| {
            async move {
                // Get subscription info
                let subscription = match tx
                    .subscription()
                    .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
                    .select(SubscriptionWithAdminInfo::select())
                    .exec()
                    .await
                {
                    Ok(Some(sub)) => sub,
                    Ok(None) => {
                        warn!("Subscription not found for token_id: {}", token_id);
                        return Err(Error::NotFound("Subscription not found".to_string()));
                    }
                    Err(e) => {
                        error!("Database error while fetching subscription: {}", e);
                        return Err(Error::Database(e));
                    }
                };

                // Get all user's ports
                let ports_data = tx
                    .port()
                    .find_many(vec![prisma::port::subscription_id::equals(Some(
                        subscription.id,
                    ))])
                    .order_by(prisma::port::id::order(Direction::Desc))
                    .select(prisma::port::select!({
                        id
                        display_name
                        target_addr_list
                        target_address_v_4
                        target_port_v_4
                    }))
                    .exec()
                    .await
                    .map_err(Error::Database)?;

                let mut updated_ports = Vec::new();
                let mut success_count = 0u32;
                let mut failed_count = 0u32;
                let mut errors = Vec::new();

                for port in ports_data {
                    let original_addresses = if port.target_addr_list.is_empty() {
                        vec![format!(
                            "{}:{}",
                            port.target_address_v_4, port.target_port_v_4
                        )]
                    } else {
                        port.target_addr_list.clone()
                    };

                    let mut new_addresses = Vec::new();
                    let mut has_changes = false;

                    match batch_request.operation_type {
                        BatchUpdateOperation::Replace => {
                            // Original replace logic
                            for addr in &original_addresses {
                                let new_addr = regex.replace_all(addr, &batch_request.replacement);
                                if new_addr != *addr {
                                    has_changes = true;
                                }
                                new_addresses.push(new_addr.to_string());
                            }
                        },
                        BatchUpdateOperation::Append => {
                            // Append mode: add the replacement address to existing addresses
                            // Only add if the condition_address (if specified) exists in current addresses
                            new_addresses = original_addresses.clone();
                            let new_address = &batch_request.replacement;
                            
                            let should_add = if let Some(condition_addr) = &batch_request.condition_address {
                                // Only add if the condition address exists in current target addresses
                                original_addresses.contains(condition_addr)
                            } else {
                                // If no condition specified, add to all ports (backward compatibility)
                                true
                            };
                            
                            if should_add && !original_addresses.contains(new_address) {
                                new_addresses.push(new_address.clone());
                                has_changes = true;
                            }
                        },
                        BatchUpdateOperation::Remove => {
                            // Remove mode: remove addresses that match the regex pattern
                            for addr in &original_addresses {
                                if !regex.is_match(addr) {
                                    new_addresses.push(addr.clone());
                                } else {
                                    has_changes = true;
                                }
                            }
                            
                            // Safety check: ensure at least one address remains
                            if new_addresses.is_empty() {
                                failed_count += 1;
                                errors.push(format!("Port '{}' (ID: {}): Cannot remove all target addresses - at least one address must remain", port.display_name, port.id));
                                
                                updated_ports.push(PortUpdateDetail {
                                    port_id: port.id,
                                    port_name: port.display_name,
                                    old_addresses: original_addresses.clone(),
                                    new_addresses: original_addresses.clone(),
                                    changes_made: false,
                                });
                                continue;
                            }
                        }
                    }

                    if has_changes {
                        // Validate new addresses
                        if let Err(e) = check_addr_list_with_admin_check(&new_addresses, subscription.is_admin) {
                            failed_count += 1;
                            errors.push(format!("Port '{}' (ID: {}): {}", port.display_name, port.id, e));
                            
                            updated_ports.push(PortUpdateDetail {
                                port_id: port.id,
                                port_name: port.display_name,
                                old_addresses: original_addresses,
                                new_addresses: new_addresses,
                                changes_made: false,
                            });
                            continue;
                        }

                        // If not preview mode, update the port
                        if !batch_request.preview_only {
                            let (first_target, port_str) = new_addresses
                                .first()
                                .unwrap()
                                .rsplit_once(':')
                                .ok_or_else(|| Error::InvalidInput("Invalid address format".to_string()))?;
                            
                            let target_port = port_str.parse::<u16>()
                                .map_err(|_| Error::InvalidInput("Invalid port number".to_string()))?;

                            match tx
                                .port()
                                .update(
                                    prisma::port::id::equals(port.id),
                                    vec![
                                        prisma::port::target_address_v_4::set(first_target.to_string()),
                                        prisma::port::target_port_v_4::set(target_port as i32),
                                        prisma::port::target_addr_list::set(new_addresses.clone()),
                                    ],
                                )
                                .exec()
                                .await
                            {
                                Ok(_) => {
                                    success_count += 1;
                                    info!("Successfully updated port '{}' (ID: {})", port.display_name, port.id);
                                }
                                Err(e) => {
                                    failed_count += 1;
                                    let error_msg = format!("Failed to update port '{}' (ID: {}): {}", port.display_name, port.id, e);
                                    error!("{}", error_msg);
                                    errors.push(error_msg);
                                }
                            }
                        } else {
                            success_count += 1;
                        }

                        updated_ports.push(PortUpdateDetail {
                            port_id: port.id,
                            port_name: port.display_name,
                            old_addresses: original_addresses,
                            new_addresses: new_addresses,
                            changes_made: has_changes,
                        });
                    }
                }

                Ok(BatchUpdatePortsResponse {
                    updated_ports,
                    success_count,
                    failed_count,
                    errors,
                })
            }
        })
        .await
    {
        Ok(result) => result,
        Err(e) => return Err(warp::reject::custom(e)),
    };

    info!(
        "Batch update completed for token_id: {}: {} successful, {} failed",
        token_id_clone, result.success_count, result.failed_count
    );

    Ok(warp::reply::json(&result))
}
