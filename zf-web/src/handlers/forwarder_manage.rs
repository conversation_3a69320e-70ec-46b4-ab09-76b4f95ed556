use anyhow::anyhow;
use base64::Engine;
use common::TotConfig;
use log::{info, warn};
use private_tun::snell_impl_ver::config::{ClientConfig, ServerConfig};
use rand::Rng;
use warp::reject::Rejection;
use warp::reply::Reply;

use crate::forwarder::{
    generate_hammer_server_config, generate_hammer_server_config_update_port,
    generate_random_hammer_server_config, generate_setup_script, update_hammer_proxy_config,
    update_tot_proxy_config,
};
use crate::handlers::{
    handle_modify_port, handle_modify_server, matches_regex_or_exact, update_port_proxy_config,
    ToUpdateForwardEndpoint, ToUpdatePortForwardConfig,
};
use crate::message::{
    AddForwardEndpointRequest, CopyForwardEndpointRequest, ForwardEndpoint,
    ForwardEndpointSearchRequest, ModifyForwardEndpointRequest, ModifyPortRequest,
    ModifyServerRequest, PaginatedForwardEndpointResponse, PaginationInfo, PaginationRequest,
    RmvForwardEndpointRequest,
};

use crate::AppState;
use crate::{error::Error, prisma};
use prisma_client_rust::{operator::*, Direction};

// Helper function to calculate match score for forward endpoints (higher score = better match)
fn calculate_forward_endpoint_match_score(
    endpoint: &ForwardEndpoint,
    name_pattern: Option<&String>,
    ingress_address_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if endpoint.name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if endpoint
            .name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = ingress_address_pattern {
        if endpoint.ingress_address.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if endpoint
            .ingress_address
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

pub async fn handle_get_forward_endpoints_with_search(
    token_id: String,
    search_request: Option<ForwardEndpointSearchRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let ingress_address_search = search_request
        .as_ref()
        .and_then(|s| s.ingress_address.as_ref());

    info!(
        "Get forward endpoints with search for token_id: '{}', page: {}, page_size: {}, id: {:?}, name: {:?}, ingress_address: {:?}",
        token_id, page, page_size, id_search, name_search, ingress_address_search
    );

    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    // For search functionality, we need to get all forward endpoints first and then filter
    // This is because regex filtering happens in application code, not database
    let forward_endpoints_data = app_state
        .db
        .forward_endpoint()
        .find_many(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .order_by(prisma::forward_endpoint::id::order(Direction::Desc))
        .select(prisma::forward_endpoint::select!(
            {
                id
                name
                ingress_address
                protocol
                serve_port
                is_public
                token_id
                allow_ip_v_6
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let forward_endpoints_data = if let Some(search_id) = id_search {
        forward_endpoints_data
            .into_iter()
            .filter(|endpoint| endpoint.id == search_id)
            .collect()
    } else {
        forward_endpoints_data
    };

    let mut forward_endpoints: Vec<ForwardEndpoint> = vec![];
    for endpoint in forward_endpoints_data.into_iter() {
        // If ID search is specified, skip other filters since exact ID match takes priority
        if id_search.is_none() {
            // Check name filter (regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&endpoint.name, name_pattern) {
                    continue;
                }
            }

            // Check ingress address filter (regex)
            if let Some(ingress_pattern) = ingress_address_search {
                if !matches_regex_or_exact(&endpoint.ingress_address, ingress_pattern) {
                    continue;
                }
            }
        }

        // Convert to ForwardEndpoint struct
        if let Ok(protocol) = endpoint.protocol.parse() {
            forward_endpoints.push(ForwardEndpoint {
                id: endpoint.id,
                name: endpoint.name,
                ingress_address: endpoint.ingress_address,
                protocol,
                serve_port: endpoint.serve_port,
                is_public: endpoint.is_public,
                token_id: endpoint.token_id,
                allow_ipv6: endpoint.allow_ip_v_6,
            });
        }
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none() && (name_search.is_some() || ingress_address_search.is_some()) {
        forward_endpoints.sort_by(|a, b| {
            let a_score =
                calculate_forward_endpoint_match_score(a, name_search, ingress_address_search);
            let b_score =
                calculate_forward_endpoint_match_score(b, name_search, ingress_address_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Apply pagination to filtered results
    let total_filtered = forward_endpoints.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        (total_filtered + page_size - 1) / page_size
    };

    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(forward_endpoints.len());
    let paginated_endpoints = if start_index < forward_endpoints.len() {
        forward_endpoints[start_index..end_index].to_vec()
    } else {
        vec![]
    };

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedForwardEndpointResponse {
        forward_endpoints: paginated_endpoints,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} filtered forward endpoints for token_id: {} (page {}/{}, total: {})",
        response.forward_endpoints.len(),
        token_id,
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_add_forward_endpoint(
    token_id: String,
    request: AddForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!(
            {
                id
                max_forward_endpoint_num
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let max_forward_endpoint_num = subscription.max_forward_endpoint_num;
    let forward_endpoints = app_state
        .db
        .forward_endpoint()
        .count(vec![prisma::forward_endpoint::subscription_id::equals(
            Some(subscription.id),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;
    if forward_endpoints >= max_forward_endpoint_num as i64 {
        return Err(warp::reject::custom(Error::InvalidInput(format!(
            "Max forward endpoint num reached: {}",
            max_forward_endpoint_num
        ))));
    }
    let (server_port, bind_port) = match (request.serve_port, request.bind_port) {
        (Some(server_port), Some(bind_port)) => (Some(server_port), Some(bind_port)),
        (Some(server_port), None) => (Some(server_port), Some(server_port)),
        (None, Some(bind_port)) => (Some(bind_port), Some(bind_port)),
        (None, None) => (None, None),
    };
    let (server_port, proxy_server_config) = if let Some(server_port) = server_port {
        (
            server_port,
            generate_hammer_server_config(bind_port.unwrap() as u16) // bind port must be specified
                .map_err(|e| warp::reject::custom(Error::InternalError(e)))?,
        )
    } else {
        let (server_port, proxy_server_config) = generate_random_hammer_server_config()
            .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
        (server_port as i32, proxy_server_config)
    };
    let forward_token_id = uuid::Uuid::new_v4().to_string();
    let setup_script = generate_setup_script(
        &app_state.opt.host_url,
        &proxy_server_config,
        &request.protocol.to_string(),
    )
    .await
    .unwrap_or_default();
    app_state
        .db
        .forward_endpoint()
        .create(
            request.name.clone(),
            request.name,
            request.ingress_address.trim().to_string(),
            server_port,
            request.protocol.to_string(),
            proxy_server_config,
            "".to_string(), // TODO: generate priv key current no used
            forward_token_id,
            vec![
                prisma::forward_endpoint::subscription_id::set(Some(subscription.id)),
                prisma::forward_endpoint::setup_script::set(Some(setup_script)),
                prisma::forward_endpoint::is_public::set(false),
                prisma::forward_endpoint::traffic_scale::set(Some(0.0)),
                prisma::forward_endpoint::allow_ip_v_6::set(request.allow_ipv6.unwrap_or(false)),
                prisma::forward_endpoint::inner_port::set(request.bind_port),
            ],
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_forward_endpoint(
    token_id: String,
    pagination: Option<PaginationRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Check if fetch_all is requested
    let fetch_all = pagination
        .as_ref()
        .and_then(|p| p.fetch_all)
        .unwrap_or(false);

    // Extract pagination parameters with defaults
    let page = pagination.as_ref().and_then(|p| p.page).unwrap_or(1);
    let page_size = pagination.as_ref().and_then(|p| p.page_size).unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination (ignored if fetch_all is true)
    let skip = if fetch_all {
        0
    } else {
        ((page - 1) * page_size) as i64
    };
    let take = if fetch_all { -1 } else { page_size as i64 }; // -1 means no limit in Prisma

    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    // Get total count of forward endpoints for this subscription
    let total_count = app_state
        .db
        .forward_endpoint()
        .count(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    // Build the base query
    let mut query = app_state
        .db
        .forward_endpoint()
        .find_many(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .order_by(prisma::forward_endpoint::id::order(Direction::Desc));

    // Apply pagination only if not fetching all
    if !fetch_all {
        query = query.skip(skip).take(take);
    }

    let forward_endpoints = query
        .select(prisma::forward_endpoint::select!(
            {
                id
                name
                ingress_address
                protocol
                serve_port
                is_public
                token_id
                allow_ip_v_6
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?;
    let forward_endpoints = forward_endpoints
        .iter()
        .filter_map(|x| {
            Some(ForwardEndpoint {
                id: x.id,
                name: x.name.clone(),
                ingress_address: x.ingress_address.clone(),
                protocol: x
                    .protocol
                    .parse()
                    .map_err(|e| warp::reject::custom(Error::InternalError(e)))
                    .ok()?,
                serve_port: x.serve_port,
                is_public: x.is_public,
                token_id: x.token_id.clone(),
                allow_ipv6: x.allow_ip_v_6,
            })
        })
        .collect::<Vec<_>>();

    // Handle pagination info based on fetch_all flag
    let pagination_info = if fetch_all {
        // When fetching all, return pagination info indicating all items are on page 1
        PaginationInfo {
            current_page: 1,
            page_size: total_count,
            total_items: total_count,
            total_pages: 1,
        }
    } else {
        // Normal pagination
        let total_pages = if total_count == 0 {
            1
        } else {
            (total_count + page_size - 1) / page_size
        };

        PaginationInfo {
            current_page: page,
            page_size,
            total_items: total_count,
            total_pages,
        }
    };

    let response = PaginatedForwardEndpointResponse {
        forward_endpoints,
        pagination: pagination_info,
    };
    Ok(warp::reply::json(&response))
}

pub async fn handle_rmv_forward_endpoint(
    token_id: String,
    request: RmvForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    // TODO: how to delete forward without subscription_id??
    // detect if any port is using this forward_endpoint
    let ports = app_state
        .db
        .port()
        .find_many(vec![prisma::port::related_forward_endpoint_ids::has(Some(
            request.id,
        ))])
        .exec()
        .await
        .map_err(Error::Database)?;
    for p in ports.iter() {
        if p.related_forward_endpoint_ids.len() == 1 {
            return Err(warp::reject::custom(Error::InvalidInput(format!(
                "Port: {}, id: {} is using this forward endpoint as the only forwarder server, please remove it first",
                p.display_name,
                p.id
            ))));
        }
    }
    let servers = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![
            prisma::outbound_endpoint::related_forward_endpoint_ids::has(Some(request.id)),
        ])
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            related_forward_endpoint_ids
            related_tot_server_ids
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    for s in servers.iter() {
        if s.related_forward_endpoint_ids.len() == 1 {
            return Err(warp::reject::custom(Error::InvalidInput(format!(
                "Worker: {} is using this forward endpoint as the only forwarder server, please remove it first",
                s.display_name
            ))));
        }
    }

    // remove forward endpoint from ports
    for p in ports.iter() {
        let new_forward_endpoints = p
            .related_forward_endpoint_ids
            .clone()
            .into_iter()
            .filter(|x| *x != request.id)
            .collect();
        let new_tot_server_list = if p.related_tot_server_ids.is_empty() {
            None
        } else {
            Some(
                p.related_tot_server_ids
                    .clone()
                    .into_iter()
                    .filter(|x| *x != request.id)
                    .collect::<Vec<_>>(),
            )
        };
        let target_address_list = if p.target_addr_list.is_empty() {
            if p.target_address_v_4.len() == 0 {
                vec![]
            } else {
                vec![format!("{}:{}", p.target_address_v_4, p.target_port_v_4)]
            }
        } else {
            p.target_addr_list.clone()
        };
        let modify_port_req = ModifyPortRequest {
            id: p.id,
            display_name: p.display_name.clone(),
            target_address_list,
            forward_endpoints: Some(new_forward_endpoints),
            tot_server_list: new_tot_server_list,
            target_select_mode: p.select_mode.map(|x| x as u32),
            test_method: p.test_method.map(|x| x as u32),
            ..Default::default()
        };
        handle_modify_port(token_id.clone(), modify_port_req, app_state.clone()).await?;
    }

    // remove forward endpoint from servers
    for s in servers.iter() {
        let new_forward_endpoints = s
            .related_forward_endpoint_ids
            .clone()
            .into_iter()
            .filter(|x| *x != request.id)
            .collect();
        let new_tot_server_list = if s.related_tot_server_ids.is_empty() {
            None
        } else {
            Some(
                s.related_tot_server_ids
                    .clone()
                    .into_iter()
                    .filter(|x| *x != request.id)
                    .collect::<Vec<_>>(),
            )
        };
        let modify_server_req = ModifyServerRequest {
            server_id: s.id,
            display_name: s.display_name.clone(),
            ip_addr: s.ingress_ipv_4.clone(),
            forward_endpoints: Some(new_forward_endpoints),
            tot_server_list: new_tot_server_list,
            ..Default::default()
        };
        handle_modify_server(token_id.clone(), app_state.clone(), modify_server_req).await?;
    }

    let _forward_endpoint = app_state
        .db
        .forward_endpoint()
        .delete_many(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::id::equals(request.id),
        ])
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_modify_forward_endpoint(
    token_id: String,
    request: ModifyForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let forward_endpoint = app_state
        .db
        .forward_endpoint()
        .find_unique(prisma::forward_endpoint::id::equals(request.id))
        .select(ToUpdateForwardEndpoint::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Forward endpoint not found".to_string()))?;

    if let Some(subscription_id) = forward_endpoint.subscription_id {
        if subscription_id != subscription.id {
            return Err(warp::reject::custom(Error::NotFound(
                "Forward endpoint not found".to_string(),
            )));
        }
    }
    // 检查是否需要更新配置
    if forward_endpoint.ingress_address != request.ingress_address
        || request
            .serve_port
            .as_ref()
            .map(|x| x != &forward_endpoint.serve_port)
            .unwrap_or_default()
        || request
            .allow_ipv6
            .map(|x| x != forward_endpoint.allow_ip_v_6)
            .unwrap_or_default()
    {
        // update config
        let ip_addr = request.ingress_address.trim().to_string();
        let serve_port = *request
            .serve_port
            .as_ref()
            .unwrap_or(&forward_endpoint.serve_port) as u16;
        let bind_port = request.bind_port.unwrap_or(serve_port as i32) as u16;
        let allow_ipv6 = request.allow_ipv6.unwrap_or(forward_endpoint.allow_ip_v_6);
        let old_proxy_server_config =
            serde_json::from_str::<ServerConfig>(&forward_endpoint.protocol_config)
                .map_err(|e| warp::reject::custom(Error::InternalError(anyhow!(e.to_string()))))?;
        let new_proxy_server_config =
            generate_hammer_server_config_update_port(old_proxy_server_config, bind_port)
                .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
        let protocol = request.protocol.to_string();
        app_state
            .db
            ._transaction()
            .run(move |tx| async move {
                let ports = tx
                    .port()
                    .find_many(vec![prisma::port::related_forward_endpoint_ids::has(Some(
                        forward_endpoint.id,
                    ))])
                    .select(ToUpdatePortForwardConfig::select())
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                for port in ports {
                    if let Ok(new_config) = update_port_proxy_config(
                        &port,
                        &forward_endpoint,
                        &ip_addr,
                        serve_port,
                        allow_ipv6,
                    )
                    .await
                    {
                        tx.port()
                            .update(
                                prisma::port::id::equals(port.id),
                                vec![prisma::port::forward_config::set(Some(new_config))],
                            )
                            .exec()
                            .await
                            .map_err(Error::Database)?;
                    }
                }

                // update worker's proxy config
                let servers = tx
                    .outbound_endpoint()
                    .find_many(vec![
                        prisma::outbound_endpoint::related_forward_endpoint_ids::has(Some(
                            forward_endpoint.id,
                        )),
                    ])
                    .select(prisma::outbound_endpoint::select!(
                        {
                            id
                            display_name
                            proxy_config
                        }
                    ))
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                for server in servers {
                    if let Some(proxy_config) = server.proxy_config {
                        let Ok(decoded) =
                            base64::engine::general_purpose::STANDARD.decode(&proxy_config)
                        else {
                            warn!(
                                "Failed to decode proxy config for server: {}",
                                server.display_name
                            );
                            continue;
                        };
                        let new_config = if let Ok(proxy_config) =
                            serde_json::from_slice::<ClientConfig>(&decoded)
                        {
                            update_hammer_proxy_config(
                                proxy_config,
                                forward_endpoint.id,
                                &ip_addr,
                                serve_port,
                                allow_ipv6,
                            )
                            .map_err(|e| {
                                Error::InternalError(anyhow!(
                                    "Failed to update worker's proxy config: {}",
                                    e
                                ))
                            })?
                        } else if let Ok(proxy_config) =
                            serde_json::from_slice::<TotConfig>(&decoded)
                        {
                            update_tot_proxy_config(
                                proxy_config,
                                forward_endpoint.id,
                                &ip_addr,
                                serve_port,
                                allow_ipv6,
                            )
                            .map_err(|e| {
                                Error::InternalError(anyhow!(
                                    "Failed to update worker's tot proxy config: {}",
                                    e
                                ))
                            })?
                        } else {
                            continue;
                        };

                        let new_config =
                            base64::engine::general_purpose::STANDARD.encode(new_config);
                        tx.outbound_endpoint()
                            .update(
                                prisma::outbound_endpoint::id::equals(server.id),
                                vec![prisma::outbound_endpoint::proxy_config::set(Some(
                                    new_config,
                                ))],
                            )
                            .exec()
                            .await
                            .map_err(Error::Database)?;
                    }
                }

                let setup_script = generate_setup_script(
                    &app_state.opt.host_url,
                    &new_proxy_server_config,
                    &protocol,
                )
                .await
                .unwrap_or_default();

                tx.forward_endpoint()
                    .update(
                        prisma::forward_endpoint::id::equals(request.id),
                        vec![
                            prisma::forward_endpoint::protocol_config::set(new_proxy_server_config),
                            prisma::forward_endpoint::setup_script::set(Some(setup_script)),
                        ],
                    )
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                Ok::<(), Error>(())
            })
            .await
            .map_err(|e| warp::reject::custom(e))?;
    }
    let mut params = vec![
        prisma::forward_endpoint::name::set(request.name.clone()),
        prisma::forward_endpoint::display_name::set(request.name.clone()),
        prisma::forward_endpoint::ingress_address::set(request.ingress_address),
        prisma::forward_endpoint::protocol::set(request.protocol.to_string()),
    ];
    if let Some(serve_port) = request.serve_port {
        params.push(prisma::forward_endpoint::serve_port::set(serve_port));
    }
    if let Some(allow_ipv6) = request.allow_ipv6 {
        params.push(prisma::forward_endpoint::allow_ip_v_6::set(allow_ipv6));
    }
    let _forward_endpoint = app_state
        .db
        .forward_endpoint()
        .update(prisma::forward_endpoint::id::equals(request.id), params)
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_copy_forward_endpoint(
    token_id: String,
    request: CopyForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    // Validate subscription and check limits (same as add endpoint)
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!(
            {
                id
                max_forward_endpoint_num
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    let max_forward_endpoint_num = subscription.max_forward_endpoint_num;
    let forward_endpoints_count = app_state
        .db
        .forward_endpoint()
        .count(vec![prisma::forward_endpoint::subscription_id::equals(
            Some(subscription.id),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;

    if forward_endpoints_count >= max_forward_endpoint_num as i64 {
        return Err(warp::reject::custom(Error::InvalidInput(format!(
            "Max forward endpoint num reached: {}",
            max_forward_endpoint_num
        ))));
    }

    // Fetch the source forward endpoint with all necessary fields
    let source_endpoint = app_state
        .db
        .forward_endpoint()
        .find_unique(prisma::forward_endpoint::id::equals(request.source_id))
        .select(prisma::forward_endpoint::select!(
            {
                name
                display_name
                ingress_address
                serve_port
                protocol
                protocol_config
                priv_key
                ws_port
                is_public
                setup_script
                traffic_scale
                allow_ip_v_6
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound(
            "Source forward endpoint not found".to_string(),
        ))?;

    // Generate new unique identifiers
    let timestamp = chrono::Utc::now().timestamp();
    let random_suffix = rand::rng().random_range(100..999);
    let new_name = format!(
        "{}-copy-{}-{}",
        source_endpoint.name, timestamp, random_suffix
    );
    let new_token_id = uuid::Uuid::new_v4().to_string();

    // Create new endpoint preserving all source configuration
    app_state
        .db
        .forward_endpoint()
        .create(
            new_name.clone(),
            new_name,
            source_endpoint.ingress_address,
            source_endpoint.serve_port,
            source_endpoint.protocol,
            source_endpoint.protocol_config, // PRESERVE existing config including passwords!
            source_endpoint.priv_key,
            new_token_id,
            vec![
                prisma::forward_endpoint::ws_port::set(source_endpoint.ws_port),
                prisma::forward_endpoint::is_public::set(source_endpoint.is_public),
                prisma::forward_endpoint::setup_script::set(source_endpoint.setup_script),
                prisma::forward_endpoint::traffic_scale::set(source_endpoint.traffic_scale),
                prisma::forward_endpoint::allow_ip_v_6::set(source_endpoint.allow_ip_v_6),
                // Set to current user's subscription
                prisma::forward_endpoint::subscription_id::set(Some(subscription.id)),
            ],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    Ok(warp::reply::html("Ok"))
}
