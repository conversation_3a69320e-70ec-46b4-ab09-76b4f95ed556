use std::collections::HashMap;

use anyhow::anyhow;
use log::{error, warn};
use warp::{reject::Rejection, reply::Reply};

use crate::{
    error::Error,
    message::{
        GetUserHistoricalSpeedsRequest, GetUserHistoricalSpeedsResponse, GetUserLineSpeedsResponse,
        GetUserSpeedsResponse, UserTotalSpeedItem,
    },
    prisma,
    stats::{get_latest_line_speeds_summary, get_user_historical_speeds, get_user_line_speeds},
    AppState,
};

pub async fn handle_get_user_speeds(app_state: AppState) -> Result<impl Reply, Rejection> {
    // Get all subscription IDs
    let subscriptions = app_state
        .db
        .subscription()
        .find_many(vec![])
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?;

    let subscription_ids: Vec<i32> = subscriptions.iter().map(|s| s.id).collect();

    if subscription_ids.is_empty() {
        let response = GetUserSpeedsResponse {
            user_speeds: HashMap::new(),
        };
        return Ok(warp::reply::json(&response));
    }

    // Get user line speeds from TDengine
    let client = app_state.tdengine_client.get().await.map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to get TDengine client: {}",
            e
        )))
    })?;
    let line_speeds = get_user_line_speeds(&subscription_ids, &client, &app_state.redis_pool)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

    // Aggregate speeds by subscription_id
    let mut user_speeds = HashMap::new();
    for speed in line_speeds {
        let entry =
            user_speeds
                .entry(speed.subscription_id)
                .or_insert_with(|| UserTotalSpeedItem {
                    subscription_id: speed.subscription_id,
                    total_upload_speed: 0.0,
                    total_download_speed: 0.0,
                    line_speeds: Vec::new(),
                });

        entry.total_upload_speed += speed.upload_speed;
        entry.total_download_speed += speed.download_speed;
        entry.line_speeds.push(speed);
    }

    let response = GetUserSpeedsResponse { user_speeds };
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_user_line_speeds(
    subscription_id: i32,
    app_state: AppState,
    token_id: String,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Getting line speeds for subscription_id: {}, token_id: {}",
        subscription_id,
        token_id
    );

    // Get the current user's subscription to check permissions
    let current_user_subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .exec()
        .await
        .map_err(Error::Database)?;

    let current_user_subscription = match current_user_subscription {
        Some(sub) => sub,
        None => {
            error!("No subscription found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::Auth(
                "Invalid token or subscription not found".to_string(),
            )));
        }
    };

    // Non-admin users can only access their own subscription data
    if !current_user_subscription.is_admin && current_user_subscription.id != subscription_id {
        warn!(
            "User {} attempted to access data for subscription {}",
            token_id, subscription_id
        );
        return Err(warp::reject::custom(Error::Auth(
            "Access denied: You can only view your own data".to_string(),
        )));
    }

    let client = app_state.tdengine_client.get().await.map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to get TDengine client: {}",
            e
        )))
    })?;
    let mut line_speeds = get_user_line_speeds(&[subscription_id], &client, &app_state.redis_pool)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    line_speeds.sort_by_key(|x| -((x.download_speed + x.upload_speed) as i64));

    // populate line name
    let lines = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![prisma::outbound_endpoint::id::in_vec(
            line_speeds.iter().map(|x| x.line_id).collect::<Vec<_>>(),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;

    for speed in &mut line_speeds {
        speed.line_name = lines
            .iter()
            .find(|line| line.id == speed.line_id)
            .map(|line| line.display_name.clone());
    }

    let response = GetUserLineSpeedsResponse { line_speeds };
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_user_historical_speeds(
    request: GetUserHistoricalSpeedsRequest,
    app_state: AppState,
    token_id: String,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Getting historical speeds for subscription_id: {}, time_range: {}, token_id: {}",
        request.subscription_id,
        request.time_range,
        token_id
    );

    // Get the current user's subscription to check permissions
    let current_user_subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .exec()
        .await
        .map_err(Error::Database)?;

    let current_user_subscription = match current_user_subscription {
        Some(sub) => sub,
        None => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
    };

    // Permission check: Users can only view their own data, admins can view any data
    if !current_user_subscription.is_admin
        && current_user_subscription.id != request.subscription_id
    {
        warn!(
            "User {} attempted to access data for subscription {}",
            token_id, request.subscription_id
        );
        return Err(warp::reject::custom(Error::Auth(
            "Access denied: You can only view your own data".to_string(),
        )));
    }

    // Convert time range to minutes
    let time_range_minutes = request.minutes.unwrap_or_else(|| {
        match request.time_range.as_str() {
            "1h" => 60,
            "3h" => 180,
            "6h" => 360,
            "24h" => 1440,
            "7d" => 10080,
            _ => 1440, // Default to 24 hours
        }
    });

    // Get subscription to access user's lines
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::id::equals(request.subscription_id))
        .exec()
        .await
        .map_err(Error::Database)?;

    if let Some(subscription) = subscription {
        let user_line_ids = subscription.lines;

        if user_line_ids.is_empty() {
            let response = GetUserHistoricalSpeedsResponse {
                historical_speeds: Vec::new(),
                available_lines: Vec::new(),
                has_more_lines: false,
                total_lines_count: 0,
            };
            return Ok(warp::reply::json(&response));
        }

        // Get line information from database
        let lines = app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(
                user_line_ids.clone(),
            )])
            .exec()
            .await
            .map_err(Error::Database)?;

        // Create a mapping of line_id to line_name
        let line_names: HashMap<i32, String> = lines
            .into_iter()
            .map(|line| (line.id, line.display_name))
            .collect();

        let client = app_state.tdengine_client.get().await.map_err(|e| {
            warp::reject::custom(Error::InternalError(anyhow!(
                "Failed to get TDengine client: {}",
                e
            )))
        })?;
        // Get latest speed summary for all user lines
        let mut line_speed_summaries =
            get_latest_line_speeds_summary(request.subscription_id, &user_line_ids, &client, &app_state.redis_pool)
                .await
                .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

        // Populate line names in the summaries
        for summary in &mut line_speed_summaries {
            summary.line_name = line_names.get(&summary.line_id).cloned();
        }

        // Determine which lines to query for historical data
        let lines_to_query = if let Some(ref requested_line_ids) = request.line_ids {
            // Use the specifically requested line IDs (filtered to user's lines)
            requested_line_ids
                .iter()
                .filter(|&id| user_line_ids.contains(id))
                .copied()
                .collect::<Vec<_>>()
        } else {
            // Auto-select lines: if more than 10 lines, select top 10 by speed
            if user_line_ids.len() > 10 {
                line_speed_summaries
                    .iter()
                    .take(10) // Already sorted by speed in get_latest_line_speeds_summary
                    .map(|summary| summary.line_id)
                    .collect()
            } else {
                user_line_ids.clone()
            }
        };

        // Mark which lines are selected in the summaries
        for summary in &mut line_speed_summaries {
            summary.is_selected = lines_to_query.contains(&summary.line_id);
        }

        // Get historical speed data from InfluxDB for selected lines
        let line_ids_filter = if lines_to_query.is_empty() {
            None
        } else {
            Some(lines_to_query.as_slice())
        };
        let mut historical_speeds = get_user_historical_speeds(
            request.subscription_id,
            time_range_minutes,
            &client,
            line_ids_filter,
        )
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

        // Populate line names in the historical speed data
        for speed_item in &mut historical_speeds {
            speed_item.line_name = line_names.get(&speed_item.line_id).cloned();
        }

        let response = GetUserHistoricalSpeedsResponse {
            historical_speeds,
            available_lines: line_speed_summaries,
            has_more_lines: request.line_ids.is_none() && user_line_ids.len() > 10,
            total_lines_count: user_line_ids.len() as i32,
        };
        Ok(warp::reply::json(&response))
    } else {
        let response = GetUserHistoricalSpeedsResponse {
            historical_speeds: Vec::new(),
            available_lines: Vec::new(),
            has_more_lines: false,
            total_lines_count: 0,
        };
        Ok(warp::reply::json(&response))
    }
}
