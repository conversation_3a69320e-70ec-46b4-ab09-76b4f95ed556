use std::collections::HashMap;

use crate::add_machine::generate_worker_setup_script;
use crate::error::Error;
use crate::forwarder::{
    generate_port_forward_config, update_hammer_proxy_config, update_tot_proxy_config,
    ForwardEndpointInfoForForwarder,
};
use crate::{prisma, AppState};
use anyhow::anyhow;
use base64::Engine;
use common::app_message::{LatencyTestMethod, Mode};
use common::server_custom_config::ServerCustomConfig;
use common::{TotConfig, TotGlobalConfig};
use private_tun::snell_impl_ver::config::ClientConfig;
use reqwest::Url;
// use influxdb::{Client, ReadQuery};
use crate::message::*;
use log::{error, info};
use prisma_client_rust::operator::*;

use crate::stats::{get_line_netcard_speed, get_line_system_stats};
use warp::{reject::Rejection, Reply};
macro_rules! get_number {
    ($conn:expr, $k:expr, $d:expr) => {
        match $conn.get($k).await {
            Ok(v) => v,
            Err(e) => match e.kind() {
                redis::ErrorKind::TypeError => $d,
                _ => return Err(warp::reject::custom(crate::error::Error::Redis(e.into()))),
            },
        }
    };
}

pub mod forwarder_manage;
pub mod latency_monitoring;
pub mod license;
pub mod package_manage;
pub mod port_manage;
pub mod port_stats;
pub mod server_manage;
pub mod subscription_manage;
pub mod system_settings;
pub mod user_stats;

pub use forwarder_manage::*;
pub use latency_monitoring::*;
pub use license::*;
pub use package_manage::*;
pub use port_manage::*;
pub use port_stats::*;
pub use server_manage::*;
pub use subscription_manage::*;
pub use system_settings::*;
pub use user_stats::*;

// Helper function to check if a string matches a regex pattern or exact match
fn matches_regex_or_exact(text: &str, pattern: &str) -> bool {
    // First check for exact match (case-insensitive)
    if text.to_lowercase().contains(&pattern.to_lowercase()) {
        return true;
    }

    // Then try regex match
    match regex::Regex::new(pattern) {
        Ok(re) => re.is_match(text),
        Err(_) => false, // If regex is invalid, fall back to substring match
    }
}

// Helper function to calculate match score for sorting (higher score = better match)
fn calculate_match_score(
    port: &PortInfo,
    name_pattern: Option<&String>,
    entry_point_pattern: Option<&String>,
    target_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if port.display_name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if port
            .display_name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = entry_point_pattern {
        let entry_point = format!("{}:{}", port.ip_addr, port.port_v4);
        if entry_point.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if entry_point.to_lowercase().contains(&pattern.to_lowercase()) {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = target_pattern {
        let target_addresses = port.target_address_list.join(", ");
        if target_addresses.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if target_addresses
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

// Helper function to calculate match score for servers (higher score = better match)
fn calculate_server_match_score(
    server: &ServerInfo,
    name_pattern: Option<&String>,
    ip_addr_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if server.display_name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if server
            .display_name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = ip_addr_pattern {
        if server.ip_addr.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if server
            .ip_addr
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

pub async fn generate_port_forward_data(
    token_id: &str,
    forward_endpoints: Option<Vec<i32>>,
    balance_strategy: Option<u32>,
    subscription: &SubscriptionWithAdminInfo::Data,
    tx: &prisma::PrismaClient,
    custom_config: &Option<ServerCustomConfig>,
) -> anyhow::Result<Option<(String, Vec<i32>)>> {
    let mut set_forward_config = None;
    if let Some(forward_endpoints) = forward_endpoints {
        let max_forward_endpoint_num = subscription.max_forward_endpoint_num as usize;
        if forward_endpoints.len() > max_forward_endpoint_num {
            error!(
                "Add port failed, current user exceed the max forward endpoint number: {}",
                max_forward_endpoint_num
            );
            return Err(anyhow::anyhow!(
                "Add port failed, current user exceed the max forward endpoint number: {}",
                max_forward_endpoint_num
            ));
        }
        if forward_endpoints.len() > 0 {
            let forward_endpoint_list = tx
                .forward_endpoint()
                .find_many(vec![or(vec![
                    // forward endpoint that belong to this subscription
                    and(vec![
                        prisma::forward_endpoint::id::in_vec(forward_endpoints.clone()),
                        prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
                    ]),
                    // forward endpoint that is public
                    and(vec![
                        prisma::forward_endpoint::id::in_vec(forward_endpoints.clone()),
                        prisma::forward_endpoint::is_public::equals(true),
                    ]),
                ])])
                .select(ForwardEndpointInfoForForwarder::select())
                .exec()
                .await
                .map_err(Error::Database)?
                .iter()
                .map(|x| (x.id, x.clone()))
                .collect::<HashMap<_, _>>();
            // 按照请求ID顺序的顺序排序数据
            let new_list = forward_endpoints
                .iter()
                .filter_map(|id| forward_endpoint_list.get(id).clone())
                .collect::<Vec<_>>();
            let forward_config =
                generate_port_forward_config(&new_list, balance_strategy, custom_config)
                    .map_err(Error::InternalError)?;
            info!("token_id: {}, forward config: {}", token_id, forward_config);
            let related_forward_endpoint_ids = new_list.iter().map(|x| x.id).collect::<Vec<_>>();
            info!(
                "token_id: {}, related_forward_endpoint_ids: {:?}",
                token_id, related_forward_endpoint_ids
            );
            set_forward_config = Some((forward_config, related_forward_endpoint_ids));
        }
    }
    Ok(set_forward_config)
}

prisma::subscription::select!(
    SubscriptionCommonInfo {
        id
        max_port_num_per_server
        max_forward_endpoint_num
        lines
        ports: select {
            id
            outbound_endpoint_id
            port_v_4
        }
        bandwidth
    }
);

prisma::subscription::select!(
    SubscriptionWithAdminInfo {
        id
        max_port_num_per_server
        max_forward_endpoint_num
        lines
        ports: select {
            id
            outbound_endpoint_id
            port_v_4
        }
        bandwidth
        is_admin
    }
);

pub async fn generate_tot_config_or_forward_config(
    token_id: &str,
    forward_endpoints: Option<Vec<i32>>,
    fwd_balance_strategy: Option<u32>,
    mut tot_server_list: Option<Vec<i32>>,
    tot_server_select_mode: Option<u32>,
    tot_server_test_method: Option<u32>,
    subscription: &SubscriptionWithAdminInfo::Data,
    tx: &prisma::PrismaClient,
    custom_config: &Option<ServerCustomConfig>,
) -> anyhow::Result<Option<(String, (Vec<i32>, Option<Vec<i32>>))>> {
    let Some((forward_config, related_forward_endpoint_ids)) = generate_port_forward_data(
        token_id,
        forward_endpoints,
        fwd_balance_strategy,
        subscription,
        tx,
        custom_config,
    )
    .await?
    else {
        return Ok(None);
    };

    let Some(tot_server_lists) = tot_server_list.take() else {
        // only forward endpoint
        return Ok(Some((forward_config, (related_forward_endpoint_ids, None))));
    };

    if tot_server_lists.is_empty() {
        return Ok(Some((forward_config, (related_forward_endpoint_ids, None))));
    }

    let max_forward_endpoint_num = subscription.max_forward_endpoint_num as usize;
    if tot_server_lists.len() > max_forward_endpoint_num {
        error!(
            "Add port failed, current user exceed the max tot server number: {}",
            max_forward_endpoint_num
        );
        return Err(anyhow::anyhow!(
            "Add port failed, current user exceed the max tot server number: {}",
            max_forward_endpoint_num
        ));
    }
    let forward_endpoint_list = tx
        .forward_endpoint()
        .find_many(vec![or(vec![
            // forward endpoint that belong to this subscription
            and(vec![
                prisma::forward_endpoint::id::in_vec(tot_server_lists.clone()),
                prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            ]),
            // forward endpoint that is public
            and(vec![
                prisma::forward_endpoint::id::in_vec(tot_server_lists.clone()),
                prisma::forward_endpoint::is_public::equals(true),
            ]),
        ])])
        .select(ForwardEndpointInfoForForwarder::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .iter()
        .map(|x| (x.id, x.clone()))
        .collect::<HashMap<_, _>>();
    // 按照请求ID顺序的顺序排序数据
    let new_list = tot_server_lists
        .iter()
        .filter_map(|id| forward_endpoint_list.get(id).clone())
        .collect::<Vec<_>>();
    let tot_fwd_config =
        generate_port_forward_config(&new_list, tot_server_select_mode.clone(), custom_config)
            .map_err(Error::InternalError)?;
    info!(
        "token_id: {}, tot forward config: {}",
        token_id, tot_fwd_config
    );
    let related_tot_endpoint_ids = new_list.iter().map(|x| x.id).collect::<Vec<_>>();
    info!(
        "token_id: {}, related_tot_endpoint_ids: {:?}",
        token_id, related_tot_endpoint_ids
    );
    let tot_total_config = TotConfig {
        fwd_config: serde_json::from_str(&forward_config)?,
        tot_server_list: serde_json::from_str(&tot_fwd_config)?,
        tot_server_select_mode: tot_server_select_mode
            .and_then(|x| TryFrom::<u32>::try_from(x as u32).ok())
            .unwrap_or(Mode::BestLatency),
        tot_server_test_method: tot_server_test_method
            .and_then(|x| TryFrom::<u32>::try_from(x as u32).ok())
            .unwrap_or(LatencyTestMethod::Tcpping),
        tot_config: TotGlobalConfig {
            enable_affinity: None,
            enable_1rtt: None,
            recv_queue_size: None,
            max_used_server: None,
        },
    };
    Ok(Some((
        serde_json::to_string(&tot_total_config)?,
        (related_forward_endpoint_ids, Some(related_tot_endpoint_ids)),
    )))
}

fn check_addr_list(addr_list: &Vec<String>) -> anyhow::Result<()> {
    if addr_list.is_empty() {
        return Err(anyhow::anyhow!("target address list is empty"));
    }
    if addr_list.len() > 10 {
        return Err(anyhow::anyhow!("target address list is too long max is 10"));
    }
    for addr in addr_list {
        let (addr, _comment) = match addr.split_once('#') {
            Some((addr, comment)) => (addr.to_string(), comment.to_string()),
            None => (addr.to_string(), "".to_string()),
        };
        let port_str = addr
            .rsplit(':')
            .next()
            .ok_or(anyhow::anyhow!("target: {} not contain port", addr))?;
        let port = port_str.parse::<u32>()?;
        if port < 1 || port > 65535 {
            return Err(anyhow::anyhow!(
                "target: {} port out of range: {}",
                addr,
                port
            ));
        }
        let _url = Url::parse(&format!("http://{addr}")).map_err(|e| {
            error!("parse addr faile: {e}");
            anyhow!("target: {addr} is not valid")
        })?;
    }
    Ok(())
}

// Enhanced check_addr_list with admin privilege validation
fn check_addr_list_with_admin_check(addr_list: &Vec<String>, is_admin: bool) -> anyhow::Result<()> {
    // First run the basic validation
    check_addr_list(addr_list)?;

    // Then validate public IPs only for non-admin users
    common::ip_utils::validate_public_ips_only(addr_list, is_admin)?;

    Ok(())
}

pub async fn handle_get_line_stats(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    log::info!("Get line stats for token_id: \'{}\'", token_id,);
    let lines = app_state
        .cache_sub_lines
        .get(&token_id)
        .await
        .map_err(|_| warp::reject::custom(Error::NotFound("Subscription not found".to_string())))?;
    let client = app_state.tdengine_client.get().await.map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to get TDengine client: {}",
            e
        )))
    })?;
    let netcard_speed = get_line_netcard_speed(&lines, &client, &app_state.redis_pool)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let system_stats = get_line_system_stats(&lines, &client, &app_state.redis_pool)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let result = GetLineStatsResponse {
        netcard_speed,
        system_stats,
    };
    Ok(warp::reply::json(&result))
}

prisma::port::select!(
    ToUpdatePortForwardConfig {
        id
        display_name
        forward_config
        forward_protocol
    }
);

prisma::forward_endpoint::select!(
    ToUpdateForwardEndpoint {
        id
        subscription_id
        ingress_address
        serve_port
        inner_port
        allow_ip_v_6
        protocol_config
    }
);

pub async fn update_port_proxy_config(
    port_data: &ToUpdatePortForwardConfig::Data,
    forward_endpoint: &ToUpdateForwardEndpoint::Data,
    ip_addr: &str,
    serve_port: u16,
    allow_ipv6: bool,
) -> Result<String, Error> {
    if let Some(forward_config) = &port_data.forward_config {
        if forward_config.is_empty() {
            return Err(Error::InternalError(anyhow!(
                "Forward config is empty for port: {}",
                port_data.display_name
            )));
        }
        let Some(forward_protocol) = &port_data.forward_protocol else {
            log::warn!(
                "Forward protocol is empty for port: {} id: {}",
                port_data.display_name,
                port_data.id,
            );
            return Err(Error::InternalError(anyhow!(
                "Forward protocol is empty for port: {}",
                port_data.display_name
            )));
        };
        let forward_protocol = forward_protocol.as_str().trim().to_lowercase();
        match forward_protocol.as_str() {
            "hammer" => {
                let Ok(forward_config) = serde_json::from_str::<ClientConfig>(&forward_config)
                else {
                    log::warn!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    );
                    return Err(Error::InternalError(anyhow!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    )));
                };
                let new_config = update_hammer_proxy_config(
                    forward_config,
                    forward_endpoint.id,
                    &ip_addr,
                    serve_port,
                    allow_ipv6,
                )
                .map_err(|e| {
                    log::error!(
                        "Failed to update hammer proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    );
                    Error::InternalError(anyhow!(
                        "Failed to update hammer proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    ))
                })?;
                return Ok(new_config);
            }
            "tot" => {
                let Ok(tot_config) = serde_json::from_str::<TotConfig>(&forward_config) else {
                    log::warn!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    );
                    return Err(Error::InternalError(anyhow!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    )));
                };
                let new_config = update_tot_proxy_config(
                    tot_config,
                    forward_endpoint.id,
                    &ip_addr,
                    serve_port,
                    allow_ipv6,
                )
                .map_err(|e| {
                    log::error!(
                        "Failed to update tot proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    );
                    Error::InternalError(anyhow!(
                        "Failed to update tot proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    ))
                })?;
                return Ok(new_config);
            }
            _ => {
                log::error!(
                    "Unsupported forward protocol: {:?} for port: {} id: {}",
                    &port_data.forward_protocol,
                    port_data.display_name,
                    port_data.id
                );
                return Err(Error::InternalError(anyhow!(
                    "Unsupported forward protocol: {:?} for port: {} id: {}",
                    &port_data.forward_protocol,
                    port_data.display_name,
                    port_data.id
                )));
            }
        }
    }
    return Err(Error::InternalError(anyhow!(
        "Forward config is empty for port: {}",
        port_data.display_name
    )));
}

pub async fn handle_get_setup_script(
    forward_token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let forward_endpoint = app_state
        .db
        .forward_endpoint()
        .find_unique(prisma::forward_endpoint::token_id::equals(forward_token_id))
        .select(prisma::forward_endpoint::select!({ setup_script }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Forward endpoint not found".to_string()))?;
    Ok(warp::reply::html(
        forward_endpoint.setup_script.unwrap_or_default(),
    ))
}

pub async fn handle_get_worker_setup_script(
    worker_pubkey: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let setup_script = generate_worker_setup_script(
        app_state.db.clone(),
        &worker_pubkey,
        &app_state.opt.mgmt_pubkey,
        &app_state.opt.mgmt_url.to_string(),
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate worker setup script: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html(setup_script))
}

fn get_balance_strategy_from_config(config: &str) -> Option<i32> {
    let decoded_config = base64::engine::general_purpose::STANDARD
        .decode(config)
        .ok()?;
    let config: ClientConfig = serde_json::from_slice(&decoded_config).ok()?;
    if let Some(multi_server_config) = config.multi_server_config {
        match multi_server_config.mode {
            private_tun::snell_impl_ver::server_provider::Mode::BestLatency => Some(0),
            private_tun::snell_impl_ver::server_provider::Mode::Fallback => Some(1),
            private_tun::snell_impl_ver::server_provider::Mode::Balance { balance_mode } => {
                match balance_mode {
                    private_tun::snell_impl_ver::server_provider::BalanceMode::DomainFollow => {
                        Some(2)
                    }
                    private_tun::snell_impl_ver::server_provider::BalanceMode::RoundRobin => {
                        Some(3)
                    }
                    private_tun::snell_impl_ver::server_provider::BalanceMode::Random => Some(4),
                }
            }
            _ => None,
        }
    } else {
        None
    }
}
