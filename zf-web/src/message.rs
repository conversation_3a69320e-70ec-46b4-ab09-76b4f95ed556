use std::{collections::HashMap, str::FromStr};

use chrono::{DateTime, Utc};
use common::license_message::Entitlements;
use serde::{Deserialize, Serialize};

#[derive(Debug, serde::Serialize, serde::Deserialize, Clone)]
pub enum FilterType {
    Http,
    Socks5,
    BitTorrent,
    Tls,
}

impl FilterType {
    pub fn as_str(&self) -> &'static str {
        match self {
            FilterType::Http => "Http",
            FilterType::Socks5 => "Socks5",
            FilterType::BitTorrent => "BitTorrent",
            FilterType::Tls => "Tls",
        }
    }

    pub fn from_str(s: &str) -> Option<FilterType> {
        match s {
            "Http" => Some(FilterType::Http),
            "Socks5" => Some(FilterType::Socks5),
            "BitTorrent" => Some(FilterType::BitTorrent),
            "Tls" => Some(FilterType::Tls),
            _ => None,
        }
    }

    pub fn all() -> Vec<FilterType> {
        vec![
            FilterType::Http,
            FilterType::Socks5,
            FilterType::BitTorrent,
            FilterType::Tls,
        ]
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub jwt: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub token_id: String,
    pub exp: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionInfo {
    pub id: i32,
    pub valid_until: DateTime<Utc>,
    pub next_reset: Option<DateTime<Utc>>,
    pub traffic_used: i64,
    pub traffic_total: i64,
    pub bandwidth: Option<i32>,
    pub lines: Vec<LineInfo>,
    pub is_admin: bool,
    pub allow_forward_endpoint: bool,
    pub is_expired: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LineInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub is_online: Option<bool>,
    pub port_start: Option<i32>,
    pub port_end: Option<i32>,
    pub allow_forward: Option<bool>,
    pub is_in_package: Option<bool>,
    pub traffic_scale: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PortInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub port_v4: i32,
    pub traffic_in: i64,
    pub traffic_out: i64,
    pub outbound_endpoint_id: Option<i32>,
    pub line_name: Option<String>,
    pub is_suspended: bool,
    pub bandwidth: Option<i32>, // Port-specific bandwidth limit in Mbps

    // forward config
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // target select
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,

    // proxy protocol
    pub accept_proxy_protocol: Option<bool>,
    pub send_proxy_protocol_version: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub fetch_all: Option<bool>, // When true, ignore pagination and return all items
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub line: Option<i32>,
    pub entry_point: Option<String>,
    pub target: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminPortsByUserRequest {
    pub subscription_id: i32,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminLineDetailsByUserRequest {
    pub subscription_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ForwardEndpointSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub ingress_address: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub token_id: Option<String>,
    pub email: Option<String>,
    pub valid_until_start: Option<String>,
    pub valid_until_end: Option<String>,
    pub next_reset_start: Option<String>,
    pub next_reset_end: Option<String>,
    pub lines: Option<Vec<i32>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServerSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub ip_addr: Option<String>,
    pub version: Option<Vec<String>>,
    pub status: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedPortsResponse {
    pub ports: Vec<PortInfo>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub current_page: u32,
    pub page_size: u32,
    pub total_items: u32,
    pub total_pages: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePortRequest {
    pub display_name: String,
    pub expected_port: Option<u16>,
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,
    pub bandwidth: Option<u32>, // Port-specific bandwidth limit in Mbps

    pub outbound_endpoint_id: i32,
    pub balance_strategy: Option<u32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,

    // proxy protocol
    pub accept_proxy_protocol: Option<bool>,
    pub send_proxy_protocol_version: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RmvPortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct ModifyPortRequest {
    pub id: i32, // port id
    pub display_name: String,
    pub expected_port: Option<u16>,
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,
    pub bandwidth: Option<u32>, // Port-specific bandwidth limit in Mbps

    pub balance_strategy: Option<u32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,

    // proxy protocol
    pub accept_proxy_protocol: Option<bool>,
    pub send_proxy_protocol_version: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SuspendPortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResumePortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NetSpeedItem {
    pub rx: u64,       // unit: byte/s
    pub tx: u64,       // unit: byte/s
    pub total_rx: u64, // unit: byte
    pub total_tx: u64, // unit: byte
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatsItem {
    pub cpu_usage: f32,       // percentage
    pub memory_total: u64,    // unit: byte
    pub memory_used: u64,     // unit: byte
    pub uptime: u64,          // unit: second
    pub tcp_connections: u32, // total TCP connections count
    pub udp_connections: u32, // total UDP connections count
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLineStatsResponse {
    pub netcard_speed: HashMap<i32, NetSpeedItem>, // line_id -> netcard_speed
    pub system_stats: HashMap<i32, SystemStatsItem>, // line_id -> system_stats
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserLineSpeedItem {
    pub subscription_id: i32,
    pub line_id: i32,
    pub line_name: Option<String>,
    pub upload_speed: f64,     // unit: byte/s
    pub download_speed: f64,   // unit: byte/s
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserTotalSpeedItem {
    pub subscription_id: i32,
    pub total_upload_speed: f64,   // unit: byte/s
    pub total_download_speed: f64, // unit: byte/s
    pub line_speeds: Vec<UserLineSpeedItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetUserSpeedsResponse {
    pub user_speeds: HashMap<i32, UserTotalSpeedItem>, // subscription_id -> speed data
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetUserLineSpeedsResponse {
    pub line_speeds: Vec<UserLineSpeedItem>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ForwardProtocol {
    Hammer,
}
impl ForwardProtocol {
    pub fn to_string(&self) -> String {
        match self {
            ForwardProtocol::Hammer => "hammer".to_string(),
        }
    }
}
impl FromStr for ForwardProtocol {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "hammer" => Ok(ForwardProtocol::Hammer),
            s => Err(anyhow::anyhow!("Invalid protocol: {}", s)),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ForwardEndpoint {
    pub id: i32,
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: i32,
    pub is_public: bool,
    pub token_id: String,
    pub allow_ipv6: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AddForwardEndpointRequest {
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: Option<i32>, // serve port leave empty for auto assign
    pub bind_port: Option<i32>, // bind port leave empty for auto assign
    pub allow_ipv6: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ModifyForwardEndpointRequest {
    pub id: i32,
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: Option<i32>,
    pub bind_port: Option<i32>,
    pub allow_ipv6: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RmvForwardEndpointRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CopyForwardEndpointRequest {
    pub source_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetForwardEndpointListResponse {
    pub forward_endpoints: Vec<ForwardEndpoint>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedForwardEndpointResponse {
    pub forward_endpoints: Vec<ForwardEndpoint>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TestLatencyRequest {
    pub port_id: i32,
}

// #[derive(Debug, Serialize, Deserialize)]
// pub struct TestLatencyResponse {
//     pub fwd_server_latency: Option<HashMap<String, String>>, // entry -> fwd: fwd_server_name -> latency
//     pub remote_latency: HashMap<String, (Option<String>, String)>, // fwd -> remote: remote_address -> (fwd_server_name, latency)
// }

#[derive(Debug, Serialize, Deserialize)]
pub struct TestLatencyResponse {
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetConnectionStatsRequest {
    pub port_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetConnectionStatsResponse {
    pub connections: Vec<common::app_message::ConnectionStatsInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionItem {
    pub id: i32,
    pub email_address: String,
    pub token_id: String,
    pub valid_until: DateTime<Utc>,
    pub next_reset: Option<DateTime<Utc>>,
    pub traffic_used: i64,
    pub traffic_total: i64,
    pub lines: Vec<LineInfo>,
    pub activated: bool,
    pub allow_forward_endpoint: bool,
    pub max_ports_per_server: i32,
    pub bill_type: BillingType,
    pub total_days: u32,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub package_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionListResponse {
    pub subscriptions: Vec<SubscriptionItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedSubscriptionResponse {
    pub subscriptions: Vec<SubscriptionItem>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AddeUserInfo {
    pub address: String,
    pub bandwidth: Option<u32>, //mbps
    pub traffic: u64,           // GB
    pub activated: bool,
    pub max_ports_per_server: u32,
    pub bill_type: BillingType,
    pub total_days: u32,
    pub lines: Vec<i32>,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub package_id: Option<i32>,
    pub traffic_reset_days: Option<u32>, // Independent traffic reset cycle in days
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub enum BillingType {
    Cycle { days: usize, price: usize },
    OneTime { price: usize, days: usize },
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RmvUserRequest {
    pub user_id: i32,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct EditUserInfo {
    pub user_id: i32,
    pub address: String,
    pub bandwidth: Option<u32>, //mbps
    pub traffic: u64,           // GB
    pub activated: bool,
    pub max_ports_per_server: u32,
    pub bill_type: BillingType,
    pub lines: Vec<i32>,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub package_id: Option<i32>,
    pub traffic_reset_days: Option<u32>, // Independent traffic reset cycle in days
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ExtendSubscriptionTimeRequest {
    pub user_id: i32,
    pub days: Option<i32>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ServerListResponse {
    pub servers: Vec<ServerInfo>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct PaginatedServerResponse {
    pub servers: Vec<ServerInfo>,
    pub pagination: PaginationInfo,
    pub available_versions: Vec<String>,
    pub available_tags: Vec<String>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ServerInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub is_online: Option<bool>,
    pub port_start: Option<i32>,
    pub port_end: Option<i32>,
    pub used_ports: Option<Vec<i32>>,
    pub interface_name: Option<String>,
    pub server_pubkey: String,
    pub version: Option<String>,
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub traffic_scale: Option<f32>,
    pub allow_forward: Option<bool>,
    pub allow_latency_test: Option<bool>,
    pub allow_ipv6: Option<bool>,
    pub use_forward_as_tun: Option<bool>,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub protocol_filters: Option<Vec<FilterType>>,
    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,
    // custom config
    pub custom_config: Option<String>,
    // tags
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AddServerRequest {
    pub display_name: String,
    pub ip_addr: String,
    pub interface_name: String,
    pub port_start: Option<i32>, // default 30000
    pub port_end: Option<i32>,   // default 31000
    // advanced options
    pub traffic_scale: Option<f32>,       // default 1.0
    pub allow_forward: Option<bool>,      // default false
    pub allow_latency_test: Option<bool>, // default true
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub allow_ipv6: Option<bool>,         // default false
    pub use_forward_as_tun: Option<bool>, // default false
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub protocol_filters: Option<Vec<FilterType>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,

    // custom config
    pub custom_config: Option<String>,
    // tags
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Default)]
pub struct ModifyServerRequest {
    pub server_id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub interface_name: Option<String>,
    pub port_start: Option<i32>, // default 30000
    pub port_end: Option<i32>,   // default 31000
    // advanced options
    pub traffic_scale: Option<f32>,       // default 1.0
    pub allow_forward: Option<bool>,      // default false
    pub allow_latency_test: Option<bool>, // default true
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub allow_ipv6: Option<bool>,         // default false
    pub use_forward_as_tun: Option<bool>, // default false
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub protocol_filters: Option<Vec<FilterType>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,

    // custom config
    pub custom_config: Option<String>,
    // tags
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RmvServerRequest {
    pub server_id: i32,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ResetUserTrafficRequest {
    pub user_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetUserHistoricalSpeedsRequest {
    pub subscription_id: i32,
    pub time_range: String,         // "1h", "3h", "6h", "24h", "7d"
    pub minutes: Option<i32>,       // Optional: override time range with specific minutes
    pub line_ids: Option<Vec<i32>>, // Optional: specific line IDs to query
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserHistoricalSpeedItem {
    pub subscription_id: i32,
    pub line_id: i32,
    pub line_name: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub upload_speed: f64,     // unit: byte/s
    pub download_speed: f64,   // unit: byte/s
    pub traffic_inc: u64,      // unit: byte (traffic increment)
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LineSpeedSummary {
    pub line_id: i32,
    pub line_name: Option<String>,
    pub latest_total_speed: f64, // Latest upload + download speed combined (bytes/s)
    pub is_selected: bool,       // Whether this line is included in the current result
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetUserHistoricalSpeedsResponse {
    pub historical_speeds: Vec<UserHistoricalSpeedItem>,
    pub available_lines: Vec<LineSpeedSummary>, // All available lines for the user
    pub has_more_lines: bool,                   // Whether there are more lines than displayed
    pub total_lines_count: i32,                 // Total number of lines available for the user
}

// 套餐相关结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct PackageInfo {
    pub id: i32,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub bandwidth: Option<i32>,
    pub total_traffic: i64,
    pub max_ports_per_server: i32,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub is_active: bool,
    pub is_default: bool,
    pub package_lines: Vec<PackageLineInfo>,
    pub bill_type: Option<BillingType>,
    pub total_days: Option<u32>,
    pub traffic_reset_days: Option<i32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PackageLineInfo {
    pub id: i32,
    pub line_id: i32,
    pub line_name: String,
    pub bandwidth_limit: Option<i32>,
    pub traffic_scale: Option<f32>,
    pub line_traffic: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PackageListResponse {
    pub packages: Vec<PackageInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePackageRequest {
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub bandwidth: Option<i32>,
    pub total_traffic: u64, // GB
    pub max_ports_per_server: i32,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub is_active: bool,
    pub is_default: bool,
    pub lines: Vec<CreatePackageLineRequest>,
    // 计费字段
    pub bill_type: Option<BillingType>,
    pub traffic_reset_days: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePackageLineRequest {
    pub line_id: i32,
    pub bandwidth_limit: Option<i32>,
    pub traffic_scale: Option<f32>,
    pub line_traffic: Option<u64>, // GB
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePackageRequest {
    pub id: i32,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub bandwidth: Option<i32>,
    pub total_traffic: u64, // GB
    pub max_ports_per_server: i32,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub is_active: bool,
    pub is_default: bool,
    pub lines: Vec<UpdatePackageLineRequest>,
    // 计费字段
    pub bill_type: Option<BillingType>,
    pub traffic_reset_days: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePackageLineRequest {
    pub id: Option<i32>, // None for new lines
    pub line_id: i32,
    pub bandwidth_limit: Option<i32>,
    pub traffic_scale: Option<f32>,
    pub line_traffic: Option<u64>, // GB
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DeletePackageRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PackageSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub name: Option<String>,
    pub is_active: Option<bool>,
    pub is_default: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedPackageResponse {
    pub packages: Vec<PackageInfo>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePackageResponse {
    pub id: i32,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PackageOperationResponse {
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DetailedPackageInfo {
    pub id: i32,
    pub name: String,
    pub display_name: String,
    pub is_default: bool,
    pub bandwidth: Option<i32>,
    pub total_traffic: i64,
    pub max_ports_per_server: i32,
    pub allow_forward_endpoint: bool,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub lines: Vec<LineInfo>,
    pub bill_type: Option<BillingType>,
    pub total_days: Option<u32>,
    pub traffic_reset_days: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DetailedPackageListResponse {
    pub packages: Vec<DetailedPackageInfo>,
}

// Line Details API structures
#[derive(Debug, Serialize, Deserialize)]
pub struct LineDetailInfo {
    pub line_id: i32,
    pub line_name: String,
    pub entry_ip: String,
    pub traffic_scale: f32,
    pub traffic_limit: Option<i64>, // 流量限制 (字节)
    pub used_traffic: i64,          // 已使用流量 (字节)
    pub port_count: i32,            // 该线路上设置的端口数量
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLineDetailsResponse {
    pub line_details: Vec<LineDetailInfo>,
}

// Latency Monitoring API structures
#[derive(Debug, Serialize, Deserialize)]
pub struct GetLatencyConfigsRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub server_id: Option<i32>,
    pub is_enabled: Option<bool>,
    pub test_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LatencyConfigInfo {
    pub id: i32,
    pub server_id: i32,
    pub server_name: Option<String>,
    pub server_ip: Option<String>,
    pub display_name: String,
    pub target_address: String,
    pub target_port: Option<i32>,
    pub test_type: String,
    pub packet_size: Option<i32>,
    pub timeout: i32,
    pub interval: i32,
    pub alert_threshold: i32,
    pub is_public: bool,
    pub is_enabled: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLatencyConfigsResponse {
    pub configs: Vec<LatencyConfigInfo>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateLatencyConfigRequest {
    pub server_id: i32,
    pub display_name: String,
    pub target_address: String,
    pub target_port: Option<i32>,
    pub test_type: String, // "tcp" or "icmp"
    pub packet_size: Option<i32>,
    pub timeout: Option<i32>,
    pub interval: Option<i32>,
    pub alert_threshold: Option<i32>,
    pub is_public: Option<bool>,
    pub is_enabled: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateLatencyConfigRequest {
    pub display_name: Option<String>,
    pub target_address: Option<String>,
    pub target_port: Option<Option<i32>>,
    pub packet_size: Option<Option<i32>>,
    pub timeout: Option<i32>,
    pub interval: Option<i32>,
    pub alert_threshold: Option<i32>,
    pub is_public: Option<bool>,
    pub is_enabled: Option<bool>,
    pub test_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLatencyHistoryRequest {
    pub server_id: i32,
    pub config_id: Option<i32>,
    pub time_range: Option<String>, // "1h", "3h", "6h", "12h", "24h", "7d"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LatencyHistoryPoint {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub config_id: i32,
    pub target_address: String,
    pub latency_us: Option<u64>,
    pub success: bool,
    pub error_msg: Option<String>,
    // SmokePing-style statistics
    pub test_round: Option<i32>,
    pub samples_count: Option<i32>,
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub median_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,
    pub packet_loss_rate: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLatencyHistoryResponse {
    pub server_id: i32,
    pub config_id: Option<i32>,
    pub time_range: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub data_points: Vec<LatencyHistoryPoint>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LatencyRealtimeStatus {
    pub config_id: i32,
    pub target_address: String,
    pub display_name: String,
    pub test_type: String,
    pub latency_us: Option<u64>,
    pub success: bool,
    pub error_msg: Option<String>,
    pub last_test_time: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLatencyRealtimeResponse {
    pub server_id: i32,
    pub status: std::collections::BTreeMap<i32, LatencyRealtimeStatus>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum BatchUpdateOperation {
    Replace,
    Append,
    Remove,
}

impl Default for BatchUpdateOperation {
    fn default() -> Self {
        BatchUpdateOperation::Replace
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchUpdatePortsRequest {
    pub regex_pattern: String,
    pub replacement: String,
    pub preview_only: bool,
    #[serde(default)]
    pub operation_type: BatchUpdateOperation,
    // Condition for Append operation: only ports containing this address will be modified
    #[serde(default)]
    pub condition_address: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortUpdateDetail {
    pub port_id: i32,
    pub port_name: String,
    pub old_addresses: Vec<String>,
    pub new_addresses: Vec<String>,
    pub changes_made: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchUpdatePortsResponse {
    pub updated_ports: Vec<PortUpdateDetail>,
    pub success_count: u32,
    pub failed_count: u32,
    pub errors: Vec<String>,
}
#[derive(Debug, Serialize, Deserialize)]
pub struct CalculateRenewalPriceResponse {
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: String,
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32,
    #[serde(rename = "totalPrice")]
    pub total_price: String,
    pub currency: String,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct SoftwareLicenseInfo {
    pub valid_until: DateTime<Utc>,
    pub is_expired: bool,
    pub days_remaining: i32,
    pub license_id: String,
    pub entitlements: Entitlements,
    pub monthly_rate: Option<String>,
}
