FROM rust:1.70 as builder

# 设置工作目录
WORKDIR /app

# 复制 Cargo 文件
COPY Cargo.toml Cargo.lock ./
COPY rrd-service/Cargo.toml ./rrd-service/

# 复制源代码
COPY rrd-service/src ./rrd-service/src

# 构建
RUN cargo build --release --bin rrd-service

# 运行时镜像
FROM debian:bullseye-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -r -s /bin/false rrd-service

# 创建数据目录
RUN mkdir -p /data/rrd && chown rrd-service:rrd-service /data/rrd

# 复制二进制文件
COPY --from=builder /app/target/release/rrd-service /usr/local/bin/rrd-service

# 设置权限
RUN chmod +x /usr/local/bin/rrd-service

# 切换到非root用户
USER rrd-service

# 暴露端口
EXPOSE 8082

# 设置环境变量
ENV RRD_DATA_PATH=/data/rrd
ENV PORT=8082

# 启动命令
CMD ["rrd-service"]