use bb8_redis::bb8::RunError;
use serde::Serialize;
use thiserror::Error;
use warp::{reject::Reject, Rejection, Reply};

#[derive(Error, Debug)]
pub enum Error {
    #[error("Authentication error: {0}")]
    Auth(String),

    #[error("Database error: {0}")]
    Database(#[from] prisma_client_rust::QueryError),

    #[error("Redis error: {0}")]
    Redis(#[from] RunError<redis::RedisError>),

    #[error("TDengine error: {0}")]
    TDengine(#[from] taos::Error),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("internal error: {0}")]
    InternalError(#[from] anyhow::Error),
}

impl Reject for Error {}

#[derive(Serialize)]
struct ErrorResponse {
    message: String,
    error_code: String,
}

pub async fn handle_rejection(err: Rejection) -> Result<impl Reply, Rejection> {
    let (code, message) = if err.is_not_found() {
        (warp::http::StatusCode::NOT_FOUND, "Not Found".to_string())
    } else if let Some(e) = err.find::<Error>() {
        match e {
            Error::Auth(err) => (warp::http::StatusCode::UNAUTHORIZED, err.to_string()),
            Error::Database(err) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                err.to_string(),
            ),
            Error::Redis(err) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                err.to_string(),
            ),
            Error::InvalidInput(err) => (warp::http::StatusCode::BAD_REQUEST, err.to_string()),
            Error::NotFound(err) => (warp::http::StatusCode::NOT_FOUND, err.to_string()),
            Error::TDengine(err) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                err.to_string(),
            ),
            Error::InternalError(err) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                err.to_string(),
            ),
        }
    } else if let Some(missing) = err.find::<warp::reject::MissingHeader>() {
        (
            warp::http::StatusCode::UNAUTHORIZED,
            format!("Missing required header: {}", missing.name()),
        )
    } else {
        (
            warp::http::StatusCode::INTERNAL_SERVER_ERROR,
            "Internal Server Error".to_string(),
        )
    };
    let json = warp::reply::json(&ErrorResponse {
        message,
        error_code: code.to_string(),
    });

    Ok(warp::reply::with_status(json, code))
}
