use std::{
    collections::{HashMap, HashSet},
    net::{Ip<PERSON>ddr, SocketAddr},
};

use crate::{
    app::WorkerInfo,
    latency_monitoring::LatencyMonitoringService,
    online_tracker::OnlineTracker,
    preludes::*,
    prisma,
    stats::{write_netcard_stats, write_system_stats, write_user_line_info},
};
use app_message::*;
use common::{
    retry_queue::{Job, RetryError},
    tokio_util::sync::CancellationToken,
};
use futures_util::future::join_all;
use lru_time_cache::LruCache;
use prisma_client_rust::operator::*;
use private_tun::snell_impl_ver::config::ClientConfig;
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tokio::{
    select,
    sync::{oneshot, Mutex},
};

pub struct WorkerSessionGuard {
    pub worker_id: i32,
    pub ctx: WrappedAppContext,
}

pub struct WorkerSession {
    pub worker_info: WorkerInfo::Data,
    pub worker_display_name: String,
    pub ctx: WrappedAppContext,
    pub config_map: Arc<Mutex<HashMap<u16, PortConfig>>>,
    pub pending_latency_test:
        Arc<Mutex<LruCache<u64, oneshot::Sender<Result<WorkerTestLatencyResponse>>>>>,
    pub pending_update_latency_monitoring_config:
        Arc<Mutex<LruCache<u64, oneshot::Sender<Result<WorkerLatencyMonitoringConfigResponse>>>>>,
    pub pending_connection_stats:
        Arc<Mutex<LruCache<u64, oneshot::Sender<Result<WorkerGetConnectionStatsResponse>>>>>,
    pub traffic_cache: Arc<Mutex<LruCache<(i32, i32), CachedTrafficInfo>>>,
}

macro_rules! get_number {
    ($conn:expr, $k:expr, $d:expr) => {
        match $conn.get($k).await {
            Ok(v) => v,
            Err(e) => match e.kind() {
                redis::ErrorKind::TypeError => $d,
                _ => return Err(e.into()),
            },
        }
    };
}

async fn get_one_socket_addr(
    worker_id: i32,
    target_addr: &str,
    port: u16,
) -> anyhow::Result<smallvec::SmallVec<[SocketAddr; 3]>> {
    // ipv6
    if target_addr.starts_with('[') && target_addr.ends_with(']') {
        let v = target_addr[1..target_addr.len() - 1].parse::<IpAddr>()?;
        return Ok(smallvec::smallvec![(v, port).into()]);
    }
    match target_addr.parse::<IpAddr>() {
        Ok(v) => Ok(smallvec::smallvec![(v, port).into()]),
        Err(_e) => {
            // try lookup dns
            let s = match tokio::net::lookup_host((target_addr, port)).await {
                Ok(v) => v,
                Err(e) => {
                    return Err(anyhow::anyhow!(
                        "{}  target_addr:{} port:{} dns lookup failed: {}",
                        worker_identifier_from_id(worker_id),
                        target_addr,
                        port,
                        e
                    ));
                }
            };
            let mut rst = s.collect::<smallvec::SmallVec<_>>();
            if rst.is_empty() {
                return Err(anyhow::anyhow!(
                    "{}  target_addr:{} port:{} dns lookup result empty",
                    worker_identifier_from_id(worker_id),
                    target_addr,
                    port
                ));
            }
            if rst.len() > 1 {
                rst.sort();
            }
            Ok(rst)
        }
    }
}

fn split_target_addr(worker_id: i32, target_addr: &str) -> anyhow::Result<(&str, u16)> {
    let (addr, _comment) = match target_addr.split_once('#') {
        Some((addr, comment)) => (addr, comment),
        None => (target_addr, ""),
    };
    let (domain, port) = addr.rsplit_once(':').ok_or(anyhow::anyhow!(
        "{}  target_addr:{} dns lookup result empty",
        worker_identifier_from_id(worker_id),
        target_addr
    ))?;
    Ok((domain, port.parse::<u16>()?))
}

async fn get_target_addr_list(
    worker_id: i32,
    p: &UpdatePortInfo::Data,
) -> anyhow::Result<Vec<SocketAddr>> {
    let socket_addrs = if p.target_addr_list.is_empty() {
        // just only one target addr
        Vec::from_iter(
            get_one_socket_addr(
                worker_id,
                p.target_address_v_4.as_str(),
                p.target_port_v_4 as u16,
            )
            .await?
            .into_iter(),
        )
    } else {
        let socket_str_list = p
            .target_addr_list
            .iter()
            .map(|x| (x, split_target_addr(worker_id, x)))
            .collect::<Vec<_>>();
        let tasks = socket_str_list
            .into_iter()
            .filter_map(|x| x.1.ok())
            .map(|(socket_str, port)| async move {
                get_one_socket_addr(worker_id, socket_str, port).await
            })
            .collect::<Vec<_>>();
        let len = tasks.len();
        join_all(tasks)
            .await
            .into_iter()
            .filter_map(|x| x.ok())
            .fold(Vec::with_capacity(len), |mut acc, x| {
                acc.extend(x);
                acc
            })
    };

    // Validate that non-admin users can only target public IPs after DNS resolution
    if let Some(subscription) = &p.subscription {
        if !subscription.is_admin {
            for socket_addr in &socket_addrs {
                if common::ip_utils::is_private_ip(&socket_addr.ip()) {
                    return Err(anyhow::anyhow!(
                        "Non-admin users cannot target private IP addresses: {} (worker: {})",
                        socket_addr.ip(),
                        worker_id
                    ));
                }
            }
        }
    }

    Ok(socket_addrs)
}

prisma::port::select!( UpdatePortInfo {
    port_v_4
    target_port_v_4
    target_address_v_4
    target_addr_list
    forward_protocol
    forward_config
    select_mode
    test_method
    is_suspended
    bandwidth
    accept_proxy_protocol
    send_proxy_protocol_version
    outbound_endpoint: select {
        allow_forward
        allow_ip_num
        allow_conn_num
    }
    subscription: select {
        id
        traffic
        valid_until
        bandwidth
        allow_ip_num
        allow_conn_num
        package_id
        is_admin
    }
});
fn two_option_get_min<'a, T>(a: &'a Option<T>, b: &'a Option<T>) -> Option<&'a T>
where
    T: Ord,
{
    match (a, b) {
        (Some(a), Some(b)) => Some(a.min(b)),
        (Some(a), None) => Some(a),
        (None, Some(b)) => Some(b),
        (None, None) => None,
    }
}

pub fn worker_identifier_from_pubkey(pubkey_str: &str) -> String {
    format!("worker:{}", pubkey_str)
}

pub fn worker_identifier_from_name(display_name: &str) -> String {
    format!("worker:{}", display_name)
}

pub fn worker_identifier_from_id(worker_id: i32) -> String {
    format!("worker:id:{}", worker_id)
}

impl WorkerSession {
    pub fn worker_identifier(&self) -> String {
        format!("worker:{}", self.worker_display_name)
    }

    pub fn create(worker_info: WorkerInfo::Data, ctx: WrappedAppContext) -> Arc<Self> {
        let worker_display_name = worker_info.display_name.clone();
        let ret = Self {
            worker_info,
            worker_display_name,
            ctx,
            config_map: Arc::new(Mutex::new(HashMap::new())),
            pending_latency_test: Arc::new(Mutex::new(LruCache::with_expiry_duration(
                Duration::from_secs(30),
            ))),
            pending_update_latency_monitoring_config: Arc::new(Mutex::new(
                LruCache::with_expiry_duration(Duration::from_secs(30)),
            )),
            pending_connection_stats: Arc::new(Mutex::new(LruCache::with_expiry_duration(
                Duration::from_secs(30),
            ))),
            traffic_cache: Arc::new(Mutex::new(LruCache::with_expiry_duration(
                Duration::from_secs(30),
            ))),
        };
        Arc::new(ret)
    }

    // filter out expired, suspended, and exceed traffic subscriptions
    pub async fn filter_subs(
        self: &Arc<Self>,
        port_list_data: &mut Vec<UpdatePortInfo::Data>,
    ) -> Result<()> {
        let redis = self.ctx.redis.clone();
        let mut conn = redis.get().await?;
        let current_time = chrono::Utc::now().fixed_offset();
        loop {
            let mut to_remove_sub = None;
            let mut to_remove_port = None;
            for p in port_list_data.iter_mut() {
                if let Some(s) = &p.subscription {
                    if current_time >= s.valid_until {
                        log::debug!(
                            "{} session_loop: find db ports success, port: {}, subscription: {} expired",
                            self.worker_identifier(),
                            p.port_v_4,
                            s.id
                        );
                        to_remove_sub = Some(s.id);
                        break;
                    }

                    // Skip suspended ports
                    if p.is_suspended {
                        log::debug!(
                            "{} session_loop: skipping suspended port: {}, subscription: {}",
                            self.worker_identifier(),
                            p.port_v_4,
                            s.id
                        );
                        to_remove_port = Some(p.port_v_4);
                        break;
                    }

                    let used: u128 = get_number!(conn, format!("sub:used:{}", s.id), 0);

                    let avail_traffics = s.traffic;
                    let avail_traffics = (avail_traffics as u128) * 1024 * 1024 * 1024;
                    if used >= avail_traffics {
                        log::debug!(
                            "{}  port: {}, subscription: {} used: {}, avail_traffics: {}",
                            self.worker_identifier(),
                            p.port_v_4,
                            s.id,
                            used,
                            avail_traffics
                        );
                        to_remove_sub = Some(s.id);
                        break;
                    }
                    // check package line traffic limit
                    let line_traffic_limit = if let Some(package_id) = s.package_id {
                        let packaget_info =
                            self.ctx.cached_package_list.get(&package_id).await.ok();
                        packaget_info
                            .and_then(|x| {
                                x.package_lines.and_then(|lines| {
                                    lines
                                        .iter()
                                        .find(|x| x.line_id == self.worker_info.id)
                                        .and_then(|x| x.line_traffic.clone())
                                })
                            })
                            .map(|x| x as i128)
                    } else {
                        None
                    };
                    if let Some(line_traffic_limit) = line_traffic_limit {
                        let sub_line_used_amount: i128 = get_number!(
                            conn,
                            format!("sub:line:used:{}:{}", s.id, self.worker_info.id),
                            0
                        );
                        if sub_line_used_amount > line_traffic_limit {
                            warn!(
                            "{} session_loop: subscription:{} line:{} used:{} limit:{} exceed",
                            self.worker_identifier(),
                            s.id,
                            self.worker_info.id,
                            sub_line_used_amount,
                            line_traffic_limit
                        );
                            to_remove_sub = Some(s.id);
                            break;
                        }
                    }
                }
            }
            if to_remove_sub.is_none() && to_remove_port.is_none() {
                break;
            }
            if let Some(sub_id) = to_remove_sub {
                port_list_data.retain(|x| {
                    x.subscription
                        .as_ref()
                        .map(|s| s.id != sub_id)
                        .unwrap_or(true)
                });
            }
            if let Some(port) = to_remove_port {
                port_list_data.retain(|x| x.port_v_4 != port);
            }
        }
        Ok(())
    }
    pub async fn update_config_map(self: &Arc<Self>) -> Result<()> {
        let redis = self.ctx.redis.clone();
        let db = self.ctx.db.clone();
        let current_time = chrono::Utc::now().fixed_offset();
        let mut port_list_data = match db
            .port()
            .find_many(vec![and(vec![
                port::outbound_endpoint_id::equals(Some(self.worker_info.id)),
                port::subscription::is(vec![and(vec![
                    subscription::activated::equals(true),
                    subscription::valid_until::gte(current_time),
                ])]),
            ])])
            .select(UpdatePortInfo::select())
            .exec()
            .await
        {
            Ok(v) => v,
            Err(e) => {
                error!(
                    "{} session_loop: find db ports error: {}",
                    self.worker_identifier(), e
                );
                return Err(e.into());
            }
        };
        log::debug!(
            "{}, ports: {:?}",
            self.worker_identifier(),
            port_list_data
                .iter()
                .map(|x| x.port_v_4)
                .collect::<Vec<_>>()
        );
        // performance optimization
        if port_list_data.is_empty() {
            *self.config_map.lock().await = HashMap::new();
            return Ok(());
        }
        // filter out expired, suspended, and exceed traffic subscriptions
        self.filter_subs(&mut port_list_data).await?;
        let mut conn = redis.get().await?;
        let mut config_map = HashMap::new();
        for p in port_list_data.iter() {
            if let Some(s) = &p.subscription {
                let port = p.port_v_4 as u16;

                let used: u128 = get_number!(conn, format!("sub:used:{}", s.id), 0);

                let avail_traffics = s.traffic;
                let avail_traffics = (avail_traffics as u128) * 1024 * 1024 * 1024;
                let avail_traffics = if used >= avail_traffics {
                    log::debug!(
                        "{}  port: {}, subscription: {} used: {}, avail_traffics: {}",
                        self.worker_identifier(),
                        p.port_v_4,
                        s.id,
                        used,
                        avail_traffics
                    );
                    continue;
                } else {
                    avail_traffics - used
                };

                let target_addr_list = match get_target_addr_list(self.worker_info.id, p).await {
                    Ok(v) => {
                        if v.is_empty() {
                            warn!(
                                "{} session_loop: find db ports success, port: {}, subscription: {} get_target_addr_list result empty",
                                self.worker_identifier(),
                                p.port_v_4,
                                s.id
                            );
                            continue;
                        }
                        v
                    }
                    Err(e) => {
                        error!(
                            "{} session_loop: find db ports success, port: {}, subscription: {} get_target_addr_list failed, error: {}",
                            self.worker_identifier(),
                            p.port_v_4,
                            s.id,
                            e
                            );
                        continue;
                    }
                };
                let protocol = if p
                    .outbound_endpoint
                    .as_ref()
                    .map(|x| x.allow_forward)
                    .flatten()
                    .unwrap_or_default()
                {
                    if let (Some(forward_protocol), Some(forward_config)) =
                        (p.forward_protocol.as_ref(), p.forward_config.as_ref())
                    {
                        // current only support hammer
                        if forward_protocol.to_lowercase() == "hammer" {
                            Some(Protocol::Hammer {
                                config: forward_config.clone(),
                            })
                        } else if forward_protocol.to_lowercase() == "tot" {
                            Some(Protocol::Tot {
                                config: forward_config.clone(),
                            })
                        } else {
                            None
                        }
                    } else {
                        None
                    }
                } else {
                    None
                };
                log::debug!(
                    "{} session_loop: update config map, port: {}",
                    self.worker_identifier(),
                    port
                );
                let mode = {
                    // 如果只有一个目标地址，则使用protocol的mode
                    let run_mode = if target_addr_list.len() == 1 {
                        // 使用protocol的mode
                        match protocol.as_ref() {
                            Some(Protocol::Hammer { config }) => {
                                if let Ok(config) = serde_json::from_str::<ClientConfig>(config) {
                                    if let Some(mode) =
                                        config.multi_server_config.as_ref().map(|x| x.mode)
                                    {
                                        mode.try_into().ok()
                                    } else {
                                        None
                                    }
                                } else {
                                    None
                                }
                            }
                            _ => None,
                        }
                    } else {
                        None
                    };
                    match run_mode {
                        Some(m) => Some(m),
                        None => match p.select_mode.as_ref().and_then(|m| {
                            TryInto::<common::app_message::Mode>::try_into(*m as u32).ok()
                        }) {
                            Some(m) => Some(m),
                            None => None,
                        },
                    }
                };
                let allow_ip_num = two_option_get_min(
                    &s.allow_ip_num,
                    &p.outbound_endpoint
                        .as_ref()
                        .map(|x| x.allow_ip_num)
                        .flatten(),
                )
                .copied();
                let allow_conn_num = two_option_get_min(
                    &s.allow_conn_num,
                    &p.outbound_endpoint
                        .as_ref()
                        .map(|x| x.allow_conn_num)
                        .flatten(),
                )
                .copied();
                // Priority: port bandwidth > subscription bandwidth
                let bandwidth = p.bandwidth
                    .map(|x| x as u16)
                    .or_else(|| s.bandwidth.map(|x| x as u16));

                let _ = config_map.insert(
                    port,
                    PortConfig {
                        port,
                        subscription_id: s.id,
                        bandwidth,
                        avail_traffics,
                        target_addr_list,
                        protocol,
                        mode,
                        latency_test_method: p
                            .test_method
                            .as_ref()
                            .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok()),
                        allow_ip_num,
                        allow_conn_num,
                        accept_proxy_protocol: Some(p.accept_proxy_protocol),
                        send_proxy_protocol_version: p.send_proxy_protocol_version,
                    },
                );
            }
        }
        *self.config_map.lock().await = config_map;
        Ok(())
    }
    pub async fn session_loop(self: Arc<Self>, cancel_token: CancellationToken) -> Result<()> {
        loop {
            if cancel_token.is_cancelled() {
                info!(
                    "{} session_loop exited by cancel token",
                    self.worker_identifier()
                );
                return Ok(());
            }
            match self.update_config_map().await {
                Ok(_) => {}
                Err(e) => {
                    error!(
                        "{} session_loop find db ports error: {}",
                        self.worker_identifier(), e
                    );
                }
            }
            select! {
                _ = tokio::time::sleep(std::time::Duration::from_secs(
                    10 + rand::random::<u64>() % 10,
                )) => {
                    continue;
                }
                _ = cancel_token.cancelled() => {
                    return Ok(());
                }
            }
        }
    }

    pub async fn handle_ws_message(
        self: Arc<Self>,
        tx: mpsc::UnboundedSender<AppWsResponse>,
        msg: AppWsRequest,
        online_tracker: Arc<OnlineTracker>,
        worker_id: usize,
    ) -> Result<()> {
        match msg {
            AppWsRequest::Encrypted(_) => {
                bail!("Unexpected encrypted message!");
            }
            AppWsRequest::Error(e) => {
                error!(
                    "Error from {}: {}",
                    self.worker_identifier(),
                    e
                );
            }
            AppWsRequest::Pong => {
                let tracker = online_tracker.clone();
                tokio::spawn(async move { tracker.tick(worker_id).await });
            }
            AppWsRequest::PortTrafficUsage(u) => {
                self.clone().handle_port_traffic_usage(u).await;
            }
            AppWsRequest::ForwardEndpointTrafficReport(report) => {
                self.clone()
                    .handle_forward_endpoint_traffic_usage(tx.clone(), report)
                    .await;
            }
            AppWsRequest::ShouldDumpPortMap => {
                log::debug!("recv ShouldDumpPortMap for worker: {}", worker_id);
                tokio::spawn(self.clone().send_port_map(tx.clone()));
            }
            AppWsRequest::StatsReport(stats_report) => {
                tokio::spawn(self.clone().handle_stats_report(stats_report));
            }
            AppWsRequest::TestLatencyResp {
                request_id,
                latencies,
            } => {
                let pending_latency_test = self.pending_latency_test.clone();
                tokio::spawn(async move {
                    let mut map = pending_latency_test.lock().await;
                    if let Some(tx) = map.remove(&request_id) {
                        log::info!(
                            "test latency response: {:?} req_id: {}",
                            latencies,
                            request_id
                        );
                        let _ = tx.send(Ok(WorkerTestLatencyResponse { latencies }));
                    }
                });
            }
            AppWsRequest::UpdateLatencyMonitoringConfigResp { request_id, result } => {
                let pending_update_latency_monitoring_config =
                    self.pending_update_latency_monitoring_config.clone();
                tokio::spawn(async move {
                    let mut map = pending_update_latency_monitoring_config.lock().await;
                    if let Some(tx) = map.remove(&request_id) {
                        let _ = tx.send(Ok(result));
                    }
                });
            }
            AppWsRequest::GetConnectionStatsResp {
                request_id,
                connections,
            } => {
                let pending_connection_stats = self.pending_connection_stats.clone();
                tokio::spawn(async move {
                    let mut map = pending_connection_stats.lock().await;
                    if let Some(tx) = map.remove(&request_id) {
                        log::info!(
                            "connection stats response: {} connections for req_id: {}",
                            connections.len(),
                            request_id
                        );
                        let _ = tx.send(Ok(WorkerGetConnectionStatsResponse { connections }));
                    }
                });
            }
            AppWsRequest::LatencyTestResult(results) => {
                let ctx_clone = self.ctx.clone();
                let batch_writer_clone = self.ctx.batch_writer.clone();
                let redis_pool_clone = self.ctx.redis.clone();
                log::debug!(
                    "recv LatencyTestResult: {:?} from worker: {}",
                    results,
                    worker_id
                );
                tokio::spawn(async move {
                    // 获取RRD客户端引用
                    let rrd_client = ctx_clone.rrd_client.as_ref()
                        .expect("RRD client should be initialized");
                    
                    if let Err(e) = LatencyMonitoringService::store_latency_results(
                        &ctx_clone,
                        &batch_writer_clone,
                        &redis_pool_clone,
                        rrd_client,
                        results.into_iter().map(LatencyTestResult::from).collect(),
                    )
                    .await
                    {
                        error!("store latency test result to tdengine failed: {}", e);
                    }
                });
            }
        }
        Ok(())
    }

    async fn handle_port_traffic_usage(self: Arc<Self>, u: Vec<PortTrafficUsage>)
    where
        Self: Send + Sync + 'static,
    {
        let factory = UpdatePortTrafficUsageJob::new(self.clone(), u);
        let _ = self.ctx.traffic_retry_queue.send(Box::new(factory)).await;
    }

    async fn handle_forward_endpoint_traffic_usage(
        self: Arc<Self>,
        _tx: mpsc::UnboundedSender<AppWsResponse>,
        u: Vec<StatData>,
    ) {
        let job = UpdateForwardEndpointTrafficUsageJob::new(self.clone(), u);
        let _ = self.ctx.traffic_retry_queue.send(Box::new(job)).await;
    }

    async fn send_port_map(
        self: Arc<Self>,
        tx: mpsc::UnboundedSender<AppWsResponse>,
    ) -> Result<()> {
        let m = self.config_map.lock().await.clone();
        let _ = tx.send(AppWsResponse::PortMapUpdate(m));
        Ok(())
    }

    async fn handle_stats_report(self: Arc<Self>, stats_report: StatsReport) -> Result<()> {
        let worker_id = self.worker_info.id;
        let batch_writer = &self.ctx.batch_writer;
        let redis_pool = &self.ctx.redis;
        if let Err(e) =
            write_netcard_stats(batch_writer, redis_pool, worker_id, &stats_report).await
        {
            error!("handle_stats_report: write_netcard_stats error: {}", e);
        }
        if let Err(e) = write_system_stats(batch_writer, redis_pool, worker_id, &stats_report).await
        {
            error!("handle_stats_report: write_system_stats error: {}", e);
        }
        trace!(
            "handle_stats_report: {}, report: {:?}",
            worker_id,
            stats_report
        );

        Ok(())
    }
}

prisma::port::select!(
    UpdateTrafficData{
        port_v_4
        subscription_id
    }
);

async fn handle_port_traffic_usage_inner(
    session: Arc<WorkerSession>,
    u: &mut Vec<PortTrafficUsage>,
    sub_acc: &mut HashMap<i32, u128>,
) -> Result<(), RetryError<anyhow::Error>> {
    let redis = session.ctx.redis.clone();
    let worker_id = session.worker_info.id;
    let port_temp_map = u.iter().fold(HashMap::new(), |mut acc, usage| {
        let port = usage.port as i32;
        let e = acc.entry(port).or_insert((0 as u128, HashSet::new()));
        e.0 += usage.used;
        if let Some(client_ip) = &usage.client_ip {
            e.1.insert(client_ip.clone());
        }
        acc
    });
    debug!("handle_port_traffic_usage: {}, usage: {:?}", worker_id, u);
    let all_ports_set: std::collections::HashSet<i32> = u.iter().map(|x| x.port as i32).collect();
    let Ok(worker_ports_data) = session.ctx.cached_worker_ports_list.get(&worker_id).await else {
        error!("handle_port_traffic_usage: find ports failed!");
        return Err(RetryError::Retry(anyhow::anyhow!(
            "worker:{} find ports failed when handle port traffic usage",
            worker_id
        )));
    };

    let need_refresh = all_ports_set.iter().any(|report_port| {
        !worker_ports_data
            .iter()
            .any(|port| port.port_v_4 == *report_port)
    });
    let worker_ports_data = if need_refresh {
        let Ok(worker_ports_data) = session
            .ctx
            .cached_worker_ports_list
            .get_force_refresh(&worker_id)
            .await
        else {
            error!("handle_port_traffic_usage: refresh ports failed!");
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} refresh ports failed when handle port traffic usage",
                worker_id
            )));
        };
        worker_ports_data
    } else {
        worker_ports_data
    };
    // Filter the cached data to only include the ports we need

    let line_lock = get_line_lock(&session.ctx.redis, &session.ctx.redis_line_lock, worker_id)
        .await
        .map_err(RetryError::Retry)?;
    let mut conn = redis.get().await.map_err(|e| {
        RetryError::Retry(anyhow::anyhow!(
            "worker:{} get redis failed when handle port traffic usage: {}",
            worker_id,
            e
        ))
    })?;
    let all_ports_data: Vec<_> = worker_ports_data
        .into_iter()
        .filter(|port| all_ports_set.contains(&port.port_v_4))
        .collect();
    for port in &all_ports_data {
        let Ok(end_point_data) = session.ctx.cached_endpoint_list.get(&worker_id).await else {
            error!(
                "worker: {} Port: {} endpoint not found!",
                worker_id, port.port_v_4
            );
            continue;
        };
        let Some(subscription_id) = port.subscription_id else {
            error!(
                "worker: {} Port: {} subscription is None!",
                worker_id, port.port_v_4
            );
            continue;
        };
        let Some(subscription_data) = session
            .ctx
            .cached_subscription_list
            .get(&subscription_id)
            .await
            .ok()
        else {
            error!(
                "worker: {} Port: {} subscription not found!",
                worker_id, port.port_v_4
            );
            continue;
        };
        let package_traffic_scale = if let Some(package_id) = subscription_data.package_id {
            let Some(package_data) = session.ctx.cached_package_list.get(&package_id).await.ok()
            else {
                error!(
                    "worker: {} Port: {} sub:{} package:{} not found!",
                    worker_id, port.port_v_4, subscription_id, package_id
                );
                continue;
            };
            let Some(package_lines) = package_data.package_lines else {
                error!(
                    "worker: {} Port: {} sub:{} package:{} package_lines not found!",
                    worker_id, port.port_v_4, subscription_id, package_id
                );
                continue;
            };
            let Some(package_line_info) = package_lines.iter().find(|x| x.line_id == worker_id)
            else {
                error!(
                    "worker: {} Port: {} sub:{} package:{} package_line_info not found!",
                    worker_id, port.port_v_4, subscription_id, package_id
                );
                continue;
            };
            package_line_info.traffic_scale
        } else {
            None
        };
        let Some((raw_used, client_ips)) = port_temp_map.get(&port.port_v_4) else {
            error!(
                "worker: {} Port: {} used is None!",
                worker_id, port.port_v_4
            );
            continue;
        };
        log::debug!(
            "sub:{} worker: {} Port: {} raw_used: {} client_ip: {:?}",
            subscription_id,
            worker_id,
            port.port_v_4,
            raw_used,
            client_ips
        );
        // 优先使用套餐中的倍率，其次使用端点的倍率，否则使用1.0
        let scale = if let Some(scale) = &package_traffic_scale {
            *scale
        } else if let Some(scale) = end_point_data.traffic_scale {
            scale
        } else {
            1.0
        };
        log::debug!(
            "sub:{} worker: {} Port: {} scale: {}",
            subscription_id,
            worker_id,
            port.port_v_4,
            scale
        );

        let used = (*raw_used as f64 * scale) as i128;
        if used == 0 {
            continue;
        }
        if used < 0 || used > *********** {
            log::error!(
                "sub:{} worker: {} Port: {} scaled_used: {} abnormal (raw_used: {}, scale: {})",
                subscription_id,
                worker_id,
                port.port_v_4,
                used,
                raw_used,
                scale
            );
            continue;
        }
        // let used_cache: i128 = get_number!(conn, format!("sub:used:{}", subscription_id), 0);
        let port_used_cache: i128 = get_number!(
            conn,
            format!(
                "sub:line:port:used:{}:{}:{}",
                subscription_id, worker_id, port.port_v_4
            ),
            0
        );

        log::debug!(
            "sub:{} worker: {} Port: {}, port_used_cache: {} used: {}",
            subscription_id,
            worker_id,
            port.port_v_4,
            port_used_cache,
            used
        );

        let port_amount = match port_used_cache.checked_add(used) {
            Some(amount) => amount,
            None => {
                log::error!(
                    "sub:{} worker: {} Port: {} port_amount overflow: {} + {}",
                    subscription_id,
                    worker_id,
                    port.port_v_4,
                    port_used_cache,
                    used
                );
                continue;
            }
        };

        match conn
            .set::<_, _, ()>(
                format!(
                    "sub:line:port:used:{}:{}:{}",
                    subscription_id, worker_id, port.port_v_4
                ),
                port_amount.to_string(),
            )
            .await
        {
            Ok(_) => {
                u.retain(|x| x.port as i32 != port.port_v_4);
                *sub_acc.entry(subscription_id).or_insert(0) += used as u128;
            }
            Err(e) => {
                error!(
                    "worker:{} set sub:used:{} failed: {}",
                    worker_id, subscription_id, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set sub:used:{} failed: {}",
                    worker_id,
                    subscription_id,
                    e
                )));
            }
        }
    }
    drop(line_lock);
    // proc sub_acc
    let _lock = match session.ctx.redis_lock.clone().acquire().await {
        Ok(g) => g,
        Err(_) => {
            error!("worker:{} get global traffic_usage_lock failed", worker_id);
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} get global traffic_usage_lock failed",
                worker_id
            )));
        }
    };
    for (sub_id, inc) in sub_acc.iter_mut() {
        if *inc == 0 {
            continue;
        }
        // update sub line used traffic
        let sub_line_used_cache: i128 =
            get_number!(conn, format!("sub:line:used:{}:{}", sub_id, worker_id), 0);

        let sub_line_used_cache = sub_line_used_cache + *inc as i128;
        match conn
            .set::<_, _, ()>(
                format!("sub:line:used:{}:{}", sub_id, worker_id),
                sub_line_used_cache.to_string(),
            )
            .await
        {
            Ok(_) => {
                log::debug!(
                    "worker:{} set sub:line:used:{}:{} amount:{} success",
                    worker_id,
                    sub_id,
                    worker_id,
                    sub_line_used_cache
                );
            }
            Err(e) => {
                error!(
                    "worker:{} set sub:line:used:{}:{} amount:{} failed: {}",
                    worker_id, sub_id, worker_id, sub_line_used_cache, e
                );
            }
        }

        let used_cache: i128 = get_number!(conn, format!("sub:used:{}", sub_id), 0);
        let added_amount = used_cache + *inc as i128;
        match conn
            .set::<_, _, ()>(format!("sub:used:{}", sub_id), added_amount.to_string())
            .await
        {
            Ok(_) => {
                *inc = 0;
                log::debug!(
                    "worker:{} set sub:{} amount:{}, inc:{} success",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc
                );
            }
            Err(e) => {
                error!(
                    "worker:{} set sub:{} amount:{}, inc:{} failed: {}",
                    worker_id, sub_id, added_amount, inc, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set sub:{} amount:{}, inc:{} failed: {}",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc,
                    e
                )));
            }
        }
    }

    // Calculate and store user-line level speeds
    let current_time = chrono::Utc::now().timestamp();
    tokio::spawn(async move {
        if let Err(e) = calculate_and_store_user_line_info(
            session.clone(),
            &all_ports_data,
            &port_temp_map,
            current_time,
        )
        .await
        {
            error!(
                "worker:{} calculate and store user-line level speeds failed: {:?}",
                worker_id, e
            );
        }
    });

    Ok(())
}

prisma::port::select!(
    UpdateForwardEndpointTrafficData{
        port_v_4
        subscription_id
        related_forward_endpoint_ids
        related_tot_server_ids
    }
);

async fn handle_forward_endpoint_traffic_usage_inner(
    session: Arc<WorkerSession>,
    u: &mut Vec<StatData>,
    sub_acc: &mut HashMap<i32, u128>,
) -> Result<(), RetryError<anyhow::Error>> {
    let redis = session.ctx.redis.clone();
    let worker_id = session.worker_info.id;
    debug!(
        "handle_forward_endpoint_traffic_usage: {}, usage: {:?}",
        worker_id, u
    );
    let all_ports_set: std::collections::HashSet<i32> = u.iter().map(|x| x.port as i32).collect();

    let Ok(worker_forward_ports_data) = session
        .ctx
        .cached_worker_forward_ports_list
        .get(&worker_id)
        .await
    else {
        error!("handle_forward_endpoint_traffic_usage: find ports failed!");
        return Err(RetryError::Retry(anyhow::anyhow!(
            "worker:{} find ports failed when handle forward endpoint traffic usage",
            worker_id
        )));
    };

    let need_refresh = all_ports_set.iter().any(|report_port| {
        !worker_forward_ports_data
            .iter()
            .any(|port| port.port_v_4 == *report_port)
    });
    let worker_forward_ports_data = if need_refresh {
        let Ok(worker_forward_ports_data) = session
            .ctx
            .cached_worker_forward_ports_list
            .get_force_refresh(&worker_id)
            .await
        else {
            error!("handle_forward_endpoint_traffic_usage: refresh ports failed!");
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} refresh ports failed when handle forward endpoint traffic usage",
                worker_id
            )));
        };
        worker_forward_ports_data
    } else {
        worker_forward_ports_data
    };

    // Filter the cached data to only include the ports we need
    let all_ports_data: Vec<_> = worker_forward_ports_data
        .into_iter()
        .filter(|port| all_ports_set.contains(&port.port_v_4))
        .collect();
    let mut conn = redis.get().await.map_err(|e| {
        RetryError::Retry(anyhow::anyhow!(
            "worker:{} get redis failed when handle forward endpoint traffic usage: {}",
            worker_id,
            e
        ))
    })?;
    // todo support client_ips
    let port_temp_map = u.iter().fold(HashMap::new(), |mut acc, usage| {
        let port = usage.port;
        let forward_id = usage.forward_endpoint.clone();
        let e = acc.entry((port, forward_id)).or_insert(0);
        *e += usage.traffic;
        acc
    });
    let line_lock = get_line_lock(&session.ctx.redis, &session.ctx.redis_line_lock, worker_id)
        .await
        .map_err(RetryError::Retry)?;
    for port in all_ports_data {
        let related_ids = port
            .related_forward_endpoint_ids
            .iter()
            .chain(port.related_tot_server_ids.iter());
        for forward_id in related_ids {
            let Some(used) = port_temp_map.get(&(port.port_v_4 as u16, forward_id.to_string()))
            else {
                continue;
            };

            let Some(subscription_id) = port.subscription_id else {
                error!(
                    "worker: {} Port: {} subscription is None!",
                    worker_id, port.port_v_4
                );
                continue;
            };

            let Ok(forward_endpoint_data) = session
                .ctx
                .cached_forward_endpoint_list
                .get(&forward_id)
                .await
            else {
                error!(
                    "worker: {} Port: {} forward endpoint {} not found!",
                    worker_id, port.port_v_4, forward_id
                );
                continue;
            };

            let scale = forward_endpoint_data.traffic_scale.unwrap_or(1.0);
            let used = (*used as f64 * scale) as i128;
            if used > *********** {
                log::error!(
                    "worker: {} Port: {} forward endpoint {} used: {} abnormal",
                    worker_id,
                    port.port_v_4,
                    forward_id,
                    used
                );
                continue;
            }
            log::debug!(
                "worker: {} Port: {} forward endpoint {} used: {} fwd_scale: {}",
                worker_id,
                port.port_v_4,
                forward_id,
                used,
                scale
            );

            // let used_cache: i128 = get_number!(conn, format!("sub:used:{}", subscription_id), 0);
            let port_used_cache: i128 = get_number!(
                conn,
                format!(
                    "sub:line:port:used:{}:{}:{}",
                    subscription_id, worker_id, port.port_v_4
                ),
                0
            );

            let port_amount = port_used_cache + used;
            match conn
                .set::<_, _, ()>(
                    format!(
                        "sub:line:port:used:{}:{}:{}",
                        subscription_id, worker_id, port.port_v_4
                    ),
                    port_amount.to_string(),
                )
                .await
            {
                Ok(_) => {}
                Err(e) => {
                    error!(
                        "worker:{} set sub:line:port:used:{}:{}:{} failed: {}",
                        worker_id, subscription_id, worker_id, port.port_v_4, e
                    );
                    return Err(RetryError::Retry(anyhow::anyhow!(
                        "worker:{} set sub:line:port:used:{}:{}:{} failed: {}",
                        worker_id,
                        subscription_id,
                        worker_id,
                        port.port_v_4,
                        e
                    )));
                }
            }
            u.retain(|x| x.port as i32 != port.port_v_4);
            *sub_acc.entry(subscription_id).or_insert(0) += used as u128;
        }
    }

    drop(line_lock);
    // proc sub_acc
    let _lock = match session.ctx.redis_lock.clone().acquire().await {
        Ok(g) => g,
        Err(_) => {
            error!("worker:{} get global traffic_usage_lock failed", worker_id);
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} get global traffic_usage_lock failed",
                worker_id
            )));
        }
    };
    for (sub_id, inc) in sub_acc.iter_mut() {
        if *inc == 0 {
            continue;
        }
        let used_cache: i128 = get_number!(conn, format!("sub:used:{}", sub_id), 0);
        let added_amount = used_cache + *inc as i128;
        match conn
            .set::<_, _, ()>(format!("sub:used:{}", sub_id), added_amount.to_string())
            .await
        {
            Ok(_) => {
                log::debug!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} success",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc
                );
            }
            Err(e) => {
                error!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} failed: {}",
                    worker_id, sub_id, added_amount, inc, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} failed: {}",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc,
                    e
                )));
            }
        }
        *inc = 0;
    }
    Ok(())
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CachedTrafficInfo {
    last_timestamp: i64,
}

async fn calculate_and_store_user_line_info(
    session: Arc<WorkerSession>,
    all_ports_data: &[UpdateTrafficData::Data],
    port_temp_map: &HashMap<i32, (u128, HashSet<SocketAddr>)>,
    current_time: i64,
) -> Result<(), anyhow::Error> {
    // (sub_id, line_id) -> (used, client_ips)
    let line_id = session.worker_info.id; // line_id is the worker_id
    for port in all_ports_data {
        let Some(subscription_id) = port.subscription_id else {
            continue;
        };
        let Some((incremental_traffic, client_ips)) = port_temp_map.get(&port.port_v_4) else {
            continue;
        };
        let incremental_traffic = *incremental_traffic;
        // Get previous cached info from LruCache
        let cached_traffic_info: Option<CachedTrafficInfo> = {
            let mut cache = session.traffic_cache.lock().await;
            cache.get(&(subscription_id, line_id)).cloned()
        };

        let total_speed = if let Some(cached_traffic_info) = &cached_traffic_info {
            // Parse previous timestamp
            let time_diff = current_time - cached_traffic_info.last_timestamp;
            log::debug!(
                    "worker:{} calculate and store user-line level speeds: subscription_id: {}, line_id: {}, time_diff: {}s, incremental_traffic: {}",
                    session.worker_info.id,
                    subscription_id,
                    line_id,
                    time_diff,
                    incremental_traffic
                );

            // Check if the previous timestamp is too old (more than 10 minutes)
            // This prevents incorrect speed calculations when worker has been offline
            if time_diff > 20 {
                // 600 seconds = 10 minutes
                // Too old, treat as first time
                0.0
            } else if time_diff > 0 {
                // Use incremental traffic directly from zf-worker
                // For simplicity, we'll treat all traffic as download (rx)
                // In a real implementation, you might want to separate upload/download
                let speed = incremental_traffic as f64 / time_diff as f64; // bytes per second
                speed
            } else {
                // Invalid time_diff, treat as no speed
                0.0
            }
        } else {
            // First time, no speed can be calculated
            0.0
        };

        let new_cached_traffic_info = CachedTrafficInfo {
            last_timestamp: current_time,
        };
        // Update LruCache
        {
            let mut cache = session.traffic_cache.lock().await;
            cache.insert((subscription_id, line_id), new_cached_traffic_info.clone());
        }
        let batch_writer = &session.ctx.batch_writer;
        let redis_pool = &session.ctx.redis;
        if let Err(e) = write_user_line_info(
            batch_writer,
            redis_pool,
            subscription_id,
            line_id,
            incremental_traffic as u64,
            total_speed,
            current_time,
            &client_ips,
        )
        .await
        {
            log::error!(
                "Failed to write user line speed: subscription_id={}, line_id={}, error={}",
                subscription_id,
                line_id,
                e
            );
        }
    }

    Ok(())
}

pub struct UpdatePortTrafficUsageJob {
    session: Arc<WorkerSession>,
    u: Vec<PortTrafficUsage>,
    sub_acc: HashMap<i32, u128>,
}

impl UpdatePortTrafficUsageJob {
    pub fn new(session: Arc<WorkerSession>, u: Vec<PortTrafficUsage>) -> Self {
        Self {
            session,
            u,
            sub_acc: HashMap::new(),
        }
    }
}

#[async_trait::async_trait]
impl Job for UpdatePortTrafficUsageJob {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        handle_port_traffic_usage_inner(self.session.clone(), &mut self.u, &mut self.sub_acc).await
    }
}

pub struct UpdateForwardEndpointTrafficUsageJob {
    session: Arc<WorkerSession>,
    u: Vec<StatData>,
    sub_acc: HashMap<i32, u128>,
}

impl UpdateForwardEndpointTrafficUsageJob {
    pub fn new(session: Arc<WorkerSession>, u: Vec<StatData>) -> Self {
        Self {
            session,
            u,
            sub_acc: HashMap::new(),
        }
    }
}

#[async_trait::async_trait]
impl Job for UpdateForwardEndpointTrafficUsageJob {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        handle_forward_endpoint_traffic_usage_inner(
            self.session.clone(),
            &mut self.u,
            &mut self.sub_acc,
        )
        .await
    }
}
