use crate::connection_stats::get_connection_stats;
use crate::error::Error;
use crate::latency::{check_mgmt_pubkey, test_latency};
use crate::latency_monitoring::LatencyMonitoringService;
use crate::license::{
    handle_apply_renewal, handle_calculate_renewal_price, handle_cancel_renewal_request,
    handle_create_renewal_request, handle_get_entitlements, handle_get_license_info,
    handle_get_renewal_history, handle_get_renewal_request_messages, handle_get_renewal_requests,
    handle_send_renewal_request_message,
};
use crate::online_tracker::{Event, OnlineTracker};
use crate::worker::{worker_identifier_from_pubkey, WorkerSession};
use crate::{preludes::*, prisma};
use anyhow::{bail, Context};
use app_message::*;
use base64::Engine;
use common::license_message::CreateRenewalRequestRequest;
use common::reset_traffic_task::{ArcResetTrafficContext, Update2DBTask};
use common::{
    chrono::{DateTime, Utc},
    tokio_util::sync::CancellationToken,
};
use futures_util::future::BoxFuture;
use futures_util::stream::SplitStream;
use futures_util::{SinkExt, StreamExt};
use k256::ecdh::SharedSecret;
use std::convert::Infallible;
use tokio::sync::mpsc;
use warp::ws::{Message, WebSocket};
use warp::{hyper::StatusCode, *};

async fn handle_update_latency_monitoring_config(
    ctx: WrappedAppContext,
    pubkey_str: String,
    request: UpdateLatencyMonitoringConfigRequest,
) -> std::result::Result<impl Reply, warp::Rejection> {
    use crate::latency_monitoring::LatencyMonitoringService;
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    let result = match request.server_id {
        Some(server_id) => {
            // 更新特定server的配置
            match LatencyMonitoringService::refresh_configs_to_worker(ctx.clone(), server_id).await
            {
                Ok(_) => UpdateLatencyMonitoringConfigResponse {
                    success: true,
                    message: format!(
                        "Successfully updated latency configs for server {}",
                        server_id
                    ),
                },
                Err(e) => {
                    error!(
                        "Failed to update latency configs for server {}: {}",
                        server_id, e
                    );
                    UpdateLatencyMonitoringConfigResponse {
                        success: false,
                        message: format!(
                            "Failed to update latency configs for server {}: {}",
                            server_id, e
                        ),
                    }
                }
            }
        }
        None => {
            // 更新所有server的配置
            match LatencyMonitoringService::check_and_distribute_configs(ctx.clone()).await {
                Ok(_) => UpdateLatencyMonitoringConfigResponse {
                    success: true,
                    message: "Successfully updated latency configs for all servers".to_string(),
                },
                Err(e) => {
                    error!("Failed to update latency configs for all servers: {}", e);
                    UpdateLatencyMonitoringConfigResponse {
                        success: false,
                        message: format!("Failed to update latency configs for all servers: {}", e),
                    }
                }
            }
        }
    };

    Ok(reply::json(&result))
}

/// 处理图表生成请求
async fn handle_generate_chart(
    ctx: WrappedAppContext,
    pubkey_str: String,
    server_id: i32,
    config_id: i32,
    time_range: String,
) -> std::result::Result<impl Reply, warp::Rejection> {
    // 验证pubkey
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;

    // 获取RRD客户端
    let rrd_client = ctx.rrd_client.as_ref().ok_or_else(|| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "RRD client not available"
        )))
    })?;

    // 验证时间范围
    let valid_ranges = ["1h", "3h", "6h", "12h", "24h", "7d"];
    if !valid_ranges.contains(&time_range.as_str()) {
        return Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "Invalid time range: {}. Supported: {}",
            time_range,
            valid_ranges.join(", ")
        ))));
    }

    // 获取配置信息以获取target_address
    let config = ctx
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .exec()
        .await
        .map_err(|e| {
            warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                "Failed to fetch config: {}",
                e
            )))
        })?
        .ok_or_else(|| {
            warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                "Config {} not found",
                config_id
            )))
        })?;

    // 生成图表
    match rrd_client
        .generate_chart(
            server_id,
            config_id,
            &time_range,
            &config.target_address,
            Some(Utc::now()),
        )
        .await
    {
        Ok(image_data) => {
            // 返回Base64编码的图片数据
            let encoded_data = base64::engine::general_purpose::STANDARD.encode(&image_data);
            Ok(reply::json(&serde_json::json!({
                "success": true,
                "server_id": server_id,
                "config_id": config_id,
                "time_range": time_range,
                "target_address": config.target_address,
                "display_name": config.display_name,
                "image_data": encoded_data,
                "generated_at": chrono::Utc::now(),
            })))
        }
        Err(e) => {
            error!(
                "Failed to generate chart for server {} config {} range {}: {}",
                server_id, config_id, time_range, e
            );
            Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                "Failed to generate chart: {}",
                e
            ))))
        }
    }
}

pub async fn start_app(
    ctx: WrappedAppContext,
    reset_traffic_ctx: ArcResetTrafficContext,
) -> Result<()> {
    let ctx_move = ctx.clone();
    let traffic_retry_queue = reset_traffic_ctx.traffic_retry_queue.clone();
    let _handler_update = tokio::spawn(async move {
        loop {
            let _ = traffic_retry_queue
                .send(Box::new(Update2DBTask::new(reset_traffic_ctx.clone())))
                .await;
            tokio::time::sleep(std::time::Duration::from_secs(60)).await;
        }
    });
    let redis = ctx_move.redis.clone();
    let (online_evt_tx, mut online_evt_rx) = tokio::sync::mpsc::channel(10);
    let _h = tokio::spawn(async move {
        while let Some(evt) = online_evt_rx.recv().await {
            let (id, st) = match evt {
                Event::Offline(id) => (id, 0),
                Event::Online(id) => (id, 1),
            };
            let _ = async {
                redis
                    .get()
                    .await?
                    .set::<_, _, ()>(format!("server:status:{}", id), st.to_string())
                    .await?;
                Ok::<_, anyhow::Error>(())
            }
            .await;
        }
        Ok::<_, anyhow::Error>(())
    });
    let online_tracker = Arc::new(OnlineTracker::new(online_evt_tx));

    let ctx_move = ctx.clone();

    let route_ws = path("w")
        .and(header("zfc-pubkey"))
        .and(header("zfc-ts"))
        .and(header("zfc-sign"))
        .and(ws())
        .map(move |pubkey_str, ts, sign_buf_str, ws: ws::Ws| {
            let ctx_move = ctx_move.clone();
            let online_tracker = online_tracker.clone();
            ws.on_upgrade(move |socket| {
                handle_websocket(
                    ctx_move,
                    socket,
                    pubkey_str,
                    ts,
                    sign_buf_str,
                    online_tracker,
                )
            })
        });

    let route_root = path::end().map(|| "🌚".to_string());
    let ctx_move = ctx.clone();
    let query_latency = path("test_latency")
        .and(header("zfc-pubkey"))
        .and(post())
        .and(body::json())
        .and_then(move |pubkey_str, body: WorkerTestLatencyRequest| {
            let cloned = ctx_move.clone();
            async move { test_latency(cloned, pubkey_str, body).await }
        });
    let ctx_move = ctx.clone();
    let update_latency_monitoring_config =
        path("update_latency_monitoring_config")
            .and(header("zfc-pubkey"))
            .and(post())
            .and(body::json())
            .and_then(
                move |pubkey_str, body: UpdateLatencyMonitoringConfigRequest| {
                    let cloned = ctx_move.clone();
                    async move {
                        handle_update_latency_monitoring_config(cloned, pubkey_str, body).await
                    }
                },
            );
    let ctx_move = ctx.clone();
    let get_connection_stats_route = path("get_connection_stats")
        .and(header("zfc-pubkey"))
        .and(post())
        .and(body::json())
        .and_then(move |pubkey_str, body: WorkerGetConnectionStatsRequest| {
            let cloned = ctx_move.clone();
            async move { get_connection_stats(cloned, pubkey_str, body).await }
        });
    let ctx_move = ctx.clone();
    let check_user_filter =
        warp::header::<String>("worker-pubkey").and_then(move |pub_key: String| {
            let ctx_move = ctx_move.clone();
            async move {
                if ctx_move.black_worker_list.lock().await.contains(&pub_key) {
                    return Err(warp::reject::not_found());
                }
                let worker_info = ctx_move
                    .db
                    .outbound_endpoint()
                    .find_unique(outbound_endpoint::pubkey::equals(pub_key.clone()))
                    .select(prisma::outbound_endpoint::select!({ id }))
                    .exec()
                    .await
                    .map_err(|_| warp::reject::not_found())?;
                if worker_info.is_some() {
                    // 校验通过，进入下一个阶段
                    Ok(pub_key)
                } else {
                    // 校验失败，抛出自定义拒绝
                    error!("{} not found", worker_identifier_from_pubkey(&pub_key));
                    Err(warp::reject::not_found())
                }
            }
        });

    let hash_val = calculate_hash(
        ctx.worker_version.clone(),
        tokio::fs::read(ctx.opt.worker_binary_path.clone())
            .await?
            .as_ref(),
    )
    .await?;
    let ctx_move = ctx.clone();
    let version_route = warp::path!("api" / "worker" / "version")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and_then(move |_worker_pubkey: String| {
            let ctx_move = ctx_move.clone();
            let hash_val = hash_val.clone();
            async move { handle_worker_version(ctx_move, hash_val).await }
        });

    let download_route = warp::path!("api" / "worker" / "download")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and(warp::fs::file(ctx.opt.worker_binary_path.clone()))
        .map(|_, f| f);

    let ctx_forwarder_download = ctx.clone();
    let forwarder_download_route = warp::path!("api" / "forwarder" / "download")
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and(warp::fs::file(ctx.opt.forwarder_binary_path.clone()))
        .and_then(move |pubkey_str: String, file_reply: warp::fs::File| {
            let ctx_move = ctx_forwarder_download.clone();
            async move {
                // Verify mgmt pubkey
                check_mgmt_pubkey(&ctx_move, &pubkey_str).map_err(|e| {
                    warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                        "pubkey:{} not match: {}",
                        pubkey_str,
                        e
                    )))
                })?;

                Ok::<warp::fs::File, warp::Rejection>(file_reply)
            }
        });

    let ctx_forwarder_version = ctx.clone();
    let forwarder_version_route = warp::path!("api" / "forwarder" / "version")
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |pubkey_str: String| {
            let ctx_move = ctx_forwarder_version.clone();
            async move {
                // Verify mgmt pubkey
                check_mgmt_pubkey(&ctx_move, &pubkey_str).map_err(|e| {
                    warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                        "pubkey:{} not match: {}",
                        pubkey_str,
                        e
                    )))
                })?;

                match ctx_move.opt.get_forwarder_version().await {
                    Ok(version) => {
                        let response = serde_json::json!({
                            "version": version
                        });
                        Ok(warp::reply::json(&response))
                    }
                    Err(e) => {
                        error!("Failed to get forwarder version: {}", e);
                        Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                            "Failed to get forwarder version: {}",
                            e
                        ))))
                    }
                }
            }
        });

    let ctx_move = ctx.clone();
    let version_report = path!("api" / "worker" / "version_report")
        .and(warp::post())
        .and(check_user_filter.clone())
        .and(body::json())
        .and_then(move |pubkey_str, body: WorkerVersionReportRequest| {
            let ctx_move = ctx_move.clone();
            async move { handle_version_report(ctx_move, pubkey_str, body).await }
        });

    let ctx_move = ctx.clone();
    let fetch_global_latest_config = path!("api" / "worker" / "fetch_global_latest_config")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and_then(move |pubkey_str| {
            let ctx_move = ctx_move.clone();
            async move { handle_worker_fetch_global_latest_config(ctx_move, pubkey_str).await }
        });

    // License management routes (admin only)
    let ctx_move = ctx.clone();
    let apply_renewal_route = path!("api" / "license" / "apply-renewal")
        .and(warp::post())
        .and(body::json())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |body: ApplyRenewalRequest, pubkey_str: String| {
            let ctx_move = ctx_move.clone();
            async move { handle_apply_renewal(ctx_move, body, pubkey_str).await }
        });

    let ctx_move = ctx.clone();
    let license_info_route = path!("api" / "license" / "info")
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |pubkey_str: String| {
            let ctx_move = ctx_move.clone();
            async move { handle_get_license_info(ctx_move, pubkey_str).await }
        });

    let ctx_move = ctx.clone();
    let generate_chart_route = path!("api" / "chart" / "generate" / i32 / i32 / String)
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(
            move |server_id: i32, config_id: i32, time_range: String, pubkey_str: String| {
                let ctx_move = ctx_move.clone();
                async move {
                    handle_generate_chart(ctx_move, pubkey_str, server_id, config_id, time_range)
                        .await
                }
            },
        );

    let ctx_move = ctx.clone();
    let renewal_history_route = path!("api" / "license" / "renewal-history")
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |pubkey_str: String| {
            let ctx_move = ctx_move.clone();
            async move { handle_get_renewal_history(ctx_move, pubkey_str).await }
        });

    let ctx_move = ctx.clone();
    let calculate_renewal_price_route = path!("api" / "license" / "calculate-renewal-price")
        .and(warp::get())
        .and(warp::query::<std::collections::HashMap<String, String>>())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(
            move |params: std::collections::HashMap<String, String>, pubkey_str: String| {
                let ctx_move = ctx_move.clone();
                async move {
                    let requested_duration = params
                        .get("requested_duration")
                        .and_then(|d| d.parse::<i32>().ok())
                        .unwrap_or(1);
                    handle_calculate_renewal_price(ctx_move, requested_duration, pubkey_str).await
                }
            },
        );

    // 续费请求相关路由
    let ctx_move = ctx.clone();
    let create_renewal_request_route = path!("api" / "license" / "renewal-requests")
        .and(warp::post())
        .and(body::json())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(
            move |body: CreateRenewalRequestRequest, pubkey_str: String| {
                let ctx_move = ctx_move.clone();
                async move { handle_create_renewal_request(ctx_move, body, pubkey_str).await }
            },
        );

    let ctx_move = ctx.clone();
    let get_renewal_requests_route = path!("api" / "license" / "renewal-requests")
        .and(warp::get())
        .and(query::query::<std::collections::HashMap<String, String>>())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(
            move |params: std::collections::HashMap<String, String>, pubkey_str: String| {
                let ctx_move = ctx_move.clone();
                async move {
                    let page = params.get("page").and_then(|p| p.parse::<u32>().ok());
                    let page_size = params.get("page_size").and_then(|p| p.parse::<u32>().ok());
                    handle_get_renewal_requests(ctx_move, page, page_size, pubkey_str).await
                }
            },
        );

    let ctx_move = ctx.clone();
    let cancel_renewal_request_route = path!("api" / "license" / "renewal-requests" / String)
        .and(warp::delete())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |request_id: String, pubkey_str: String| {
            let ctx_move = ctx_move.clone();
            async move { handle_cancel_renewal_request(ctx_move, request_id, pubkey_str).await }
        });

    // 消息相关路由
    let ctx_move = ctx.clone();
    let send_renewal_request_message_route =
        path!("api" / "license" / "renewal-requests" / String / "messages")
            .and(warp::post())
            .and(body::json())
            .and(warp::header::<String>("zfc-pubkey"))
            .and_then(
                move |request_id: String, body: serde_json::Value, pubkey_str: String| {
                    let ctx_move = ctx_move.clone();
                    async move {
                        let content = body
                            .get("content")
                            .and_then(|v| v.as_str())
                            .unwrap_or("")
                            .to_string();
                        let fixed = common::SendRenewalRequestMessageRequest {
                            content,
                            message_type: "customer".into(),
                        };
                        handle_send_renewal_request_message(ctx_move, request_id, fixed, pubkey_str)
                            .await
                    }
                },
            );

    let ctx_move = ctx.clone();
    let get_renewal_request_messages_route =
        path!("api" / "license" / "renewal-requests" / String / "messages")
            .and(warp::get())
            .and(warp::header::<String>("zfc-pubkey"))
            .and_then(move |request_id: String, pubkey_str: String| {
                let ctx_move = ctx_move.clone();
                async move {
                    handle_get_renewal_request_messages(ctx_move, request_id, pubkey_str).await
                }
            });

    let ctx_move = ctx.clone();
    let config_updated_route = path!("api" / "config" / "updated")
        .and(warp::post())
        .and(warp::header::<String>("zfc-pubkey"))
        .and(body::json())
        .and_then(move |pubkey_str: String, body: ConfigUpdatedRequest| {
            let ctx_move = ctx_move.clone();
            async move { handle_config_updated(ctx_move, pubkey_str, body).await }
        });

    let ctx_move = ctx.clone();
    let entitlements_route = path!("api" / "license" / "entitlements")
        .and(warp::get())
        .and(warp::header::<String>("zfc-pubkey"))
        .and_then(move |pubkey_str: String| {
            let ctx_move = ctx_move.clone();
            async move { handle_get_entitlements(ctx_move, pubkey_str).await }
        });
    let routes = any()
        .and(
            route_root
                .or(route_ws)
                .or(query_latency)
                .or(get_connection_stats_route)
                .or(update_latency_monitoring_config)
                .or(version_route)
                .or(download_route)
                .or(forwarder_download_route)
                .or(forwarder_version_route)
                .or(version_report)
                .or(fetch_global_latest_config)
                .or(apply_renewal_route)
                .or(license_info_route)
                .or(generate_chart_route)
                .or(renewal_history_route)
                .or(calculate_renewal_price_route)
                // 续费请求路由按具体程度排序，避免冲突
                // 最具体的路由放在前面
                .or(send_renewal_request_message_route) // api/license/renewal-requests/{id}/messages POST
                .or(get_renewal_request_messages_route) // api/license/renewal-requests/{id}/messages GET
                .or(cancel_renewal_request_route) // api/license/renewal-requests/{id} DELETE
                .or(create_renewal_request_route) // api/license/renewal-requests POST
                .or(get_renewal_requests_route) // api/license/renewal-requests GET
                .or(config_updated_route)
                .or(entitlements_route),
        )
        .recover(handle_rejection)
        .with(cors().allow_any_origin())
        .with(warp::log("zfc-arranger"));

    let port = ctx.opt.mgmt_port;
    info!("Listening on 0.0.0.0:{}", port);
    serve(routes).run(([0, 0, 0, 0], port)).await;

    Ok(())
}
prisma::outbound_endpoint::select!(
    WorkerInfo {
        id
        pubkey
        display_name
    }
);
async fn create_worker_session(
    ctx: WrappedAppContext,
    ws_tx: mpsc::UnboundedSender<AppWsResponse>,
    pubkey_str: &String,
    time: DateTime<Utc>,
    sign_buf_str: String,
) -> Result<Arc<WorkerSession>> {
    let remote_pubkey = parse_public_key(pubkey_str).context(format!(
        "{} Invalid pubkey",
        worker_identifier_from_pubkey(pubkey_str)
    ))?;
    let message = "WorkerStartSession".as_bytes().into();
    let singature = hex::decode(sign_buf_str).context(format!(
        "{} Invalid sign",
        worker_identifier_from_pubkey(pubkey_str)
    ))?;
    if !check_signature(remote_pubkey, message, time, singature, false)? {
        bail!(
            "{} Invalid signature!",
            worker_identifier_from_pubkey(pubkey_str)
        );
    }

    // Check max_workers limit if entitlements are available
    {
        let entitlements_guard = ctx.entitlements.read().await.clone();
        if let Some(ref entitlements) = entitlements_guard {
            let current_connected_workers = ctx.worker_command_tx.lock().await.len() as u32;
            if current_connected_workers >= entitlements.max_workers {
                error!(
                    "{} connection rejected: max_workers limit exceeded ({} >= {})",
                    worker_identifier_from_pubkey(&pubkey_str),
                    current_connected_workers,
                    entitlements.max_workers
                );
                ws_tx.send(AppWsResponse::Error(format!(
                    "{} connection rejected: max_workers limit exceeded ({} >= {})",
                    worker_identifier_from_pubkey(pubkey_str),
                    current_connected_workers,
                    entitlements.max_workers
                )))?;
                tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                bail!(
                    "{} connection rejected: max_workers limit exceeded ({} >= {})",
                    worker_identifier_from_pubkey(pubkey_str),
                    current_connected_workers,
                    entitlements.max_workers
                );
            }
            info!(
                "{} worker connection accepted ({}/{} workers)",
                worker_identifier_from_pubkey(&pubkey_str),
                current_connected_workers + 1,
                entitlements.max_workers
            );
        }
    }

    let worker_info = if let Some(worker_info) = ctx
        .db
        .outbound_endpoint()
        .find_unique(outbound_endpoint::pubkey::equals(pubkey_str.clone()))
        .select(WorkerInfo::select())
        .exec()
        .await?
    {
        worker_info
    } else {
        ctx.black_worker_list
            .lock()
            .await
            .insert(pubkey_str.clone());
        ws_tx.send(AppWsResponse::Error(format!(
            "{} Unknown worker, rejecting!",
            worker_identifier_from_pubkey(pubkey_str)
        )))?;
        tokio::time::sleep(std::time::Duration::from_secs(2)).await;
        bail!(
            "{} Unknown worker, rejecting!",
            worker_identifier_from_pubkey(pubkey_str)
        );
    };
    let session = WorkerSession::create(worker_info.clone(), ctx.clone());
    Ok(session)
}

async fn do_handle_websocket(
    tx: mpsc::UnboundedSender<AppWsResponse>,
    mut rx: SplitStream<WebSocket>,
    online_tracker: Arc<OnlineTracker>,
    session: Arc<WorkerSession>,
    secret: SharedSecret,
    cancel_token: CancellationToken,
) -> Result<()> {
    let worker_id = session.worker_info.id;

    loop {
        let msg = tokio::select! {
            msg = rx.next() => {
                msg
            }
            _ = cancel_token.cancelled() => {
                break;
            }
        };
        let Some(msg) = msg else {
            break;
        };

        let msg = msg?;
        let msg = msg.as_bytes();
        let msg: AppWsRequest = rmp_serde::from_slice(msg).context(format!(
            "{} do_handle_websocket: rmp_serde error",
            session.worker_identifier()
        ))?;

        let mut outer_msg = Some(msg);
        while let Some(msg) = outer_msg {
            outer_msg = None;
            match msg {
                AppWsRequest::Encrypted(enc_msg) => {
                    let msg = enc_msg.decrypt(&secret)?;
                    let msg = rmp_serde::from_slice(msg.as_slice()).context(format!(
                        "{} do_handle_websocket: rmp_serde error",
                        session.worker_identifier()
                    ))?;
                    outer_msg = Some(msg);
                }
                _ => {
                    session
                        .clone()
                        .handle_ws_message(
                            tx.clone(),
                            msg,
                            online_tracker.clone(),
                            worker_id as usize,
                        )
                        .await
                        .context(format!(
                            "{} do_handle_websocket: handle_ws_message error",
                            session.worker_identifier()
                        ))?;
                }
            }
        }
    }

    info!("{} do_handle_websocket exited", session.worker_identifier());
    Ok(())
}

pub type FutureQueueItem = BoxFuture<'static, Result<()>>;

async fn handle_websocket(
    ctx: WrappedAppContext,
    socket: ws::WebSocket,
    pubkey_str: String,
    ts: DateTime<Utc>,
    sign_buf_str: String,
    online_tracker: Arc<OnlineTracker>,
) {
    if ctx.black_worker_list.lock().await.contains(&pubkey_str) {
        return;
    }
    let Ok(remote_pubkey) = parse_public_key(&pubkey_str) else {
        error!(
            "{} Invalid pubkey",
            worker_identifier_from_pubkey(&pubkey_str)
        );
        return;
    };

    let Ok(secret) = get_shared_key(&ctx.mgmt_priv_key, &remote_pubkey) else {
        error!(
            "{} get shared key failed",
            worker_identifier_from_pubkey(&pubkey_str)
        );
        return;
    };

    let cancel_token = CancellationToken::new();
    let _cancel_guard = cancel_token.clone().drop_guard();

    let (mut ws_tx, ws_rx) = socket.split();
    let (inner_tx, mut inner_rx) = mpsc::unbounded_channel::<AppWsResponse>();

    let cancel_token_clone = cancel_token.clone();
    let inner_tx_clone = inner_tx.clone();
    let pubkey_str_clone = pubkey_str.clone();
    let handle_for_ping = tokio::spawn(async move {
        loop {
            if cancel_token_clone.is_cancelled() {
                info!(
                    "{} handle_for_ping exited by cancel token",
                    worker_identifier_from_pubkey(&pubkey_str_clone)
                );
                break;
            }
            if let Err(e) = inner_tx_clone.send(AppWsResponse::Ping) {
                error!(
                    "{} handle_for_ping send ping failed: {}",
                    worker_identifier_from_pubkey(&pubkey_str_clone),
                    e
                );
                break;
            }
            tokio::time::sleep(std::time::Duration::from_secs(10)).await;
        }
        info!(
            "{} handle_for_ping exited",
            worker_identifier_from_pubkey(&pubkey_str_clone)
        );
    });

    let pubkey_str_tx = pubkey_str.clone();
    let secret_move = clone_shared_secret(&secret);
    let cancel_clone = cancel_token.clone();
    let ws_tx_handle = tokio::spawn(async move {
        loop {
            let msg = tokio::select! {
                msg = inner_rx.recv() => {
                    msg
                }
                _ = cancel_clone.cancelled() => {
                    break;
                }
            };
            let Some(msg) = msg else {
                break;
            };
            match encode_rx_message(&msg, &secret_move) {
                Ok(msg) => {
                    if let Err(e) = ws_tx.send(Message::binary(msg.as_slice())).await {
                        error!(
                            "{} ws_tx_handle send error: {}",
                            worker_identifier_from_pubkey(&pubkey_str_tx),
                            e
                        );
                        break;
                    }
                }
                Err(e) => {
                    error!("ws_tx_handle: {}", e);
                }
            }
        }
    });
    info!(
        "{} begin wait session_rx",
        worker_identifier_from_pubkey(&pubkey_str)
    );
    let session =
        match create_worker_session(ctx.clone(), inner_tx.clone(), &pubkey_str, ts, sign_buf_str)
            .await
        {
            Ok(session) => session,
            Err(e) => {
                error!(
                    "{} create_worker_session error: {}",
                    worker_identifier_from_pubkey(&pubkey_str),
                    e
                );
                // send error to client to avoid infinite loop
                let _ = inner_tx.send(AppWsResponse::Error(e.to_string()));
                tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                return;
            }
        };
    let (cmd_tx, mut cmd_rx) = mpsc::channel::<WorkerCommand>(100);
    let inner_tx_clone = inner_tx.clone();
    let session_clone = session.clone();
    let ctx_clone = ctx.clone();
    let pubkey_str_clone = pubkey_str.clone();
    let cancel_clone = cancel_token.clone();
    // start command process
    let cmd_process_handle = tokio::spawn(async move {
        loop {
            let msg = tokio::select! {
                msg = cmd_rx.recv() => {
                    msg
                }
                _ = cancel_clone.cancelled() => {
                    break;
                }
            };
            let Some(cmd) = msg else {
                break;
            };
            match cmd {
                WorkerCommand::TestLatency(cmd) => {
                    let request_id = ctx_clone
                        .request_id
                        .fetch_add(1, std::sync::atomic::Ordering::AcqRel);
                    let Ok(_) = inner_tx_clone.send(AppWsResponse::TestLatencyReq {
                        request_id,
                        fwd_configs: cmd.command.fwd_configs.clone(),
                        info: cmd.command.info.clone(),
                    }) else {
                        error!(
                            "{} send test latency req:{} failed",
                            worker_identifier_from_pubkey(&pubkey_str_clone),
                            request_id
                        );
                        continue;
                    };
                    session_clone
                        .pending_latency_test
                        .lock()
                        .await
                        .insert(request_id, cmd.rst_tx);
                }
                WorkerCommand::UpdateLatencyMonitoringConfig(cmd) => {
                    let request_id = ctx_clone
                        .request_id
                        .fetch_add(1, std::sync::atomic::Ordering::AcqRel);
                    info!(
                        "Received latency monitoring update request for worker_id: {}",
                        cmd.command.worker_id
                    );
                    let Ok(_) =
                        inner_tx_clone.send(AppWsResponse::UpdateLatencyMonitoringConfigReq {
                            request_id,
                            configs: cmd.command.configs.clone(),
                        })
                    else {
                        error!(
                            "worker:{} send update latency monitoring req failed",
                            cmd.command.worker_id
                        );
                        continue;
                    };
                    session_clone
                        .pending_update_latency_monitoring_config
                        .lock()
                        .await
                        .insert(request_id, cmd.rst_tx);
                }
                WorkerCommand::GetConnectionStats(cmd) => {
                    let request_id = ctx_clone
                        .request_id
                        .fetch_add(1, std::sync::atomic::Ordering::AcqRel);
                    let Ok(_) = inner_tx_clone.send(AppWsResponse::GetConnectionStatsReq {
                        request_id,
                        port: cmd.command.port,
                    }) else {
                        error!(
                            "{} send connection stats req:{} failed",
                            worker_identifier_from_pubkey(&pubkey_str_clone),
                            request_id
                        );
                        continue;
                    };
                    session_clone
                        .pending_connection_stats
                        .lock()
                        .await
                        .insert(request_id, cmd.rst_tx);
                }
                WorkerCommand::ConfigUpdated => {
                    let Ok(_) = inner_tx_clone.send(AppWsResponse::ConfigUpdated {}) else {
                        error!(
                            "{} send config updated notification failed",
                            worker_identifier_from_pubkey(&pubkey_str_clone)
                        );
                        continue;
                    };
                    info!(
                        "{} sent config updated notification",
                        worker_identifier_from_pubkey(&pubkey_str_clone)
                    );
                }
            }
        }
        info!(
            "{} cmd_process exited",
            worker_identifier_from_pubkey(&pubkey_str_clone)
        );
    });
    // update config map before session_loop and do_handle_websocket so the map will not be empty
    match session.update_config_map().await {
        Ok(_) => {}
        Err(e) => {
            error!(
                "{} do_handle_websocket: update_config_map error: {}",
                worker_identifier_from_pubkey(&pubkey_str),
                e
            );
            return;
        }
    }

    ctx.worker_command_tx
        .lock()
        .await
        .insert(session.worker_info.id, cmd_tx);
    info!(
        "{} end wait session_rx",
        worker_identifier_from_pubkey(&pubkey_str)
    );
    let session_handle = tokio::spawn(session.clone().session_loop(cancel_token.clone()));

    let ws_rx_handle = tokio::spawn(do_handle_websocket(
        inner_tx.clone(),
        ws_rx,
        online_tracker,
        session.clone(),
        secret,
        cancel_token.clone(),
    ));
    // 更新特定server的配置
    match LatencyMonitoringService::refresh_configs_to_worker(ctx.clone(), session.worker_info.id)
        .await
    {
        Ok(_) => {
            info!(
                "Successfully updated latency configs for server {}",
                session.worker_info.id
            );
        }
        Err(e) => {
            error!(
                "Failed to update latency configs for server {}: {}",
                session.worker_info.id, e
            );
        }
    }
    tokio::select! {
        ret = session_handle => {
            info!("{} session_handle exited: {:?}", worker_identifier_from_pubkey(&pubkey_str), ret);
        }
        ret = ws_rx_handle => {
            info!("{} ws_rx_handle exited: {:?}", worker_identifier_from_pubkey(&pubkey_str), ret);
            if let Some(e) = match ret {
                Ok(e) => {
                    if let Err(e) = e {
                        error!("{} handle_websocket(inner): {:?}", worker_identifier_from_pubkey(&pubkey_str), &e);
                        Some(format!("{:?}", e))
                    } else {
                        info!("{} handle_websocket(inner) return Ok", worker_identifier_from_pubkey(&pubkey_str));
                        None
                    }
                }
                Err(e) => {
                    error!("{} handle_websocket(outer): {:?}", worker_identifier_from_pubkey(&pubkey_str), &e);
                    Some(format!("{:?}", e))
                }
            } {
                let m = AppWsResponse::Error(e.to_string());
                let _ = inner_tx.send(m);
            }
        }
        ret = ws_tx_handle => {
            info!("{} ws_tx_handle exited: {:?}", worker_identifier_from_pubkey(&pubkey_str), ret);
        }
        ret = handle_for_ping => {
            info!("{} handle_for_ping exited: {:?}", worker_identifier_from_pubkey(&pubkey_str), ret);
        }
        ret = cmd_process_handle => {
            info!("{} cmd_process_handle exited: {:?}", worker_identifier_from_pubkey(&pubkey_str), ret);
        }
    }
    ctx.worker_command_tx
        .lock()
        .await
        .remove(&session.worker_info.id);
}

async fn handle_rejection(e: Rejection) -> Result<impl Reply, Infallible> {
    warn!("handle_rejection: {:?}", &e);
    let e = AppError::PlainError(format!("{:?}", &e));
    Ok(reply::with_status(
        rmp_serde::encode::to_vec(&e).unwrap(),
        StatusCode::BAD_REQUEST,
    ))
}
async fn handle_worker_version(
    ctx: WrappedAppContext,
    hash: String,
) -> Result<impl Reply, warp::Rejection> {
    let version_info = WorkerVersionInfo {
        version: ctx.worker_version.clone(),
        download_url: Some(format!(
            "{}/api/worker/download",
            ctx.opt.arranger_hosts_url
        )),
        hash: Some(hash),
    };
    Ok(warp::reply::json(&version_info))
}

async fn handle_version_report(
    ctx: WrappedAppContext,
    pubkey_str: String,
    body: WorkerVersionReportRequest,
) -> Result<impl Reply, warp::Rejection> {
    ctx.db
        .outbound_endpoint()
        .update(
            prisma::outbound_endpoint::pubkey::equals(pubkey_str),
            vec![prisma::outbound_endpoint::version::set(Some(body.version))],
        )
        .exec()
        .await
        .map_err(|e| {
            error!("handle_version_report: {}", e);
            warp::reject()
        })?;
    Ok(warp::reply::html("ok"))
}

async fn handle_worker_fetch_global_latest_config(
    ctx: WrappedAppContext,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    let server = ctx
        .db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::pubkey::equals(
            pubkey_str.clone(),
        ))
        .select(prisma::outbound_endpoint::select!({
            proxy_config
            interface_name
            use_forward_as_tun
            protocol_filters
        }
        ))
        .exec()
        .await
        .map_err(|_e| warp::reject())?
        .ok_or(warp::reject::not_found())?;
    let proxy_config = server.proxy_config;
    let interface_name = server.interface_name.unwrap_or_default();
    let traffic_filter = if server.protocol_filters.is_empty() {
        None
    } else {
        Some(server.protocol_filters)
    };
    let response = WorkerFetchGlobalLatestConfigResponse {
        proxy_config,
        interface_name,
        use_forward_as_tun: server.use_forward_as_tun,
        traffic_filter,
    };
    debug!(
        "{} get latest global config: {:?}",
        worker_identifier_from_pubkey(&pubkey_str),
        &response
    );
    Ok(warp::reply::json(&response))
}

#[derive(serde::Deserialize)]
struct ConfigUpdatedRequest {
    server_id: i32,
}

async fn handle_config_updated(
    ctx: WrappedAppContext,
    pubkey_str: String,
    body: ConfigUpdatedRequest,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    info!("Config updated notification for server {}", body.server_id);

    if let Some(worker_tx) = ctx.worker_command_tx.lock().await.get(&body.server_id) {
        if let Err(e) = worker_tx.send(WorkerCommand::ConfigUpdated).await {
            error!(
                "Failed to send config updated notification to server {}: {}",
                body.server_id, e
            );
            return Ok(warp::reply::json(&serde_json::json!({
                "success": false,
                "message": format!("Failed to send notification: {}", e)
            })));
        }

        info!(
            "Successfully sent config updated notification to server {}",
            body.server_id
        );
        Ok(warp::reply::json(&serde_json::json!({
            "success": true,
            "message": "Config updated notification sent successfully"
        })))
    } else {
        warn!(
            "Server {} is not online, cannot send config updated notification",
            body.server_id
        );
        Ok(warp::reply::json(&serde_json::json!({
            "success": false,
            "message": "Server is not online"
        })))
    }
}
