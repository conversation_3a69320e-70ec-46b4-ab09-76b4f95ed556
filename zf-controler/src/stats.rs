use anyhow::Result;
use chrono::{DateTime, Utc};
use common::{
    app_message::StatsReport,
    batch_writer::BatchWriter,
    reset_traffic_task::RedisPool,
    stats::{NetcardSpeedMeasurement, SystemStatsMeasurement, UserLineInfoMeasurement, CachedNetSpeedItem, CachedSystemStatsItem, CachedUserLineSpeedItem},
};
use std::{collections::HashSet, net::SocketAddr};
use serde_json;
use redis::AsyncCommands;

pub async fn write_netcard_stats(
    batch_writer: &BatchWriter,
    redis_pool: &RedisPool,
    agent_id: i32,
    data: &StatsReport,
) -> Result<()> {
    // 聚合所有接口的速度数据
    let mut total_rx = 0.0;
    let mut total_tx = 0.0;
    let mut total_rx_bytes = 0u64;
    let mut total_tx_bytes = 0u64;

    for (interface, (rx, tx, total_rx_iface, total_tx_iface)) in data.netcard_speed.iter() {
        let measurement = NetcardSpeedMeasurement {
            time: Utc::now(),
            interface: interface.to_owned(),
            agent_id,
            tx: *tx as f64,
            rx: *rx as f64,
            total_tx: *total_tx_iface as u64,
            total_rx: *total_rx_iface as u64,
        };
        if let Err(e) = batch_writer.write_netcard_stats(measurement).await {
            log::error!("write_netcard_stats: {}, error: {}", agent_id, e);
        }
        
        // 累加所有接口的数据
        total_rx += *rx as f64;
        total_tx += *tx as f64;
        total_rx_bytes += *total_rx_iface;
        total_tx_bytes += *total_tx_iface;
    }

    // 写入Redis缓存
    if let Ok(mut conn) = redis_pool.get().await {
        let cache_key = format!("netcard_speed:{}", agent_id);
        let cache_data = CachedNetSpeedItem {
            rx: total_rx as u64,
            tx: total_tx as u64,
            total_rx: total_rx_bytes,
            total_tx: total_tx_bytes,
        };
        
        if let Ok(json_str) = serde_json::to_string(&cache_data) {
            if let Err(e) = conn.set_ex::<_, _, ()>(&cache_key, json_str, 60).await {
                log::error!("Failed to cache netcard_speed for agent {}: {}", agent_id, e);
            }
        }
    }
    Ok(())
}

pub async fn write_system_stats(
    batch_writer: &BatchWriter,
    redis_pool: &RedisPool,
    agent_id: i32,
    data: &StatsReport,
) -> Result<()> {
    let measurement = SystemStatsMeasurement {
        time: Utc::now(),
        agent_id,
        cpu_usage: data.cpu_usage,
        memory_total: data.memory_usage.1,
        memory_used: data.memory_usage.0,
        uptime: data.uptime,
        tcp_connections: data.tcp_connections,
        udp_connections: data.udp_connections,
    };
    if let Err(e) = batch_writer.write_system_stats(measurement.clone()).await {
        log::error!("write_system_stats: {}, error: {}", agent_id, e);
    }

    // 写入Redis缓存
    if let Ok(mut conn) = redis_pool.get().await {
        let cache_key = format!("system_stats:{}", agent_id);
        let cache_data = CachedSystemStatsItem {
            cpu_usage: measurement.cpu_usage,
            memory_total: measurement.memory_total,
            memory_used: measurement.memory_used,
            uptime: measurement.uptime,
            tcp_connections: measurement.tcp_connections,
            udp_connections: measurement.udp_connections,
        };
        
        if let Ok(json_str) = serde_json::to_string(&cache_data) {
            if let Err(e) = conn.set_ex::<_, _, ()>(&cache_key, json_str, 60).await {
                log::error!("Failed to cache system_stats for agent {}: {}", agent_id, e);
            }
        }
    }
    Ok(())
}

pub async fn write_user_line_info(
    batch_writer: &BatchWriter,
    redis_pool: &RedisPool,
    subscription_id: i32,
    line_id: i32,
    traffic_inc: u64,
    total_speed: f64,
    current_time: i64,
    client_ips: &HashSet<SocketAddr>,
) -> Result<()> {
    let client_ips_only_ip = client_ips.iter().map(|s| s.ip()).collect::<HashSet<_>>();
    let measurement = UserLineInfoMeasurement {
        time: DateTime::<Utc>::from_timestamp(current_time, 0).unwrap_or(Utc::now()),
        subscription_id,
        line_id,
        traffic_inc,
        total_speed,
        client_ips: serde_json::to_string(&client_ips_only_ip).unwrap(),
        client_ips_count: client_ips_only_ip.len() as i32,
        connection_count: client_ips.len() as i32,
    };
    if let Err(e) = batch_writer.write_user_line_info(measurement.clone()).await {
        log::error!(
            "write_user_line_info: subscription_id: {}, line_id: {}, error: {}",
            subscription_id,
            line_id,
            e
        );
    }

    // 写入Redis缓存
    if let Ok(mut conn) = redis_pool.get().await {
        let cache_key = format!("user_line_info:{}:{}", subscription_id, line_id);
        let cache_data = CachedUserLineSpeedItem {
            subscription_id,
            line_id,
            line_name: None,
            upload_speed: 0.0,
            download_speed: total_speed,
            client_ips_count: measurement.client_ips_count,
            connection_count: measurement.connection_count,
        };
        
        if let Ok(json_str) = serde_json::to_string(&cache_data) {
            if let Err(e) = conn.set_ex::<_, _, ()>(&cache_key, json_str, 60).await {
                log::error!("Failed to cache user_line_info for subscription {} line {}: {}", subscription_id, line_id, e);
            }
        }
    }
    Ok(())
}
