use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use reqwest;
use serde::{Deserialize, Serialize};
use std::time::Duration;

/// 延迟历史数据点结构 (从rrd-service复制)
#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct LatencyHistoryPoint {
    pub timestamp: DateTime<Utc>,
    pub config_id: i32,
    pub target_address: String,
    pub latency_us: Option<u64>,
    pub success: bool,
    pub error_msg: Option<String>,
    pub test_round: Option<i32>,
    pub samples_count: Option<i32>,
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub median_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,
    pub packet_loss_rate: Option<f32>,
}

/// SmokePing风格的RRD数据点结构 (与rrd-service保持一致)
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RrdDataPoint {
    pub timestamp: DateTime<Utc>,

    // SmokePing核心字段
    /// 丢包计数 (0-20)，SmokePing标准
    pub loss_count: u32,
    /// 中位数延迟 (微秒)
    pub median_latency_us: Option<u64>,
    /// 正常运行时间 (秒)
    pub uptime: Option<u64>,

    // 20个ping样本 (SmokePing标准ping数量)
    /// 个别ping结果，最多20个样本
    pub ping_samples: Vec<Option<u64>>,

    // 兼容性和统计字段
    /// 平均延迟 (基于有效样本计算)
    pub avg_latency_us: Option<u64>,
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,

    // 元数据
    /// 总样本数（通常是20）
    pub total_samples: u32,
}

/// HTTP请求结构
#[derive(Debug, Serialize)]
struct CreateRrdRequest {
    server_id: i32,
    config_id: i32,
}

#[derive(Debug, Serialize)]
struct UpdateRrdRequest {
    server_id: i32,
    config_id: i32,
    data_point: RrdDataPoint,
}

#[derive(Debug, Serialize)]
struct GenerateChartRequest {
    server_id: i32,
    config_id: i32,
    time_range: String,
    target_address: String,
    end_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
struct InitHistoricalDataRequest {
    server_id: i32,
    config_id: i32,
    history_points: Vec<LatencyHistoryPoint>,
}

#[derive(Debug, Serialize)]
struct CleanupRequest {
    active_configs: Vec<(i32, i32)>,
}

/// HTTP响应结构
#[derive(Debug, Deserialize)]
struct ApiResponse {
    success: bool,
    message: Option<String>,
}

#[derive(Debug, Deserialize)]
struct GenerateChartResponse {
    image_data: String, // base64 encoded
}

#[derive(Debug, Deserialize)]
struct ExistsResponse {
    exists: bool,
}

/// RRD HTTP 客户端
pub struct RrdClient {
    base_url: String,
    client: reqwest::Client,
}

impl RrdClient {
    /// 创建新的RRD客户端
    pub fn new(base_url: String) -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self { base_url, client }
    }

    /// 创建RRD文件 - 与原RrdManager接口兼容
    pub async fn create_rrd_file(&self, server_id: i32, config_id: i32) -> Result<()> {
        let request = CreateRrdRequest {
            server_id,
            config_id,
        };

        let response = self
            .client
            .post(&format!("{}/rrd/create", self.base_url))
            .json(&request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send create RRD request: {}", e))?;

        if response.status().is_success() {
            let _: ApiResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse create RRD response: {}", e))?;
            Ok(())
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 更新RRD数据 - 与原RrdManager接口兼容
    pub async fn update_rrd_data(
        &self,
        server_id: i32,
        config_id: i32,
        data_point: &RrdDataPoint,
    ) -> Result<()> {
        let request = UpdateRrdRequest {
            server_id,
            config_id,
            data_point: data_point.clone(),
        };

        let response = self
            .client
            .put(&format!("{}/rrd/update", self.base_url))
            .json(&request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send update RRD request: {}", e))?;

        if response.status().is_success() {
            let _: ApiResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse update RRD response: {}", e))?;
            Ok(())
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 生成图表 - 与原RrdManager接口兼容
    pub async fn generate_chart(
        &self,
        server_id: i32,
        config_id: i32,
        time_range: &str,
        target_address: &str,
        end_time: Option<DateTime<Utc>>,
    ) -> Result<Vec<u8>> {
        let request = GenerateChartRequest {
            server_id,
            config_id,
            time_range: time_range.to_string(),
            target_address: target_address.to_string(),
            end_time,
        };

        let response = self
            .client
            .post(&format!("{}/chart/generate", self.base_url))
            .json(&request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send generate chart request: {}", e))?;

        if response.status().is_success() {
            let chart_response: GenerateChartResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse generate chart response: {}", e))?;

            // 解码base64图片数据
            let image_data = base64::Engine::decode(
                &base64::engine::general_purpose::STANDARD,
                &chart_response.image_data,
            )
            .map_err(|e| anyhow!("Failed to decode base64 image data: {}", e))?;

            Ok(image_data)
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 检查RRD文件是否存在 - 与原RrdManager接口兼容
    pub async fn rrd_file_exists(&self, server_id: i32, config_id: i32) -> bool {
        match self
            .client
            .get(&format!(
                "{}/rrd/exists/{}/{}",
                self.base_url, server_id, config_id
            ))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    match response.json::<ExistsResponse>().await {
                        Ok(exists_response) => exists_response.exists,
                        Err(_) => false,
                    }
                } else {
                    false
                }
            }
            Err(_) => false,
        }
    }

    /// 删除RRD文件 - 与原RrdManager接口兼容
    pub async fn delete_rrd_file(&self, server_id: i32, config_id: i32) -> Result<()> {
        let response = self
            .client
            .delete(&format!(
                "{}/rrd/{}/{}",
                self.base_url, server_id, config_id
            ))
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send delete RRD request: {}", e))?;

        if response.status().is_success() {
            let _: ApiResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse delete RRD response: {}", e))?;
            Ok(())
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 清理过期的RRD文件 - 与原RrdManager接口兼容
    pub async fn cleanup_expired_rrd_files(&self, active_configs: &[(i32, i32)]) -> Result<()> {
        let request = CleanupRequest {
            active_configs: active_configs.to_vec(),
        };

        let response = self
            .client
            .post(&format!("{}/rrd/cleanup", self.base_url))
            .json(&request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send cleanup request: {}", e))?;

        if response.status().is_success() {
            let _: ApiResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse cleanup response: {}", e))?;
            Ok(())
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 批量初始化历史数据 - 与原RrdManager接口兼容
    pub async fn initialize_historical_data(
        &self,
        server_id: i32,
        config_id: i32,
        history_points: &[LatencyHistoryPoint],
    ) -> Result<()> {
        let request = InitHistoricalDataRequest {
            server_id,
            config_id,
            history_points: history_points.to_vec(),
        };

        let response = self
            .client
            .post(&format!("{}/rrd/init-historical", self.base_url))
            .json(&request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send init historical data request: {}", e))?;

        if response.status().is_success() {
            let _: ApiResponse = response
                .json()
                .await
                .map_err(|e| anyhow!("Failed to parse init historical data response: {}", e))?;
            Ok(())
        } else {
            Err(anyhow!("RRD service error: HTTP {}", response.status()))
        }
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        let response = self
            .client
            .get(&format!("{}/health", self.base_url))
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send health check request: {}", e))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(anyhow!(
                "RRD service health check failed: HTTP {}",
                response.status()
            ))
        }
    }

    /// 从LatencyTestResult转换为RrdDataPoint
    ///
    /// 这个方法实现了从worker报告的LatencyTestResult到SmokePing风格的RrdDataPoint的转换
    pub fn convert_latency_result_to_rrd_data_point(
        latency_result: &common::app_message::LatencyTestResult,
    ) -> RrdDataPoint {
        RrdDataPoint {
            timestamp: latency_result.timestamp,
            loss_count: latency_result.loss_count,
            median_latency_us: latency_result.median_latency_us,
            uptime: latency_result.uptime,
            ping_samples: latency_result.ping_samples.clone(),
            avg_latency_us: latency_result.avg_latency_us,
            min_latency_us: latency_result.min_latency_us,
            max_latency_us: latency_result.max_latency_us,
            stddev_latency_us: latency_result.stddev_latency_us,
            total_samples: latency_result.total_samples,
        }
    }

    /// 批量转换并更新RRD数据
    ///
    /// 这个方法接收worker报告的LatencyTestResult列表，转换为RrdDataPoint并批量更新
    pub async fn update_rrd_data_from_results(
        &self,
        server_id: i32,
        config_id: i32,
        latency_results: &[common::app_message::LatencyTestResult],
    ) -> Result<()> {
        for result in latency_results {
            let rrd_data_point = Self::convert_latency_result_to_rrd_data_point(result);
            self.update_rrd_data(server_id, config_id, &rrd_data_point)
                .await?;
        }
        Ok(())
    }
}
