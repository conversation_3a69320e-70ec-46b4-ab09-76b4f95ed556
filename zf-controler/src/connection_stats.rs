use std::{sync::Arc, time::Duration};

use super::error::Error;
use crate::{App<PERSON>ontext, CommandWrapper, WorkerCommand};
use crate::worker::worker_identifier_from_id;
use common::{
    app_message::{WorkerGetConnectionStatsRequest, WorkerGetConnectionStatsResponse},
    constant::LATENCY_TEST_TIMEOUT,
    crypto::{parse_secret_key, stringify_public_key_from_secret_key},
};
use tokio::sync::oneshot;

pub fn check_mgmt_pubkey(ctx: &Arc<AppContext>, pubkey_str: &String) -> Result<(), anyhow::Error> {
    let key = parse_secret_key(&ctx.opt.mgmt_priv_key)?;
    let expected_pubkey_str = stringify_public_key_from_secret_key(&key);
    if pubkey_str != &expected_pubkey_str {
        return Err(anyhow::anyhow!("pubkey:{} not match", pubkey_str));
    }
    Ok(())
}

pub async fn get_connection_stats(
    ctx: Arc<AppContext>,
    pubkey_str: String,
    body: WorkerGetConnectionStatsRequest,
) -> Result<impl warp::Reply, warp::Rejection> {
    log::info!("get connection stats request: {:?}", body);
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    let (tx, rx) = oneshot::channel();
    if let Some(worker_tx) = ctx.worker_command_tx.lock().await.get(&body.worker_id) {
        if let Err(_e) = worker_tx
            .send(WorkerCommand::GetConnectionStats(CommandWrapper::new(
                body.clone(),
                tx,
            )))
            .await
        {
            return Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                "{} send connection stats req:{:?} tx failed",
                worker_identifier_from_id(body.worker_id),
                body.port
            ))));
        }
    } else {
        return Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "server is not online"
        ))));
    }
    let res = match tokio::time::timeout(Duration::from_secs(LATENCY_TEST_TIMEOUT), rx).await {
        Ok(res) => res
            .map_err(|e| {
                warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                    "wait rx error: {e} for {}",
                    worker_identifier_from_id(body.worker_id)
                )))
            })?
            .map_err(|e| {
                warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                    "connection stats error: {e} for {}",
                    worker_identifier_from_id(body.worker_id)
                )))
            })?,
        Err(_) => {
            return Err(warp::reject::custom(Error::InternalError(anyhow::anyhow!(
                "timeout for connection stats {}",
                worker_identifier_from_id(body.worker_id)
            ))))
        }
    };
    Ok(warp::reply::json(&WorkerGetConnectionStatsResponse {
        connections: res.connections,
    }))
}