use crate::error::Error;
use common::{
    license_message::{ApiResponse, ApplyRenewalCodeResponse, CalculateRenewalPriceResponse},
    ApplyRenewalRequest, CreateRenewalRequestRequest, Entitlements, EntitlementsData,
    GetEntitlementsResponse,
};
use log::{error, info};
use warp::reply::Reply;
use zf_auth_client::{
    apply_renewal_code, cancel_renewal_request, create_renewal_request, get_license_info,
    get_renewal_history, get_renewal_requests,
};

use crate::{latency::check_mgmt_pubkey, WrappedAppContext};

pub async fn handle_apply_renewal(
    ctx: WrappedAppContext,
    body: ApplyRenewalRequest,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match apply_renewal_code(auth_client, body.renewal_code_id).await {
            Ok(()) => {
                let response = ApplyRenewalCodeResponse {
                    success: true,
                    message: "Renewal code applied successfully".to_string(),
                    data: None,
                };
                Ok(warp::reply::json(&response))
            }
            Err(e) => {
                error!("Failed to apply renewal code: {}", e);
                let response = ApplyRenewalCodeResponse {
                    success: false,
                    message: format!("Failed to apply renewal code: {}", e),
                    data: None,
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        let response = ApplyRenewalCodeResponse {
            success: false,
            message: "Auth client not initialized - running in unlicensed mode".to_string(),
            data: None,
        };
        Ok(warp::reply::json(&response))
    }
}

pub async fn handle_get_license_info(
    ctx: WrappedAppContext,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match get_license_info(auth_client).await {
            Ok(license_info) => {
                let response = ApiResponse {
                    success: true,
                    data: Some(license_info),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&response))
            }
            Err(e) => {
                error!("Failed to get license info: {}", e);
                let response = ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(format!("Failed to get license info: {}", e)),
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        let response = ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not initialized - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&response))
    }
}

pub async fn handle_get_renewal_history(
    ctx: WrappedAppContext,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match get_renewal_history(auth_client).await {
            Ok(history) => {
                let response = ApiResponse {
                    success: true,
                    data: Some(history),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&response))
            }
            Err(e) => {
                error!("Failed to get renewal history: {}", e);
                let response = ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(format!("Failed to get renewal history: {}", e)),
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        let response = ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not initialized - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&response))
    }
}

pub async fn handle_calculate_renewal_price(
    ctx: WrappedAppContext,
    requested_duration: i32,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match zf_auth_client::calculate_renewal_price(auth_client, requested_duration).await {
            Ok(response) => Ok(warp::reply::json(&response)),
            Err(e) => {
                error!("Failed to calculate renewal price: {}", e);
                let response = CalculateRenewalPriceResponse {
                    monthly_rate: "0.00".to_string(),
                    requested_duration,
                    total_price: "0.00".to_string(),
                    currency: "CNY".to_string(),
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        // Fallback for unlicensed mode - provide basic pricing
        let monthly_rate = "299.00";
        let total_price =
            (monthly_rate.parse::<f64>().unwrap() * requested_duration as f64).to_string();

        let response = CalculateRenewalPriceResponse {
            monthly_rate: monthly_rate.to_string(),
            requested_duration,
            total_price,
            currency: "CNY".to_string(),
        };

        Ok(warp::reply::json(&response))
    }
}

// 创建续费请求
pub async fn handle_create_renewal_request(
    ctx: WrappedAppContext,
    body: CreateRenewalRequestRequest,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match create_renewal_request(auth_client, body).await {
            Ok(response) => {
                let api_response = ApiResponse {
                    success: true,
                    data: Some(response),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&api_response))
            }
            Err(e) => {
                error!("Failed to create renewal request: {}", e);
                let api_response = ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(format!("Failed to create renewal request: {}", e)),
                };
                Ok(warp::reply::json(&api_response))
            }
        }
    } else {
        let api_response = ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not initialized - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&api_response))
    }
}

// 获取续费请求列表
pub async fn handle_get_renewal_requests(
    ctx: WrappedAppContext,
    page: Option<u32>,
    page_size: Option<u32>,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match get_renewal_requests(auth_client, page, page_size).await {
            Ok(response) => {
                let api_response = ApiResponse {
                    success: true,
                    data: Some(response),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&api_response))
            }
            Err(e) => {
                error!("Failed to get renewal requests: {}", e);
                let api_response = ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(format!("Failed to get renewal requests: {}", e)),
                };
                Ok(warp::reply::json(&api_response))
            }
        }
    } else {
        let api_response = ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not initialized - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&api_response))
    }
}

// 取消续费请求
pub async fn handle_cancel_renewal_request(
    ctx: WrappedAppContext,
    request_id: String,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    if let Some(ref auth_client) = ctx.auth_client {
        match cancel_renewal_request(auth_client, request_id).await {
            Ok(response) => {
                let api_response = ApiResponse {
                    success: true,
                    data: Some(response),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&api_response))
            }
            Err(e) => {
                error!("Failed to cancel renewal request: {}", e);
                let api_response = ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(format!("Failed to cancel renewal request: {}", e)),
                };
                Ok(warp::reply::json(&api_response))
            }
        }
    } else {
        let api_response = ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not initialized - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&api_response))
    }
}

pub async fn handle_get_entitlements(
    ctx: WrappedAppContext,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;
    info!("Get entitlements request");
    let entitlements = ctx.entitlements.read().await.clone();
    if let Some(ref auth_entitlements) = entitlements {
        // Get current worker count (number of servers in database)
        let current_workers = match ctx.db.outbound_endpoint().count(vec![]).exec().await {
            Ok(count) => count as u32,
            Err(e) => {
                error!("Failed to count workers: {}", e);
                0
            }
        };

        // Get currently connected workers count
        let connected_workers = ctx.worker_command_tx.lock().await.len() as u32;

        // Get current subscription count
        let current_subscription_count = match ctx.db.subscription().count(vec![]).exec().await {
            Ok(count) => count as u32,
            Err(e) => {
                error!("Failed to count subscriptions: {}", e);
                0
            }
        };

        // Convert zf_auth_client::Entitlements to our local Entitlements struct
        let entitlements = Entitlements {
            max_workers: auth_entitlements.max_workers,
            max_subscription_number: auth_entitlements.max_subscription_number,
            max_users_per_worker: auth_entitlements.max_users_per_worker,
            feature_udp_forwarding_enabled: auth_entitlements.feature_udp_forwarding_enabled,
        };

        let data = EntitlementsData {
            entitlements,
            current_workers,
            connected_workers,
            current_subscription_count,
        };

        let response = GetEntitlementsResponse {
            success: true,
            data: Some(data),
            error: None,
        };

        Ok(warp::reply::json(&response))
    } else {
        let response = GetEntitlementsResponse {
            success: false,
            data: None,
            error: Some("No entitlements available - running in unlicensed mode".to_string()),
        };
        Ok(warp::reply::json(&response))
    }
}

// 消息相关处理函数

pub async fn handle_send_renewal_request_message(
    ctx: WrappedAppContext,
    request_id: String,
    body: common::SendRenewalRequestMessageRequest,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;

    if let Some(ref auth_client) = ctx.auth_client {
        match zf_auth_client::send_renewal_request_message(auth_client, request_id, body).await {
            Ok(message) => {
                let response = common::license_message::ApiResponse {
                    success: true,
                    data: Some(message),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&response))
            }
            Err(e) => {
                error!("Failed to send renewal request message: {}", e);
                let response = common::license_message::ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(e.to_string()),
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        let response = common::license_message::ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not available".to_string()),
        };
        Ok(warp::reply::json(&response))
    }
}

pub async fn handle_get_renewal_request_messages(
    ctx: WrappedAppContext,
    request_id: String,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    check_mgmt_pubkey(&ctx, &pubkey_str).map_err(|e| {
        warp::reject::custom(Error::InternalError(anyhow::anyhow!(
            "pubkey:{} not match: {}",
            pubkey_str,
            e
        )))
    })?;

    if let Some(ref auth_client) = ctx.auth_client {
        match zf_auth_client::get_renewal_request_messages(auth_client, request_id).await {
            Ok(messages) => {
                let response = common::license_message::ApiResponse {
                    success: true,
                    data: Some(messages),
                    message: None,
                    error: None,
                };
                Ok(warp::reply::json(&response))
            }
            Err(e) => {
                error!("Failed to get renewal request messages: {}", e);
                let response = common::license_message::ApiResponse::<()> {
                    success: false,
                    data: None,
                    message: None,
                    error: Some(e.to_string()),
                };
                Ok(warp::reply::json(&response))
            }
        }
    } else {
        let response = common::license_message::ApiResponse::<()> {
            success: false,
            data: None,
            message: None,
            error: Some("Auth client not available".to_string()),
        };
        Ok(warp::reply::json(&response))
    }
}
