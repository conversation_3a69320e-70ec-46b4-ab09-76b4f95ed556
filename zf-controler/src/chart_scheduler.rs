use anyhow::{anyhow, Result};
use base64::Engine;
use chrono::{DateTime, Utc};
use common::stats::CachedChart;
use futures_util::stream::{self, StreamExt};
use log::{debug, error, info, warn};
use redis::AsyncCommands;
use std::sync::Arc;
use std::time::Instant;
use taos::{AsyncFetchable, AsyncQueryable};
use tokio::sync::RwLock;
use tokio_cron_scheduler::{Job, JobScheduler};

use crate::preludes::*;
use crate::rrd_client::RrdClient;

/// 图表生成调度器
pub struct ChartScheduler {
    /// RRD 客户端
    rrd_client: Arc<RrdClient>,
    /// 应用状态
    app_state: Arc<AppContext>,
    /// 调度器
    scheduler: Arc<RwLock<Option<JobScheduler>>>,
    /// 配置
    config: ChartSchedulerConfig,
}

/// 调度器配置
#[derive(Debug, Clone)]
pub struct ChartSchedulerConfig {
    /// 图表生成间隔 (分钟)
    pub generation_interval_minutes: u64,
    /// Redis 缓存过期时间 (秒)
    pub cache_ttl_seconds: u64,
    /// 支持的时间范围
    pub time_ranges: Vec<String>,
    /// 最大并发请求数
    pub max_concurrent_requests: usize,
}

impl Default for ChartSchedulerConfig {
    fn default() -> Self {
        Self {
            generation_interval_minutes: 2,
            cache_ttl_seconds: 600, // 10分钟
            time_ranges: vec![
                "1h".to_string(),
                "3h".to_string(),
                "6h".to_string(),
                "12h".to_string(),
                "24h".to_string(),
                "7d".to_string(),
            ],
            max_concurrent_requests: 8,
        }
    }
}

impl ChartScheduler {
    /// 创建新的图表调度器
    pub fn new(rrd_client: Arc<RrdClient>, app_state: Arc<AppContext>) -> Self {
        Self {
            rrd_client,
            app_state,
            scheduler: Arc::new(RwLock::new(None)),
            config: ChartSchedulerConfig::default(),
        }
    }

    /// 使用自定义配置创建调度器
    pub fn with_config(
        rrd_client: Arc<RrdClient>,
        app_state: Arc<AppContext>,
        config: ChartSchedulerConfig,
    ) -> Self {
        Self {
            rrd_client,
            app_state,
            scheduler: Arc::new(RwLock::new(None)),
            config,
        }
    }

    /// 启动调度器
    pub async fn start(&self) -> Result<()> {
        let mut scheduler_guard = self.scheduler.write().await;

        if scheduler_guard.is_some() {
            return Err(anyhow!("Chart scheduler is already running"));
        }

        let scheduler = JobScheduler::new().await?;

        // 创建图表生成任务
        let chart_job = self.create_chart_generation_job().await?;
        scheduler.add(chart_job).await?;

        // 创建清理任务 (每天运行一次)
        let cleanup_job = self.create_cleanup_job().await?;
        scheduler.add(cleanup_job).await?;

        scheduler.start().await?;
        *scheduler_guard = Some(scheduler);

        info!(
            "Chart scheduler started with {} minute interval",
            self.config.generation_interval_minutes
        );
        Ok(())
    }

    /// 停止调度器
    pub async fn stop(&self) -> Result<()> {
        let mut scheduler_guard = self.scheduler.write().await;

        if let Some(mut scheduler) = scheduler_guard.take() {
            scheduler.shutdown().await?;
            info!("Chart scheduler stopped");
        }

        Ok(())
    }

    /// 创建图表生成任务
    async fn create_chart_generation_job(&self) -> Result<Job> {
        let rrd_client = Arc::clone(&self.rrd_client);
        let app_state = Arc::clone(&self.app_state);
        let config = self.config.clone();

        let cron_expression = format!("0 */{} * * * *", config.generation_interval_minutes);

        let job = Job::new_async(cron_expression.as_str(), move |_uuid, _l| {
            let rrd_client = Arc::clone(&rrd_client);
            let app_state = Arc::clone(&app_state);
            let config = config.clone();

            Box::pin(async move {
                info!("Starting scheduled chart generation");
                if let Err(e) = generate_all_charts(rrd_client, app_state, config).await {
                    error!("Chart generation failed: {}", e);
                } else {
                    info!("Chart generation completed successfully");
                }
            })
        })?;

        Ok(job)
    }

    /// 创建清理任务
    async fn create_cleanup_job(&self) -> Result<Job> {
        let rrd_client = Arc::clone(&self.rrd_client);
        let app_state = Arc::clone(&self.app_state);

        let job = Job::new_async("0 0 2 * * *", move |_uuid, _l| {
            // 每天凌晨2点运行
            let rrd_client = Arc::clone(&rrd_client);
            let app_state = Arc::clone(&app_state);

            Box::pin(async move {
                info!("Starting scheduled cleanup");
                if let Err(e) = cleanup_expired_data(rrd_client, app_state).await {
                    error!("Cleanup failed: {}", e);
                } else {
                    info!("Cleanup completed successfully");
                }
            })
        })?;

        Ok(job)
    }

    /// 手动触发图表生成
    pub async fn trigger_generation(&self) -> Result<()> {
        let rrd_client = Arc::clone(&self.rrd_client);
        let app_state = Arc::clone(&self.app_state);
        let config = self.config.clone();

        info!("Manually triggering chart generation");
        generate_all_charts(rrd_client, app_state, config).await
    }

    /// 生成单个配置的图表
    pub async fn generate_config_charts(&self, server_id: i32, config_id: i32) -> Result<()> {
        let rrd_client = Arc::clone(&self.rrd_client);
        let app_state = Arc::clone(&self.app_state);
        let config = self.config.clone();

        generate_charts_for_config(rrd_client, app_state, config, server_id, config_id).await
    }

    /// 获取缓存的图表
    pub async fn get_cached_chart(
        &self,
        server_id: i32,
        config_id: i32,
        time_range: &str,
    ) -> Result<Option<CachedChart>> {
        let cache_key = format!("latency_chart:{}:{}:{}", server_id, config_id, time_range);

        match self.app_state.redis.get().await {
            Ok(mut conn) => {
                match conn.get::<_, String>(&cache_key).await {
                    Ok(cached_data) => {
                        match serde_json::from_str::<CachedChart>(&cached_data) {
                            Ok(chart) => {
                                // 检查是否过期
                                if chart.expires_at > Utc::now() {
                                    debug!("Cache hit for chart: {}", cache_key);
                                    Ok(Some(chart))
                                } else {
                                    debug!("Cache expired for chart: {}", cache_key);
                                    // 删除过期缓存
                                    let _ = conn.del::<_, ()>(&cache_key).await;
                                    Ok(None)
                                }
                            }
                            Err(e) => {
                                warn!("Failed to deserialize cached chart {}: {}", cache_key, e);
                                Ok(None)
                            }
                        }
                    }
                    Err(e) => {
                        warn!("Redis error for chart {}: {}", cache_key, e);
                        Ok(None)
                    }
                }
            }
            Err(e) => {
                warn!("Failed to get Redis connection: {}", e);
                Ok(None)
            }
        }
    }
}

/// 生成所有配置的图表
async fn generate_all_charts(
    rrd_client: Arc<RrdClient>,
    app_state: Arc<AppContext>,
    config: ChartSchedulerConfig,
) -> Result<()> {
    // 获取所有启用的延迟配置
    let latency_configs = app_state
        .db
        .latency_test_config()
        .find_many(vec![latency_test_config::is_enabled::equals(true)])
        .exec()
        .await
        .map_err(|e| anyhow!("Failed to fetch latency configs: {}", e))?;

    info!(
        "Found {} enabled latency configurations",
        latency_configs.len()
    );
    let now = Instant::now();
    // 并发处理配置，最多同时处理 max_concurrent_requests 个
    let results: Vec<_> = stream::iter(latency_configs.into_iter().map(|latency_config| {
        let rrd_client = Arc::clone(&rrd_client);
        let app_state = Arc::clone(&app_state);
        let config = config.clone();
        let server_id = latency_config.server_id;
        let config_id = latency_config.id;

        async move {
            let result =
                generate_charts_for_config(rrd_client, app_state, config, server_id, config_id)
                    .await;

            if let Err(e) = &result {
                error!(
                    "Failed to generate charts for server {} config {}: {}",
                    server_id, config_id, e
                );
            }

            (server_id, config_id, result)
        }
    }))
    .buffer_unordered(config.max_concurrent_requests)
    .collect()
    .await;

    // 统计成功和失败的数量
    let mut success_count = 0;
    let mut failure_count = 0;

    for (_server_id, _config_id, result) in results {
        match result {
            Ok(_) => success_count += 1,
            Err(_) => failure_count += 1,
        }
    }

    info!(
        "Chart generation completed: {} successful, {} failed, time: {}ms",
        success_count,
        failure_count,
        now.elapsed().as_millis()
    );

    Ok(())
}

/// 为单个配置生成图表（包含数据新鲜度检查）
async fn generate_charts_for_config(
    rrd_client: Arc<RrdClient>,
    app_state: Arc<AppContext>,
    config: ChartSchedulerConfig,
    server_id: i32,
    config_id: i32,
) -> Result<()> {
    // 获取配置信息
    let latency_config = app_state
        .db
        .latency_test_config()
        .find_unique(latency_test_config::id::equals(config_id))
        .exec()
        .await
        .map_err(|e| anyhow!("Failed to fetch config {}: {}", config_id, e))?
        .ok_or_else(|| anyhow!("Config {} not found", config_id))?;

    info!(
        "Checking data freshness for config: {} ({})",
        latency_config.display_name, latency_config.target_address
    );

    // 检查数据新鲜度
    let has_new_data = check_data_freshness(&app_state, server_id, config_id).await?;
    if !has_new_data {
        debug!(
            "No new data for server {} config {}, skipping chart generation",
            server_id, config_id
        );
        return Ok(());
    }

    info!(
        "Generating charts for config: {} ({})",
        latency_config.display_name, latency_config.target_address
    );

    // 并发为每个时间范围生成图表
    let chart_tasks: Vec<_> = config
        .time_ranges
        .iter()
        .map(|time_range| {
            let rrd_client = Arc::clone(&rrd_client);
            let app_state = Arc::clone(&app_state);
            let config = config.clone();
            let time_range = time_range.clone();
            let target_address = latency_config.target_address.clone();
            let display_name = latency_config.display_name.clone();

            async move {
                match rrd_client
                    .generate_chart(
                        server_id,
                        config_id,
                        &time_range,
                        &target_address,
                        Some(Utc::now()),
                    )
                    .await
                {
                    Ok(image_data) => {
                        // 缓存图表到 Redis
                        match cache_chart_to_redis(
                            &app_state,
                            &config,
                            server_id,
                            config_id,
                            &time_range,
                            &target_address,
                            &display_name,
                            &image_data,
                        )
                        .await
                        {
                            Ok(_) => Ok(()),
                            Err(e) => {
                                error!(
                                    "Failed to cache chart for server {} config {} range {}: {}",
                                    server_id, config_id, time_range, e
                                );
                                Err(e)
                            }
                        }
                    }
                    Err(e) => {
                        error!(
                            "Failed to generate chart for server {} config {} range {}: {}",
                            server_id, config_id, time_range, e
                        );
                        Err(e)
                    }
                }
            }
        })
        .collect();

    // 等待所有图表生成完成
    let results = futures_util::future::join_all(chart_tasks).await;

    // 检查是否有任何成功的生成
    let any_success = results.iter().any(|result| result.is_ok());

    // 如果至少有一个图表生成成功，更新生成时间戳
    if any_success {
        if let Err(e) = update_generation_timestamp(&app_state, server_id, config_id).await {
            error!(
                "Failed to update generation timestamp for server {} config {}: {}",
                server_id, config_id, e
            );
        }
    }

    Ok(())
}

/// 检查数据新鲜度（独立函数版本）
async fn check_data_freshness(
    app_state: &AppContext,
    server_id: i32,
    config_id: i32,
) -> Result<bool> {
    let latest_data_time =
        get_latest_data_time_from_tdengine(app_state, server_id, config_id).await?;
    let last_generation_time =
        get_last_generation_time_from_redis(app_state, server_id, config_id).await?;

    match (latest_data_time, last_generation_time) {
        (Some(data_time), Some(gen_time)) => {
            let has_new = data_time > gen_time;
            debug!(
                "Data freshness check for server {} config {}: data_time={}, gen_time={}, has_new={}",
                server_id, config_id, data_time, gen_time, has_new
            );
            Ok(has_new)
        }
        (Some(_), None) => {
            debug!(
                "Has data but no generation record for server {} config {}, needs generation",
                server_id, config_id
            );
            Ok(true)
        }
        (None, _) => {
            debug!(
                "No data available for server {} config {}, skipping generation",
                server_id, config_id
            );
            Ok(false)
        }
    }
}

/// 从TDengine获取最新数据时间（独立函数版本）
async fn get_latest_data_time_from_tdengine(
    app_state: &AppContext,
    server_id: i32,
    config_id: i32,
) -> Result<Option<DateTime<Utc>>> {
    let sql = format!(
        "SELECT last(ts) FROM server_latency_tests WHERE server_id={} AND config_id={}",
        server_id, config_id
    );

    match app_state.tdengine.get().await {
        Ok(taos) => match taos.query(&sql).await {
            Ok(mut result_set) => {
                let mut rows = result_set.rows();
                if let Some(Ok(row)) = rows.next().await {
                    let values = row.into_values();
                    if let Some(value) = values.get(0) {
                        match value {
                            taos::Value::Timestamp(ts) => {
                                let timestamp = ts.to_naive_datetime().and_utc();
                                debug!(
                                    "Latest data time for server {} config {}: {}",
                                    server_id, config_id, timestamp
                                );
                                Ok(Some(timestamp))
                            }
                            taos::Value::Null(_) => {
                                debug!(
                                    "No data found for server {} config {}",
                                    server_id, config_id
                                );
                                Ok(None)
                            }
                            _ => {
                                warn!("Unexpected value type for timestamp: {:?}", value);
                                Ok(None)
                            }
                        }
                    } else {
                        debug!(
                            "No data found for server {} config {}",
                            server_id, config_id
                        );
                        Ok(None)
                    }
                } else {
                    debug!(
                        "No data found for server {} config {}",
                        server_id, config_id
                    );
                    Ok(None)
                }
            }
            Err(e) => {
                warn!(
                    "Failed to query latest data time for server {} config {}: {}",
                    server_id, config_id, e
                );
                Ok(None)
            }
        },
        Err(e) => {
            warn!("Failed to get TDengine connection: {}", e);
            Ok(None)
        }
    }
}

/// 从Redis获取上次生成时间（独立函数版本）
async fn get_last_generation_time_from_redis(
    app_state: &AppContext,
    server_id: i32,
    config_id: i32,
) -> Result<Option<DateTime<Utc>>> {
    let cache_key = format!("chart_last_generated:{}:{}", server_id, config_id);

    match app_state.redis.get().await {
        Ok(mut conn) => match conn.get::<_, String>(&cache_key).await {
            Ok(timestamp_str) => match timestamp_str.parse::<DateTime<Utc>>() {
                Ok(timestamp) => {
                    debug!(
                        "Last generation time for server {} config {}: {}",
                        server_id, config_id, timestamp
                    );
                    Ok(Some(timestamp))
                }
                Err(e) => {
                    warn!("Failed to parse timestamp {}: {}", timestamp_str, e);
                    Ok(None)
                }
            },
            Err(_) => {
                debug!(
                    "No generation time found for server {} config {}",
                    server_id, config_id
                );
                Ok(None)
            }
        },
        Err(e) => {
            warn!("Failed to get Redis connection: {}", e);
            Ok(None)
        }
    }
}

/// 更新Redis中的生成时间戳（独立函数版本）
async fn update_generation_timestamp(
    app_state: &AppContext,
    server_id: i32,
    config_id: i32,
) -> Result<()> {
    let cache_key = format!("chart_last_generated:{}:{}", server_id, config_id);
    let timestamp = Utc::now().to_rfc3339();

    match app_state.redis.get().await {
        Ok(mut conn) => {
            // 设置24小时过期时间，避免无限制累积
            conn.set_ex::<_, _, ()>(&cache_key, timestamp, 86400)
                .await
                .map_err(|e| anyhow!("Failed to update generation timestamp: {}", e))?;

            debug!(
                "Updated generation timestamp for server {} config {}",
                server_id, config_id
            );
            Ok(())
        }
        Err(e) => Err(anyhow!("Failed to get Redis connection: {}", e)),
    }
}

/// 将图表缓存到 Redis
async fn cache_chart_to_redis(
    app_state: &AppContext,
    config: &ChartSchedulerConfig,
    server_id: i32,
    config_id: i32,
    time_range: &str,
    target_address: &str,
    display_name: &str,
    image_data: &[u8],
) -> Result<()> {
    let cache_key = format!("latency_chart:{}:{}:{}", server_id, config_id, time_range);
    let now = Utc::now();
    let expires_at = now + chrono::Duration::seconds(config.cache_ttl_seconds as i64);

    let cached_chart = CachedChart {
        server_id,
        config_id,
        time_range: time_range.to_string(),
        target_address: target_address.to_string(),
        display_name: display_name.to_string(),
        image_data: base64::engine::general_purpose::STANDARD.encode(image_data),
        generated_at: now,
        expires_at,
    };

    let cached_data = serde_json::to_string(&cached_chart)
        .map_err(|e| anyhow!("Failed to serialize cached chart: {}", e))?;

    match app_state.redis.get().await {
        Ok(mut conn) => {
            conn.set_ex::<_, _, ()>(&cache_key, cached_data, config.cache_ttl_seconds as usize)
                .await
                .map_err(|e| anyhow!("Failed to cache chart: {}", e))?;

            debug!("Cached chart: {}", cache_key);
            Ok(())
        }
        Err(e) => Err(anyhow!("Failed to get Redis connection: {}", e)),
    }
}

/// 清理过期数据
async fn cleanup_expired_data(
    rrd_client: Arc<RrdClient>,
    app_state: Arc<AppContext>,
) -> Result<()> {
    // 获取所有活跃的配置
    let active_configs = app_state
        .db
        .latency_test_config()
        .find_many(vec![])
        .select(latency_test_config::select!({ server_id id }))
        .exec()
        .await
        .map_err(|e| anyhow!("Failed to fetch active configs: {}", e))?;

    let active_pairs: Vec<(i32, i32)> = active_configs
        .into_iter()
        .map(|config| (config.server_id, config.id))
        .collect();

    // 清理过期的 RRD 文件
    rrd_client.cleanup_expired_rrd_files(&active_pairs).await?;

    info!(
        "Cleanup completed for {} active configurations",
        active_pairs.len()
    );
    Ok(())
}
