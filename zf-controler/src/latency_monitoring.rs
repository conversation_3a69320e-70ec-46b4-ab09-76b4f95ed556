use std::{collections::HashMap, sync::Arc, time::Duration};

use anyhow::{anyhow, Result};
use common::{
    app_message::{
        LatencyMonitorConfig as CommonLatencyMonitorConfig, LatencyTestResult,
        WorkerLatencyMonitoringRequest,
    },
    batch_writer::BatchWriter,
    reset_traffic_task::RedisPool,
    stats::{LatencyTestMeasurement, CachedLatencyRealtimeStatus},
};
use log::{error, info, warn};
use redis::AsyncCommands;
use tokio::{sync::oneshot, task::Join<PERSON><PERSON><PERSON>, time::interval};

use crate::{prisma::latency_test_config, rrd_client::RrdClient, AppContext, CommandWrapper, WorkerCommand, worker::worker_identifier_from_id};

pub fn from_latency_test_config(config: &latency_test_config::Data) -> CommonLatencyMonitorConfig {
    CommonLatencyMonitorConfig {
        config_id: config.id,
        server_id: config.server_id,
        target_address: config.target_address.clone(),
        target_port: config.target_port.map(|p| p as u16),
        test_type: config.test_type.clone(),
        packet_size: config.packet_size,
        timeout: config.timeout as u32,
        interval: config.interval as u32,
        display_name: config.display_name.clone(),
        alert_threshold: config.alert_threshold as u32,
    }
}

pub struct LatencyMonitoringService {
    ctx: Arc<AppContext>,
}

impl LatencyMonitoringService {
    pub fn new(ctx: Arc<AppContext>) -> Self {
        Self { ctx }
    }

    /// 启动延迟监控服务
    pub async fn start(self) -> Result<JoinHandle<()>> {
        info!("Starting latency monitoring service...");

        // 每5分钟秒检查一次配置更新并分发任务
        let mut check_interval = interval(Duration::from_secs(300));
        let ctx = self.ctx.clone();

        let handle = tokio::spawn(async move {
            loop {
                check_interval.tick().await;
                if let Err(e) = Self::check_and_distribute_configs(ctx.clone()).await {
                    error!("Failed to check and distribute latency configs: {}", e);
                }
            }
        });

        Ok(handle)
    }

    /// 检查配置更新并分发给workers
    pub async fn check_and_distribute_configs(ctx: Arc<AppContext>) -> Result<()> {
        // 获取所有启用的延迟测试配置
        let configs = ctx
            .db
            .latency_test_config()
            .find_many(vec![latency_test_config::is_enabled::equals(true)])
            .exec()
            .await
            .map_err(|e| anyhow!("Failed to fetch latency configs: {}", e))?;

        // 按server_id分组配置
        let mut server_configs: HashMap<i32, Vec<CommonLatencyMonitorConfig>> = HashMap::new();
        for config in configs {
            let monitor_config = from_latency_test_config(&config);
            server_configs
                .entry(config.server_id)
                .or_insert_with(Vec::new)
                .push(monitor_config);
        }

        // 为每个server分发配置
        for (server_id, configs) in server_configs {
            if let Err(e) =
                Self::distribute_configs_to_worker(ctx.clone(), server_id, configs).await
            {
                error!(
                    "Failed to distribute configs to server {}: {}",
                    server_id, e
                );
            }
        }

        Ok(())
    }

    pub async fn refresh_configs_to_worker(ctx: Arc<AppContext>, server_id: i32) -> Result<()> {
        // 获取所有启用的延迟测试配置
        let configs = ctx
            .db
            .latency_test_config()
            .find_many(vec![
                latency_test_config::is_enabled::equals(true),
                latency_test_config::server_id::equals(server_id),
            ])
            .exec()
            .await
            .map_err(|e| anyhow!("Failed to fetch latency configs: {}", e))?;

        // 按server_id分组配置

        let configs = configs
            .iter()
            .map(|c| from_latency_test_config(c))
            .collect::<Vec<_>>();

        Self::distribute_configs_to_worker(ctx, server_id, configs).await?;
        Ok(())
    }

    /// 向特定worker分发延迟监控配置
    async fn distribute_configs_to_worker(
        ctx: Arc<AppContext>,
        server_id: i32,
        configs: Vec<CommonLatencyMonitorConfig>,
    ) -> Result<()> {
        let (tx, rx) = oneshot::channel();

        // 检查worker是否在线（worker_command_tx使用server_id作为key）
        if let Some(worker_tx) = ctx.worker_command_tx.lock().await.get(&server_id) {
            let request = WorkerLatencyMonitoringRequest {
                worker_id: server_id.to_string(),
                configs: configs.clone(),
            };

            if let Err(_) = worker_tx
                .send(WorkerCommand::UpdateLatencyMonitoringConfig(
                    CommandWrapper::new(request, tx),
                ))
                .await
            {
                warn!(
                    "Failed to send latency monitoring config to {}",
                    worker_identifier_from_id(server_id)
                );
                return Ok(());
            }

            // 等待worker响应
            match tokio::time::timeout(Duration::from_secs(10), rx).await {
                Ok(Ok(_)) => {
                    info!(
                        "Successfully distributed {} latency configs to server {}",
                        configs.len(),
                        server_id
                    );
                }
                Ok(Err(e)) => {
                    error!(
                        "Worker {} rejected latency monitoring config: {}",
                        server_id, e
                    );
                }
                Err(_) => {
                    warn!(
                        "Timeout waiting for latency monitoring config response from {}",
                        worker_identifier_from_id(server_id)
                    );
                }
            }
        } else {
            info!(
                "Server {} is offline, skipping config distribution",
                server_id
            );
        }

        Ok(())
    }

    /// 存储延迟测试结果到TDengine和RRD
    pub async fn store_latency_results(
        ctx: &Arc<AppContext>,
        batch_writer: &BatchWriter,
        redis_pool: &RedisPool,
        rrd_client: &Arc<RrdClient>,
        results: Vec<LatencyTestResult>,
    ) -> Result<()> {
        if results.is_empty() {
            return Ok(());
        }
        let len = results.len();
        
        // 收集所有需要的config_id用于批量查询
        let config_ids: Vec<i32> = results.iter().map(|r| r.config_id).collect();
        
        // 批量查询配置信息
        let configs = ctx
            .db
            .latency_test_config()
            .find_many(vec![latency_test_config::id::in_vec(config_ids)])
            .exec()
            .await
            .map_err(|e| anyhow!("Failed to fetch latency configs: {}", e))?;
        
        let config_map: HashMap<i32, &latency_test_config::Data> = 
            configs.iter().map(|c| (c.id, c)).collect();
        
        for result in results {
            let measurement = LatencyTestMeasurement {
                time: result.timestamp,
                server_id: result.server_id,
                config_id: result.config_id,
                target_address: result.target_address.clone(),
                
                // 从 SmokePing风格数据转换为兼容格式
                latency_us: result.avg_latency_us.or(result.median_latency_us).unwrap_or(65535000) as i64,
                success: result.loss_count < 20, // SmokePing风格: 非全丢包即成功
                error_msg: result.error_msg.clone().unwrap_or_default(),
                
                // SmokePing统计数据
                test_round: result.test_round.unwrap_or(1),
                samples_count: result.total_samples as i32,
                min_latency_us: result.min_latency_us.map(|x| x as i64),
                max_latency_us: result.max_latency_us.map(|x| x as i64),
                median_latency_us: result.median_latency_us.map(|x| x as i64),
                stddev_latency_us: result.stddev_latency_us,
                packet_loss_rate: (result.loss_count as f32 / 20.0) * 100.0, // 转换为百分比
            };

            if let Err(e) = batch_writer.write_latency_test(measurement).await {
                error!("Failed to write latency result to batch writer: {}", e);
                return Err(anyhow!("Failed to write latency result: {}", e));
            }
            
            // 同时更新Redis缓存
            if let Some(config) = config_map.get(&result.config_id) {
                if let Ok(mut conn) = redis_pool.get().await {
                    let cache_key = format!("latency_realtime:{}:{}", result.server_id, result.config_id);
                    let cache_data = CachedLatencyRealtimeStatus {
                        config_id: result.config_id,
                        target_address: result.target_address.clone(),
                        display_name: config.display_name.clone(),
                        test_type: config.test_type.clone(),
                        
                        // SmokePing风格字段
                        loss_count: result.loss_count,
                        median_latency_us: result.median_latency_us,
                        avg_latency_us: result.avg_latency_us,
                        
                        // 兼容性字段
                        error_msg: result.error_msg.clone(),
                        last_test_time: Some(result.timestamp),
                    };
                    
                    if let Ok(json_str) = serde_json::to_string(&cache_data) {
                        if let Err(e) = conn.set_ex::<_, _, ()>(&cache_key, json_str, 300).await {
                            error!("Failed to cache latency result for server {} config {}: {}", 
                                   result.server_id, result.config_id, e);
                        }
                    }
                }
            }
            
            // 同时写入RRD，使用SmokePing风格转换
            let rrd_data_point = crate::rrd_client::RrdClient::convert_latency_result_to_rrd_data_point(&result);
            
            if let Err(e) = rrd_client.update_rrd_data(result.server_id, result.config_id, &rrd_data_point).await {
                warn!("Failed to update RRD data for server {} config {}: {}", 
                      result.server_id, result.config_id, e);
                // 不返回错误，因为RRD写入失败不应该影响主要的数据存储流程
            }
        }

        info!("Successfully queued {} latency results for storage", len);
        Ok(())
    }
}
