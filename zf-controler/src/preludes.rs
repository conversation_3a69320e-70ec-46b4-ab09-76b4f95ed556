pub use crate::prisma::*;
use crate::redis_lock::DistributedLock;
use crate::retry_queue::BoxedRetryQueue;
pub use anyhow::{bail, Error, Result};
use app_message::{
    WorkerGetConnectionStatsRequest, WorkerGetConnectionStatsResponse,
    WorkerLatencyMonitoringConfigResponse, WorkerLatencyMonitoringRequest,
    WorkerTestLatencyRequest, WorkerTestLatencyResponse,
};
use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use cache::Cache;
use clap::Parser;
pub use common::crypto::*;
pub use common::*;
use common::{batch_writer::BatchWriter, tdengine_init::TaosClient};
pub use log::{debug, error, info, trace, warn};
pub use primitive_types::{U128, U256};
pub use redis::AsyncCommands;
use std::collections::{HashMap, HashSet};
pub use std::str::FromStr;
use std::sync::atomic::AtomicU64;
pub use std::sync::Arc;
pub use tokio::sync::mpsc;
use tokio::sync::{Mutex, RwLock};

pub struct CommandWrapper<T, R> {
    pub command: T,
    pub rst_tx: tokio::sync::oneshot::Sender<anyhow::Result<R>>,
}
impl<T, R> CommandWrapper<T, R> {
    pub fn new(command: T, rst_tx: tokio::sync::oneshot::Sender<anyhow::Result<R>>) -> Self {
        Self { command, rst_tx }
    }
}

pub enum WorkerCommand {
    TestLatency(CommandWrapper<WorkerTestLatencyRequest, WorkerTestLatencyResponse>),
    UpdateLatencyMonitoringConfig(
        CommandWrapper<WorkerLatencyMonitoringRequest, WorkerLatencyMonitoringConfigResponse>,
    ),
    GetConnectionStats(
        CommandWrapper<WorkerGetConnectionStatsRequest, WorkerGetConnectionStatsResponse>,
    ),
    ConfigUpdated,
}
pub struct AppContext {
    pub opt: Opt,
    pub worker_version: String,
    pub db: AppPrismaClient,
    pub tdengine: Arc<TaosClient>,
    pub batch_writer: BatchWriter,
    pub redis: RedisPool,
    pub mgmt_priv_key: SecretKey,
    pub black_worker_list: Arc<Mutex<HashSet<String>>>,
    pub cached_endpoint_list: Arc<Cache<i32, outbound_endpoint::Data>>,
    pub cached_forward_endpoint_list: Arc<Cache<i32, forward_endpoint::Data>>,
    pub cached_worker_ports_list: Arc<Cache<i32, Vec<crate::worker::UpdateTrafficData::Data>>>,
    pub cached_worker_forward_ports_list:
        Arc<Cache<i32, Vec<crate::worker::UpdateForwardEndpointTrafficData::Data>>>,
    pub cached_package_list: Arc<Cache<i32, package::Data>>, // package_id -> package_data
    pub cached_subscription_list: Arc<Cache<i32, subscription::Data>>, // subscription_id -> subscription_data
    pub worker_command_tx: Arc<Mutex<HashMap<i32, mpsc::Sender<WorkerCommand>>>>,
    pub request_id: Arc<AtomicU64>,
    pub traffic_retry_queue: Arc<BoxedRetryQueue>,
    pub redis_lock: Arc<DistributedLock>,
    pub redis_line_lock: RedisLineLock,
    pub entitlements: Arc<RwLock<Option<Entitlements>>>,
    pub auth_client: Option<Arc<zf_auth_client::WebSocketAuthClient>>,
    pub rrd_client: Option<Arc<crate::rrd_client::RrdClient>>,
}

pub async fn setup_redis(opt: &Opt) -> Result<RedisPool> {
    let manager = RedisConnectionManager::new(opt.redis_path.as_str())?;
    Ok(Pool::builder().build(manager).await?)
}

pub type WrappedAppContext = Arc<AppContext>;

pub type AppPrismaClient = Arc<PrismaClient>;
pub type RedisPool = Pool<RedisConnectionManager>;

#[derive(Debug, Parser, Clone)]
pub struct Opt {
    #[clap(short = 'p', long, env, default_value_t = 3100)]
    pub mgmt_port: u16,
    #[clap(
        short,
        long,
        env,
        default_value = "postgres://postgres:postgres@127.0.0.1/datium"
    )]
    pub db_path: String,
    #[clap(short, long, env, default_value = "redis://127.0.0.1:6379")]
    pub redis_path: String,
    #[clap(short = 'k', long, env = "MGMT_ARRANGER_PRIV_KEY")]
    pub mgmt_priv_key: String,
    #[clap(short, long, env, default_value = "taos+ws://127.0.0.1:6030")]
    pub tdengine_url: String,
    #[clap(long, env, default_value = "zfc")]
    pub tdengine_db: String,
    #[clap(short, long, env, default_value = "./worker/zf-worker")]
    pub worker_binary_path: String,
    #[clap(long, env, default_value = "./forwarder/forwarder-server")]
    pub forwarder_binary_path: String,
    #[clap(short, long, env, default_value = "https://127.0.0.1:3100")]
    pub arranger_hosts_url: String,

    // Licensing configuration
    #[clap(long, env = "ZFC_INSTANCE_ID")]
    pub zfc_instance_id: Option<String>,

    #[clap(
        long,
        env = "ZFC_AUTH_SERVER_URL",
        default_value = "https://zf-license.luny60.top"
    )]
    pub zfc_auth_server_url: Option<String>,

    #[clap(long, env = "ZFC_API_KEY")]
    pub zfc_api_key: Option<String>,

    // RRD service configuration
    #[clap(long, env = "RRD_SERVICE_URL", default_value = "http://localhost:8082")]
    pub rrd_service_url: String,

    #[clap(
        long,
        env = "RRD_GRAPH_GENERATION_INTERVAL_MINUTES",
        default_value = "2"
    )]
    pub rrd_graph_generation_interval_minutes: u64,

    #[clap(long, env = "RRD_GRAPH_CACHE_TTL_SECONDS", default_value = "600")]
    pub rrd_graph_cache_ttl_seconds: u64,

    #[clap(long, env = "RRD_MAX_CONCURRENT_REQUESTS", default_value = "8")]
    pub rrd_max_concurrent_requests: usize,
}

impl Opt {
    /// Get the worker version by executing the worker binary with --version flag
    pub async fn get_worker_version(&self) -> Result<String> {
        use tokio::process::Command;

        let output = Command::new(&self.worker_binary_path)
            .arg("--version")
            .output()
            .await?;

        if !output.status.success() {
            anyhow::bail!(
                "Failed to get worker version: {}",
                String::from_utf8_lossy(&output.stderr)
            );
        }

        let version_output = String::from_utf8(output.stdout)?;
        // The output format should be like "zf-worker 0.1.88"
        // Extract just the version number
        let version = version_output
            .trim()
            .split_whitespace()
            .last()
            .unwrap_or(&version_output.trim())
            .to_string();

        Ok(version)
    }

    /// Get the forwarder version by executing the forwarder binary with --version flag
    pub async fn get_forwarder_version(&self) -> Result<String> {
        use tokio::process::Command;

        let output = Command::new(&self.forwarder_binary_path)
            .arg("--version")
            .output()
            .await?;

        if !output.status.success() {
            anyhow::bail!(
                "Failed to get forwarder version: {}",
                String::from_utf8_lossy(&output.stderr)
            );
        }

        let version_output = String::from_utf8(output.stdout)?;
        // The output format should be like "forwarder-server 0.1.0"
        // Extract just the version number
        let version = version_output
            .trim()
            .split_whitespace()
            .last()
            .unwrap_or(&version_output.trim())
            .to_string();

        Ok(version)
    }
}
