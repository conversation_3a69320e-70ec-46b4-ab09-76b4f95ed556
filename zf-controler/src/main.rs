#![recursion_limit = "256"]
use common::license_message::Entitlements;
use tokio::sync::RwLock;
use zf_auth_client::{
    download_forwarder_binary, get_recommended_forwarder_version, get_recommended_worker_version,
};
use zf_controler::{chart_scheduler::ChartSchedulerConfig, *};

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    let opt = Opt::parse();

    // Initialize licensing if configured
    let (auth_client, initial_entitlements) = if let (
        Some(instance_id),
        Some(auth_server_url),
        Some(api_key),
    ) = (
        &opt.zfc_instance_id,
        &opt.zfc_auth_server_url,
        &opt.zfc_api_key,
    ) {
        info!("Initializing licensing for instance: {}", instance_id);
        let auth_config = AuthConfig {
            instance_id: instance_id.clone(),
            server_url: auth_server_url.clone(),
        };

        match initialize_auth(auth_config, instance_id.clone(), api_key.clone()).await {
            Ok((auth_client, entitlements)) => {
                info!("Licensing initialized successfully");
                (Some(auth_client), Some(entitlements))
            }
            Err(e) => {
                error!("Failed to initialize licensing: {}", e);
                error!("This instance is not licensed to run. Exiting.");
                std::process::exit(1);
            }
        }
    } else {
        warn!("No licensing configuration found (ZFC_INSTANCE_ID, ZFC_AUTH_SERVER_URL, and ZFC_API_KEY not set)");
        warn!("This instance is not licensed to run. Exiting.");
        std::process::exit(1);
    };

    let mgmt_priv_key = parse_secret_key(&opt.mgmt_priv_key)?;

    let db = Arc::new(prisma::new_client_with_url(&opt.db_path).await?);
    let redis = setup_redis(&opt).await?;
    let tdengine = setup_tdengine(&opt.tdengine_url, &opt.tdengine_db).await?;
    let batch_writer = BatchWriter::new(Arc::new((*tdengine).clone()));

    // 检查worker二进制文件版本匹配
    let mut need_download = false;
    let controller_version = env!("CARGO_PKG_VERSION");

    if let (Some(instance_id), Some(auth_server_url), Some(api_key)) = (
        &opt.zfc_instance_id,
        &opt.zfc_auth_server_url,
        &opt.zfc_api_key,
    ) {
        let auth_config = AuthConfig {
            instance_id: instance_id.clone(),
            server_url: auth_server_url.clone(),
        };

        // 查询推荐的worker版本
        info!(
            "Checking recommended worker version for controller v{}",
            controller_version
        );

        match get_recommended_worker_version(
            &auth_config,
            instance_id.clone(),
            api_key.clone(),
            controller_version.to_string(),
        )
        .await
        {
            Ok(recommended_version) => {
                info!("Server recommends worker version: {}", recommended_version);

                // 检查worker二进制文件是否存在
                if !tokio::fs::try_exists(&opt.worker_binary_path)
                    .await
                    .unwrap_or(false)
                {
                    info!("Worker binary not found at: {}", opt.worker_binary_path);
                    need_download = true;
                } else {
                    // 检查现有worker版本是否匹配
                    match opt.get_worker_version().await {
                        Ok(current_worker_version) => {
                            info!("Current worker version: {}", current_worker_version);
                            if current_worker_version != recommended_version {
                                info!(
                                    "Worker version mismatch: current={}, recommended={}. Will download new version.",
                                    current_worker_version, recommended_version
                                );
                                need_download = true;
                            } else {
                                info!(
                                    "Worker version matches recommendation: {}",
                                    current_worker_version
                                );
                            }
                        }
                        Err(e) => {
                            warn!("Failed to get current worker version: {}. Will download new version.", e);
                            need_download = true;
                        }
                    }
                }

                // 如果需要下载，执行下载
                if need_download {
                    info!("Downloading worker binary version: {}", recommended_version);

                    match download_worker_binary(
                        &auth_config,
                        instance_id.clone(),
                        api_key.clone(),
                        opt.worker_binary_path.clone(),
                        Some(controller_version.to_string()),
                    )
                    .await
                    {
                        Ok(()) => {
                            info!("Worker binary downloaded successfully to: {} (v{} for controller v{})", 
                                   opt.worker_binary_path, recommended_version, controller_version);
                        }
                        Err(e) => {
                            error!("Failed to download worker binary: {}", e);
                            error!("Cannot proceed without worker binary. Please ensure the binary exists at {} or configure proper authentication.", opt.worker_binary_path);
                            std::process::exit(1);
                        }
                    }
                }
            }
            Err(e) => {
                error!("Failed to query recommended worker version: {}", e);

                // 如果版本查询失败，但worker存在，继续运行
                if tokio::fs::try_exists(&opt.worker_binary_path)
                    .await
                    .unwrap_or(false)
                {
                    warn!("Using existing worker binary despite version query failure");
                } else {
                    error!("No worker binary found and version query failed. Cannot proceed.");
                    std::process::exit(1);
                }
            }
        }
    } else {
        // 没有认证配置时的处理逻辑
        if !tokio::fs::try_exists(&opt.worker_binary_path)
            .await
            .unwrap_or(false)
        {
            error!("Worker binary not found at: {}", opt.worker_binary_path);
            error!("No authentication configuration found to download worker binary.");
            error!("Please either:");
            error!(
                "  1. Place the zf-worker binary at: {}",
                opt.worker_binary_path
            );
            error!("  2. Configure ZFC_INSTANCE_ID, ZFC_AUTH_SERVER_URL, and ZFC_API_KEY for automatic download and version matching");
            std::process::exit(1);
        } else {
            warn!("No licensing configuration found. Using existing worker binary without version verification.");
        }
    }

    // 获取worker版本
    let worker_version = opt.get_worker_version().await?;
    info!("Worker version: {}", worker_version);

    // 检查forwarder二进制文件版本匹配
    let mut need_forwarder_download = false;

    if let (Some(instance_id), Some(auth_server_url), Some(api_key)) = (
        &opt.zfc_instance_id,
        &opt.zfc_auth_server_url,
        &opt.zfc_api_key,
    ) {
        let auth_config = AuthConfig {
            instance_id: instance_id.clone(),
            server_url: auth_server_url.clone(),
        };

        // 查询推荐的forwarder版本
        info!(
            "Checking recommended forwarder version for controller v{}",
            controller_version
        );

        match get_recommended_forwarder_version(
            &auth_config,
            instance_id.clone(),
            api_key.clone(),
            controller_version.to_string(),
        )
        .await
        {
            Ok(recommended_version) => {
                info!(
                    "Server recommends forwarder version: {}",
                    recommended_version
                );

                // 检查forwarder二进制文件是否存在
                if !tokio::fs::try_exists(&opt.forwarder_binary_path)
                    .await
                    .unwrap_or(false)
                {
                    info!(
                        "Forwarder binary not found at: {}",
                        opt.forwarder_binary_path
                    );
                    need_forwarder_download = true;
                } else {
                    // 检查现有forwarder版本是否匹配
                    match opt.get_forwarder_version().await {
                        Ok(current_forwarder_version) => {
                            info!("Current forwarder version: {}", current_forwarder_version);
                            if current_forwarder_version != recommended_version {
                                info!(
                                    "Forwarder version mismatch: current={}, recommended={}. Will download new version.",
                                    current_forwarder_version, recommended_version
                                );
                                need_forwarder_download = true;
                            } else {
                                info!(
                                    "Forwarder version matches recommendation: {}",
                                    current_forwarder_version
                                );
                            }
                        }
                        Err(e) => {
                            warn!("Failed to get current forwarder version: {}. Will download new version.", e);
                            need_forwarder_download = true;
                        }
                    }
                }

                // 如果需要下载，执行下载
                if need_forwarder_download {
                    info!(
                        "Downloading forwarder binary version: {}",
                        recommended_version
                    );

                    match download_forwarder_binary(
                        &auth_config,
                        instance_id.clone(),
                        api_key.clone(),
                        opt.forwarder_binary_path.clone(),
                        Some(controller_version.to_string()),
                    )
                    .await
                    {
                        Ok(()) => {
                            info!("Forwarder binary downloaded successfully to: {} (v{} for controller v{})", 
                                   opt.forwarder_binary_path, recommended_version, controller_version);
                        }
                        Err(e) => {
                            error!("Failed to download forwarder binary: {}", e);
                            error!("Cannot proceed without forwarder binary. Please ensure the binary exists at {} or configure proper authentication.", opt.forwarder_binary_path);
                            std::process::exit(1);
                        }
                    }
                }
            }
            Err(e) => {
                error!("Failed to query recommended forwarder version: {}", e);

                // 如果版本查询失败，但forwarder存在，继续运行
                if tokio::fs::try_exists(&opt.forwarder_binary_path)
                    .await
                    .unwrap_or(false)
                {
                    warn!("Using existing forwarder binary despite version query failure");
                } else {
                    error!("No forwarder binary found and version query failed. Cannot proceed.");
                    std::process::exit(1);
                }
            }
        }
    } else {
        // 没有认证配置时的处理逻辑
        if !tokio::fs::try_exists(&opt.forwarder_binary_path)
            .await
            .unwrap_or(false)
        {
            error!(
                "Forwarder binary not found at: {}",
                opt.forwarder_binary_path
            );
            error!("No authentication configuration found to download forwarder binary.");
            error!("Please either:");
            error!(
                "  1. Place the forwarder-server binary at: {}",
                opt.forwarder_binary_path
            );
            error!("  2. Configure ZFC_INSTANCE_ID, ZFC_AUTH_SERVER_URL, and ZFC_API_KEY for automatic download and version matching");
            std::process::exit(1);
        } else {
            warn!("No licensing configuration found. Using existing forwarder binary without version verification.");
        }
    }

    // 获取forwarder版本
    let forwarder_version = opt.get_forwarder_version().await?;
    info!("Forwarder version: {}", forwarder_version);

    // 创建RRD客户端
    let rrd_client = Arc::new(RrdClient::new(opt.rrd_service_url.clone()));
    let db_clone = db.clone();
    let db_clone1 = db.clone();
    let db_clone2 = db.clone();
    let db_clone3 = db.clone();
    let db_clone4 = db.clone();
    let db_clone5 = db.clone();
    let redis_lock = Arc::new(DistributedLock::new(redis.clone(), "traffic_lock", 5000));
    // Set up entitlements update channel
    let (entitlements_tx, mut entitlements_rx) = mpsc::unbounded_channel::<Entitlements>();

    let ctx =
        Arc::new(
            AppContext {
                opt,
                worker_version,
                db,
                redis,
                tdengine: Arc::new(tdengine),
                batch_writer,
                mgmt_priv_key,
                black_worker_list: Arc::new(Mutex::new(HashSet::new())),
                worker_command_tx: Arc::new(Mutex::new(HashMap::new())),
                request_id: Arc::new(AtomicU64::new(0)),
                traffic_retry_queue: Arc::new(RetryQueue::new()),
                // forward_traffic_retry_queue: Arc::new(RetryQueue::new()),
                redis_lock,
                redis_line_lock: Arc::new(Mutex::new(HashMap::new())),
                entitlements: Arc::new(RwLock::new(initial_entitlements)),
                auth_client,
                rrd_client: Some(rrd_client.clone()),
                cached_endpoint_list: Arc::new(Cache::new(
                    Box::new(
                        move |end_point_id: &i32| -> BoxFuture<
                            'static,
                            Result<outbound_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone1 = db_clone.clone();
                            let end_point_id_clone = end_point_id.clone();
                            Box::pin(async move {
                                let end_point_data = db_clone1
                                    .outbound_endpoint()
                                    .find_unique(outbound_endpoint::id::equals(end_point_id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("endpoint not found"))?;
                                Ok(end_point_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<outbound_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_forward_endpoint_list: Arc::new(Cache::new(
                    Box::new(
                        move |id: &i32| -> BoxFuture<
                            'static,
                            Result<forward_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone_fwd = db_clone1.clone();
                            let id_clone = id.clone();
                            Box::pin(async move {
                                let forward_endpoint_data = db_clone_fwd
                                    .forward_endpoint()
                                    .find_unique(forward_endpoint::id::equals(id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("forward endpoint not found"))?;
                                Ok(forward_endpoint_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<forward_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_package_list:
                    Arc::new(
                        Cache::new(
                            Box::new(
                                move |package_id: &i32| -> BoxFuture<
                                    'static,
                                    Result<package::Data, anyhow::Error>,
                                > {
                                    let db_clone_pkg = db_clone4.clone();
                                    let package_id = package_id.clone();
                                    Box::pin(async move {
                                        let package_data = db_clone_pkg
                                            .package()
                                            .find_unique(package::id::equals(package_id))
                                            .with(package::package_lines::fetch(vec![]))
                                            .exec()
                                            .await?
                                            .ok_or_else(|| anyhow::anyhow!("package not found"))?;
                                        Ok(package_data)
                                    })
                                },
                            )
                                as Box<
                                    dyn Fn(
                                            &i32,
                                        ) -> BoxFuture<
                                            'static,
                                            Result<package::Data, anyhow::Error>,
                                        > + Send
                                        + Sync
                                        + 'static,
                                >,
                            std::time::Duration::from_secs(30),
                        ),
                    ),
                cached_subscription_list: Arc::new(
                    Cache::new(
                        Box::new(
                            move |subscription_id: &i32| -> BoxFuture<
                                'static,
                                Result<subscription::Data, anyhow::Error>,
                            > {
                                let db_clone_sub = db_clone5.clone();
                                let subscription_id_clone = subscription_id.clone();
                                Box::pin(async move {
                                    let subscription_data = db_clone_sub
                                        .subscription()
                                        .find_unique(subscription::id::equals(
                                            subscription_id_clone,
                                        ))
                                        .exec()
                                        .await?
                                        .ok_or_else(|| anyhow::anyhow!("subscription not found"))?;
                                    Ok(subscription_data)
                                })
                            },
                        )
                            as Box<
                                dyn Fn(
                                        &i32,
                                    ) -> BoxFuture<
                                        'static,
                                        Result<subscription::Data, anyhow::Error>,
                                    > + Send
                                    + Sync
                                    + 'static,
                            >,
                        std::time::Duration::from_secs(30),
                    ),
                ),
                cached_worker_ports_list: Arc::new(Cache::new(
                    Box::new(
                        move |worker_id: &i32| -> BoxFuture<
                            'static,
                            Result<Vec<worker::UpdateTrafficData::Data>, anyhow::Error>,
                        > {
                            let db_clone_ports = db_clone2.clone();
                            let worker_id_clone = worker_id.clone();
                            Box::pin(async move {
                                let ports_data = db_clone_ports
                                    .port()
                                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(
                                        Some(worker_id_clone),
                                    )])
                                    .select(worker::UpdateTrafficData::select())
                                    .exec()
                                    .await?;
                                Ok(ports_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<Vec<worker::UpdateTrafficData::Data>, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_worker_forward_ports_list: Arc::new(Cache::new(
                    Box::new(
                        move |worker_id: &i32| -> BoxFuture<
                            'static,
                            Result<
                                Vec<worker::UpdateForwardEndpointTrafficData::Data>,
                                anyhow::Error,
                            >,
                        > {
                            let db_clone_forward_ports = db_clone3.clone();
                            let worker_id_clone = worker_id.clone();
                            Box::pin(async move {
                                let ports_data = db_clone_forward_ports
                                    .port()
                                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(
                                        Some(worker_id_clone),
                                    )])
                                    .select(worker::UpdateForwardEndpointTrafficData::select())
                                    .exec()
                                    .await?;
                                Ok(ports_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<
                                        Vec<worker::UpdateForwardEndpointTrafficData::Data>,
                                        anyhow::Error,
                                    >,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
            },
        );

    // Set up entitlements update monitoring if auth client is available
    if let Some(ref auth_client) = ctx.auth_client {
        auth_client
            .set_entitlements_update_channel(entitlements_tx)
            .await;

        // Start entitlements update monitoring task
        let entitlements_arc = ctx.entitlements.clone();
        tokio::spawn(async move {
            while let Some(new_entitlements) = entitlements_rx.recv().await {
                info!("Received entitlements update: {:?}", new_entitlements);
                *entitlements_arc.write().await = Some(new_entitlements);
            }
            info!("Entitlements update monitoring task ended");
        });
    }

    let reset_traffic_ctx = Arc::new(ResetTrafficContext::new(
        ctx.db.clone(),
        ctx.redis.clone(),
        ctx.traffic_retry_queue.clone(),
        ctx.redis_lock.clone(),
        ctx.redis_line_lock.clone(),
        true,
    ));
    tokio::spawn(reset_traffic_background(reset_traffic_ctx.clone()));

    let mut chart_scheduler_config = ChartSchedulerConfig::default();
    chart_scheduler_config.generation_interval_minutes =
        ctx.opt.rrd_graph_generation_interval_minutes;
    chart_scheduler_config.cache_ttl_seconds = ctx.opt.rrd_graph_cache_ttl_seconds;
    chart_scheduler_config.max_concurrent_requests = ctx.opt.rrd_max_concurrent_requests;

    // 初始化图表调度器
    let chart_scheduler = Arc::new(ChartScheduler::with_config(
        rrd_client.clone(),
        ctx.clone(),
        chart_scheduler_config,
    ));

    // 启动图表调度器
    if let Err(e) = chart_scheduler.start().await {
        error!("Failed to start chart scheduler: {}", e);
    } else {
        info!("Chart scheduler started successfully");
    }

    let app_handle = tokio::spawn(app::start_app(ctx.clone(), reset_traffic_ctx.clone()));
    let latency_monitoring_service = LatencyMonitoringService::new(ctx.clone());
    let latency_monitoring_handle = latency_monitoring_service.start().await?;
    tokio::select! {
        ret = app_handle => {
            match ret {
                Ok(ret) => {
                    if let Err(e) = ret {
                        error!("app_handle: {:?}", e)
                    }
                }
                Err(e) => error!("app_handle: {:?}", e)
            }
        }
        _ = latency_monitoring_handle => {
            info!("Latency monitoring service exited");
        }
        ret = signal::ctrl_c() => {
            if let Err(e) = ret {
                error!("ctrl_c: {e}")
            }
        }
    }
    info!("Exiting...");
    Ok(())
}
