FROM rust:1.75 as builder

WORKDIR /app

# Copy workspace configuration
COPY Cargo.toml Cargo.lock ./
COPY rust-toolchain.toml ./

# Copy source code
COPY zf-controler ./zf-controler
COPY zf-auth-client ./zf-auth-client
COPY common ./common

# Build the controller
RUN cargo build --release --bin zf-controler

# Runtime image
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary
COPY --from=builder /app/target/release/zf-controler /usr/local/bin/zf-controler

# Create a non-root user
RUN useradd -r -s /bin/false controller

USER controller

CMD ["zf-controler"]