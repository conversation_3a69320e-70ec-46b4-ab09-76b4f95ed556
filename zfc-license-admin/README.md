# ZFC License Admin

A Vue.js admin panel for managing ZFC license system instances and renewal codes.

## Features

- **Dashboard Overview**: Monitor license system status with key metrics
- **Instance Management**: Create, view, edit, and delete license instances
- **Renewal Code Management**: Generate and manage license renewal codes
- **Bearer Token Authentication**: Secure admin access
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- Vue 3 with Composition API
- TypeScript
- Vite
- Element Plus UI Framework
- Pinia for state management
- Vue Router
- Axios for API calls

## Quick Start

### Prerequisites

- Node.js 16+ 
- npm or yarn

### Installation

1. Clone the repository and navigate to the admin folder:
```bash
cd zfc-license-admin
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
```bash
cp .env.example .env
```
Edit `.env` and set your API URL:
```
VITE_API_URL=http://your-api-server:8080
```

4. Start the development server:
```bash
npm run dev
```

The application will be available at http://localhost:5174

### Building for Production

```bash
npm run build
```

## Configuration

### Environment Variables

- `VITE_API_URL`: Base URL for the ZFC auth server API

### API Endpoints

The application expects the following API endpoints to be available:

#### Authentication
- `POST /admin/auth` - Login with admin token
- `GET /admin/auth/validate` - Validate current token

#### Dashboard
- `GET /admin/dashboard/stats` - Get dashboard statistics

#### Instances
- `GET /admin/instances` - List instances (with pagination and search)
- `POST /admin/instances` - Create new instance
- `PUT /admin/instances/:id` - Update instance
- `DELETE /admin/instances/:id` - Delete instance
- `POST /admin/instances/:id/renew` - Renew instance with code
- `POST /admin/instances/:id/force-expire` - Force expire current session

#### Renewal Codes
- `GET /admin/renewal-codes` - List renewal codes (with pagination and filters)
- `POST /admin/renewal-codes` - Generate new codes
- `DELETE /admin/renewal-codes/:id` - Delete unused code

## Usage

### Login

1. Open the application in your browser
2. Enter your admin bearer token
3. Click "Login"

### Dashboard

The dashboard shows:
- Active instances count
- Expired instances count  
- Instances nearing expiration (7 days)
- Available renewal codes count

### Instance Management

**Create Instance:**
1. Click "Create New Instance"
2. Enter instance name, duration, and entitlements (JSON)
3. Copy the generated Instance ID to send to customer

**Edit Instance:**
- Update instance name
- Edit entitlements using the JSON editor

**Renew Instance:**
1. Click "Renew" on an instance
2. Enter the customer's renewal code
3. The expiration date will be extended

**Force Expire Session:**
- Use when a customer needs to move their license to a new machine
- This clears the current session, allowing a new machine to connect

### Renewal Code Management

**Generate Codes:**
1. Click "Generate New Codes"
2. Set duration (days) and quantity
3. Copy the generated codes to distribute

**Filter Codes:**
- View all codes, only available codes, or only used codes

## Development

### Project Structure

```
src/
├── api/           # API service functions
├── components/    # Reusable Vue components
├── stores/        # Pinia stores for state management
├── types/         # TypeScript type definitions
├── views/         # Page components
├── router/        # Vue Router configuration
├── App.vue        # Root component
└── main.ts        # Application entry point
```

### Adding New Features

1. Define types in `src/types/`
2. Add API functions in `src/api/`
3. Create Pinia stores in `src/stores/`
4. Build components in `src/components/`
5. Add routes in `src/router/`

### Code Style

- Use TypeScript for type safety
- Follow Vue 3 Composition API patterns
- Use Element Plus components for consistency
- Implement responsive design for mobile support

## License

This project is part of the ZFC licensing system.