export interface ControllerInstance {
  id: string
  name: string
  licenseExpiresAt: string
  activatedAt?: string
  apiKey: string
  entitlements: Record<string, any>
  lastSeenAt?: string
  lastSeenIp?: string
  currentSessionId?: string
  createdAt: string
  updatedAt: string
  monthlyRate?: string // Monthly subscription rate
  setupFee?: string    // One-time setup fee
}

export enum LicenseStatus {
  PENDING = 'pending',
  ACTIVE = 'active', 
  EXPIRED = 'expired'
}

export interface RenewalCode {
  id: string
  code: string
  duration: number
  amount?: string // Renewal amount/price
  usedAt?: string
  usedBy?: string
  createdAt: string
}

export interface DashboardStats {
  activeInstances: number
  expiredInstances: number
  nearExpirationInstances: number
  pendingInstances: number
  availableRenewalCodes: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface CreateInstanceRequest {
  name: string
  initialDuration: number
  entitlements: Record<string, any>
  monthlyRate?: string
  setupFee?: string
}

export interface RenewInstanceRequest {
  renewalCode: string
}

export interface GenerateCodesRequest {
  duration: number
  quantity: number
  amount?: string // Optional price for the renewal codes
}

export interface BatchCreateInstancesRequest {
  nameTemplate: string
  initialDuration: number
  entitlements: Record<string, any>
  quantity: number
}

export interface OnlineClient {
  sessionId: string
  instanceId: string
  instanceName: string
  deviceFingerprint: string
  clientIp: string
  connectedAt: string
  lastPing: string
  isStale: boolean
}

// Renewal Request Types
export enum PaymentMethod {
  ALIPAY_HONGBAO = 'alipay_hongbao',
  CRYPTOCURRENCY = 'cryptocurrency'
}

export enum RenewalRequestStatus {
  PENDING = 'PENDING',
  PROCESSED = 'PROCESSED', 
  CANCELLED = 'CANCELLED',
  CLOSED_BY_ADMIN = 'CLOSED_BY_ADMIN'
}

export enum MessageType {
  CUSTOMER = 'CUSTOMER',
  ADMIN = 'ADMIN'
}

export interface RenewalRequest {
  id: string
  instanceId: string
  instanceName: string // Derived field for display
  paymentMethod: PaymentMethod
  paymentAmount: string
  paymentProof: string
  customerMessage?: string
  status: RenewalRequestStatus
  requestedDuration: number
  createdAt: string
  processedAt?: string
  processedBy?: string
  adminNotes?: string
  generatedRenewalCodeId?: string
}

export interface ProcessRenewalRequestData {
  renewalDuration: number // How many days to extend the license
  adminNotes?: string // Optional admin notes
}

export interface RenewalRequestMessage {
  id: string
  renewalRequestId: string
  content: string
  messageType: string // Use string to match backend
  createdAt: string
  authorId?: string
  authorName?: string
}

export interface SendMessageRequest {
  content: string
}

export interface CloseRenewalRequestRequest {
  adminNotes?: string
}