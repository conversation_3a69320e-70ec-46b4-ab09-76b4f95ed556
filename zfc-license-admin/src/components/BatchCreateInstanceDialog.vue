<template>
  <el-dialog
    v-model="dialogVisible"
    title="Batch Create Instances"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="180px"
      label-position="top"
    >
      <el-form-item label="Instance Name Template" prop="nameTemplate">
        <el-input
          v-model="form.nameTemplate"
          placeholder="e.g., Customer-{{index}}"
        />
        <div class="form-hint">
          Use {{index}} as placeholder for instance number (e.g., Customer-{{index}} becomes Customer-1, Customer-2, etc.)
        </div>
      </el-form-item>
      
      <el-form-item label="Initial License Duration (days)" prop="initialDuration">
        <el-input-number
          v-model="form.initialDuration"
          :min="1"
          :max="3650"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="Quantity" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="1"
          :max="100"
          style="width: 100%"
        />
        <div class="form-hint">
          Number of instances to create (1-100)
        </div>
      </el-form-item>
      
      <el-form-item label="Entitlements (JSON)" prop="entitlements">
        <div style="width: 100%">
          <el-input
            v-model="entitlementsText"
            type="textarea"
            :rows="8"
            placeholder='{ "max_workers": 1, "features": ["basic"] }'
          />
          <div class="form-hint">
            Enter a valid JSON object defining the instance entitlements
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Create Instances
        </el-button>
      </span>
    </template>
    
    <!-- Success Dialog -->
    <el-dialog
      v-model="showSuccessDialog"
      title="Instances Created Successfully"
      width="700px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon class="success-icon"><CircleCheck /></el-icon>
        <p>{{ createdInstances.length }} instances have been created successfully!</p>
        
        <div class="instances-display">
          <div class="instances-header">
            <h4>Created Instances:</h4>
            <div class="header-buttons">
              <el-button
                type="primary"
                size="small"
                @click="copyAllInstanceIds"
                :icon="CopyDocument"
              >
                Copy All IDs
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="copyAllCredentials"
                :icon="CopyDocument"
              >
                Copy All Credentials
              </el-button>
            </div>
          </div>
          
          <div class="instances-list">
            <div
              v-for="instance in createdInstances"
              :key="instance.id"
              class="instance-item"
            >
              <div class="instance-info">
                <div class="instance-name">{{ instance.name }}</div>
                <div class="credential-row">
                  <span class="credential-label">ID:</span>
                  <code class="credential-value">{{ instance.id }}</code>
                  <el-button
                    type="text"
                    size="small"
                    @click="copyInstanceId(instance.id)"
                    :icon="CopyDocument"
                    title="Copy ID"
                  />
                </div>
                <div class="credential-row">
                  <span class="credential-label">API Key:</span>
                  <code class="credential-value">{{ maskApiKey(instance.apiKey) }}</code>
                  <el-button
                    type="text"
                    size="small"
                    @click="copyApiKey(instance.apiKey)"
                    :icon="CopyDocument"
                    title="Copy API Key"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="closeSuccessDialog">
          Close
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import { CircleCheck, CopyDocument } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { ControllerInstance } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)
const showSuccessDialog = ref(false)
const createdInstances = ref<ControllerInstance[]>([])

const form = ref({
  nameTemplate: '',
  initialDuration: 30,
  quantity: 1,
  entitlements: { max_workers: 1 }
})

const entitlementsText = ref('{\n  "max_workers": 1\n}')

const rules: FormRules = {
  nameTemplate: [
    { required: true, message: 'Please enter name template', trigger: 'blur' },
    { min: 3, message: 'Name template must be at least 3 characters', trigger: 'blur' }
  ],
  initialDuration: [
    { required: true, message: 'Please enter initial duration', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: 'Duration must be between 1 and 3650 days', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: 'Please enter quantity', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: 'Quantity must be between 1 and 100', trigger: 'blur' }
  ],
  entitlements: [
    { required: true, message: 'Please enter entitlements', trigger: 'blur' }
  ]
}

watch(entitlementsText, (newValue) => {
  try {
    form.value.entitlements = JSON.parse(newValue)
  } catch {
    // Invalid JSON, will be caught by validation
  }
})

// Reset success dialog state when main dialog opens
watch(dialogVisible, (newValue) => {
  if (newValue) {
    // Dialog is opening, reset success dialog state
    showSuccessDialog.value = false
    createdInstances.value = []
  }
})

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // Validate JSON
        JSON.parse(entitlementsText.value)
      } catch {
        ElMessage.error('Invalid JSON format in entitlements')
        return
      }
      
      isLoading.value = true
      
      try {
        const instances = await instancesStore.batchCreateInstances({
          nameTemplate: form.value.nameTemplate,
          initialDuration: form.value.initialDuration,
          quantity: form.value.quantity,
          entitlements: JSON.parse(entitlementsText.value)
        })
        
        createdInstances.value = instances || []
        showSuccessDialog.value = true
        emit('created')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to create instances')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const copyInstanceId = async (id: string) => {
  try {
    await navigator.clipboard.writeText(id)
    ElMessage.success('Instance ID copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const copyAllInstanceIds = async () => {
  try {
    const allIds = createdInstances.value.map(instance => instance.id).join('\n')
    await navigator.clipboard.writeText(allIds)
    ElMessage.success('All instance IDs copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const copyApiKey = async (apiKey: string) => {
  try {
    await navigator.clipboard.writeText(apiKey)
    ElMessage.success('API Key copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const copyAllCredentials = async () => {
  try {
    const credentials = createdInstances.value.map(instance => 
      `${instance.name}:\n  Instance ID: ${instance.id}\n  API Key: ${instance.apiKey}`
    ).join('\n\n')
    await navigator.clipboard.writeText(credentials)
    ElMessage.success('All credentials copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey || apiKey.length < 8) return apiKey
  return `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
}

const closeSuccessDialog = () => {
  showSuccessDialog.value = false
  createdInstances.value = []
  // Close the main dialog after success dialog is closed
  dialogVisible.value = false
}

const resetForm = () => {
  form.value = {
    nameTemplate: '',
    initialDuration: 30,
    quantity: 1,
    entitlements: { max_workers: 1 }
  }
  entitlementsText.value = '{\n  "max_workers": 1\n}'
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-content p {
  font-size: 16px;
  color: #303133;
  margin-bottom: 24px;
}

.instances-display {
  text-align: left;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.instances-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.instances-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.instances-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
}

.instance-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.instance-item:last-child {
  border-bottom: none;
}

.instance-info {
  width: 100%;
}

.instance-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  font-size: 14px;
}

.credential-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.credential-row:last-child {
  margin-bottom: 0;
}

.credential-label {
  min-width: 60px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.credential-value {
  font-family: monospace;
  font-size: 12px;
  color: #409eff;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Scrollbar styling */
.instances-list::-webkit-scrollbar {
  width: 6px;
}

.instances-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.instances-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.instances-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>