<template>
  <el-dialog
    v-model="dialogVisible"
    title="Renew Instance License"
    width="500px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <div v-if="props.instance" class="instance-info">
      <h4>Instance: {{ props.instance.name }}</h4>
      <p>Current expiration: {{ formatDate(props.instance.licenseExpiresAt) }}</p>
    </div>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="150px"
      label-position="top"
    >
      <el-form-item label="Renewal Code" prop="renewalCode">
        <el-input
          v-model="form.renewalCode"
          placeholder="Enter renewal code"
          maxlength="50"
          show-word-limit
        />
        <div class="form-hint">
          Enter the renewal code provided by the customer
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Apply Code
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { ControllerInstance } from '@/types'

interface Props {
  modelValue: boolean
  instance: ControllerInstance | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'renewed'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)

const form = ref({
  renewalCode: ''
})

const rules: FormRules = {
  renewalCode: [
    { required: true, message: 'Please enter renewal code', trigger: 'blur' },
    { min: 5, message: 'Renewal code must be at least 5 characters', trigger: 'blur' }
  ]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const handleSubmit = async () => {
  if (!formRef.value || !props.instance) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        await instancesStore.renewInstance(props.instance!.id, {
          renewalCode: form.value.renewalCode
        })
        
        ElMessage.success('Instance renewed successfully')
        emit('renewed')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to renew instance')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  form.value = {
    renewalCode: ''
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.instance-info {
  background: #f5f6fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.instance-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.instance-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>