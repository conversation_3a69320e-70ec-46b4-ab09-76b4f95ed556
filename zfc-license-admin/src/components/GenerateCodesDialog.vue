<template>
  <el-dialog
    v-model="dialogVisible"
    title="Generate Renewal Codes"
    width="500px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      label-position="top"
    >
      <el-form-item label="Duration (days)" prop="duration">
        <el-input-number
          v-model="form.duration"
          :min="1"
          :max="3650"
          style="width: 100%"
        />
        <div class="form-hint">
          Number of days this code will extend a license
        </div>
      </el-form-item>
      
      <el-form-item label="Quantity" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="1"
          :max="100"
          style="width: 100%"
        />
        <div class="form-hint">
          Number of codes to generate (1-100)
        </div>
      </el-form-item>
      
      <el-form-item label="Price (optional)" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="e.g., 29.99"
          style="width: 100%"
        >
          <template #prefix>$</template>
        </el-input>
        <div class="form-hint">
          Optional price for this renewal code
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Generate Codes
        </el-button>
      </span>
    </template>
    
    <!-- Success Dialog -->
    <el-dialog
      v-model="showSuccessDialog"
      title="Codes Generated Successfully"
      width="700px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon class="success-icon"><CircleCheck /></el-icon>
        <p>{{ generatedCodes.length }} renewal codes have been generated successfully!</p>
        
        <div class="codes-display">
          <div class="codes-header">
            <h4>Generated Codes:</h4>
            <el-button
              type="primary"
              size="small"
              @click="copyAllCodes"
              :icon="CopyDocument"
            >
              Copy All
            </el-button>
          </div>
          
          <div class="codes-list">
            <div
              v-for="code in generatedCodes"
              :key="code.id"
              class="code-item"
            >
              <code class="code-text">{{ code.code }}</code>
              <el-button
                type="text"
                size="small"
                @click="copyCode(code.code)"
                :icon="CopyDocument"
                title="Copy this code"
              />
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="closeSuccessDialog">
          Close
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRenewalCodesStore } from '@/stores/renewalCodes'
import { ElMessage } from 'element-plus'
import { CircleCheck, CopyDocument } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { RenewalCode } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'generated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const renewalCodesStore = useRenewalCodesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)
const showSuccessDialog = ref(false)
const generatedCodes = ref<RenewalCode[]>([])

const form = ref({
  duration: 30,
  quantity: 1,
  amount: ''
})

const rules: FormRules = {
  duration: [
    { required: true, message: 'Please enter duration', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: 'Duration must be between 1 and 3650 days', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: 'Please enter quantity', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: 'Quantity must be between 1 and 100', trigger: 'blur' }
  ],
  amount: [
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback() // Optional field, so empty is ok
          return
        }
        const numValue = parseFloat(value)
        if (isNaN(numValue) || numValue < 0) {
          callback(new Error('Please enter a valid price (e.g., 29.99)'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// Reset success dialog state when main dialog opens
watch(dialogVisible, (newValue) => {
  if (newValue) {
    // Dialog is opening, reset success dialog state
    showSuccessDialog.value = false
    generatedCodes.value = []
  }
})

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        const codes = await renewalCodesStore.generateCodes({
          duration: form.value.duration,
          quantity: form.value.quantity,
          amount: form.value.amount || undefined
        })
        
        generatedCodes.value = codes || []
        showSuccessDialog.value = true
        emit('generated')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to generate codes')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('Code copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const copyAllCodes = async () => {
  try {
    const allCodes = generatedCodes.value.map(code => code.code).join('\n')
    await navigator.clipboard.writeText(allCodes)
    ElMessage.success('All codes copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const closeSuccessDialog = () => {
  showSuccessDialog.value = false
  generatedCodes.value = []
  // Close the main dialog after success dialog is closed
  dialogVisible.value = false
}

const resetForm = () => {
  form.value = {
    duration: 30,
    quantity: 1,
    amount: ''
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-content p {
  font-size: 16px;
  color: #303133;
  margin-bottom: 24px;
}

.codes-display {
  text-align: left;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.codes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.codes-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.codes-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
}

.code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.code-item:last-child {
  border-bottom: none;
}

.code-text {
  font-family: monospace;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  flex: 1;
  margin-right: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Scrollbar styling */
.codes-list::-webkit-scrollbar {
  width: 6px;
}

.codes-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.codes-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.codes-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>