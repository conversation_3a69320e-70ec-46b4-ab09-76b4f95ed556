<template>
  <el-dialog
    v-model="dialogVisible"
    title="View/Edit Entitlements"
    width="600px"
    :close-on-click-modal="false"
    @opened="initForm"
    @closed="resetForm"
  >
    <div v-if="props.instance" class="instance-info">
      <h4>Instance: {{ props.instance.name }}</h4>
      <p>ID: {{ props.instance.id }}</p>
    </div>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="Entitlements (JSON)" prop="entitlements">
        <el-input
          v-model="entitlementsText"
          type="textarea"
          :rows="12"
          placeholder="Enter valid JSON"
        />
        <div class="form-hint">
          Edit the entitlements as a JSON object. Make sure the JSON is valid before saving.
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button @click="formatJson" type="info" size="small">
          Format JSON
        </el-button>
        <el-button @click="validateJson" type="success" size="small">
          Validate JSON
        </el-button>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Update Entitlements
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { ControllerInstance } from '@/types'

interface Props {
  modelValue: boolean
  instance: ControllerInstance | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)
const entitlementsText = ref('')

const form = ref({
  entitlements: ''
})

const rules: FormRules = {
  entitlements: [
    { required: true, message: 'Please enter entitlements', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        try {
          JSON.parse(entitlementsText.value)
          callback()
        } catch {
          callback(new Error('Invalid JSON format'))
        }
      },
      trigger: 'blur'
    }
  ]
}

const initForm = () => {
  if (props.instance) {
    entitlementsText.value = JSON.stringify(props.instance.entitlements, null, 2)
    form.value.entitlements = entitlementsText.value
  }
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(entitlementsText.value)
    entitlementsText.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON formatted successfully')
  } catch {
    ElMessage.error('Invalid JSON format')
  }
}

const validateJson = () => {
  try {
    JSON.parse(entitlementsText.value)
    ElMessage.success('JSON is valid')
  } catch {
    ElMessage.error('Invalid JSON format')
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.instance) return
  
  // Update form value before validation
  form.value.entitlements = entitlementsText.value
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        const entitlements = JSON.parse(entitlementsText.value)
        
        await instancesStore.updateInstance(props.instance!.id, {
          entitlements
        })
        
        ElMessage.success('Entitlements updated successfully')
        emit('updated')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to update entitlements')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  entitlementsText.value = ''
  form.value = {
    entitlements: ''
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.instance-info {
  background: #f5f6fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.instance-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.instance-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  font-family: monospace;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}
</style>