<template>
  <el-dialog
    v-model="dialogVisible"
    title="Edit Instance"
    width="500px"
    :close-on-click-modal="false"
    @opened="initForm"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
    >
      <el-form-item label="Instance Name" prop="name">
        <el-input
          v-model="form.name"
          placeholder="Enter instance name"
        />
      </el-form-item>
      
      <el-form-item label="Monthly Rate" prop="monthlyRate">
        <el-input
          v-model="form.monthlyRate"
          type="number"
          placeholder="Enter monthly subscription rate (e.g. 29.99)"
          step="0.01"
          min="0"
        >
          <template #prefix>$</template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="Setup Fee" prop="setupFee">
        <el-input
          v-model="form.setupFee"
          type="number"
          placeholder="Enter one-time setup fee (e.g. 99.00)"
          step="0.01"
          min="0"
        >
          <template #prefix>$</template>
        </el-input>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Update Instance
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { ControllerInstance } from '@/types'

interface Props {
  modelValue: boolean
  instance: ControllerInstance | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)

const form = ref({
  name: '',
  monthlyRate: '',
  setupFee: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: 'Please enter instance name', trigger: 'blur' },
    { min: 3, message: 'Name must be at least 3 characters', trigger: 'blur' }
  ],
  monthlyRate: [
    { 
      validator: (rule, value, callback) => {
        if (value && (isNaN(parseFloat(value)) || parseFloat(value) < 0)) {
          callback(new Error('Please enter a valid amount (0 or greater)'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  setupFee: [
    { 
      validator: (rule, value, callback) => {
        if (value && (isNaN(parseFloat(value)) || parseFloat(value) < 0)) {
          callback(new Error('Please enter a valid amount (0 or greater)'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

const initForm = () => {
  if (props.instance) {
    form.value.name = props.instance.name
    form.value.monthlyRate = props.instance.monthlyRate || ''
    form.value.setupFee = props.instance.setupFee || ''
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.instance) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        await instancesStore.updateInstance(props.instance!.id, {
          name: form.value.name,
          monthlyRate: form.value.monthlyRate || undefined,
          setupFee: form.value.setupFee || undefined
        })
        
        ElMessage.success('Instance updated successfully')
        emit('updated')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to update instance')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  form.value = {
    name: '',
    monthlyRate: '',
    setupFee: ''
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>