import { defineStore } from 'pinia'
import { ref } from 'vue'
import { instancesApi } from '@/api'
import type { ControllerInstance, PaginatedResponse, CreateInstanceRequest, RenewInstanceRequest, BatchCreateInstancesRequest } from '@/types'

export const useInstancesStore = defineStore('instances', () => {
  const instances = ref<ControllerInstance[]>([])
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')

  const fetchInstances = async (params?: { page?: number; pageSize?: number; search?: string }) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await instancesApi.getInstances({
        page: params?.page || pagination.value.page,
        pageSize: params?.pageSize || pagination.value.pageSize,
        search: params?.search || searchQuery.value,
      })

      if (response.data.success && response.data.data) {
        const data = response.data.data as PaginatedResponse<ControllerInstance>
        instances.value = data.data
        pagination.value = {
          page: data.page,
          pageSize: data.pageSize,
          total: data.total,
          totalPages: data.totalPages,
        }
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch instances'
    } finally {
      isLoading.value = false
    }
  }

  const createInstance = async (data: CreateInstanceRequest) => {
    try {
      const response = await instancesApi.createInstance(data)
      if (response.data.success) {
        await fetchInstances()
        return response.data.data
      }
      throw new Error(response.data.message || 'Failed to create instance')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to create instance')
    }
  }

  const batchCreateInstances = async (data: BatchCreateInstancesRequest) => {
    try {
      const response = await instancesApi.batchCreateInstances(data)
      if (response.data.success) {
        await fetchInstances()
        return response.data.data
      }
      throw new Error(response.data.message || 'Failed to create instances')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to create instances')
    }
  }

  const updateInstance = async (id: string, data: Partial<ControllerInstance>) => {
    try {
      const response = await instancesApi.updateInstance(id, data)
      if (response.data.success) {
        await fetchInstances()
        return response.data.data
      }
      throw new Error(response.data.message || 'Failed to update instance')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to update instance')
    }
  }

  const deleteInstance = async (id: string) => {
    try {
      const response = await instancesApi.deleteInstance(id)
      if (response.data.success) {
        await fetchInstances()
        return true
      }
      throw new Error(response.data.message || 'Failed to delete instance')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to delete instance')
    }
  }

  const renewInstance = async (id: string, data: RenewInstanceRequest) => {
    try {
      const response = await instancesApi.renewInstance(id, data)
      if (response.data.success) {
        await fetchInstances()
        return response.data.data
      }
      throw new Error(response.data.message || 'Failed to renew instance')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to renew instance')
    }
  }

  const forceExpireSession = async (id: string) => {
    try {
      const response = await instancesApi.forceExpireSession(id)
      if (response.data.success) {
        await fetchInstances()
        return true
      }
      throw new Error(response.data.message || 'Failed to expire session')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to expire session')
    }
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setPage = (page: number) => {
    pagination.value.page = page
  }

  return {
    instances,
    pagination,
    isLoading,
    error,
    searchQuery,
    fetchInstances,
    createInstance,
    batchCreateInstances,
    updateInstance,
    deleteInstance,
    renewInstance,
    forceExpireSession,
    setSearchQuery,
    setPage,
  }
})