import { defineStore } from 'pinia'
import { ref } from 'vue'
import { renewalCodes<PERSON>pi } from '@/api'
import type { RenewalCode, PaginatedResponse, GenerateCodesRequest } from '@/types'

export const useRenewalCodesStore = defineStore('renewalCodes', () => {
  const codes = ref<RenewalCode[]>([])
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const statusFilter = ref<'available' | 'used' | undefined>()

  const fetchCodes = async (params?: { 
    page?: number
    pageSize?: number
    status?: 'available' | 'used'
  }) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await renewalCodesApi.getCodes({
        page: params?.page || pagination.value.page,
        pageSize: params?.pageSize || pagination.value.pageSize,
        status: params?.status || statusFilter.value,
      })

      if (response.data.success && response.data.data) {
        const data = response.data.data as PaginatedResponse<RenewalCode>
        codes.value = data.data
        pagination.value = {
          page: data.page,
          pageSize: data.pageSize,
          total: data.total,
          totalPages: data.totalPages,
        }
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch renewal codes'
    } finally {
      isLoading.value = false
    }
  }

  const generateCodes = async (data: GenerateCodesRequest) => {
    try {
      const response = await renewalCodesApi.generateCodes(data)
      if (response.data.success) {
        await fetchCodes()
        return response.data.data
      }
      throw new Error(response.data.message || 'Failed to generate codes')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to generate codes')
    }
  }

  const deleteCode = async (id: string) => {
    try {
      const response = await renewalCodesApi.deleteCode(id)
      if (response.data.success) {
        await fetchCodes()
        return true
      }
      throw new Error(response.data.message || 'Failed to delete code')
    } catch (err: any) {
      throw new Error(err.response?.data?.message || 'Failed to delete code')
    }
  }

  const setStatusFilter = (status: 'available' | 'used' | undefined) => {
    statusFilter.value = status
  }

  const setPage = (page: number) => {
    pagination.value.page = page
  }

  return {
    codes,
    pagination,
    isLoading,
    error,
    statusFilter,
    fetchCodes,
    generateCodes,
    deleteCode,
    setStatusFilter,
    setPage,
  }
})