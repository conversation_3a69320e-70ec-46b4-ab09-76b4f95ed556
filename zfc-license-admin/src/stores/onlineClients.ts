import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { onlineClientsApi } from '@/api'
import type { OnlineClient } from '@/types'
import { ElMessage } from 'element-plus'

export const useOnlineClientsStore = defineStore('onlineClients', () => {
  const clients = ref<OnlineClient[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Auto-refresh settings
  const autoRefresh = ref(true)
  const refreshInterval = ref(10000) // 10 seconds
  let refreshTimer: number | null = null

  // Computed
  const activeClients = computed(() => 
    clients.value.filter(client => !client.isStale)
  )

  const staleClients = computed(() => 
    clients.value.filter(client => client.isStale)
  )

  const clientCount = computed(() => clients.value.length)
  const activeClientCount = computed(() => activeClients.value.length)
  const staleClientCount = computed(() => staleClients.value.length)

  // Actions
  const fetchOnlineClients = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await onlineClientsApi.getOnlineClients()
      
      if (response.data.success && response.data.data) {
        clients.value = response.data.data
      } else {
        throw new Error(response.data.error || 'Failed to fetch online clients')
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch online clients'
      console.error('Error fetching online clients:', err)
    } finally {
      isLoading.value = false
    }
  }

  const getClientDetails = async (sessionId: string): Promise<OnlineClient | null> => {
    try {
      const response = await onlineClientsApi.getClientDetails(sessionId)
      
      if (response.data.success && response.data.data) {
        return response.data.data
      } else {
        throw new Error(response.data.error || 'Failed to fetch client details')
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch client details'
      console.error('Error fetching client details:', err)
      return null
    }
  }

  const disconnectClient = async (sessionId: string) => {
    try {
      const response = await onlineClientsApi.disconnectClient(sessionId)
      
      if (response.data.success) {
        ElMessage.success('Client disconnected successfully')
        // Remove from local state
        clients.value = clients.value.filter(client => client.sessionId !== sessionId)
        // Refresh to get updated state
        await fetchOnlineClients()
      } else {
        throw new Error(response.data.error || 'Failed to disconnect client')
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to disconnect client'
      ElMessage.error(err.message || 'Failed to disconnect client')
      console.error('Error disconnecting client:', err)
    }
  }

  const startAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
    
    if (autoRefresh.value) {
      refreshTimer = setInterval(() => {
        fetchOnlineClients()
      }, refreshInterval.value)
    }
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  const setAutoRefresh = (enabled: boolean) => {
    autoRefresh.value = enabled
    if (enabled) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  const setRefreshInterval = (interval: number) => {
    refreshInterval.value = interval
    if (autoRefresh.value) {
      startAutoRefresh()
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Find client by instance ID
  const findClientByInstanceId = (instanceId: string): OnlineClient | undefined => {
    return clients.value.find(client => client.instanceId === instanceId)
  }

  // Check if instance is online
  const isInstanceOnline = (instanceId: string): boolean => {
    return !!findClientByInstanceId(instanceId)
  }

  // Get connection duration in human readable format
  const getConnectionDuration = (client: OnlineClient): string => {
    const connectedAt = new Date(client.connectedAt)
    const now = new Date()
    const diffMs = now.getTime() - connectedAt.getTime()
    
    const seconds = Math.floor(diffMs / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  // Format last ping time
  const formatLastPing = (client: OnlineClient): string => {
    const lastPing = new Date(client.lastPing)
    const now = new Date()
    const diffMs = now.getTime() - lastPing.getTime()
    const seconds = Math.floor(diffMs / 1000)
    
    if (seconds < 60) {
      return `${seconds}s ago`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      return `${minutes}m ago`
    } else {
      const hours = Math.floor(seconds / 3600)
      return `${hours}h ago`
    }
  }

  return {
    // State
    clients,
    isLoading,
    error,
    autoRefresh,
    refreshInterval,
    
    // Computed
    activeClients,
    staleClients,
    clientCount,
    activeClientCount,
    staleClientCount,
    
    // Actions
    fetchOnlineClients,
    getClientDetails,
    disconnectClient,
    startAutoRefresh,
    stopAutoRefresh,
    setAutoRefresh,
    setRefreshInterval,
    clearError,
    findClientByInstanceId,
    isInstanceOnline,
    getConnectionDuration,
    formatLastPing,
  }
})