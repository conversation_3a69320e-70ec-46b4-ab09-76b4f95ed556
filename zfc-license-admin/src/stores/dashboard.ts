import { defineStore } from 'pinia'
import { ref } from 'vue'
import { dashboardApi } from '@/api'
import type { DashboardStats } from '@/types'

export const useDashboardStore = defineStore('dashboard', () => {
  const stats = ref<DashboardStats>({
    activeInstances: 0,
    expiredInstances: 0,
    nearExpirationInstances: 0,
    availableRenewalCodes: 0,
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const fetchStats = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await dashboardApi.getStats()
      if (response.data.success && response.data.data) {
        stats.value = response.data.data
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch dashboard stats'
    } finally {
      isLoading.value = false
    }
  }

  return {
    stats,
    isLoading,
    error,
    fetchStats,
  }
})