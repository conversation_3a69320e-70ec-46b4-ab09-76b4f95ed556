import { defineS<PERSON> } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api'

// Helper function to decode JWT without verification
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Failed to decode JWT:', error)
    return null
  }
}

// Helper function to check if token is expired
function isTokenExpired(token: string): boolean {
  const decoded = decodeJWT(token)
  if (!decoded || !decoded.exp) return true
  
  const currentTime = Math.floor(Date.now() / 1000)
  return decoded.exp < currentTime
}

// Helper function to get token expiration time
function getTokenExpiration(token: string): number | null {
  const decoded = decodeJWT(token)
  return decoded?.exp || null
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const currentUser = ref<any>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref<boolean | null>(null)

  const isAuthenticated = computed(() => {
    if (!token.value) return false
    
    // Check if token is expired
    if (isTokenExpired(token.value)) {
      // Token is expired, logout automatically
      logout()
      return false
    }
    
    return true
  })

  const checkInitialization = async () => {
    try {
      const response = await authApi.checkInitialization()
      isInitialized.value = response.data.data.is_initialized
      return response.data.data.is_initialized
    } catch (err: any) {
      console.error('Failed to check initialization:', err)
      return false
    }
  }

  const createAdminUser = async (username: string, password: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      await authApi.createAdminUser({ username, password })
      isInitialized.value = true
      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to create admin user'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const login = async (username: string, password: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await authApi.login({ username, password })
      token.value = response.data.data.token
      localStorage.setItem('admin_token', response.data.data.token)
      
      // Get current user info after login
      await getCurrentUser()
      
      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const getCurrentUser = async () => {
    if (!token.value) return false
    
    try {
      const response = await authApi.getCurrentAdmin()
      currentUser.value = response.data.data
      return true
    } catch (err: any) {
      console.error('Failed to get current user:', err)
      return false
    }
  }

  const logout = () => {
    token.value = null
    currentUser.value = null
    localStorage.removeItem('admin_token')
  }

  const validateToken = async () => {
    if (!token.value) return false
    
    // Check if token is expired first
    if (isTokenExpired(token.value)) {
      console.log('Token is expired, logging out...')
      logout()
      return false
    }
    
    try {
      await getCurrentUser()
      return true
    } catch {
      logout()
      return false
    }
  }

  const checkTokenExpiration = () => {
    if (!token.value) return false
    return !isTokenExpired(token.value)
  }

  const getTokenExpirationTime = () => {
    if (!token.value) return null
    return getTokenExpiration(token.value)
  }

  return {
    token,
    currentUser,
    isLoading,
    error,
    isInitialized,
    isAuthenticated,
    checkInitialization,
    createAdminUser,
    login,
    logout,
    validateToken,
    getCurrentUser,
    checkTokenExpiration,
    getTokenExpirationTime,
  }
})