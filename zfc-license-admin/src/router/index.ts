import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/setup',
      name: 'AdminSetup',
      component: () => import('@/views/AdminSetup.vue'),
      meta: { requiresUninitialized: true }
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresGuest: true, requiresInitialized: true }
    },
    {
      path: '/',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard.vue'),
      meta: { requiresAuth: true, requiresInitialized: true },
      children: [
        {
          path: '',
          name: 'Overview',
          component: () => import('@/views/Overview.vue')
        },
        {
          path: 'instances',
          name: 'Instances',
          component: () => import('@/views/Instances.vue')
        },
        {
          path: 'renewal-codes',
          name: 'RenewalCodes',
          component: () => import('@/views/RenewalCodes.vue')
        },
        {
          path: 'online-clients',
          name: 'OnlineClients',
          component: () => import('@/views/OnlineClients.vue')
        },
        {
          path: 'renewal-requests',
          name: 'RenewalRequests',
          component: () => import('@/views/RenewalRequests.vue')
        }
      ]
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresInitialized = to.matched.some(record => record.meta.requiresInitialized)
  const requiresUninitialized = to.matched.some(record => record.meta.requiresUninitialized)

  // Check initialization status first
  if (authStore.isInitialized === null) {
    await authStore.checkInitialization()
  }

  // Handle uninitialized system
  if (requiresUninitialized && authStore.isInitialized) {
    next('/login')
    return
  }

  // Handle initialized system requirements
  if (requiresInitialized && !authStore.isInitialized) {
    next('/setup')
    return
  }

  // Handle authentication requirements
  if (requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (requiresGuest && authStore.isAuthenticated) {
    next('/')
  } else if (requiresAuth && authStore.isAuthenticated) {
    // Validate token
    const isValid = await authStore.validateToken()
    if (!isValid) {
      next('/login')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router