import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

export function useTokenMonitor() {
  const authStore = useAuthStore()
  const intervalId = ref<number | null>(null)
  
  // Check token every 30 seconds
  const CHECK_INTERVAL = 30 * 1000
  
  // Warning threshold: 5 minutes before expiration
  const WARNING_THRESHOLD = 5 * 60 * 1000
  
  const checkTokenStatus = () => {
    if (!authStore.token) return
    
    const expirationTime = authStore.getTokenExpirationTime()
    if (!expirationTime) return
    
    const currentTime = Math.floor(Date.now() / 1000)
    const timeToExpiry = (expirationTime - currentTime) * 1000
    
    // If token is expired or about to expire, logout
    if (timeToExpiry <= 0) {
      console.log('Token expired, logging out...')
      authStore.logout()
      // Force redirect to login if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    } else if (timeToExpiry <= WARNING_THRESHOLD) {
      console.warn(`Token expires in ${Math.floor(timeToExpiry / 1000)} seconds`)
    }
  }
  
  const startMonitoring = () => {
    if (intervalId.value) return
    
    // Initial check
    checkTokenStatus()
    
    // Set up periodic checks
    intervalId.value = window.setInterval(checkTokenStatus, CHECK_INTERVAL)
  }
  
  const stopMonitoring = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value)
      intervalId.value = null
    }
  }
  
  onMounted(() => {
    if (authStore.isAuthenticated) {
      startMonitoring()
    }
  })
  
  onUnmounted(() => {
    stopMonitoring()
  })
  
  return {
    startMonitoring,
    stopMonitoring,
    checkTokenStatus
  }
}