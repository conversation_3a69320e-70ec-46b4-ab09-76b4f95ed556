<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { useTokenMonitor } from '@/composables/useTokenMonitor'
import { useAuthStore } from '@/stores/auth'
import { watch } from 'vue'

// Initialize token monitoring
const { startMonitoring, stopMonitoring } = useTokenMonitor()
const authStore = useAuthStore()

// Watch for authentication changes and start/stop monitoring accordingly
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated) {
      startMonitoring()
    } else {
      stopMonitoring()
    }
  },
  { immediate: true }
)
</script>

<style>
#app {
  min-height: 100vh;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}
</style>