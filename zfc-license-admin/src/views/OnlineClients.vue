<template>
  <div class="online-clients-page">
    <div class="page-header">
      <div class="header-content">
        <h2>Online Clients</h2>
        <p>Monitor and manage connected license clients</p>
      </div>
      <div class="header-stats">
        <el-statistic title="Total Connected" :value="onlineClientsStore.clientCount" />
        <el-statistic title="Active" :value="onlineClientsStore.activeClientCount" />
        <el-statistic title="Stale" :value="onlineClientsStore.staleClientCount" />
      </div>
    </div>
    
    <div class="controls-section">
      <div class="refresh-controls">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="handleManualRefresh"
          :loading="onlineClientsStore.isLoading"
        >
          Refresh
        </el-button>
        
        <el-switch
          v-model="onlineClientsStore.autoRefresh"
          @change="onlineClientsStore.setAutoRefresh"
          active-text="Auto Refresh"
          inactive-text="Manual"
          style="margin-left: 16px;"
        />
        
        <el-select
          v-model="refreshInterval"
          @change="handleIntervalChange"
          :disabled="!onlineClientsStore.autoRefresh"
          placeholder="Refresh Interval"
          style="margin-left: 16px; width: 150px;"
        >
          <el-option label="5 seconds" :value="5000" />
          <el-option label="10 seconds" :value="10000" />
          <el-option label="30 seconds" :value="30000" />
          <el-option label="1 minute" :value="60000" />
        </el-select>
      </div>
      
      <div class="filter-controls">
        <el-input
          v-model="searchQuery"
          placeholder="Search by instance name, IP, or device..."
          :prefix-icon="Search"
          clearable
          style="max-width: 400px"
        />
        
        <el-select
          v-model="statusFilter"
          placeholder="Filter by Status"
          clearable
          style="margin-left: 16px; width: 150px;"
        >
          <el-option label="Active" value="active" />
          <el-option label="Stale" value="stale" />
        </el-select>
      </div>
    </div>
    
    <div class="table-container">
      <el-table
        :data="filteredClients"
        v-loading="onlineClientsStore.isLoading"
        style="width: 100%"
        stripe
        :default-sort="{ prop: 'connectedAt', order: 'descending' }"
        @row-click="handleRowClick"
      >
        <el-table-column prop="instanceName" label="Instance" min-width="200">
          <template #default="{ row }">
            <div class="instance-cell">
              <strong>{{ row.instanceName }}</strong>
              <code class="instance-id">{{ row.instanceId }}</code>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="clientIp" label="Client IP" min-width="140">
          <template #default="{ row }">
            <span class="ip-address">{{ row.clientIp }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="Status" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isStale ? 'warning' : 'success'" size="small">
              {{ row.isStale ? 'Stale' : 'Active' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="Connected" min-width="150" sortable prop="connectedAt">
          <template #default="{ row }">
            <div class="time-info">
              <div>{{ formatDate(row.connectedAt) }}</div>
              <div class="duration">{{ onlineClientsStore.getConnectionDuration(row) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="Last Ping" min-width="120" sortable prop="lastPing">
          <template #default="{ row }">
            <span :class="{ 'stale-ping': row.isStale }">
              {{ onlineClientsStore.formatLastPing(row) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="deviceFingerprint" label="Device" min-width="200">
          <template #default="{ row }">
            <div class="device-cell">
              <code class="device-fingerprint">{{ truncateFingerprint(row.deviceFingerprint) }}</code>
              <el-button
                type="text"
                :icon="CopyDocument"
                @click.stop="copyToClipboard(row.deviceFingerprint)"
                title="Copy Device Fingerprint"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" width="120" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="primary" size="small">
                Actions <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="details">View Details</el-dropdown-item>
                  <el-dropdown-item command="disconnect" divided>Disconnect</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="!onlineClientsStore.isLoading && filteredClients.length === 0" class="empty-state">
        <el-empty description="No online clients found" />
      </div>
    </div>
    
    <!-- Client Details Dialog -->
    <el-dialog
      v-model="showDetailsDialog"
      title="Client Details"
      width="600px"
      :before-close="handleCloseDetails"
    >
      <div v-if="selectedClient" class="client-details">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="Session ID">
            <code>{{ selectedClient.sessionId }}</code>
            <el-button
              type="text"
              :icon="CopyDocument"
              @click="copyToClipboard(selectedClient.sessionId)"
              style="margin-left: 8px;"
            />
          </el-descriptions-item>
          
          <el-descriptions-item label="Instance">
            <div>
              <strong>{{ selectedClient.instanceName }}</strong>
              <br>
              <code>{{ selectedClient.instanceId }}</code>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="Client IP">
            {{ selectedClient.clientIp }}
          </el-descriptions-item>
          
          <el-descriptions-item label="Device Fingerprint">
            <code style="word-break: break-all;">{{ selectedClient.deviceFingerprint }}</code>
            <el-button
              type="text"
              :icon="CopyDocument"
              @click="copyToClipboard(selectedClient.deviceFingerprint)"
              style="margin-left: 8px;"
            />
          </el-descriptions-item>
          
          <el-descriptions-item label="Status">
            <el-tag :type="selectedClient.isStale ? 'warning' : 'success'">
              {{ selectedClient.isStale ? 'Stale' : 'Active' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="Connected At">
            {{ formatDate(selectedClient.connectedAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="Connection Duration">
            {{ onlineClientsStore.getConnectionDuration(selectedClient) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="Last Ping">
            {{ formatDate(selectedClient.lastPing) }}
            <br>
            <small>({{ onlineClientsStore.formatLastPing(selectedClient) }})</small>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailsDialog = false">Close</el-button>
          <el-button 
            type="danger" 
            @click="handleDisconnectFromDialog"
            :loading="disconnecting"
          >
            Disconnect Client
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useOnlineClientsStore } from '@/stores/onlineClients'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Search, 
  CopyDocument, 
  ArrowDown 
} from '@element-plus/icons-vue'
import type { OnlineClient } from '@/types'

const onlineClientsStore = useOnlineClientsStore()

const searchQuery = ref('')
const statusFilter = ref('')
const refreshInterval = ref(10000)
const showDetailsDialog = ref(false)
const selectedClient = ref<OnlineClient | null>(null)
const disconnecting = ref(false)

const filteredClients = computed(() => {
  let filtered = onlineClientsStore.clients
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(client => 
      client.instanceName.toLowerCase().includes(query) ||
      client.instanceId.toLowerCase().includes(query) ||
      client.clientIp.includes(query) ||
      client.deviceFingerprint.toLowerCase().includes(query)
    )
  }
  
  // Apply status filter
  if (statusFilter.value) {
    filtered = filtered.filter(client => {
      if (statusFilter.value === 'active') return !client.isStale
      if (statusFilter.value === 'stale') return client.isStale
      return true
    })
  }
  
  return filtered
})

onMounted(() => {
  onlineClientsStore.fetchOnlineClients()
  onlineClientsStore.startAutoRefresh()
})

onBeforeUnmount(() => {
  onlineClientsStore.stopAutoRefresh()
})

const handleManualRefresh = () => {
  onlineClientsStore.fetchOnlineClients()
}

const handleIntervalChange = (interval: number) => {
  onlineClientsStore.setRefreshInterval(interval)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const truncateFingerprint = (fingerprint: string) => {
  if (fingerprint.length > 20) {
    return `${fingerprint.substring(0, 10)}...${fingerprint.substring(fingerprint.length - 6)}`
  }
  return fingerprint
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('Copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const handleRowClick = (row: OnlineClient) => {
  selectedClient.value = row
  showDetailsDialog.value = true
}

const handleAction = async (command: string, client: OnlineClient) => {
  selectedClient.value = client
  
  switch (command) {
    case 'details':
      showDetailsDialog.value = true
      break
    case 'disconnect':
      await handleDisconnectClient(client)
      break
  }
}

const handleDisconnectClient = async (client: OnlineClient) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to disconnect client "${client.instanceName}" from ${client.clientIp}?`,
      'Disconnect Client',
      {
        confirmButtonText: 'Disconnect',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    await onlineClientsStore.disconnectClient(client.sessionId)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || 'Failed to disconnect client')
    }
  }
}

const handleDisconnectFromDialog = async () => {
  if (!selectedClient.value) return
  
  try {
    disconnecting.value = true
    await handleDisconnectClient(selectedClient.value)
    showDetailsDialog.value = false
  } finally {
    disconnecting.value = false
  }
}

const handleCloseDetails = (done: () => void) => {
  selectedClient.value = null
  done()
}
</script>

<style scoped>
.online-clients-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.header-stats {
  display: flex;
  gap: 32px;
  align-items: center;
}

.controls-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.refresh-controls {
  display: flex;
  align-items: center;
}

.filter-controls {
  display: flex;
  align-items: center;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.instance-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.instance-id {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.ip-address {
  font-family: monospace;
  font-weight: 500;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.duration {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stale-ping {
  color: #e6a23c;
  font-weight: 600;
}

.device-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-fingerprint {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.client-details {
  margin: 20px 0;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-stats {
    justify-content: space-around;
  }
  
  .controls-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .refresh-controls,
  .filter-controls {
    justify-content: center;
  }
  
  :deep(.el-table) {
    font-size: 14px;
  }
}

/* Animation for status changes */
.el-tag {
  transition: all 0.3s ease;
}

/* Hover effects */
.el-table :deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}
</style>