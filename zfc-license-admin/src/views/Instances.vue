<template>
  <div class="instances-page">
    <div class="page-header">
      <div class="header-content">
        <h2>Instance Management</h2>
        <p>Manage customer license instances</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true" :icon="Plus">
          Create New Instance
        </el-button>
        <el-button type="success" @click="showBatchCreateDialog = true" :icon="Plus">
          Batch Create Instances
        </el-button>
      </div>
    </div>
    
    <div class="filters-section">
      <el-input
        v-model="searchQuery"
        placeholder="Search instances by name or ID..."
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
        style="max-width: 400px"
      />
      <el-select
        v-model="statusFilter"
        placeholder="Filter by Status"
        clearable
        @change="handleStatusFilter"
        style="width: 200px; margin-left: 12px"
      >
        <el-option label="All Statuses" value="" />
        <el-option label="Pending Activation" value="pending" />
        <el-option label="Active" value="active" />
        <el-option label="Expired" value="expired" />
      </el-select>
    </div>
    
    <div class="table-container">
      <el-table
        :data="instancesStore.instances"
        v-loading="instancesStore.isLoading"
        style="width: 100%"
        stripe
        :default-sort="{ prop: 'licenseExpiresAt', order: 'ascending' }"
      >
        <el-table-column prop="name" label="Name" min-width="200" />
        
        <el-table-column prop="id" label="Instance ID" min-width="150">
          <template #default="{ row }">
            <div class="id-cell">
              <code class="instance-id">{{ row.id }}</code>
              <el-button
                type="text"
                :icon="CopyDocument"
                @click="copyToClipboard(row.id)"
                title="Copy Instance ID"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="apiKey" label="API Key" min-width="180">
          <template #default="{ row }">
            <div class="api-key-cell">
              <code class="api-key">{{ maskApiKey(row.apiKey) }}</code>
              <el-button
                type="text"
                :icon="CopyDocument"
                @click="copyToClipboard(row.apiKey)"
                title="Copy API Key"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="licenseExpiresAt" label="License Expires At" min-width="180" sortable>
          <template #default="{ row }">
            <span :class="getExpirationClass(row.licenseExpiresAt)">
              {{ formatDate(row.licenseExpiresAt) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="License Status" min-width="140">
          <template #default="{ row }">
            <el-tag :type="getLicenseStatusType(row)" size="small">
              {{ getLicenseStatusText(row) }}
            </el-tag>
            <div v-if="row.activatedAt" class="activation-info">
              <small>Activated: {{ formatDate(row.activatedAt) }}</small>
            </div>
            <div v-else class="activation-info">
              <small class="text-muted">Not activated</small>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="Pricing Info" min-width="150">
          <template #default="{ row }">
            <div v-if="row.monthlyRate || row.setupFee">
              <div v-if="row.monthlyRate" class="price-item">
                <span class="price-label">Monthly:</span>
                <span class="price-value">{{ formatCurrency(row.monthlyRate) }}</span>
              </div>
              <div v-if="row.setupFee" class="price-item">
                <span class="price-label">Setup:</span>
                <span class="price-value">{{ formatCurrency(row.setupFee) }}</span>
              </div>
            </div>
            <span v-else class="text-muted">N/A</span>
          </template>
        </el-table-column>
        
        <el-table-column label="Entitlements" min-width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewEntitlements(row)">
              View/Edit
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column label="Last Seen" min-width="200">
          <template #default="{ row }">
            <div v-if="row.lastSeenAt">
              <div>{{ formatDate(row.lastSeenAt) }}</div>
              <div class="ip-address">{{ row.lastSeenIp }}</div>
            </div>
            <span v-else class="text-muted">Never</span>
          </template>
        </el-table-column>
        
        <el-table-column label="Session ID" min-width="120">
          <template #default="{ row }">
            <el-tag v-if="row.currentSessionId" type="success" size="small">
              Active
            </el-tag>
            <span v-else class="text-muted">None</span>
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="180" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="primary" size="small">
                Actions <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">Edit</el-dropdown-item>
                  <el-dropdown-item command="renew">Renew</el-dropdown-item>
                  <el-dropdown-item 
                    command="forceExpire" 
                    :disabled="!row.currentSessionId"
                  >
                    Force Expire Session
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>Delete</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="instancesStore.pagination.page"
          v-model:page-size="instancesStore.pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="instancesStore.pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- Create Instance Dialog -->
    <CreateInstanceDialog 
      v-model="showCreateDialog"
      @created="handleInstanceCreated"
    />
    
    <!-- Batch Create Instance Dialog -->
    <BatchCreateInstanceDialog 
      v-model="showBatchCreateDialog"
      @created="handleBatchInstancesCreated"
    />
    
    <!-- Edit Instance Dialog -->
    <EditInstanceDialog
      v-model="showEditDialog"
      :instance="selectedInstance"
      @updated="handleInstanceUpdated"
    />
    
    <!-- Renew Instance Dialog -->
    <RenewInstanceDialog
      v-model="showRenewDialog"
      :instance="selectedInstance"
      @renewed="handleInstanceRenewed"
    />
    
    <!-- Entitlements Dialog -->
    <EntitlementsDialog
      v-model="showEntitlementsDialog"
      :instance="selectedInstance"
      @updated="handleInstanceUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, CopyDocument, ArrowDown } from '@element-plus/icons-vue'
import type { ControllerInstance } from '@/types'
import { LicenseStatus } from '@/types'
import CreateInstanceDialog from '@/components/CreateInstanceDialog.vue'
import BatchCreateInstanceDialog from '@/components/BatchCreateInstanceDialog.vue'
import EditInstanceDialog from '@/components/EditInstanceDialog.vue'
import RenewInstanceDialog from '@/components/RenewInstanceDialog.vue'
import EntitlementsDialog from '@/components/EntitlementsDialog.vue'

const instancesStore = useInstancesStore()

const searchQuery = ref('')
const statusFilter = ref('')
const showCreateDialog = ref(false)
const showBatchCreateDialog = ref(false)
const showEditDialog = ref(false)
const showRenewDialog = ref(false)
const showEntitlementsDialog = ref(false)
const selectedInstance = ref<ControllerInstance | null>(null)

onMounted(() => {
  instancesStore.fetchInstances()
})

const handleSearch = () => {
  instancesStore.setSearchQuery(searchQuery.value)
  instancesStore.fetchInstances()
}

const handleStatusFilter = () => {
  // TODO: 在后端支持状态筛选后实现
  console.log('Status filter changed:', statusFilter.value)
}

const handleSizeChange = (size: number) => {
  instancesStore.pagination.pageSize = size
  instancesStore.fetchInstances()
}

const handleCurrentChange = (page: number) => {
  instancesStore.setPage(page)
  instancesStore.fetchInstances()
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('Copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey || apiKey.length < 8) return apiKey
  return `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const getExpirationClass = (dateString: string) => {
  const now = new Date()
  const expiry = new Date(dateString)
  const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (daysUntilExpiry < 0) return 'expired'
  if (daysUntilExpiry <= 7) return 'near-expiry'
  return ''
}

const getLicenseStatus = (instance: ControllerInstance): LicenseStatus => {
  const now = new Date()
  const expiry = new Date(instance.licenseExpiresAt)
  
  if (!instance.activatedAt) {
    return LicenseStatus.PENDING
  }
  
  if (expiry < now) {
    return LicenseStatus.EXPIRED
  }
  
  return LicenseStatus.ACTIVE
}

const getLicenseStatusType = (instance: ControllerInstance): string => {
  const status = getLicenseStatus(instance)
  switch (status) {
    case LicenseStatus.PENDING:
      return 'warning'
    case LicenseStatus.ACTIVE:
      return 'success'
    case LicenseStatus.EXPIRED:
      return 'danger'
    default:
      return 'info'
  }
}

const getLicenseStatusText = (instance: ControllerInstance): string => {
  const status = getLicenseStatus(instance)
  switch (status) {
    case LicenseStatus.PENDING:
      return 'Pending'
    case LicenseStatus.ACTIVE:
      return 'Active'
    case LicenseStatus.EXPIRED:
      return 'Expired'
    default:
      return 'Unknown'
  }
}

const formatCurrency = (amount: string) => {
  if (!amount) return 'N/A'
  const numericAmount = parseFloat(amount)
  if (isNaN(numericAmount)) return amount
  return '$' + numericAmount.toFixed(2)
}

const viewEntitlements = (instance: ControllerInstance) => {
  selectedInstance.value = instance
  showEntitlementsDialog.value = true
}

const handleAction = async (command: string, instance: ControllerInstance) => {
  selectedInstance.value = instance
  
  switch (command) {
    case 'edit':
      showEditDialog.value = true
      break
    case 'renew':
      showRenewDialog.value = true
      break
    case 'forceExpire':
      await handleForceExpireSession(instance)
      break
    case 'delete':
      await handleDeleteInstance(instance)
      break
  }
}

const handleForceExpireSession = async (instance: ControllerInstance) => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to kick the current session? This will allow a new machine to acquire the license.',
      'Force Expire Session',
      {
        confirmButtonText: 'Yes, Force Expire',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    await instancesStore.forceExpireSession(instance.id)
    ElMessage.success('Session expired successfully')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || 'Failed to expire session')
    }
  }
}

const handleDeleteInstance = async (instance: ControllerInstance) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to permanently delete instance "${instance.name}"? This action cannot be undone.`,
      'Delete Instance',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'error',
      }
    )
    
    await instancesStore.deleteInstance(instance.id)
    ElMessage.success('Instance deleted successfully')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || 'Failed to delete instance')
    }
  }
}

const handleInstanceCreated = () => {
  // Don't force close the dialog - let CreateInstanceDialog handle its own closing
  // The dialog will show success message and close itself when user clicks "Close"
  instancesStore.fetchInstances()
}

const handleBatchInstancesCreated = () => {
  // Don't force close the dialog - let BatchCreateInstanceDialog handle its own closing
  // The dialog will show success message and close itself when user clicks "Close"
  instancesStore.fetchInstances()
}

const handleInstanceUpdated = () => {
  showEditDialog.value = false
  showEntitlementsDialog.value = false
  instancesStore.fetchInstances()
}

const handleInstanceRenewed = () => {
  showRenewDialog.value = false
  instancesStore.fetchInstances()
}
</script>

<style scoped>
.instances-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filters-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.instance-id {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.api-key-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-key {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  color: #606266;
}

.ip-address {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.text-muted {
  color: #909399;
}

.expired {
  color: #f56c6c;
  font-weight: 600;
}

.near-expiry {
  color: #e6a23c;
  font-weight: 600;
}

.price-item {
  font-size: 12px;
  margin-bottom: 2px;
}

.price-label {
  color: #909399;
  margin-right: 4px;
}

.price-value {
  font-weight: 600;
  color: #67c23a;
}

.activation-info {
  margin-top: 4px;
}

.activation-info small {
  color: #909399;
  font-size: 11px;
}

.activation-info .text-muted {
  color: #c0c4cc;
  font-style: italic;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  :deep(.el-table) {
    font-size: 14px;
  }
  
  .pagination-container {
    padding: 16px;
  }
  
  :deep(.el-pagination) {
    justify-content: center;
  }
}
</style>