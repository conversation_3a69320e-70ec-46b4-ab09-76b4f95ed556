<template>
  <div class="setup-container">
    <div class="setup-card">
      <div class="setup-header">
        <h1>Admin Setup</h1>
        <p>Create the first administrator account for ZFC License Management</p>
      </div>
      
      <el-form
        :model="setupForm"
        :rules="rules"
        ref="setupFormRef"
        @submit.prevent="handleSetup"
        label-position="top"
        size="large"
      >
        <el-form-item label="Username" prop="username">
          <el-input
            v-model="setupForm.username"
            placeholder="Enter admin username"
            @keyup.enter="handleSetup"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="Password" prop="password">
          <el-input
            v-model="setupForm.password"
            type="password"
            placeholder="Enter password"
            show-password
            @keyup.enter="handleSetup"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="Confirm Password" prop="confirmPassword">
          <el-input
            v-model="setupForm.confirmPassword"
            type="password"
            placeholder="Confirm password"
            show-password
            @keyup.enter="handleSetup"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="authStore.isLoading"
            style="width: 100%"
          >
            Create Admin Account
          </el-button>
        </el-form-item>
      </el-form>
      
      <el-alert
        v-if="authStore.error"
        :title="authStore.error"
        type="error"
        :closable="false"
        style="margin-top: 16px"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const setupFormRef = ref<FormInstance>()
const setupForm = reactive({
  username: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value !== setupForm.password) {
    callback(new Error('Passwords do not match'))
  } else {
    callback()
  }
}

const rules: FormRules = {
  username: [
    { required: true, message: 'Please enter username', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters', trigger: 'blur' },
    { max: 50, message: 'Username must be at most 50 characters', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter password', trigger: 'blur' },
    { min: 8, message: 'Password must be at least 8 characters', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: 'Please confirm password', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleSetup = async () => {
  if (!setupFormRef.value) return
  
  await setupFormRef.value.validate(async (valid) => {
    if (valid) {
      const success = await authStore.createAdminUser(setupForm.username, setupForm.password)
      if (success) {
        ElMessage.success('Admin account created successfully')
        router.push('/login')
      }
    }
  })
}
</script>

<style scoped>
.setup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.setup-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
}

.setup-header {
  text-align: center;
  margin-bottom: 32px;
}

.setup-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.setup-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  height: 44px;
  font-weight: 500;
}

@media (max-width: 480px) {
  .setup-card {
    padding: 24px;
  }
  
  .setup-header h1 {
    font-size: 24px;
  }
}
</style>