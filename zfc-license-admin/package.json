{"name": "zfc-license-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.0.0", "axios": "^1.7.8", "element-plus": "^2.9.0", "pinia": "^2.2.8", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "typescript": "^5.0.2", "vite": "^6.0.1", "vue-tsc": "^1.8.5"}}