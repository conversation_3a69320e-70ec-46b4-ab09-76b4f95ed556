[package]
name = "common"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { version = "1.0.75", features = ["backtrace"] }
chacha20poly1305 = "0.10.1"
chrono = "0.4.31"
dotenv = { version = "0.15.0" }
futures = "0.3"
hex = "0.4.3"
k256 = { version = "0.13.1", features = ["default", "ecdh", "ecdsa-core", "serde", "ecdsa", "alloc"] }
rand_core = "0.6.4"
rmp-serde = "1.1.2"
serde = "1.0.188"
serde_json = "1.0.107"
tokio-util = "0.7.9"
tokio =  {workspace = true}
parking_lot = {workspace = true}
taos = { version = "0.12"}
mdsn = "0.2.25"
bb8 = "0.8.1"
bb8-redis = "0.13.1"
redis = { version = "0.23.3", features = ["tokio-comp", "tls", "serde_json", "serde", "tokio-native-tls-comp", "json"] }
uuid = { version = "1.8.0", features = ["v4", "fast-rng"] }
prisma-client-rust = { git = "https://github.com/Brendonovich/prisma-client-rust", tag = "0.6.11", no-default-features = true, features = ["postgresql"] }
sql-query-connector = { git = "https://github.com/Brendonovich/prisma-engines", tag = "pcr-0.6.10", features = ["vendored-openssl"] }
private_tun = { workspace = true }
async-trait = "0.1.73"
futures-util = { version = "0.3.28", features = ["default", "sink", "channel"] }
log = "0.4"
smallvec = "1.13.2"
surge-ping = "0"
rand = "0.9"
tcp_over_multi_tcp_client = { workspace = true }
itertools = "0.10"
sha2 = "0.10.8"
socket2 = "0.6"
