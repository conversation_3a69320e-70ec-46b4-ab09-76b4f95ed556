use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// Offline token structures
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OfflineToken {
    pub instance_id: String,
    pub device_fingerprint: String,
    pub issued_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub session_id: String,
    pub entitlements: serde_json::Value,
}

impl OfflineToken {
    pub fn new(
        instance_id: String,
        device_fingerprint: String,
        session_id: String,
        entitlements: serde_json::Value,
        duration_hours: u64,
    ) -> Self {
        let now = chrono::Utc::now();
        let expires_at = now + chrono::Duration::hours(duration_hours as i64);

        Self {
            instance_id,
            device_fingerprint,
            issued_at: now,
            expires_at,
            session_id,
            entitlements,
        }
    }

    pub fn is_expired(&self) -> bool {
        chrono::Utc::now() > self.expires_at
    }

    pub fn is_valid_for_device(&self, device_fingerprint: &str) -> bool {
        self.device_fingerprint == device_fingerprint && !self.is_expired()
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SignedOfflineToken {
    pub token: OfflineToken,
    pub signature: String,
}

impl SignedOfflineToken {
    pub fn new(token: OfflineToken, signature: String) -> Self {
        Self { token, signature }
    }
}

// WebSocket message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    AuthRequest {
        jwt: String,
        device_fingerprint: String,
    },
    AuthResponse {
        success: bool,
        offline_token: Option<SignedOfflineToken>,
        error: Option<String>,
    },
    Ping {
        timestamp: DateTime<Utc>,
    },
    Pong {
        timestamp: DateTime<Utc>,
    },
    LicenseRevoked {
        reason: String,
    },
    ConnectionConflict {
        message: String,
    },
    EntitlementsRequest {
        timestamp: DateTime<Utc>,
    },
    EntitlementsResponse {
        success: bool,
        entitlements: Option<serde_json::Value>,
        error: Option<String>,
    },
}

// License-related response structures
#[derive(Debug, Serialize, Deserialize)]
pub struct LicenseInfo {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

// Renewal request structures
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRenewalRequestRequest {
    #[serde(rename = "paymentMethod")]
    pub payment_method: String,
    #[serde(rename = "paymentAmount")]
    pub payment_amount: String,
    #[serde(rename = "paymentProof")]
    pub payment_proof: String,
    #[serde(rename = "customerMessage")]
    pub customer_message: Option<String>,
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32, // 月数
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalRequestResponse {
    pub id: String,
    #[serde(rename = "paymentMethod")]
    pub payment_method: String,
    #[serde(rename = "paymentAmount")]
    pub payment_amount: String,
    #[serde(rename = "paymentProof")]
    pub payment_proof: String,
    #[serde(rename = "customerMessage")]
    pub customer_message: Option<String>,
    pub status: String,
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32,
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    #[serde(rename = "instanceName")]
    pub instance_name: Option<String>,
    #[serde(rename = "processedAt")]
    pub processed_at: Option<DateTime<Utc>>,
    #[serde(rename = "processedBy")]
    pub processed_by: Option<String>,
    #[serde(rename = "generatedRenewalCodeId")]
    pub generated_renewal_code_id: Option<String>,
    #[serde(rename = "adminNotes")]
    pub admin_notes: Option<String>,
    // 新增字段
    #[serde(rename = "hasCustomerReplies")]
    pub has_customer_replies: bool,
    #[serde(rename = "lastMessageAt")]
    pub last_message_at: Option<DateTime<Utc>>,
    #[serde(rename = "canReply")]
    pub can_reply: bool,
    // 消息列表（可选）
    pub messages: Option<Vec<RenewalRequestMessageResponse>>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

// 消息相关结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct SendRenewalRequestMessageRequest {
    pub content: String,
    #[serde(rename = "messageType")]
    pub message_type: String, // "customer" or "admin"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalRequestMessageResponse {
    pub id: String,
    #[serde(rename = "renewalRequestId")]
    pub renewal_request_id: String,
    #[serde(rename = "messageType")]
    pub message_type: String,
    pub content: String,
    #[serde(rename = "authorId")]
    pub author_id: Option<String>,
    #[serde(rename = "authorName")]
    pub author_name: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminCloseRenewalRequestRequest {
    #[serde(rename = "adminNotes")]
    pub admin_notes: Option<String>,
}

// API response structures
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub page: u32,
    #[serde(rename = "pageSize")]
    pub page_size: u32,
    pub total: u32,
    #[serde(rename = "totalPages")]
    pub total_pages: u32,
}

// Price calculation response
#[derive(Debug, Serialize, Deserialize)]
pub struct CalculateRenewalPriceResponse {
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: String,
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32,
    #[serde(rename = "totalPrice")]
    pub total_price: String,
    pub currency: String,
}

// Version responses
#[derive(Debug, Serialize, Deserialize)]
pub struct WorkerVersionResponse {
    #[serde(rename = "recommendedVersion")]
    pub recommended_version: String,
    #[serde(rename = "controllerVersion")]
    pub controller_version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ForwarderVersionResponse {
    #[serde(rename = "recommendedVersion")]
    pub recommended_version: String,
    #[serde(rename = "controllerVersion")]
    pub controller_version: String,
}

// Heartbeat request/response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeartbeatRequest {
    pub instance_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HeartbeatResponse {
    pub token: String,
}

// Public key response
#[derive(Debug, Serialize, Deserialize)]
pub struct PublicKeyResponse {
    #[serde(rename = "publicKey")]
    pub public_key: String,
    pub algorithm: String,
}

// Response structures for zf-controler API calls to fix serde_json::Value usage
#[derive(Debug, Serialize, Deserialize)]
pub struct ApplyRenewalCodeResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ControlerRenewalHistoryData {
    pub date: String,
    pub code_id: String,
    pub extended_days: i64,
    pub new_expiry_date: String,
    pub status: String,
    pub amount: Option<String>, // Renewal amount/price
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ControlerRenewalHistoryResponse {
    pub data: Vec<ControlerRenewalHistoryData>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ControlerLicenseInstanceData {
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: String,
    pub id: String,
    pub entitlements: Entitlements,
    pub monthly_rate: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ControlerLicenseInfoResponse {
    pub data: ControlerLicenseInstanceData,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ApplyRenewalCodeRequest {
    pub renewal_code: String,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RenewalHistoryResponse {
    pub history: Vec<RenewalHistoryItem>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RenewalHistoryItem {
    pub date: DateTime<Utc>,
    pub code_id: String,
    pub extended_days: i32,
    pub new_expiry_date: DateTime<Utc>,
    pub status: String,
    pub amount: Option<String>, // Adding amount field for renewal cost
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Entitlements {
    #[serde(default = "default_max_workers")]
    pub max_workers: u32,

    #[serde(default = "default_max_subscription_number")]
    pub max_subscription_number: u32,

    // todo remove
    #[serde(default = "default_max_users_per_worker")]
    pub max_users_per_worker: u32,

    // todo remove
    #[serde(default)]
    pub feature_udp_forwarding_enabled: bool,
}

fn default_max_workers() -> u32 {
    50
}
fn default_max_subscription_number() -> u32 {
    50
}

fn default_max_users_per_worker() -> u32 {
    10
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EntitlementsData {
    pub entitlements: Entitlements,
    pub current_workers: u32,
    pub connected_workers: u32,
    pub current_subscription_count: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetEntitlementsResponse {
    pub success: bool,
    pub data: Option<EntitlementsData>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApplyRenewalRequest {
    pub renewal_code_id: String,
}