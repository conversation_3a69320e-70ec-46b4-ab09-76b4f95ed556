use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// Redis缓存用的结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedNetSpeedItem {
    pub rx: u64,       // unit: byte/s
    pub tx: u64,       // unit: byte/s
    pub total_rx: u64, // unit: byte
    pub total_tx: u64, // unit: byte
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CachedSystemStatsItem {
    pub cpu_usage: f32,    // percentage
    pub memory_total: u64, // unit: byte
    pub memory_used: u64,  // unit: byte
    pub uptime: u64,       // unit: second
    pub tcp_connections: u32, // total TCP connections count
    pub udp_connections: u32, // total UDP connections count
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CachedUserLineSpeedItem {
    pub subscription_id: i32,
    pub line_id: i32,
    pub line_name: Option<String>,
    pub upload_speed: f64,   // unit: byte/s
    pub download_speed: f64, // unit: byte/s
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedLatencyRealtimeStatus {
    pub config_id: i32,
    pub target_address: String,
    pub display_name: String,
    pub test_type: String,
    
    // SmokePing风格字段
    pub loss_count: u32, // 丢包计数 (0-20)
    pub median_latency_us: Option<u64>, // 中位数延迟
    pub avg_latency_us: Option<u64>, // 平均延迟 (兼容性)
    
    // 兼容性字段
    pub error_msg: Option<String>,
    pub last_test_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl CachedLatencyRealtimeStatus {
    /// 计算成功率 (SmokePing风格)
    pub fn success_rate(&self) -> f32 {
        if self.loss_count >= 20 {
            0.0
        } else {
            (20 - self.loss_count) as f32 / 20.0
        }
    }
    
    /// 计算丢包率百分比
    pub fn packet_loss_percentage(&self) -> f32 {
        (self.loss_count as f32 / 20.0) * 100.0
    }
    
    /// 是否成功 (兼容性API)
    pub fn success(&self) -> bool {
        self.loss_count < 20
    }
    
    /// 延迟值 (兼容性API)
    pub fn latency_us(&self) -> Option<u64> {
        self.avg_latency_us.or(self.median_latency_us)
    }
}

// TDengine measurement structures
// Tags are now regular fields and will be handled in SQL INSERT statements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetcardSpeedMeasurement {
    pub time: DateTime<Utc>,
    pub interface: String, // tag field in TDengine
    pub agent_id: i32,     // tag field in TDengine
    pub tx: f64,           // unit: Byte/s
    pub rx: f64,           // unit: Byte/s
    pub total_tx: u64,     // unit: Byte
    pub total_rx: u64,     // unit: Byte
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStatsMeasurement {
    pub time: DateTime<Utc>,
    pub agent_id: i32,        // tag field in TDengine
    pub cpu_usage: f32,       // percentage
    pub memory_total: u64,    // unit: byte
    pub memory_used: u64,     // unit: byte
    pub uptime: u64,          // unit: second
    pub tcp_connections: u32, // total TCP connections count
    pub udp_connections: u32, // total UDP connections count
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLineInfoMeasurement {
    pub time: DateTime<Utc>,
    pub subscription_id: i32,  // tag field in TDengine
    pub line_id: i32,          // tag field in TDengine
    pub traffic_inc: u64,      // unit: Byte
    pub total_speed: f64,      // unit: Byte/s
    pub client_ips: String,    // json string Vec<SocketAddr>
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

// TDengine helper implementations
impl NetcardSpeedMeasurement {
    pub fn measurement_name() -> &'static str {
        "netcard_speed"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!(
            "netcard_speed_{}_{}",
            self.agent_id,
            self.interface.replace('-', "_").replace(' ', "_")
        );
        format!(
            "INSERT INTO {} USING netcard_speed TAGS ({}, '{}') VALUES ('{}', {}, {}, {}, {})",
            table_name,
            self.agent_id,
            self.interface,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.tx,
            self.rx,
            self.total_tx,
            self.total_rx
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.tx,
            self.rx,
            self.total_tx,
            self.total_rx
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}_{}", self.agent_id, self.interface)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!(
            "netcard_speed_{}_{}",
            self.agent_id,
            self.interface.replace('-', "_").replace(' ', "_")
        );
        format!(
            "INSERT INTO {} USING netcard_speed TAGS ({}, '{}')",
            table_name,
            self.agent_id,
            self.interface
        )
    }
}

impl SystemStatsMeasurement {
    pub fn measurement_name() -> &'static str {
        "system_stats"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!("system_stats_{}", self.agent_id);
        format!(
            "INSERT INTO {} USING system_stats TAGS ({}) VALUES ('{}', {}, {}, {}, {}, {}, {})",
            table_name,
            self.agent_id,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.cpu_usage,
            self.memory_total,
            self.memory_used,
            self.uptime,
            self.tcp_connections,
            self.udp_connections
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, {}, {}, {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.cpu_usage,
            self.memory_total,
            self.memory_used,
            self.uptime,
            self.tcp_connections,
            self.udp_connections
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}", self.agent_id)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!("system_stats_{}", self.agent_id);
        format!(
            "INSERT INTO {} USING system_stats TAGS ({})",
            table_name,
            self.agent_id
        )
    }
}

impl UserLineInfoMeasurement {
    pub fn measurement_name() -> &'static str {
        "user_line_info"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!("user_line_info_{}_{}", self.subscription_id, self.line_id);
        format!(
            "INSERT INTO {} USING user_line_info TAGS ({}, {}) VALUES ('{}', {}, {}, '{}', {}, {})",
            table_name,
            self.subscription_id,
            self.line_id,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.traffic_inc,
            self.total_speed,
            self.client_ips,
            self.client_ips_count,
            self.connection_count
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, '{}', {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.traffic_inc,
            self.total_speed,
            self.client_ips,
            self.client_ips_count,
            self.connection_count
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}_{}", self.subscription_id, self.line_id)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!("user_line_info_{}_{}", self.subscription_id, self.line_id);
        format!(
            "INSERT INTO {} USING user_line_info TAGS ({}, {})",
            table_name,
            self.subscription_id,
            self.line_id
        )
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LatencyTestMeasurement {
    pub time: DateTime<Utc>,
    pub server_id: i32,        // tag field in TDengine
    pub config_id: i32,        // tag field in TDengine
    pub target_address: String, // tag field in TDengine
    pub latency_us: i64,       // unit: microseconds (0 for failed tests)
    pub success: bool,         // test success/failure
    pub error_msg: String,     // error message for failed tests
    // SmokePing-style statistics
    pub test_round: i32,            // test round identifier
    pub samples_count: i32,         // number of samples in this round
    pub min_latency_us: Option<i64>, // minimum latency in this round
    pub max_latency_us: Option<i64>, // maximum latency in this round
    pub median_latency_us: Option<i64>, // median latency in this round
    pub stddev_latency_us: Option<f64>, // standard deviation
    pub packet_loss_rate: f32,      // packet loss rate (0.0-100.0)
}

impl LatencyTestMeasurement {
    pub fn measurement_name() -> &'static str {
        "server_latency_tests"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!("latency_{}_{}", self.server_id, self.config_id);
        format!(
            "INSERT INTO {} USING server_latency_tests TAGS ({}, {}, '{}') VALUES ('{}', {}, {}, '{}', {}, {}, {}, {}, {}, {}, {})",
            table_name,
            self.server_id,
            self.config_id,
            self.target_address,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.latency_us,
            self.success,
            self.error_msg,
            self.test_round,
            self.samples_count,
            self.min_latency_us.unwrap_or(0),
            self.max_latency_us.unwrap_or(0),
            self.median_latency_us.unwrap_or(0),
            self.stddev_latency_us.unwrap_or(0.0),
            self.packet_loss_rate
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, '{}', {}, {}, {}, {}, {}, {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.latency_us,
            self.success,
            self.error_msg,
            self.test_round,
            self.samples_count,
            self.min_latency_us.unwrap_or(0),
            self.max_latency_us.unwrap_or(0),
            self.median_latency_us.unwrap_or(0),
            self.stddev_latency_us.unwrap_or(0.0),
            self.packet_loss_rate
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}_{}", self.server_id, self.config_id)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!("latency_{}_{}", self.server_id, self.config_id);
        format!(
            "INSERT INTO {} USING server_latency_tests TAGS ({}, {}, '{}')",
            table_name,
            self.server_id,
            self.config_id,
            self.target_address
        )
    }
}


/// 缓存的图表数据
#[derive(Debug, Serialize, Deserialize)]
pub struct CachedChart {
    pub server_id: i32,
    pub config_id: i32,
    pub time_range: String,
    pub target_address: String,
    pub display_name: String,
    pub image_data: String, // Base64 编码的图片数据
    pub generated_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}