use crate::chrono::Utc;
use crate::prisma::PrismaClient;
use crate::redis_lock::DistributedLock;
use crate::retry_queue::{BoxedRetryQueue, Job, RetryError};
use crate::{get_line_lock, prisma};
use anyhow::{anyhow, Result};
use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use chrono::Days;
use futures::future::try_join_all;
use itertools::Itertools;
use log::error;
use redis::AsyncCommands;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::Mutex;
macro_rules! get_number {
    ($conn:expr, $k:expr, $d:expr) => {
        match $conn.get($k).await {
            Ok(v) => Ok::<_, anyhow::Error>(v),
            Err(e) => match e.kind() {
                redis::ErrorKind::TypeError => Ok::<_, anyhow::Error>($d),
                _ => Err(e.into()),
            },
        }
    };
}

pub type RedisPool = Pool<RedisConnectionManager>;
pub struct ResetTrafficContext {
    pub db: Arc<PrismaClient>,
    pub redis: RedisPool,
    pub redis_lock: Arc<DistributedLock>,
    pub traffic_retry_queue: Arc<BoxedRetryQueue>,
    pub redis_line_lock: Arc<Mutex<HashMap<i32, Arc<DistributedLock>>>>,
    pub update_reset_time: bool,
}

impl ResetTrafficContext {
    pub fn new(
        db: Arc<PrismaClient>,
        redis: RedisPool,
        traffic_retry_queue: Arc<BoxedRetryQueue>,
        redis_lock: Arc<DistributedLock>,
        redis_line_lock: Arc<Mutex<HashMap<i32, Arc<DistributedLock>>>>,
        update_reset_time: bool,
    ) -> Self {
        Self {
            db,
            redis,
            redis_lock,
            traffic_retry_queue,
            redis_line_lock,
            update_reset_time,
        }
    }
}

pub type ArcResetTrafficContext = Arc<ResetTrafficContext>;

async fn reset_traffic(ctx: ArcResetTrafficContext) -> anyhow::Result<()> {
    let all_users = ctx.db.subscription().find_many(vec![]).exec().await?;
    let now = Utc::now().fixed_offset();
    for u in all_users {
        if u.valid_until > now {
            match (u.last_reset, u.reset_days) {
                (Some(last_reset_day), Some(reset_days)) => {
                    if reset_days == 0 {
                        continue;
                    }
                    let should_reset_day = last_reset_day
                        .checked_add_days(Days::new(reset_days as u64))
                        .ok_or(anyhow!("{last_reset_day} add {reset_days} failed"))?;
                    if should_reset_day <= now {
                        log::info!("emit reset traffic task for sub: {}", u.id);
                        let _ = ctx
                            .traffic_retry_queue
                            .send(Box::new(ResetTrafficTask::new(ctx.clone(), u.id)))
                            .await;
                    }
                }
                _ => {}
            }
        }
    }
    Ok(())
}

pub async fn reset_traffic_background(ctx: ArcResetTrafficContext) {
    loop {
        log::info!("period reset traffic begin");
        let _ = reset_traffic(ctx.clone()).await;
        tokio::time::sleep(std::time::Duration::from_secs(300)).await;
    }
}

pub async fn update_usage_to_db(ctx: &ArcResetTrafficContext) -> Result<()> {
    let start_time = tokio::time::Instant::now();
    log::info!("update usage to db begin");
    let ports = ctx
        .db
        .port()
        .find_many(vec![])
        .with(prisma::port::subscription::fetch())
        .exec()
        .await?;
    let mut total_subs: HashSet<i32> = HashSet::new();
    let grouped = ports
        .iter()
        .filter(|p| p.outbound_endpoint_id.is_some())
        .group_by(|p| p.outbound_endpoint_id.as_ref().map(|x| *x).unwrap())
        .into_iter()
        .fold(HashMap::new(), |mut acc, (k, v)| {
            acc.entry(k).or_insert_with(|| Vec::new()).extend(v);
            acc
        });
    let all_tasks = grouped
        .into_iter()
        .map(|(line_id, ports)| async move {
            let _line_lock = get_line_lock(&ctx.redis, &ctx.redis_line_lock, line_id).await?;
            let tasks = ports
                .into_iter()
                .filter_map(|p| {
                    let Ok(Some(sub)) = p.subscription() else {
                        return None;
                    };
                    Some((sub, p))
                })
                .map(|(sub, p)| async move {
                    let port_used_cache: i128 =
                        match get_number!(
                            ctx.redis.get().await?,
                            format!("sub:line:port:used:{}:{}:{}", sub.id, line_id, p.port_v_4),
                            0 as i128
                        ) {
                            Ok(v) => v,
                            Err(e) => {
                                error!(
                            "get sub: {} port: {} redis usage failed: {} on update_usage_to_db",
                            sub.id, p.port_v_4, e
                        );
                                return Err(anyhow::anyhow!(
                            "get sub: {} port: {} redis usage failed: {} on update_usage_to_db",
                            sub.id, p.port_v_4, e
                        ));
                            }
                        };
                    let port_used_db = p.traffic_in;
                    let port_used_amount = std::cmp::max(port_used_cache, port_used_db as i128);
                    if port_used_amount != port_used_db as i128 {
                        if let Err(e) = ctx
                            .db
                            .port()
                            .update(
                                prisma::port::id::equals(p.id),
                                vec![prisma::port::traffic_in::set(port_used_amount as i64)],
                            )
                            .exec()
                            .await
                        {
                            error!(
                                "update sub: {} port: {} db usage failed: {} on update_usage_to_db",
                                sub.id, p.port_v_4, e
                            );
                            return Err(anyhow::anyhow!(
                                "update sub: {} port: {} db usage failed: {} on update_usage_to_db",
                                sub.id,
                                p.port_v_4,
                                e
                            ));
                        }
                    }

                    if port_used_amount != port_used_cache {
                        if let Err(e) = ctx
                            .redis
                            .get()
                            .await?
                            .set::<_, _, ()>(
                                format!("sub:line:port:used:{}:{}:{}", sub.id, line_id, p.port_v_4),
                                port_used_amount.to_string(),
                            )
                            .await
                        {
                            error!(
                            "update sub: {} port: {} redis usage failed: {} on update_usage_to_db",
                            sub.id, p.port_v_4, e
                        );
                            return Err(anyhow::anyhow!(
                            "update sub: {} port: {} redis usage failed: {} on update_usage_to_db",
                            sub.id,
                            p.port_v_4,
                            e
                        ));
                        }
                        log::info!(
                            "update usage to db sub: {} line: {} port: {}  usage: {} success",
                            sub.id,
                            line_id,
                            p.port_v_4,
                            port_used_amount
                        );
                    }
                    return Ok((sub.id, port_used_amount));
                })
                .collect::<Vec<_>>();
            try_join_all(tasks).await
        })
        .collect::<Vec<_>>();
    // 合并结果
    for line_results in try_join_all(all_tasks).await? {
        total_subs.extend(line_results.into_iter().map(|(sub, _)| sub));
    }

    let _guard = match ctx.redis_lock.clone().acquire().await {
        Ok(g) => g,
        Err(_) => {
            error!("get traffic_usage_lock failed on update_usage_to_db");
            return Err(anyhow::anyhow!(
                "get traffic_usage_lock failed on update_usage_to_db"
            ));
        }
    };
    let mut conn = ctx.redis.get().await?;

    // Update subscription line usage first
    for sub in &total_subs {
        // Get all lines for this subscription
        let sub_data = ctx
            .db
            .subscription()
            .find_unique(prisma::subscription::id::equals(*sub))
            .select(prisma::subscription::select!({ lines }))
            .exec()
            .await?;

        if let Some(sub_data) = sub_data {
            for line_id in sub_data.lines {
                let line_usage_key = format!("sub:line:used:{}:{}", sub, line_id);
                let line_usage_cache: i128 =
                    match get_number!(conn, line_usage_key.clone(), 0 as i128) {
                        Ok(v) => v,
                        Err(e) => {
                            log::warn!(
                                "get sub: {} line: {} redis usage failed: {} on update_usage_to_db",
                                sub,
                                line_id,
                                e
                            );
                            continue;
                        }
                    };

                // Get existing line usage from database
                let line_usage_db = ctx
                    .db
                    .subscription_line_usage()
                    .find_unique(prisma::subscription_line_usage::subscription_id_line_id(
                        *sub, line_id,
                    ))
                    .select(prisma::subscription_line_usage::select!({
                        used_traffic_low
                        used_traffic_high
                    }))
                    .exec()
                    .await?
                    .map(|x| {
                        let low = x.used_traffic_low.unwrap_or(0);
                        let high = x.used_traffic_high.unwrap_or(0);
                        ((high as i128) << 64) | low as i128
                    })
                    .unwrap_or_default();

                let result_line_usage = std::cmp::max(line_usage_cache, line_usage_db);

                // Update database if needed
                if result_line_usage != line_usage_db {
                    log::info!(
                        "update sub: {} line: {} usage: {} to db",
                        sub,
                        line_id,
                        result_line_usage
                    );
                    if let Err(e) = ctx
                        .db
                        .subscription_line_usage()
                        .upsert(
                            prisma::subscription_line_usage::subscription_id_line_id(*sub, line_id),
                            prisma::subscription_line_usage::create(
                                line_id,
                                prisma::subscription::id::equals(*sub),
                                vec![
                                    prisma::subscription_line_usage::used_traffic_low::set(Some(
                                        (result_line_usage & 0xffffffffffffffff) as i64,
                                    )),
                                    prisma::subscription_line_usage::used_traffic_high::set(Some(
                                        (result_line_usage >> 64) as i64,
                                    )),
                                ],
                            ),
                            vec![
                                prisma::subscription_line_usage::used_traffic_low::set(Some(
                                    (result_line_usage & 0xffffffffffffffff) as i64,
                                )),
                                prisma::subscription_line_usage::used_traffic_high::set(Some(
                                    (result_line_usage >> 64) as i64,
                                )),
                            ],
                        )
                        .exec()
                        .await
                    {
                        log::warn!(
                            "update sub: {} line: {} usage: {} to db failed: {}",
                            sub,
                            line_id,
                            result_line_usage,
                            e
                        );
                        continue;
                    }
                }

                // Update Redis if needed
                if result_line_usage != line_usage_cache {
                    if let Err(e) = conn
                        .set::<_, _, ()>(line_usage_key, result_line_usage.to_string())
                        .await
                    {
                        log::warn!(
                            "update sub: {} line: {} redis usage failed: {} on update_usage_to_db",
                            sub,
                            line_id,
                            e
                        );
                    } else {
                        log::info!(
                            "update sub: {} line: {} usage: {} success",
                            sub,
                            line_id,
                            result_line_usage
                        );
                    }
                }
            }
        }
    }

    // Update subscription total usage
    for sub in total_subs {
        let total_in_cache: i128 = match get_number!(conn, format!("sub:used:{}", sub), 0 as i128) {
            Ok(v) => v,
            Err(e) => {
                println!(
                    "get sub: {} redis usage failed: {} on update_usage_to_db",
                    sub, e
                );
                continue;
            }
        };

        let total_in_db = ctx
            .db
            .subscription()
            .find_unique(prisma::subscription::id::equals(sub))
            .select(prisma::subscription::select!({
                used_traffic_low
                used_traffic_high
            }))
            .exec()
            .await
            .into_iter()
            .filter_map(|x| {
                x.map(|x| {
                    (
                        x.used_traffic_low.unwrap_or(0),
                        x.used_traffic_high.unwrap_or(0),
                    )
                })
            })
            .map(|(low, high)| ((high as i128) << 64) | low as i128)
            .next()
            .unwrap_or_default();
        let result_total = std::cmp::max(total_in_cache, total_in_db);
        if result_total != total_in_db {
            log::info!("update sub: {} total usage: {} to db", sub, result_total);
            if let Err(e) = ctx
                .db
                .subscription()
                .update(
                    prisma::subscription::id::equals(sub),
                    vec![
                        prisma::subscription::used_traffic_low::set(Some(
                            (result_total & 0xffffffffffffffff) as i64,
                        )),
                        prisma::subscription::used_traffic_high::set(Some(
                            (result_total >> 64) as i64,
                        )),
                    ],
                )
                .exec()
                .await
            {
                log::warn!(
                    "update sub: {} total usage: {} to db failed: {}",
                    sub,
                    result_total,
                    e
                );
                continue;
            }
        }

        if result_total != total_in_cache {
            let _ = ctx
                .redis
                .get()
                .await?
                .set::<_, _, ()>(format!("sub:used:{}", sub), result_total.to_string())
                .await?;
            log::info!("update sub: {} total usage: {} success", sub, result_total);
        }
    }
    log::info!(
        "update usage to db finished time: {}ms",
        start_time.elapsed().as_millis()
    );
    Ok(())
}

enum State {
    FetchList(i32),
    ClearPortUsage {
        all_data: HashMap<i32, HashSet<i32>>, // line_id -> port_v4
        sub_id: i32,
    },
    ClearSubsUsage {
        sub_id: i32,
    },
}

pub struct ResetTrafficTask {
    ctx: ArcResetTrafficContext,
    state: State,
}

impl ResetTrafficTask {
    pub fn new(ctx: ArcResetTrafficContext, sub_id: i32) -> Self {
        Self {
            ctx,
            state: State::FetchList(sub_id),
        }
    }
}

prisma::port::select!(sub_port_only_id {
    outbound_endpoint_id
    port_v_4
});

#[async_trait::async_trait]
impl Job for ResetTrafficTask {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        loop {
            match &mut self.state {
                State::FetchList(sub_id) => {
                    let ports = self
                        .ctx
                        .db
                        .port()
                        .find_many(vec![prisma::port::subscription_id::equals(Some(*sub_id))])
                        .select(sub_port_only_id::select())
                        .exec()
                        .await
                        .map_err(|e| {
                            RetryError::Retry(anyhow::anyhow!(
                                "find ports failed when handle port traffic usage: {}",
                                e
                            ))
                        })?;
                    let all_data = ports
                        .iter()
                        .filter(|x| x.outbound_endpoint_id.is_some())
                        .group_by(|x| x.outbound_endpoint_id.as_ref().map(|x| *x).unwrap())
                        .into_iter()
                        .fold(HashMap::new(), |mut acc, (k, v)| {
                            acc.entry(k)
                                .or_insert_with(|| HashSet::new())
                                .extend(v.map(|x| x.port_v_4));
                            acc
                        });

                    self.state = State::ClearPortUsage {
                        all_data,
                        sub_id: *sub_id,
                    };
                }
                State::ClearPortUsage { all_data, sub_id } => {
                    let mut conn = self.ctx.redis.get().await.map_err(|e| {
                        RetryError::Retry(anyhow::anyhow!("get redis connection failed: {}", e))
                    })?;
                    for (line_id, ports) in all_data.iter_mut() {
                        if ports.is_empty() {
                            continue;
                        }
                        let _line_lock =
                            get_line_lock(&self.ctx.redis, &self.ctx.redis_line_lock, *line_id)
                                .await
                                .map_err(|e| {
                                    RetryError::Retry(anyhow::anyhow!(
                                        "get line lock failed: {}",
                                        e
                                    ))
                                })?;
                        let (ctl, tx) = self.ctx.db._transaction().begin().await.map_err(|e| {
                            RetryError::Retry(anyhow::anyhow!("begin transaction failed: {}", e))
                        })?;
                        match async {
                            for port in ports.iter() {
                                conn.set::<_, _, ()>(
                                    format!("sub:line:port:used:{}:{}:{}", sub_id, line_id, port),
                                    0,
                                )
                                .await?;
                            }
                            // clear port db traffic_in
                            tx.port()
                                .update_many(
                                    vec![
                                        prisma::port::subscription_id::equals(Some(*sub_id)),
                                        prisma::port::outbound_endpoint_id::equals(Some(*line_id)),
                                    ],
                                    vec![prisma::port::traffic_in::set(0)],
                                )
                                .exec()
                                .await?;
                            Ok::<_, anyhow::Error>(())
                        }
                        .await
                        {
                            Ok(_) => {
                                ctl.commit(tx).await.map_err(|e| {
                                    RetryError::Retry(anyhow::anyhow!(
                                        "commit transaction failed: {}",
                                        e
                                    ))
                                })?;
                            }
                            Err(e) => {
                                error!(
                                    "reset line: {} port usage update db failed: {}",
                                    line_id, e
                                );
                                ctl.rollback(tx).await.map_err(|e| {
                                    RetryError::Retry(anyhow::anyhow!(
                                        "rollback transaction failed: {}",
                                        e
                                    ))
                                })?;
                            }
                        }
                        ports.clear();
                    }
                    self.state = State::ClearSubsUsage { sub_id: *sub_id };
                }
                State::ClearSubsUsage { sub_id } => {
                    let _lock = match self.ctx.redis_lock.clone().acquire().await {
                        Ok(l) => l,
                        Err(_) => {
                            error!("get traffic_usage_lock failed");
                            return Err(RetryError::Retry(anyhow::anyhow!(
                                "get traffic_usage_lock failed"
                            )));
                        }
                    };
                    let (ctl, tx) = self.ctx.db._transaction().begin().await.map_err(|e| {
                        RetryError::Retry(anyhow::anyhow!("begin transaction failed: {}", e))
                    })?;
                    match async {
                        // Clear main subscription usage
                        self.ctx
                            .redis
                            .get()
                            .await?
                            .set::<_, _, ()>(format!("sub:used:{}", sub_id), 0)
                            .await?;

                        // Get subscription lines and clear line usage
                        let sub_data = tx
                            .subscription()
                            .find_unique(prisma::subscription::id::equals(*sub_id))
                            .select(prisma::subscription::select!({ lines }))
                            .exec()
                            .await?;

                        if let Some(sub_data) = sub_data {
                            let mut conn = self.ctx.redis.get().await?;
                            for line_id in sub_data.lines {
                                // Clear Redis line usage
                                conn.set::<_, _, ()>(
                                    format!("sub:line:used:{}:{}", sub_id, line_id),
                                    0,
                                )
                                .await?;
                            }
                        }

                        // Clear database line usage records
                        tx.subscription_line_usage()
                            .delete_many(vec![
                                prisma::subscription_line_usage::subscription_id::equals(*sub_id),
                            ])
                            .exec()
                            .await?;

                        let mut update_vec = vec![
                            prisma::subscription::used_traffic_low::set(Some(0)),
                            prisma::subscription::used_traffic_high::set(Some(0)),
                        ];
                        if self.ctx.update_reset_time {
                            update_vec.push(prisma::subscription::last_reset::set(Some(
                                Utc::now().fixed_offset(),
                            )));
                        }
                        tx.subscription()
                            .update(prisma::subscription::id::equals(*sub_id), update_vec)
                            .exec()
                            .await?;
                        Ok::<_, anyhow::Error>(())
                    }
                    .await
                    {
                        Ok(_) => {
                            ctl.commit(tx).await.map_err(|e| {
                                RetryError::Retry(anyhow::anyhow!(
                                    "commit transaction failed: {}",
                                    e
                                ))
                            })?;
                        }
                        Err(e) => {
                            ctl.rollback(tx).await.map_err(|e| {
                                RetryError::Retry(anyhow::anyhow!(
                                    "rollback transaction failed: {}",
                                    e
                                ))
                            })?;
                            error!("update subscription failed: {}", e);
                            return Err(RetryError::Retry(e));
                        }
                    }

                    log::info!("reset traffic for sub: {} success", sub_id);
                    return Ok(());
                }
            }
        }
    }
}

pub struct Update2DBTask {
    ctx: ArcResetTrafficContext,
}
impl Update2DBTask {
    pub fn new(ctx: ArcResetTrafficContext) -> Self {
        Self { ctx }
    }
}
#[async_trait::async_trait]
impl Job for Update2DBTask {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        update_usage_to_db(&self.ctx)
            .await
            .map_err(|e| RetryError::Discard(e))
    }
}
