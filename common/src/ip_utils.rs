use std::net::IpAddr;

/// Check if an IP address is private (non-public)
pub fn is_private_ip(ip: &IpAddr) -> bool {
    match ip {
        IpAddr::V4(ipv4) => {
            // RFC 1918 private addresses:
            // 10.0.0.0/8, **********/12, ***********/16
            // Also consider loopback (*********/8) and link-local (***********/16)
            ipv4.is_private() || ipv4.is_loopback() || ipv4.is_link_local() ||
            // RFC 3927 link-local
            (ipv4.octets()[0] == 169 && ipv4.octets()[1] == 254) ||
            // RFC 5737 documentation addresses
            (ipv4.octets()[0] == 192 && ipv4.octets()[1] == 0 && ipv4.octets()[2] == 2) ||
            (ipv4.octets()[0] == 198 && ipv4.octets()[1] == 51 && ipv4.octets()[2] == 100) ||
            (ipv4.octets()[0] == 203 && ipv4.octets()[1] == 0 && ipv4.octets()[2] == 113) ||
            // RFC 6598 Carrier-grade NAT
            (ipv4.octets()[0] == 100 && ipv4.octets()[1] >= 64 && ipv4.octets()[1] <= 127)
        }
        IpAddr::V6(ipv6) => {
            // IPv6 private/local addresses
            ipv6.is_loopback() || 
            // RFC 4193 Unique Local Addresses (fc00::/7)
            (ipv6.segments()[0] & 0xfe00) == 0xfc00 ||
            // Link-local addresses (fe80::/10)
            (ipv6.segments()[0] & 0xffc0) == 0xfe80
        }
    }
}

/// Validate that IP addresses in target list are public when user is non-admin
pub fn validate_public_ips_only(target_addr_list: &[String], is_admin: bool) -> anyhow::Result<()> {
    // If user is admin, skip additional IP validation
    if is_admin {
        return Ok(());
    }
    
    // For non-admin users, check if any target addresses contain private IPs
    for addr in target_addr_list {
        let (addr_part, _comment) = match addr.split_once('#') {
            Some((addr, comment)) => (addr, comment),
            None => (addr.as_str(), ""),
        };
        
        // Extract the host part (remove port)
        let host_part = if let Some(colon_pos) = addr_part.rfind(':') {
            &addr_part[..colon_pos]
        } else {
            addr_part
        };
        
        // Remove brackets for IPv6 addresses
        let host_clean = if host_part.starts_with('[') && host_part.ends_with(']') {
            &host_part[1..host_part.len()-1]
        } else {
            host_part
        };
        
        // Try to parse as IP address
        if let Ok(ip) = host_clean.parse::<IpAddr>() {
            if is_private_ip(&ip) {
                return Err(anyhow::anyhow!(
                    "Non-admin users cannot create ports targeting private IP addresses: {}",
                    ip
                ));
            }
        }
        // Note: For domain names, we don't resolve them here for performance reasons
        // The actual IP check will happen at runtime when the domain is resolved
    }
    
    Ok(())
}