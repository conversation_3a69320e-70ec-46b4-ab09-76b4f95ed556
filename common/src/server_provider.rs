use crate::{
    app_message::{BalanceMode, LatencyTestMethod, Mode},
    chrono::{Local, NaiveTime},
    constant::{LATENCY_THOLD_MAX_MS, LATENCY_THOLD_MIN_MS, TCP_FAST_CONNECT_TIMEOUT},
    rechecker::Checker,
    PingClient,
};
use available_clients_list::AvailableServersList;
use futures_util::{future::select_ok, stream, FutureExt, StreamExt, TryFutureExt};
use log::{debug, info, warn};
use parking_lot::{Mutex, RwLock, RwLockReadGuard};
use private_tun::{address::Address, weight_selector::WeightSelector};
use rand::Rng;
use serde::{Deserialize, Serialize};
use smallvec::SmallVec;
use std::{
    collections::HashMap,
    fmt::{Debug, Display},
    future::Future,
    hash::{DefaultHasher, <PERSON>h, <PERSON><PERSON>},
    net::{IpAddr, Ipv4Addr, SocketAddr},
    sync::{
        atomic::{AtomicBool, AtomicUsize, Ordering},
        Arc,
    },
    time::{Duration, Instant},
};
use surge_ping::{PingIdentifier, PingSequence};
use tokio::{
    select,
    sync::{mpsc::UnboundedSender, Semaphore},
};
use tokio_util::sync::CancellationToken;

type ArcLock<T> = Arc<RwLock<T>>;

mod available_clients_list;

pub trait LatencyTester: Clone + Send + Sync + 'static {
    type Server: ServerInfoProvider;
    fn test_latency(
        &self,
        server: &Self::Server,
    ) -> impl Future<Output = Result<std::time::Duration, anyhow::Error>> + Send;
    fn test_count(&self) -> usize {
        1
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Hash)]
pub struct TimeRange {
    pub start: NaiveTime,
    pub end: NaiveTime,
}

pub trait ServerInfoProvider: Send + Sync + 'static {
    fn name(&self) -> &str;
    fn select_weight(&self) -> Option<u64>;
    fn time_ranges(&self) -> &Option<Vec<TimeRange>>;
    fn allow_fallback(&self) -> bool;
    fn should_switch_server(&self, statistic: &StatisticInfo) -> bool;
    fn allow_ipv6(&self) -> bool;
}

#[async_trait::async_trait]
pub trait ServerProvider: Sized {
    type Server: ServerInfoProvider;
    async fn create<T: LatencyTester<Server = Self::Server>>(
        target_list: Vec<Self::Server>,
        config: ServerProviderConfig,
        tester: T,
        cancel_token: CancellationToken,
        rt_provider: Option<tokio::runtime::Handle>,
        conn_sem: Arc<Semaphore>,
    ) -> Result<Self, anyhow::Error>;
    fn get_server<'a, P: Fn() -> &'a Address + Send + Sync + 'static>(
        &self,
        prefered: Option<&str>,
        target: Option<P>,
        black_servers: Option<&[Arc<Self::Server>]>,
    ) -> Result<Arc<Self::Server>, anyhow::Error>;
    fn recheck(&self);
    fn server_len(&self) -> usize;
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize, Hash)]
pub struct ServerProviderConfig {
    pub mode: Option<Mode>,
    pub best_latency_thold: Option<std::time::Duration>,
    pub recheck_interval: Option<std::time::Duration>,
}

impl Default for ServerProviderConfig {
    fn default() -> Self {
        ServerProviderConfig {
            mode: None,
            best_latency_thold: Some(std::time::Duration::from_millis(50)),
            recheck_interval: Some(std::time::Duration::from_secs(180)),
        }
    }
}

pub struct ServerProviderImpl<S: ServerInfoProvider> {
    all_servers_map: Option<HashMap<String, ArcLock<ServerStatusInfo<S>>>>,
    state: State<S>,
    is_checking: Option<Arc<AtomicBool>>,
}

enum ServerStatus {
    Unknown,
    Online,
    Lost,
}

impl ServerStatus {
    fn is_online(&self) -> bool {
        matches!(self, ServerStatus::Online)
    }
    #[allow(unused)]
    fn is_offline(&self) -> bool {
        matches!(self, ServerStatus::Lost)
    }
    fn is_unknown(&self) -> bool {
        matches!(self, ServerStatus::Unknown)
    }
}

pub struct StatisticInfo {
    // 用来减少误判，需要记录当前lost了多少次，超过3次才认为真的掉线了
    pub current_lost_count: usize,
    pub current_latency_ms: u64,
    pub total_lost_count: usize,
    pub total_test_count: usize,
    pub last_lost_detected_instant: Option<Instant>,
}
impl StatisticInfo {
    pub fn new() -> Self {
        StatisticInfo {
            current_lost_count: 0,
            current_latency_ms: 65535,
            total_lost_count: 0,
            total_test_count: 1,
            last_lost_detected_instant: None,
        }
    }

    // # Penalty Calculation Constants
    /// These constants can be tuned to adjust the penalty model.

    /// **Weight for Latency**: The primary multiplier for latency.
    /// A value of 1.0 means 1ms of latency adds 1.0 to the penalty score.
    const LATENCY_WEIGHT: f64 = 1.0;

    /// **Scale for Overall Loss Rate**: A large multiplier to make the loss rate significant.
    /// Example: With a scale of 2000.0, a 5% loss rate (0.05) adds 100.0 to the penalty score,
    /// equivalent to an extra 100ms of latency.
    const LOSS_RATE_PENALTY_SCALE: f64 = 2000.0;

    /// **Time Window for Recent Loss**: The duration (in seconds) for which a recent loss
    /// continues to affect the score. The penalty decays linearly over this period.
    const RECENT_LOSS_WINDOW_SECS: f64 = 1800.0;

    /// Calculates a penalty score based on latency, overall loss rate, and recent losses.
    ///
    /// The penalty is designed with the following priorities:
    /// 1.  **Latency**: The most significant and stable component of the score.
    /// 2.  **Packet Loss Rate**: The second most important factor, reflecting historical reliability.
    /// 3.  **Recent Loss**: A temporary, high-impact penalty to quickly react to instability.
    ///
    /// A lower score is better.
    pub fn calculate_penalty_for_latency(&self) -> u64 {
        // Handle the edge case where no tests have been run.
        if self.total_test_count == 0 {
            return 0;
        }

        // --- 1. Latency Penalty (Primary Component) ---
        // The base penalty is directly derived from the current latency.
        let latency_penalty = self.current_latency_ms as f64 * Self::LATENCY_WEIGHT;

        // --- 2. Overall Loss Rate Penalty (Secondary Component) ---
        // This penalizes connections with a poor history of reliability.
        let loss_rate = self.total_lost_count as f64 / self.total_test_count as f64;
        let loss_rate_penalty = loss_rate * Self::LOSS_RATE_PENALTY_SCALE;

        // --- 3. Recent Loss Penalty (Tertiary, High-Impact Component) ---
        // This adds a large, decaying penalty if a loss occurred recently.
        let recent_loss_penalty = if let Some(last_lost_instant) = self.last_lost_detected_instant {
            let elapsed_secs = last_lost_instant.elapsed().as_secs_f64();

            if elapsed_secs < Self::RECENT_LOSS_WINDOW_SECS {
                // The penalty decays linearly from RECENT_LOSS_BASE_PENALTY to 0
                // over the time window.
                let decay_factor = 1.0 - (elapsed_secs / Self::RECENT_LOSS_WINDOW_SECS);
                self.current_latency_ms as f64 * decay_factor
            } else {
                // The loss is too old to be considered "recent".
                0.0
            }
        } else {
            // No loss has ever been detected.
            0.0
        };

        // --- Final Score ---
        // The total penalty is the sum of all components.
        (latency_penalty + loss_rate_penalty + recent_loss_penalty) as u64
    }
}

struct ServerStatusInfo<S: ServerInfoProvider> {
    server: Arc<S>,
    status: ServerStatus,
    statistic: StatisticInfo,
}

impl<S: ServerInfoProvider> ServerStatusInfo<S> {
    fn new(server: S) -> ServerStatusInfo<S> {
        ServerStatusInfo {
            server: Arc::new(server),
            status: ServerStatus::Unknown,
            statistic: StatisticInfo::new(),
        }
    }
    fn is_server_enable(&self) -> bool {
        // Check if the server should be active based on time ranges
        if let Some(time_ranges) = self.server.time_ranges() {
            let current_time = Local::now().time();
            log::debug!(
                "server: {} time_ranges: {time_ranges:?} current_time: {current_time:?}",
                self.server.name()
            );
            let is_in_time_range = time_ranges.iter().any(|range| {
                if range.start <= range.end {
                    current_time >= range.start && current_time <= range.end
                } else {
                    // Handle ranges that cross midnight
                    current_time >= range.start || current_time <= range.end
                }
            });
            if !is_in_time_range {
                log::debug!("server: {} is not in time range", self.server.name());
                return false;
            } else {
                true
            }
        } else {
            true
        }
    }
    fn is_available(&self) -> bool {
        if self.status.is_unknown() {
            // continue checking
        } else {
            if !self.status.is_online() && self.server.allow_fallback() {
                return false;
            }
        }
        if !self.is_server_enable() {
            return false;
        }
        !self.server.should_switch_server(&self.statistic)
    }
}

enum BalanceStragegy<S: ServerInfoProvider> {
    RoundRobin {
        cur_id: Arc<AtomicUsize>,
    },
    DomainFollow,
    Random {
        selector_v4: WeightSelector<AvailableServersList<S>>,
        selector_v6: WeightSelector<AvailableServersList<S>>,
    },
}

enum State<S: ServerInfoProvider> {
    Balance {
        fallback_server: ArcLock<Arc<S>>,
        fallback_server_v6: ArcLock<Arc<S>>,
        recheck_tx: Mutex<UnboundedSender<()>>,
        availables_server_list: AvailableServersList<S>,
        availables_server_list_v6: AvailableServersList<S>,
        strategy: BalanceStragegy<S>,
    },
    SelectOne {
        recheck_tx: Mutex<UnboundedSender<()>>,
        current_server: ArcLock<Arc<S>>,
        current_server_v6: ArcLock<Arc<S>>, // current ipv6 available server
    },
    OneServer {
        server: Arc<S>,
    },
}

fn try_update_server<S: ServerInfoProvider>(
    current_server: &ArcLock<Arc<S>>,
    all_servers: &Vec<ArcLock<ServerStatusInfo<S>>>,
    first_available: &Option<usize>,
    current_server_idx: &mut usize,
    force: bool,
    expect_server_idx: usize,
) {
    if *current_server_idx != expect_server_idx {
        // check if main server is recover
        let l = all_servers.get(expect_server_idx).unwrap().read();
        match &l.status {
            ServerStatus::Online => {
                log::info!("switching to main server again");
                *current_server.write() = l.server.clone();
                *current_server_idx = 0;
                return;
            }
            _ => {}
        }
    }
    // check if current server is lost
    let update = match &all_servers.get(*current_server_idx).unwrap().read().status {
        ServerStatus::Lost => true,
        _ => force,
    };
    if update {
        if let Some(new_idx) = first_available {
            log::info!("switching to server: {}", new_idx);
            let lock = all_servers.get(*new_idx).unwrap().read();
            *current_server.write() = lock.server.clone();
            drop(lock);
            *current_server_idx = *new_idx;
        }
    }
}
fn calculate_tolerance<S: ServerInfoProvider>(
    all_servers: &Vec<ArcLock<ServerStatusInfo<S>>>,
    cfg: &ServerProviderConfig,
) -> u64 {
    let mut latency_ms = Vec::with_capacity(all_servers.len());
    for server in all_servers.iter() {
        let l = server.read();
        if l.is_available() {
            latency_ms.push(l.statistic.current_latency_ms as f64);
        }
    }
    if latency_ms.len() == 0 {
        return cfg
            .best_latency_thold
            .as_ref()
            .map(|x| x.as_millis())
            .unwrap_or(10) as u64;
    }
    let avg = latency_ms.iter().sum::<f64>() / latency_ms.len() as f64;
    (avg * 0.1).clamp(LATENCY_THOLD_MIN_MS as f64, LATENCY_THOLD_MAX_MS as f64) as u64
}
async fn test_all_servers_latency<S: ServerInfoProvider, T: LatencyTester<Server = S>>(
    all_servers: &Vec<ArcLock<ServerStatusInfo<S>>>,
    tester: &T,
    conn_sem: &Arc<Semaphore>,
) {
    let tasks = all_servers.iter().map(|server| {
        let tester = tester.clone();
        async move {
            // 测量多次次，取最小值
            let rst = stream::iter((0..tester.test_count()).into_iter())
                .then(|_i| async {
                    let _sem_guard = conn_sem.acquire().await;
                    let s = server.read().server.clone();
                    tester.test_latency(&s).await
                })
                .collect::<SmallVec<[_; 3]>>()
                .map(|mut e| {
                    e.retain(|x| x.is_ok());
                    e.sort_by(|x, y| x.as_ref().unwrap().cmp(y.as_ref().unwrap()));
                    e.into_iter().map(|x| x.ok().unwrap()).next()
                })
                .await;
            let mut s = server.write();
            s.statistic.total_test_count += 1;
            match rst {
                None => {
                    s.statistic.current_lost_count += 1;
                    s.statistic.total_lost_count += 1;
                    s.statistic.last_lost_detected_instant = Some(std::time::Instant::now());
                    warn!(
                        "test server: {} all failed, current_lost_count: {}, total_lost_count: {}, total_test_count: {} last_lost_detected_instant: {:?}",
                        s.server.name(),
                        s.statistic.current_lost_count,
                        s.statistic.total_lost_count,
                        s.statistic.total_test_count,
                        s.statistic.last_lost_detected_instant
                    );
                    if s.statistic.current_lost_count >= 3 || matches!(s.status, ServerStatus::Unknown) {
                        s.status = ServerStatus::Lost;
                    }
                }
                Some(latency) => {
                    debug!(
                        "test server: {}, latency: {} ms",
                        s.server.name(),
                        latency.as_millis() as u64
                    );
                    s.statistic.current_latency_ms = latency.as_millis() as u64;
                    s.statistic.current_lost_count = 0;
                    s.status = ServerStatus::Online;
                    if let Some(last_lost_detected_instant) = &s.statistic.last_lost_detected_instant {
                        if last_lost_detected_instant.elapsed().as_secs() > 3600 {
                            // 如果上次掉线已经超过1小时，则重置统计数据
                            s.statistic.total_lost_count = 0;
                            s.statistic.total_test_count = 1;
                            s.statistic.last_lost_detected_instant = None;
                        }
                    }
                }
            }
            Ok::<_, anyhow::Error>(())
        }
        .boxed()
    });
    stream::iter(tasks.into_iter())
        .for_each_concurrent(5, |x| async move {
            let _ = x.await;
        })
        .await;
}

async fn get_first_available_server<
    S: ServerInfoProvider,
    F: 'static + Send + Sync + for<'a> Fn(&'a RwLockReadGuard<ServerStatusInfo<S>>) -> bool,
    T: LatencyTester<Server = S>,
>(
    all_servers: &Vec<ArcLock<ServerStatusInfo<S>>>,
    tester: &T,
    filter: F,
) -> anyhow::Result<usize> {
    let now = std::time::Instant::now();
    let mut streams = Vec::with_capacity(all_servers.len());
    for s in all_servers.iter().enumerate().map(|x| (x.0, x.1)) {
        let l = s.1.read();
        if filter(&l) && l.is_available() {
            streams.push((s.0, s.1))
        }
    }
    if streams.len() == 0 {
        log::error!("no available server");
        return Err(anyhow::anyhow!("no available server"));
    }
    let streams = streams.into_iter().map(|(id, server)| {
        let tester = tester.clone();
        async move {
            let (s, name) = {
                let l = server.read();
                (l.server.clone(), l.server.name().to_string())
            };
            tester.test_latency(&s).map_ok(|_| (id, name)).await
        }
        .boxed()
    });
    // try poll every future in streams and only return the first success one ignore the other
    let ((id, name), _) = select_ok(streams).await?;
    log::info!(
        "get first available server: {} id: {id} cost: {:?}ms",
        name,
        now.elapsed().as_millis()
    );
    Ok(id)
}

async fn update_server<
    'a,
    S: ServerInfoProvider,
    F: Fn(RwLockReadGuard<'a, ServerStatusInfo<S>>) -> bool,
>(
    all_servers: &'a Vec<ArcLock<ServerStatusInfo<S>>>,
    current: &ArcLock<Arc<S>>,
    current_server_idx: &mut usize,
    filter: F,
    force: bool,
    expect_server_idx: usize,
) {
    let mut first_available = None;
    for (idx, status) in all_servers.iter().enumerate() {
        let l = status.read();
        if l.is_available() && filter(l) {
            first_available = Some(idx);
            break;
        }
    }
    debug!("first_available: {first_available:?}");
    try_update_server(
        current,
        &all_servers,
        &first_available,
        current_server_idx,
        force,
        expect_server_idx,
    );
}

async fn update_best<
    'a,
    S: ServerInfoProvider,
    F: Fn(RwLockReadGuard<'a, ServerStatusInfo<S>>) -> bool,
    N: Display + PartialEq + PartialOrd + Clone + Debug + 'static,
    M: Fn(RwLockReadGuard<'a, ServerStatusInfo<S>>) -> N,
    C: Fn(&N, &N) -> std::cmp::Ordering,
    SW: Fn(&N, &N) -> bool,
>(
    all_servers: &'a Vec<ArcLock<ServerStatusInfo<S>>>,
    current: &ArcLock<Arc<S>>,
    current_select: &mut Option<usize>,
    filter: F,
    is_v6: bool,
    mapper: M,
    compare: C,
    should_switch: SW,
) {
    // Find the best server and current server's latency in one pass
    let mut best_server = None;
    let mut selected_cur_latency = None;
    for (idx, s) in all_servers.iter().enumerate() {
        let l = s.read();
        if l.is_available() && filter(l) {
            let val = mapper(s.read());

            // Track latency of currently selected server
            if let Some(cur_idx) = current_select.as_ref() {
                if *cur_idx == idx {
                    selected_cur_latency = Some(val.clone());
                }
            }

            // Update best server if this one is better
            match &best_server {
                None => best_server = Some((idx, val)),
                Some((_, best_val)) => {
                    if compare(&val, &best_val) == std::cmp::Ordering::Less {
                        best_server = Some((idx, val));
                    }
                }
            }
        }
    }
    log::debug!("best_server: {best_server:?} is_v6: {is_v6}");
    if let Some((curr_best_idx, curr_best_val)) = best_server {
        log::debug!("measure result curr_best_val: {curr_best_val} idx: {curr_best_idx:?}");
        let update = if let Some(selected_val) = selected_cur_latency.as_ref() {
            log::debug!("test should switch: {curr_best_val} {selected_val}");
            should_switch(&curr_best_val, selected_val)
        } else {
            true
        };
        let cur_idx: &usize = current_select.as_ref().unwrap();
        if (update && *cur_idx != curr_best_idx) || selected_cur_latency.is_none() {
            let lock = all_servers.get(curr_best_idx).unwrap().read();
            log::info!(
                "switching to best server: {} by val: {curr_best_val}",
                lock.server.name()
            );
            *current.write() = lock.server.clone();
            drop(lock);
            *current_select = Some(curr_best_idx);
        } else {
            log::debug!(
                "no need update best server, current best: {cur_idx} this time best: {curr_best_idx} val: {curr_best_val} "
            );
        }
    }
}

fn update_server_list<
    'a,
    S: ServerInfoProvider,
    F: Fn(RwLockReadGuard<'a, ServerStatusInfo<S>>) -> bool,
>(
    all_servers: &'a Vec<ArcLock<ServerStatusInfo<S>>>,
    availables_server_list: &AvailableServersList<S>,
    filter: F,
    vec_buffer: &mut Vec<Arc<S>>,
) {
    vec_buffer.clear();
    for server in all_servers {
        let l = server.read();
        if l.is_available() && filter(l) {
            vec_buffer.push(server.read().server.clone());
        }
    }
    availables_server_list.update_clients(vec_buffer);
}

fn need_ipv6(address: &Address) -> bool {
    match address {
        Address::Domain(_, _) => false,
        Address::Socket(ip) => ip.is_ipv6(),
    }
}

fn get_target_stable_hashval(addr: &Address) -> u64 {
    let mut state = DefaultHasher::default();
    match addr {
        Address::Domain(d, _) => {
            d.hash(&mut state);
        }
        Address::Socket(s) => {
            s.ip().hash(&mut state);
        }
    }
    state.finish()
}

#[async_trait::async_trait]
impl<S: ServerInfoProvider> ServerProvider for ServerProviderImpl<S> {
    type Server = S;
    async fn create<T: LatencyTester<Server = S>>(
        mut target_list: Vec<Self::Server>,
        cfg: ServerProviderConfig,
        tester: T,
        cancel_token: CancellationToken,
        rt_provider: Option<tokio::runtime::Handle>,
        conn_sem: Arc<Semaphore>,
    ) -> Result<Self, anyhow::Error> {
        let rt_provider = rt_provider.unwrap_or(tokio::runtime::Handle::current());
        let Some(mode) = cfg.mode else {
            return Ok(Self {
                all_servers_map: None,
                state: State::OneServer {
                    server: Arc::new(target_list.pop().unwrap()),
                },
                is_checking: None,
            });
        };
        if target_list.len() == 1 {
            return Ok(Self {
                all_servers_map: None,
                state: State::OneServer {
                    server: Arc::new(target_list.pop().unwrap()),
                },
                is_checking: None,
            });
        }

        let server_ids = target_list
            .iter()
            .map(|x| x.name().to_owned())
            .collect::<Vec<_>>();
        let mut all_servers = target_list
            .into_iter()
            .map(|x| Arc::new(RwLock::new(ServerStatusInfo::new(x))))
            .collect::<Vec<_>>();
        all_servers.shrink_to_fit();

        let main_server = all_servers
            .first()
            .map(|x| x.read().server.clone())
            .unwrap();
        let is_checking = Some(Arc::new(AtomicBool::new(false)));
        let current_server = Arc::new(RwLock::new(main_server.clone()));
        let current_clone = current_server.clone();

        let mut all_servers_map: HashMap<_, _> = server_ids
            .into_iter()
            .zip(all_servers.iter())
            .map(|s| (s.0, s.1.clone()))
            .collect();
        all_servers_map.shrink_to_fit();
        info!("all servers list: {}", all_servers.len());
        for (idx, s) in all_servers.iter().enumerate() {
            let l = s.read();
            info!("id: {} server: {}", idx, l.server.name());
        }
        let (first_available_v6_idx, first_available_server_v6) = {
            let init_available_v6 =
                get_first_available_server(&all_servers, &tester, |s| s.server.allow_ipv6())
                    .await
                    .ok()
                    .unwrap_or_default();
            (
                init_available_v6,
                all_servers
                    .get(init_available_v6)
                    .unwrap()
                    .read()
                    .server
                    .clone(),
            )
        };
        log::info!("first_available_v6_idx: {first_available_v6_idx}");
        let current_server_v6 = Arc::new(RwLock::new(first_available_server_v6.clone()));
        let current_v6_clone = current_server_v6.clone();
        let recheck_interval = cfg
            .recheck_interval
            .as_ref()
            .map(Clone::clone)
            .unwrap_or_else(|| std::time::Duration::from_secs(5 * 60));
        let (recheck_tx, mut recheck_rx) = tokio::sync::mpsc::unbounded_channel();

        let is_checking_clone = is_checking.clone();
        // check for first available server
        let init_available = get_first_available_server(&all_servers, &tester, |_s| true)
            .await
            .ok()
            .unwrap_or_default();
        let available_sever_status = all_servers.get(init_available).unwrap();
        *current_server.write() = available_sever_status.read().server.clone();
        log::info!("first available: {init_available}");
        // check task
        match mode {
            Mode::FallBack => {
                rt_provider.spawn(async move {
                    let mut current_server_idx = init_available;
                    let mut current_server_idx_v6 = first_available_v6_idx;
                    let mut force = false;
                    let mut recheck_additional_time_sec = 0;
                    loop {
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(true, Ordering::Relaxed));
                        test_all_servers_latency(&all_servers, &tester, &conn_sem).await;
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(false, Ordering::Relaxed));
                        update_server(
                            &all_servers,
                            &current_clone,
                            &mut current_server_idx,
                            |l| {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            force,
                            0,
                        )
                        .await;
                        log::debug!("current fallback server_idx = {current_server_idx:?}");
                        update_server(
                            &all_servers,
                            &current_v6_clone,
                            &mut current_server_idx_v6,
                            |l| {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && l.server.allow_ipv6()
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            force,
                            first_available_v6_idx,
                        )
                        .await;
                        log::debug!("current fallback server_v6_idx = {current_server_idx_v6:?}");
                        select! {
                            Some(_tx) = recheck_rx.recv() => {
                                force = true;
                                recheck_additional_time_sec = 0;
                                log::debug!("trig recheck server status by signal fallback");
                                let is_main_server_online = {
                                    let l = all_servers.get(0).unwrap().read();
                                    if l.is_available() {
                                        log::debug!("current main server: {} is online, skip update emergency server", l.server.name());
                                        true
                                    } else {
                                        false
                                    }
                                };
                                if !is_main_server_online {
                                    let available_idx = get_first_available_server(&all_servers, &tester, |_s| true)
                                                .await
                                                .ok()
                                                .unwrap_or_default();
                                    current_server_idx = available_idx;
                                    *current_clone.write() = all_servers.get(current_server_idx).unwrap().read().server.clone();
                                    log::info!("update current emergency server: {}", current_server_idx);
                                }
                                let init_available_v6 = if all_servers.get(current_server_idx).unwrap().read().server.allow_ipv6() {
                                    current_server_idx
                                } else {
                                    get_first_available_server(&all_servers, &tester, |s| s.server.allow_ipv6())
                                        .await
                                        .ok()
                                        .unwrap_or_default()
                                };
                                current_server_idx_v6 = init_available_v6;
                                *current_v6_clone.write() = all_servers.get(current_server_idx_v6).unwrap().read().server.clone();
                            }
                            _ = tokio::time::sleep(recheck_interval.clone() + Duration::from_secs(rand::random_range(0..30)) + Duration::from_secs(recheck_additional_time_sec)) => {
                                recheck_additional_time_sec += 30;
                                recheck_additional_time_sec = recheck_additional_time_sec.min(10 * 60);
                                debug!("trig recheck server status by timer, recheck_additional_time_sec: {recheck_additional_time_sec}s");
                            }
                            _ = cancel_token.cancelled() => {
                                log::info!("cancel recheck server status by token");
                                break;
                            }
                        }
                    }
                });
                Ok(Self {
                    all_servers_map: Some(all_servers_map),
                    state: State::SelectOne {
                        current_server,
                        current_server_v6,
                        recheck_tx: Mutex::new(recheck_tx),
                    },
                    is_checking,
                })
            }
            Mode::BestLatency => {
                rt_provider.spawn(async move {
                    let mut current_select: Option<usize> = Some(init_available);
                    let mut current_select_v6: Option<usize> = Some(first_available_v6_idx);
                    let compare = |a: &u64, b: &u64| a.cmp(b);
                    let mapper = |l: RwLockReadGuard<ServerStatusInfo<S>>| l.statistic.calculate_penalty_for_latency();
                    loop {
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(true, Ordering::Relaxed));
                        let best_thold = calculate_tolerance(&all_servers, &cfg);
                        log::debug!("best_thold: {best_thold}ms");
                        let should_switch = |cur_best: &u64, cur_selected: &u64| {
                            *cur_selected > *cur_best + best_thold
                        };
                        test_all_servers_latency(&all_servers, &tester, &conn_sem).await;
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(false, Ordering::Relaxed));
                        update_best(
                            &all_servers,
                            &current_clone,
                            &mut current_select,
                            |l| {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            false,
                            mapper,
                            compare,
                            should_switch,
                        )
                        .await;
                        log::debug!("current server_idx = {current_select:?}");
                        update_best(
                            &all_servers,
                            &current_v6_clone,
                            &mut current_select_v6,
                            |l| {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && l.server.allow_ipv6()
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            true,
                            mapper,
                            compare,
                            should_switch,
                        )
                        .await;
                        log::debug!("current_v6 server_idx = {current_select_v6:?}");
                        select! {
                            Some(_tx) = recheck_rx.recv() => {
                                debug!("trig recheck server status by signal best latency");
                                let available_idx = get_first_available_server(&all_servers, &tester, |_s| true)
                                            .await
                                            .ok()
                                            .unwrap_or_default();
                                current_select = Some(available_idx);
                                *current_clone.write() = all_servers.get(available_idx).unwrap().read().server.clone();
                                let init_available_v6 = if all_servers.get(available_idx).unwrap().read().server.allow_ipv6() {
                                    available_idx
                                } else {
                                    get_first_available_server(&all_servers, &tester, |s| s.server.allow_ipv6())
                                        .await
                                        .ok()
                                        .unwrap_or_default()
                                };
                                current_select_v6 = Some(init_available_v6);
                                *current_v6_clone.write() = all_servers.get(init_available_v6).unwrap().read().server.clone();
                            }
                            _ = tokio::time::sleep(recheck_interval.clone() + Duration::from_secs(rand::random_range(0..30))) => {
                                debug!("trig recheck server status by timer");
                            }
                            _ = cancel_token.cancelled() => {
                                log::info!("cancel recheck server status by token");
                                break;
                            }
                        }
                    }
                });
                Ok(Self {
                    all_servers_map: Some(all_servers_map),
                    state: State::SelectOne {
                        current_server,
                        current_server_v6,
                        recheck_tx: Mutex::new(recheck_tx),
                    },
                    is_checking,
                })
            }
            Mode::Balance { balance_mode } => {
                let fallback_server = Arc::new(RwLock::new(
                    all_servers
                        .get(init_available)
                        .unwrap()
                        .read()
                        .server
                        .clone(),
                ));
                let fallback_server_v6 = Arc::new(RwLock::new(
                    all_servers
                        .get(first_available_v6_idx)
                        .unwrap()
                        .read()
                        .server
                        .clone(),
                ));
                let mut availables_server_list = Vec::with_capacity(all_servers.len());
                for s in all_servers.iter() {
                    availables_server_list.push(s.read().server.clone());
                }
                availables_server_list.shrink_to_fit();

                let mut availables_server_list_v6 = Vec::with_capacity(all_servers.len());
                for s in all_servers.iter() {
                    let l = s.read();
                    if l.server.allow_ipv6() {
                        availables_server_list_v6.push(l.server.clone());
                    }
                }
                availables_server_list_v6.shrink_to_fit();
                let availables_server_list = AvailableServersList::new(availables_server_list);
                let availables_server_list_v6 =
                    AvailableServersList::new(availables_server_list_v6);
                let availables_server_list_clone = availables_server_list.clone();
                let availables_server_list_v6_clone = availables_server_list_v6.clone();
                let selector_v4 = WeightSelector::new(availables_server_list.clone())?;
                let selector_v6 = WeightSelector::new(availables_server_list_v6.clone())?;
                let mut vec_buffer = Vec::with_capacity(all_servers.len());
                let fallback_server_clone = fallback_server.clone();
                let fallback_server_v6_clone = fallback_server_v6.clone();
                let mut recheck_additional_time_sec = 0;
                rt_provider.spawn(async move {
                    loop {
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(true, Ordering::Relaxed));
                        test_all_servers_latency(&all_servers, &tester, &conn_sem).await;
                        is_checking_clone
                            .as_ref()
                            .map(|x| x.store(false, Ordering::Relaxed));
                        update_server_list(
                            &all_servers,
                            &availables_server_list_clone,
                            |l|  {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            &mut vec_buffer,
                        );
                        update_server_list(
                            &all_servers,
                            &availables_server_list_v6_clone,
                            |l| {
                                l.status.is_online()
                                    && l.statistic.current_lost_count == 0
                                    && l.server.allow_ipv6()
                                    && !l.server.should_switch_server(&l.statistic)
                            },
                            &mut vec_buffer,
                        );
                        select! {
                            _ = recheck_rx.recv() => {
                                recheck_additional_time_sec = 0;
                                let available_idx = get_first_available_server(&all_servers, &tester, |_s| true)
                                            .await
                                            .ok()
                                            .unwrap_or_default();
                                *fallback_server_clone.write() = all_servers
                                    .get(available_idx)
                                    .unwrap()
                                    .read()
                                    .server
                                    .clone();
                                let available_idx_v6 = get_first_available_server(&all_servers, &tester, |s| s.server.allow_ipv6())
                                            .await
                                            .ok()
                                            .unwrap_or_default();
                                *fallback_server_v6_clone.write() = all_servers
                                    .get(available_idx_v6)
                                    .unwrap()
                                    .read()
                                    .server
                                    .clone();
                            }
                            _ = tokio::time::sleep(recheck_interval.clone() + Duration::from_secs(rand::random_range(0..30)) + Duration::from_secs(recheck_additional_time_sec)) => {
                                recheck_additional_time_sec += 30;
                                recheck_additional_time_sec = recheck_additional_time_sec.min(10 * 60);
                                debug!("trig recheck server status by timer, recheck_additional_time_sec: {recheck_additional_time_sec}s");
                            }
                            _ = cancel_token.cancelled() => {
                                log::info!("cancel recheck server status by token");
                                break;
                            }
                        }
                    }
                });
                let strategy = match balance_mode {
                    BalanceMode::DomainFollow => BalanceStragegy::DomainFollow,
                    BalanceMode::Random => BalanceStragegy::Random {
                        selector_v4,
                        selector_v6,
                    },
                    BalanceMode::RoundRobin => BalanceStragegy::RoundRobin {
                        cur_id: Arc::new(AtomicUsize::new(0)),
                    },
                };

                Ok(Self {
                    all_servers_map: Some(all_servers_map),
                    state: State::Balance {
                        fallback_server,
                        fallback_server_v6,
                        strategy,
                        availables_server_list,
                        availables_server_list_v6,
                        recheck_tx: Mutex::new(recheck_tx),
                    },
                    is_checking,
                })
            }
        }
    }
    fn get_server<'a, P: Fn() -> &'a Address + Send + Sync + 'static>(
        &self,
        prefered: Option<&str>,
        target: Option<P>,
        black_servers: Option<&[Arc<Self::Server>]>,
    ) -> Result<Arc<Self::Server>, anyhow::Error> {
        // 如果prefered在线，使用prefered, 否则使用当前自动选择的
        if let Some(prefered) = self.try_perfered(prefered) {
            return Ok(prefered);
        }
        Ok(self.server_lagency(target, black_servers))
    }

    fn recheck(&self) {
        self.recheck_inner();
    }

    fn server_len(&self) -> usize {
        self.all_servers_map.as_ref().map(|x| x.len()).unwrap_or(1)
    }
}

impl<S: ServerInfoProvider> ServerProviderImpl<S> {
    fn try_perfered(&self, prefered: Option<&str>) -> Option<Arc<S>> {
        if let Some(prefered) = prefered {
            if let Some(server) = self.all_servers_map.as_ref().and_then(|x| x.get(prefered)) {
                let l = server.read();
                if l.is_available() {
                    log::trace!("prefered server: {}", prefered);
                    return Some(l.server.clone());
                }
            }
        }
        None
    }

    pub fn server_lagency<'a, P: 'a + Fn() -> &'a Address>(
        &self,
        target: Option<P>,
        black_servers: Option<&[Arc<S>]>,
    ) -> Arc<S> {
        let not_in_black_list = |s: &Arc<S>| {
            if let Some(black_servers) = black_servers {
                !black_servers.iter().any(|b| b.name() == s.name())
            } else {
                true
            }
        };
        match &self.state {
            State::OneServer { server } => server.clone(),
            State::SelectOne {
                current_server,
                current_server_v6,
                ..
            } => {
                let selected = if target.as_ref().map(|x| need_ipv6(x())).unwrap_or_default() {
                    current_server_v6.clone().read().clone()
                } else {
                    current_server.clone().read().clone()
                };
                let need_ipv6 = target.as_ref().map(|x| need_ipv6(x())).unwrap_or_default();
                if not_in_black_list(&selected) {
                    selected
                } else {
                    // select one available server
                    for s in self.all_servers_map.as_ref().unwrap().values() {
                        let l = s.read();
                        if l.is_available() && l.server.allow_ipv6() == need_ipv6 {
                            if not_in_black_list(&l.server) {
                                return l.server.clone();
                            }
                        }
                    }
                    // fallback to current server
                    selected
                }
            }
            State::Balance {
                availables_server_list,
                strategy,
                availables_server_list_v6,
                fallback_server,
                fallback_server_v6,
                ..
            } => match strategy {
                BalanceStragegy::Random {
                    selector_v4,
                    selector_v6,
                } => {
                    let need_ipv6 = target.map(|x| need_ipv6(x())).unwrap_or_default();
                    let selector = if need_ipv6 { selector_v6 } else { selector_v4 };
                    match selector.select_random() {
                        Ok(client) => client,
                        Err(e) => {
                            log::error!("select random server error: {e}");
                            self.recheck();
                            return if need_ipv6 {
                                fallback_server_v6.read().clone()
                            } else {
                                fallback_server.read().clone()
                            };
                        }
                    }
                }
                BalanceStragegy::DomainFollow => {
                    // todo: 使用名字而不是id
                    let need_ipv6 = target.as_ref().map(|x| need_ipv6(x())).unwrap_or_default();
                    let lst = if need_ipv6 {
                        availables_server_list_v6.0.read()
                    } else {
                        availables_server_list.0.read()
                    };
                    if lst.len() == 0 {
                        self.recheck();
                        return if need_ipv6 {
                            fallback_server_v6.read().clone()
                        } else {
                            fallback_server.read().clone()
                        };
                    }
                    let val = match target {
                        Some(target) => get_target_stable_hashval(target()),
                        None => rand::rng().random_range(0..lst.len() as u64),
                    };
                    lst.get(val as usize % lst.len()).unwrap().clone()
                }
                BalanceStragegy::RoundRobin { cur_id } => {
                    let need_ipv6 = target.map(|x| need_ipv6(x())).unwrap_or_default();
                    let lst = if need_ipv6 {
                        availables_server_list_v6.0.read()
                    } else {
                        availables_server_list.0.read()
                    };
                    if lst.len() == 0 {
                        self.recheck();
                        return if need_ipv6 {
                            fallback_server_v6.read().clone()
                        } else {
                            fallback_server.read().clone()
                        };
                    }
                    let cur = cur_id.fetch_add(1, Ordering::Acquire);
                    lst.get(cur % lst.len()).unwrap().clone()
                }
            },
        }
    }
    pub fn recheck_inner(&self) {
        match &self.state {
            State::OneServer { .. } => {}
            State::SelectOne { recheck_tx, .. } | State::Balance { recheck_tx, .. } => {
                if self
                    .is_checking
                    .as_ref()
                    .map(|x| !x.load(Ordering::Relaxed))
                    .unwrap_or_default()
                {
                    if let Some(l) = recheck_tx.try_lock() {
                        // recheck
                        let _ = l.send(());
                    }
                }
            }
        }
    }
}

pub struct ServerInfo {
    pub name: Arc<Box<str>>,
    pub target: Address,
    pub time_ranges: Option<Vec<TimeRange>>,
}

impl Display for ServerInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "[server: {}, target: {}]", self.name, self.target)
    }
}

impl ServerInfo {
    pub fn new(name: Box<str>, target: Address, time_ranges: Option<Vec<TimeRange>>) -> Self {
        Self {
            name: Arc::new(name),
            target,
            time_ranges,
        }
    }
    pub fn socket_addr(&self) -> &SocketAddr {
        match &self.target {
            Address::Socket(addr) => addr,
            Address::Domain(_, _port) => unreachable!(),
        }
    }
    pub fn port(&self) -> u16 {
        self.target.port()
    }
}

impl Default for ServerInfo {
    fn default() -> Self {
        Self {
            name: Arc::new("".to_string().into_boxed_str()),
            target: Address::Socket(SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 80)),
            time_ranges: None,
        }
    }
}

impl ServerInfoProvider for ServerInfo {
    fn name(&self) -> &str {
        &self.name
    }
    fn select_weight(&self) -> Option<u64> {
        // current only support 1
        Some(1)
    }
    fn time_ranges(&self) -> &Option<Vec<TimeRange>> {
        &self.time_ranges
    }
    fn allow_fallback(&self) -> bool {
        true
    }

    fn should_switch_server(&self, statistic: &StatisticInfo) -> bool {
        if statistic.current_latency_ms > 5000 {
            true
        } else {
            // if lost rate is too high, then switch server
            if statistic.total_test_count > 10
                && statistic.total_lost_count as f64 / statistic.total_test_count as f64 > 0.3
            {
                true
            } else {
                false
            }
        }
    }
    fn allow_ipv6(&self) -> bool {
        false
    }
}
// 直接使用Tcpping进行延迟测试
#[derive(Clone)]
pub struct DirectLatencyTester {
    support_ipv6: bool,
    method: LatencyTestMethod,
    ping_client: Arc<PingClient>,
}

impl DirectLatencyTester {
    pub fn new(
        support_ipv6: bool,
        method: LatencyTestMethod,
        ping_client: Arc<PingClient>,
    ) -> Self {
        Self {
            support_ipv6,
            method,
            ping_client,
        }
    }
}

impl LatencyTester for DirectLatencyTester {
    type Server = ServerInfo;
    async fn test_latency(
        &self,
        server: &ServerInfo,
    ) -> Result<std::time::Duration, anyhow::Error> {
        if need_ipv6(&server.target) {
            if !self.support_ipv6 {
                return Err(anyhow::anyhow!("ipv6 not supported"));
            }
        }
        let ip_addr = match &server.target {
            Address::Socket(addr) => addr.ip(),
            Address::Domain(domain, _port) => domain.parse::<IpAddr>().unwrap(),
        };
        match &self.method {
            LatencyTestMethod::Tcpping => {
                let start = Instant::now();
                let target: SocketAddr = (ip_addr, server.target.port()).into();
                match tokio::time::timeout(
                    std::time::Duration::from_secs(TCP_FAST_CONNECT_TIMEOUT),
                    tokio::net::TcpStream::connect(target),
                )
                .await
                {
                    Ok(Ok(_stream)) => {
                        log::debug!(
                            "test direct latency connect to target: {target} success in {:?}",
                            start.elapsed()
                        );
                        Ok(start.elapsed())
                    }
                    Ok(Err(e)) => {
                        log::error!("test direct latency connect to target: {target} failed: {e}");
                        Err(anyhow::anyhow!("connect to target: {target} failed: {e}"))
                    }
                    Err(_e) => {
                        log::error!("test direct latency connect to target: {target} timeout");
                        Err(anyhow::anyhow!("connect to target: {target} timeout"))
                    }
                }
            }
            LatencyTestMethod::Icmp => {
                let id = rand::rng().random_range(0..u16::MAX);
                let rst = if ip_addr.is_ipv4() {
                    self.ping_client
                        .v4_client
                        .pinger(ip_addr, PingIdentifier(id))
                        .await
                        .ping(PingSequence(id), &[0; 64])
                        .await?
                } else {
                    self.ping_client
                        .v6_client
                        .pinger(ip_addr, PingIdentifier(id))
                        .await
                        .ping(PingSequence(id), &[0; 64])
                        .await?
                };
                Ok(rst.1)
            }
        }
    }
}

impl ServerProviderImpl<ServerInfo> {
    pub async fn new(
        target_list: Vec<ServerInfo>,
        mode: Option<Mode>,
        cancel_token: CancellationToken,
        tester: impl LatencyTester<Server = ServerInfo>,
        rt_provider: Option<tokio::runtime::Handle>,
        conn_sem: Arc<Semaphore>,
    ) -> Result<Self, anyhow::Error> {
        let mut config = ServerProviderConfig::default();
        config.mode = mode;
        // let tester = DirectLatencyTester::new(false, LatencyTestMethod::Tcpping);
        let provider = ServerProviderImpl::create(
            target_list,
            config,
            tester,
            cancel_token,
            rt_provider,
            conn_sem,
        )
        .await?;
        Ok(provider)
    }
    pub fn get_target_info(
        &self,
        black_servers: Option<&[Arc<ServerInfo>]>,
    ) -> Result<Arc<ServerInfo>, anyhow::Error> {
        let target = self.get_server::<Box<dyn Fn() -> &'static Address + Send + Sync + 'static>>(
            None,
            None,
            black_servers,
        )?;
        Ok(target)
    }
}

pub type ServerSelector = Arc<ServerProviderImpl<ServerInfo>>;

impl Checker for ServerSelector {
    async fn recheck(&self) {
        self.as_ref().recheck();
    }
}

pub async fn create_server_selector(
    target_list: Vec<Address>,
    mode: Option<Mode>,
    tester: impl LatencyTester<Server = ServerInfo>,
    cancel_token: CancellationToken,
    rt_provider: Option<tokio::runtime::Handle>,
    conn_sem: Arc<Semaphore>,
) -> Result<ServerSelector, anyhow::Error> {
    let target_list = target_list
        .into_iter()
        .map(|x| ServerInfo::new(format!("{}", x).into_boxed_str(), x, None))
        .collect();
    let selector = ServerProviderImpl::new(
        target_list,
        mode,
        cancel_token,
        tester,
        rt_provider,
        conn_sem,
    )
    .await?;
    Ok(Arc::new(selector))
}

pub async fn create_server_selector_direct(
    target_list: Vec<Address>,
    mode: Option<Mode>,
    latency_test_method: Option<LatencyTestMethod>,
    cancel_token: CancellationToken,
    ping_client: Arc<PingClient>,
    conn_sem: Arc<Semaphore>,
) -> Result<ServerSelector, anyhow::Error> {
    let target_list = target_list
        .into_iter()
        .map(|x| ServerInfo::new(format!("{}", x).into_boxed_str(), x, None))
        .collect();
    let latency_test_method = latency_test_method.unwrap_or_default();
    // todo: detect ipv6 support
    let tester = DirectLatencyTester::new(true, latency_test_method, ping_client);
    let selector =
        ServerProviderImpl::new(target_list, mode, cancel_token, tester, None, conn_sem).await?;
    Ok(Arc::new(selector))
}
