use std::future::Future;

use log::error;
use tokio::sync::mpsc;

#[async_trait::async_trait]
pub trait Job: Send + Sync + 'static {
    type Error: std::fmt::Display + Send + Sync + 'static;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>>;
}

#[async_trait::async_trait]
impl<T: Future<Output = Result<(), RetryError<anyhow::Error>>> + Send + Sync + 'static> Job
    for Option<T>
{
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        self.take().unwrap().await
    }
}

#[async_trait::async_trait]
impl<
        T: Future<Output = Result<(), RetryError<anyhow::Error>>> + Send + Sync + 'static,
        F: Send + Sync + 'static + Fn() -> T,
    > Job for F
{
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        self().await
    }
}

pub struct RetryQueue<J: Job> {
    q_tx: mpsc::Sender<J>,
}

pub enum RetryError<E> {
    Retry(E),
    Error(E),
    Discard(E),
}

impl From<redis::RedisError> for RetryError<anyhow::Error> {
    fn from(e: redis::RedisError) -> Self {
        RetryError::Retry(anyhow::anyhow!(e))
    }
}

impl<J: Job> RetryQueue<J> {
    pub fn new() -> Self {
        let (q_tx, mut q_rx) = mpsc::channel::<J>(1024);
        let q_tx_clone = q_tx.clone();
        tokio::spawn(async move {
            while let Some(mut j) = q_rx.recv().await {
                match j.run().await {
                    Ok(_) => (),
                    Err(e) => match e {
                        RetryError::Retry(e) => {
                            log::warn!("retry queue because of error: {}", e);
                            let qx_clone = q_tx.clone();
                            tokio::spawn(async move {
                                tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                                let _ = qx_clone.send(j).await;
                            });
                        }
                        RetryError::Error(e) => {
                            error!("retry queue finally error: {}", e);
                        }
                        RetryError::Discard(e) => {
                            log::warn!("retry queue discard job: {}", e);
                        }
                    },
                }
            }
        });
        Self { q_tx: q_tx_clone }
    }

    pub async fn send(&self, j: J) -> Result<(), mpsc::error::SendError<J>> {
        self.q_tx.send(j).await
    }
}

pub type BoxedRetryQueue = RetryQueue<Box<dyn Job<Error = anyhow::Error>>>;

#[async_trait::async_trait]
impl Job for Box<dyn Job<Error = anyhow::Error>> {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        (**self).run().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use rand::Rng;

    struct TestJob;

    #[async_trait]
    impl Job for TestJob {
        type Error = anyhow::Error;
        async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
            let n = rand::rng().random_range(0..10);
            if n < 6 {
                Err(RetryError::Retry(anyhow::anyhow!("test error")))
            } else {
                println!("test success");
                Ok(())
            }
        }
    }

    #[tokio::test]
    async fn test_retry_queue() {
        let q = RetryQueue::new();
        q.send(TestJob).await.unwrap();
    }
}
