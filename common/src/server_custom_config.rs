use private_tun::snell_impl_ver::config::TimeRange;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone, Default)]
pub struct BackendServerCustomConfig {
    pub server_id: i32,
    pub native_udp: Option<bool>,
    pub max_latency_thold_ms: Option<u64>,
    pub active_time_ranges: Option<Vec<TimeRange>>,
    pub band_width_mbps: Option<u32>,
    pub select_weight: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct TotServerCustomConfig {
    pub server_id: i32,
    pub band_width_mbps: Option<u32>,
}
/// 服务器自定义配置结构体
///
/// 这是一个空的结构体，为未来的自定义配置扩展预留接口。
/// 您可以根据需要在这里添加具体的配置字段。
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
pub struct ServerCustomConfig {
    pub udp_buffer_size: Option<usize>,
    pub reuse_tcp: Option<bool>,
    pub native_udp: Option<bool>,
    pub udp_batch_size: Option<usize>,
    pub max_latency_thold_ms: Option<u64>,
    pub band_width_mbps: Option<u32>,
    pub global_band_width_mbps: Option<u32>,
    pub active_time_ranges: Option<Vec<TimeRange>>,
    pub select_weight: Option<u64>,
    pub backend_server_custom_configs: Option<Vec<BackendServerCustomConfig>>,
    pub tot_server_custom_configs: Option<Vec<TotServerCustomConfig>>,
}

impl ServerCustomConfig {
    /// 创建一个新的空配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 从 JSON 字符串解析配置
    pub fn from_json(json_str: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json_str)
    }

    /// 转换为 JSON 字符串
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 验证配置是否有效
    pub fn validate(&self) -> Result<(), String> {
        // 在这里添加配置验证逻辑
        // 例如检查字段范围、格式等
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_empty_config() {
        let config = ServerCustomConfig::new();
        let json = config.to_json().unwrap();
        assert_eq!(json, "{}");
    }

    #[test]
    fn test_from_json() {
        let json = "{}";
        let config = ServerCustomConfig::from_json(json).unwrap();
        assert!(config.validate().is_ok());
    }
}
