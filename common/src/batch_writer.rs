use std::{collections::HashMap, sync::Arc, time::Instant};

use anyhow::Result;
use chrono::{DateTime, Utc};
use log::{debug, error};
use taos::{AsyncQueryable, TaosPool};
use tokio::sync::mpsc;

use crate::stats::{
    LatencyTestMeasurement, NetcardSpeedMeasurement, SystemStatsMeasurement,
    UserLineInfoMeasurement,
};

const MAX_BATCH_SIZE: usize = 100; // recv_many的最大接收数量
const CHANNEL_BUFFER_SIZE: usize = 1000;

#[derive(Debug, Clone)]
pub enum BatchWriteData {
    NetcardSpeed(NetcardSpeedMeasurement),
    SystemStats(SystemStatsMeasurement),
    UserLineInfo(UserLineInfoMeasurement),
    LatencyTest(LatencyTestMeasurement),
}

impl BatchWriteData {
    pub fn to_insert_sql(&self) -> String {
        match self {
            BatchWriteData::NetcardSpeed(data) => data.to_insert_sql(),
            BatchWriteData::SystemStats(data) => data.to_insert_sql(),
            BatchWriteData::UserLineInfo(data) => data.to_insert_sql(),
            BatchWriteData::LatencyTest(data) => data.to_insert_sql(),
        }
    }

    pub fn record_time(&self) -> &DateTime<Utc> {
        match self {
            BatchWriteData::NetcardSpeed(data) => &data.time,
            BatchWriteData::SystemStats(data) => &data.time,
            BatchWriteData::UserLineInfo(data) => &data.time,
            BatchWriteData::LatencyTest(data) => &data.time,
        }
    }

    pub fn measurement_type(&self) -> &'static str {
        match self {
            BatchWriteData::NetcardSpeed(_) => "netcard_speed",
            BatchWriteData::SystemStats(_) => "system_stats",
            BatchWriteData::UserLineInfo(_) => "user_line_info",
            BatchWriteData::LatencyTest(_) => "server_latency_tests",
        }
    }

    pub fn table_key(&self) -> String {
        match self {
            BatchWriteData::NetcardSpeed(data) => data.table_key(),
            BatchWriteData::SystemStats(data) => data.table_key(),
            BatchWriteData::UserLineInfo(data) => data.table_key(),
            BatchWriteData::LatencyTest(data) => data.table_key(),
        }
    }

    pub fn to_values_part(&self) -> String {
        match self {
            BatchWriteData::NetcardSpeed(data) => data.to_values_part(),
            BatchWriteData::SystemStats(data) => data.to_values_part(),
            BatchWriteData::UserLineInfo(data) => data.to_values_part(),
            BatchWriteData::LatencyTest(data) => data.to_values_part(),
        }
    }

    pub fn get_table_sql(&self) -> String {
        match self {
            BatchWriteData::NetcardSpeed(data) => data.get_table_sql(),
            BatchWriteData::SystemStats(data) => data.get_table_sql(),
            BatchWriteData::UserLineInfo(data) => data.get_table_sql(),
            BatchWriteData::LatencyTest(data) => data.get_table_sql(),
        }
    }
}

pub struct BatchWriter {
    sender: mpsc::Sender<BatchWriteData>,
}

impl BatchWriter {
    pub fn new(taos_pool: Arc<TaosPool>) -> Self {
        let (sender, receiver) = mpsc::channel(CHANNEL_BUFFER_SIZE);

        let batch_processor = BatchProcessor::new(taos_pool, receiver);
        tokio::spawn(async move {
            if let Err(e) = batch_processor.run().await {
                error!("BatchProcessor error: {}", e);
            }
        });

        Self { sender }
    }

    pub async fn write_netcard_stats(&self, measurement: NetcardSpeedMeasurement) -> Result<()> {
        self.sender
            .send(BatchWriteData::NetcardSpeed(measurement))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send netcard stats: {}", e))?;
        Ok(())
    }

    pub async fn write_system_stats(&self, measurement: SystemStatsMeasurement) -> Result<()> {
        self.sender
            .send(BatchWriteData::SystemStats(measurement))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send system stats: {}", e))?;
        Ok(())
    }

    pub async fn write_user_line_info(&self, measurement: UserLineInfoMeasurement) -> Result<()> {
        self.sender
            .send(BatchWriteData::UserLineInfo(measurement))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send user line info: {}", e))?;
        Ok(())
    }

    pub async fn write_latency_test(&self, measurement: LatencyTestMeasurement) -> Result<()> {
        self.sender
            .send(BatchWriteData::LatencyTest(measurement))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send latency test: {}", e))?;
        Ok(())
    }
}

struct BatchProcessor {
    taos_pool: Arc<TaosPool>,
    receiver: Option<mpsc::Receiver<BatchWriteData>>,
}

async fn flush_batch(
    taos_pool: &Arc<TaosPool>,
    batch_buffer: &mut Vec<BatchWriteData>,
) -> Result<()> {
    if batch_buffer.is_empty() {
        return Ok(());
    }

    let start_time = Instant::now();
    let record_count = batch_buffer.len();

    // 按measurement类型分组数据
    let mut measurement_groups: HashMap<&'static str, Vec<BatchWriteData>> = HashMap::new();
    for data in batch_buffer.drain(..) {
        let measurement_type = data.measurement_type();
        measurement_groups
            .entry(measurement_type)
            .or_default()
            .push(data);
    }

    // 为每个measurement类型创建并发写入任务
    let mut write_tasks = Vec::new();
    for (measurement_type, data_group) in measurement_groups {
        let pool_clone = taos_pool.clone();
        let task = tokio::spawn(async move {
            flush_measurement_batch(&pool_clone, measurement_type, data_group).await
        });
        write_tasks.push(task);
    }

    // 等待所有写入任务完成
    let mut total_errors = 0;
    for task in write_tasks {
        match task.await {
            Ok(result) => {
                if let Err(e) = result {
                    error!("Measurement write task failed: {}", e);
                    total_errors += 1;
                }
            }
            Err(e) => {
                error!("Task join error: {}", e);
                total_errors += 1;
            }
        }
    }

    let elapsed = start_time.elapsed();
    if total_errors == 0 {
        debug!(
            "Batch flush completed: {} records in {:?} ({:.1} records/sec)",
            record_count,
            elapsed,
            record_count as f64 / elapsed.as_secs_f64()
        );
    } else {
        error!(
            "Batch flush completed with {} errors: {} records in {:?}",
            total_errors, record_count, elapsed
        );
    }

    Ok(())
}

async fn flush_measurement_batch(
    taos_pool: &Arc<TaosPool>,
    measurement_type: &str,
    data_group: Vec<BatchWriteData>,
) -> Result<()> {
    if data_group.is_empty() {
        return Ok(());
    }

    // 按table分组数据并保持有序
    let mut table_groups: HashMap<String, Vec<BatchWriteData>> = HashMap::new();
    for data in data_group {
        let table_key = data.table_key();
        table_groups.entry(table_key).or_default().push(data);
    }

    // 构建单个多表INSERT语句
    if !table_groups.is_empty() {
        // 获取数据库连接
        let client = taos_pool
            .get()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to get TDengine connection: {}", e))?;
        let mut sql_parts = Vec::new();

        for (_table_key, table_data) in table_groups {
            if table_data.is_empty() {
                continue;
            }

            // 使用第一个数据项获取table SQL部分（不包含INSERT INTO前缀）
            let table_sql = table_data[0].get_table_sql();
            // 移除"INSERT INTO "前缀，只保留table定义部分
            let table_part = table_sql.strip_prefix("INSERT INTO ").unwrap_or(&table_sql);

            // 收集所有VALUES部分
            let values_parts: Vec<String> = table_data
                .iter()
                .map(|data| {
                    log::debug!("record_time: {}", data.record_time().naive_local());
                    data.to_values_part()
                })
                .collect();

            // 构建table部分：table_name USING super_table TAGS (...) VALUES (...), (...)
            let table_section = format!("{} VALUES {}", table_part, values_parts.join(" "));
            sql_parts.push(table_section);
        }

        // 构建完整的多表INSERT语句
        let full_sql = format!("INSERT INTO {}", sql_parts.join(" "));
        log::debug!("full_sql: {}", full_sql);
        match client.exec(&full_sql).await {
            Ok(_) => {
                debug!(
                    "Successfully flushed {} tables for measurement '{}' with single INSERT",
                    sql_parts.len(),
                    measurement_type
                );
            }
            Err(e) => {
                error!(
                    "Failed to execute multi-table INSERT for measurement '{}': {}",
                    measurement_type, e
                );
                return Err(e.into());
            }
        }
    }

    Ok(())
}

async fn flush_loop(
    taos_pool: &Arc<TaosPool>,
    receiver: &mut mpsc::Receiver<BatchWriteData>,
    batch_buffer: &mut Vec<BatchWriteData>,
) {
    loop {
        let count = receiver.recv_many(batch_buffer, MAX_BATCH_SIZE).await;
        if count == 0 {
            break;
        }
        if let Err(e) = flush_batch(taos_pool, batch_buffer).await {
            error!("Failed to flush batch: {}", e);
        }
    }
}
impl BatchProcessor {
    fn new(taos_pool: Arc<TaosPool>, receiver: mpsc::Receiver<BatchWriteData>) -> Self {
        Self {
            taos_pool,
            receiver: Some(receiver),
        }
    }

    async fn run(mut self) -> Result<()> {
        let mut batch_buffer = Vec::with_capacity(MAX_BATCH_SIZE);
        let mut receiver = self.receiver.take().unwrap();
        flush_loop(&self.taos_pool, &mut receiver, &mut batch_buffer).await;
        Ok(())
    }
}

impl Clone for BatchWriter {
    fn clone(&self) -> Self {
        Self {
            sender: self.sender.clone(),
        }
    }
}
