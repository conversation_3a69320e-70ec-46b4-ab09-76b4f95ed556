use std::sync::Arc;

use add_user::add_user_proc;
use anyhow::anyhow;
use bb8_redis::{bb8::Pool, RedisConnectionManager};
use byte_unit::Byte;
use chrono::{Days, Utc};
use clap::Parser;
use opt::{Command, Opt};
use prisma::PrismaClient;
use prisma_client_rust::Raw;
use redis::AsyncCommands;
use common::crypto::{
    parse_secret_key, random_key, stringify_public_key_from_secret_key, stringify_secret_key,
};
use common::prisma;

mod add_user;
mod opt;
pub type RedisPool = Pool<RedisConnectionManager>;

async fn reset_traffic(db: Arc<PrismaClient>, redis_pool: RedisPool) -> anyhow::Result<()> {
    let all_users = db.subscription().find_many(vec![]).exec().await?;
    let now = Utc::now().fixed_offset();
    for u in all_users {
        if u.valid_until > now {
            match (u.last_reset, u.reset_days) {
                (Some(last_reset_day), Some(reset_days)) => {
                    if reset_days == 0 {
                        continue;
                    }
                    let should_reset_day = last_reset_day
                        .checked_add_days(Days::new(reset_days as u64))
                        .ok_or(anyhow!("{last_reset_day} add {reset_days} failed"))?;
                    if should_reset_day <= now {
                        // update redis cache
                        if let Ok(ports) = db
                            .port()
                            .find_many(vec![prisma::port::subscription_id::equals(Some(u.id))])
                            .exec()
                            .await
                        {
                            for p in ports {
                                let _ = redis_pool
                                    .get()
                                    .await?
                                    .set(
                                        format!(
                                            "sub:line:port:used:{}:{}:{}",
                                            p.subscription_id.unwrap(),
                                            p.outbound_endpoint_id.unwrap(),
                                            p.port_v_4
                                        ),
                                        0,
                                    )
                                    .await?;
                            }
                        }

                        let _ = redis_pool
                            .get()
                            .await?
                            .set(format!("sub:used:{}", u.id), 0)
                            .await?;
                        if let Err(e) = db
                            .port()
                            .update_many(
                                vec![prisma::port::subscription_id::equals(Some(u.id))],
                                vec![prisma::port::traffic_in::set(0)],
                            )
                            .exec()
                            .await
                        {
                            log::error!("reset traffic for sub: {} failed: {e}", u.id);
                            continue;
                        }

                        if let Err(e) = db
                            .subscription()
                            .update(
                                prisma::subscription::id::equals(u.id),
                                vec![prisma::subscription::last_reset::set(Some(now))],
                            )
                            .exec()
                            .await
                        {
                            log::error!("update last reset days failed: {}", e);
                        }
                        log::info!("reset user: {} success", u.owner_address);
                    }
                }
                _ => {}
            }
        }
    }
    Ok(())
}

async fn reset_traffic_background(db: Arc<PrismaClient>, redis_pool: RedisPool) {
    loop {
        let _ = reset_traffic(db.clone(), redis_pool.clone()).await;
        tokio::time::sleep(std::time::Duration::from_secs(3600)).await;
    }
}

async fn show_user_info(db: Arc<PrismaClient>) -> anyhow::Result<()> {
    let data = db
        .subscription()
        .find_many(vec![])
        .with(prisma::subscription::ports::fetch(vec![]))
        .exec()
        .await?;
    for d in data {
        if let Ok(ports) = d.ports() {
            let total_used: i64 = ports.iter().map(|x| x.traffic_in).sum();
            println!(
                "{} usage: {}, total: {}, left days: {}",
                d.owner_address,
                Byte::from_i64(total_used)
                    .unwrap()
                    .get_appropriate_unit(byte_unit::UnitType::Binary),
                Byte::from_i128(d.traffic as i128 * 1024 * 1024 * 1024)
                    .unwrap()
                    .get_appropriate_unit(byte_unit::UnitType::Binary),
                (d.valid_until - Utc::now().fixed_offset()).num_days()
            )
        }
    }
    Ok(())
}

pub async fn setup_redis(opt: &Opt) -> anyhow::Result<RedisPool> {
    let manager = RedisConnectionManager::new(opt.redis_path.as_str())?;
    Ok(Pool::builder().build(manager).await?)
}

pub async fn copy_sub_port(
    db: Arc<PrismaClient>,
    sub: &str,
    from: i32,
    to: i32,
) -> anyhow::Result<()> {
    db._transaction()
        .run(|tx| async move {
            let sub = tx
                .subscription()
                .find_unique(prisma::subscription::token_id::equals(sub.to_string()))
                .with(prisma::subscription::ports::fetch(vec![]))
                .exec()
                .await?
                .ok_or_else(|| anyhow!("sub not found"))?;
            // check if sub has from and to endpoint
            if !sub.lines.contains(&from) || !sub.lines.contains(&to) {
                return Err(anyhow!("sub not contains from or to endpoint"));
            }
            // let endpoint_from = db.outbound_endpoint().find_unique(prisma::outbound_endpoint::id::equals(from)).exec().await?.ok_or_else(|| anyhow!("endpoint not found"))?;
            let endpoint_to = tx
                .outbound_endpoint()
                .find_unique(prisma::outbound_endpoint::id::equals(to))
                .exec()
                .await?
                .ok_or_else(|| anyhow!("endpoint not found"))?;
            let port_start_for_to = endpoint_to.port_start.unwrap_or(31000);
            let port_end_for_to = endpoint_to.port_end.unwrap_or(34000);
            let from_used_ports = tx
                .port()
                .find_many(vec![
                    prisma::port::subscription_id::equals(Some(sub.id)),
                    prisma::port::outbound_endpoint_id::equals(Some(from)),
                ])
                .exec()
                .await?;
            let to_used_ports = tx
                .port()
                .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(to))])
                .exec()
                .await?;
            for p in from_used_ports.iter() {
                if to_used_ports.iter().any(|x| x.port_v_4 == p.port_v_4) {
                    return Err(anyhow!("port {} already exists", p.port_v_4));
                }
                if p.port_v_4 < port_start_for_to || p.port_v_4 > port_end_for_to {
                    return Err(anyhow!("port {} not in range", p.port_v_4));
                }
            }

            // all check passed, copy port
            for p in from_used_ports.iter() {
                tx.port()
                    .create(
                        p.display_name.clone(),
                        p.port_v_4,
                        p.target_address_v_4.clone(),
                        p.target_port_v_4,
                        0,
                        0,
                        vec![
                            prisma::port::subscription_id::set(Some(sub.id)),
                            prisma::port::outbound_endpoint_id::set(Some(to)),
                        ],
                    )
                    .exec()
                    .await?;
            }
            Ok(())
        })
        .await?;
    Ok(())
}

pub async fn remove_line(db: Arc<PrismaClient>, line_id: i32) -> anyhow::Result<()> {
    db._transaction()
        .run(|tx| async move {
            let line = tx
                .outbound_endpoint()
                .find_unique(prisma::outbound_endpoint::id::equals(line_id))
                .exec()
                .await?
                .ok_or_else(|| anyhow!("line not found"))?;
            // delete all ports for this line
            let _ = tx
                .port()
                .delete_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                    line_id,
                ))])
                .exec()
                .await?;

            // deleted this line for subscription who has this line
            let subs = tx
                .subscription()
                .find_many(vec![prisma::subscription::lines::has_some(vec![line.id])])
                .exec()
                .await?;
            for s in subs.iter() {
                let new_lines = s
                    .lines
                    .iter()
                    .filter(|x| *x != &line.id)
                    .map(|x| *x)
                    .collect::<Vec<_>>();
                let _ = tx
                    .subscription()
                    .update(
                        prisma::subscription::id::equals(s.id),
                        vec![prisma::subscription::lines::set(new_lines)],
                    )
                    .exec()
                    .await?;
            }
            // delete this line
            tx.outbound_endpoint()
                .delete(prisma::outbound_endpoint::id::equals(line.id))
                .exec()
                .await?;
            Ok::<(), anyhow::Error>(())
        })
        .await?;
    Ok(())
}

pub async fn add_machine(
    db: Arc<PrismaClient>,
    name: &str,
    ip: &str,
    port_start: i32,
    port_end: i32,
    scale: f32,
    mgmt_priv_key: &str,
    arranger_hosts_url: &str,
    admin_token_id: u32,
) -> anyhow::Result<()> {
    let key = parse_secret_key(mgmt_priv_key)?;
    let mgmt_pub_key = stringify_public_key_from_secret_key(&key);

    let key = random_key();
    let pub_key_str = stringify_public_key_from_secret_key(&key);
    let priv_key_str = stringify_secret_key(&key);
    let data = match db
        .outbound_endpoint()
        .create(
            name.to_string(),
            pub_key_str.clone(),
            name.to_string(),
            ip.to_string(),
            vec![
                prisma::outbound_endpoint::port_start::set(Some(port_start)),
                prisma::outbound_endpoint::port_end::set(Some(port_end)),
                prisma::outbound_endpoint::traffic_scale::set(Some(scale as f64)),
            ],
        )
        .exec()
        .await
    {
        Ok(v) => {
            println!(
                "bash install_zfc.sh {} {} {} eth0 {} {}",
                arranger_hosts_url, mgmt_pub_key, priv_key_str, port_start, port_end
            );
            v
        }
        Err(e) => {
            log::error!("add machine: {} failed: {}", name, e);
            return Err(anyhow!("add machine: {} failed: {}", name, e));
        }
    };
    // add this machine to admin's line
    db.subscription()
        .update(
            prisma::subscription::token_id::equals(admin_token_id.to_string()),
            vec![prisma::subscription::lines::push(vec![data.id])],
        )
        .exec()
        .await?;
    Ok(())
}

async fn modify_db(db: Arc<PrismaClient>, script_path: &str) -> anyhow::Result<()> {
    let script = std::fs::read_to_string(script_path)?;
    db._execute_raw(Raw::new(&script, vec![])).exec().await?;
    Ok(())
}

async fn show_admin_token(db: Arc<PrismaClient>) -> anyhow::Result<()> {
    let admin_users = db
        .subscription()
        .find_many(vec![prisma::subscription::is_admin::equals(true)])
        .exec()
        .await?;
    
    if admin_users.is_empty() {
        println!("No admin users found in the database.");
        return Ok(());
    }
    
    println!("Admin users:");
    for user in admin_users {
        println!("  Address: {}", user.owner_address);
        println!("  Token: {}", user.token_id);
        println!("  Valid until: {}", user.valid_until);
        println!("  Activated: {}", user.activated);
        println!("---");
    }
    
    Ok(())
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Load .env file if it exists, but don't fail if it doesn't
    let _ = dotenv::dotenv();
    env_logger::init();
    let opt = Opt::parse();
    let db = Arc::new(prisma::new_client_with_url(&opt.db_path).await?);
    let redis_pool = setup_redis(&opt).await?;
    match &opt.command {
        Command::AddUser { user_file_path } => {
            add_user_proc(&user_file_path, &db).await?;
        }
        Command::ResetTrafficOnce {} => {
            reset_traffic(db, redis_pool).await?;
        }
        Command::ResetTrafficBg {} => {
            reset_traffic_background(db, redis_pool).await;
        }
        Command::ShowUserInfo {} => {
            show_user_info(db).await?;
        }
        Command::CopySubPort { sub, from, to } => {
            copy_sub_port(db, sub.as_str(), *from, *to).await?;
        }
        Command::RemoveLine { line_id } => {
            remove_line(db, *line_id).await?;
        }
        Command::AddMachine {
            name,
            ip,
            port_start,
            port_end,
            scale,
            admin_token_id
        } => {
            add_machine(
                db,
                name.as_str(),
                ip.as_str(),
                *port_start,
                *port_end,
                scale.unwrap_or(1.0),
                opt.mgmt_priv_key.as_str(),
                opt.arranger_hosts_url.as_str(),
                *admin_token_id,
            )
            .await?;
        }
        Command::ModifyDB { script_path } => {
            modify_db(db, script_path.as_str()).await?;
        }
        Command::ShowAdminToken {} => {
            show_admin_token(db).await?;
        }
    }
    Ok(())
}
