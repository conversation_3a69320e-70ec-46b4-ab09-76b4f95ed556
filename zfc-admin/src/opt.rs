use clap::{Parser, Subcommand};

#[derive(Debug, Parser)]
pub struct Opt {
    // #[clap(short = 'p', long, env)]
    // pub mgmt_endpoint: String,
    #[clap(
        short,
        long,
        env,
        default_value = "postgres://postgres:postgres@127.0.0.1/datium"
    )]
    pub db_path: String,
    #[clap(short, long, env, default_value = "redis://127.0.0.1:6379")]
    pub redis_path: String,

    #[clap(subcommand)]
    pub command: Command,

    #[clap(short = 'k', long, env = "MGMT_ARRANGER_PRIV_KEY")]
    pub mgmt_priv_key: String,

    #[clap(short = 'u', long, env, default_value = "https://127.0.0.1:3100")]
    pub arranger_hosts_url: String,
}

#[derive(Debug, Subcommand)]
pub enum Command {
    AddUser {
        #[arg(short, long, env = "USER_FILE_PATH", default_value = "user_info.json")]
        user_file_path: String,
    },
    ResetTrafficOnce {

    },
    ResetTrafficBg {

    },
    ShowUserInfo {

    },
    CopySubPort {
        sub: String,
        from: i32,
        to: i32,
    },
    RemoveLine {
        line_id: i32,
    },
    AddMachine {
        name: String,
        ip: String,
        port_start: i32,
        port_end: i32,
        scale: Option<f32>, // default 1.0
        admin_token_id: u32,
    },
    ModifyDB {
        script_path: String,
    },
    ShowAdminToken {
        
    }
}
