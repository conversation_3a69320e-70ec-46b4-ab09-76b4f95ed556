[package]
name = "zf-auth-server"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.1", features = ["derive", "env"] }
anyhow = "1"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { version = "1.25", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync", "fs"] }
tokio-util = { version = "0.7", features = ["io"] }
futures-util = "0.3.28"
axum = { version = "0.7", features = ["ws"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "fs"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
jsonwebtoken = "9.2"
rsa = { version = "0.9", features = ["serde", "sha2"] }
rand = "0.8"
dotenv = "0.15"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.6", features = ["v4", "serde"] }
hex = "0.4"
thiserror = "1.0"
bcrypt = "0.15"
rust_decimal = { version = "1.0", features = ["serde-with-str"] }
prisma-client-rust = { git = "https://github.com/Brendonovich/prisma-client-rust", tag = "0.6.11" }
prisma-client-rust-cli = { git = "https://github.com/Brendonovich/prisma-client-rust", tag = "0.6.11" }
rust-embed = "8.0"
mime_guess = "2.0"
common = { path = "../common" }

[[bin]]
name = "zf-auth-server"
path = "src/main.rs"

[[bin]]
name = "zf-auth-cli"
path = "src/cli.rs"

[build-dependencies]
prisma-client-rust = { git = "https://github.com/Brendonovich/prisma-client-rust", tag = "0.6.11" }
