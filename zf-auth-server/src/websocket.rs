use crate::{auth::AuthService, config::Config, errors::AuthError, offline_token::OfflineTokenExt};
use axum::{
    extract::{
        ws::{Message, WebSocket},
        Path, State, WebSocketUpgrade,
    },
    response::Response,
};
use common::{OfflineToken, SignedOfflineToken, WebSocketMessage};
use futures_util::{SinkExt, StreamExt};
use std::{collections::HashMap, sync::Arc, time::Duration};
use tokio::{sync::RwLock, time::interval};
use uuid::Uuid;

fn format_duration(duration: chrono::Duration) -> String {
    let total_seconds = duration.num_seconds();
    if total_seconds < 60 {
        format!("{}s", total_seconds)
    } else if total_seconds < 3600 {
        format!("{}m {}s", total_seconds / 60, total_seconds % 60)
    } else {
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        format!("{}h {}m", hours, minutes)
    }
}

#[derive(Debug, Clone)]
pub struct ConnectionInfo {
    pub instance_id: String,
    pub session_id: String,
    pub device_fingerprint: String,
    pub client_ip: String,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub last_ping: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone)]
pub struct ConnectionRegistry {
    connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>, // session_id -> ConnectionInfo
    instance_sessions: Arc<RwLock<HashMap<String, String>>>,   // instance_id -> session_id
}

impl ConnectionRegistry {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            instance_sessions: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn add_connection(&self, mut info: ConnectionInfo) -> Result<(), AuthError> {
        // Use a single write lock to ensure atomicity
        let mut connections = self.connections.write().await;
        let mut instance_sessions = self.instance_sessions.write().await;

        // Check if instance already has an active connection (atomic check)
        if let Some(existing_session) = instance_sessions.get(&info.instance_id) {
            if let Some(old_connection) = connections.get(existing_session) {
                // Preserve the original connected_at time when replacing connection
                let original_connected_at = old_connection.connected_at;
                let duration = chrono::Utc::now().signed_duration_since(original_connected_at);

                info.connected_at = original_connected_at;

                log::warn!(
                    "Replacing existing connection for instance {} (old session: {}, new session: {}), preserving original connect time: {}, total duration: {}",
                    info.instance_id,
                    existing_session,
                    info.session_id,
                    original_connected_at,
                    format_duration(duration)
                );

                connections.remove(existing_session);
                instance_sessions.remove(&info.instance_id);
            }
        }

        // Atomic insertion - if we reach here, no active connection exists or we're replacing one
        connections.insert(info.session_id.clone(), info.clone());
        instance_sessions.insert(info.instance_id.clone(), info.session_id.clone());

        log::info!(
            "WebSocket connection added: instance={}, session={}, device={}, connected_at={}, client_ip={}",
            info.instance_id,
            info.session_id,
            info.device_fingerprint,
            info.connected_at,
            info.client_ip
        );

        Ok(())
    }

    pub async fn remove_connection(&self, session_id: &str) -> Option<ConnectionInfo> {
        let mut connections = self.connections.write().await;
        let mut instance_sessions = self.instance_sessions.write().await;

        if let Some(info) = connections.remove(session_id) {
            instance_sessions.remove(&info.instance_id);
            log::info!(
                "WebSocket connection removed: instance={}, session={}",
                info.instance_id,
                session_id
            );
            Some(info)
        } else {
            None
        }
    }

    pub async fn update_ping(&self, session_id: &str) {
        let mut connections = self.connections.write().await;
        if let Some(info) = connections.get_mut(session_id) {
            info.last_ping = chrono::Utc::now();
        }
    }

    pub async fn get_connection(&self, session_id: &str) -> Option<ConnectionInfo> {
        let connections = self.connections.read().await;
        connections.get(session_id).cloned()
    }

    pub async fn cleanup_stale_connections(&self, timeout_seconds: u64) {
        let timeout_duration = chrono::Duration::seconds(timeout_seconds as i64);
        let now = chrono::Utc::now();
        let mut connections = self.connections.write().await;
        let mut instance_sessions = self.instance_sessions.write().await;

        let stale_sessions: Vec<String> = connections
            .iter()
            .filter_map(|(session_id, info)| {
                if now.signed_duration_since(info.last_ping) > timeout_duration {
                    Some(session_id.clone())
                } else {
                    None
                }
            })
            .collect();

        for session_id in stale_sessions {
            if let Some(info) = connections.remove(&session_id) {
                instance_sessions.remove(&info.instance_id);
                log::warn!(
                    "Cleaned up stale WebSocket connection: instance={}, session={}",
                    info.instance_id,
                    session_id
                );
            }
        }
    }

    pub async fn get_all_connections(&self) -> Vec<ConnectionInfo> {
        let connections = self.connections.read().await;
        connections.values().cloned().collect()
    }

    pub async fn get_connection_count(&self) -> usize {
        let connections = self.connections.read().await;
        connections.len()
    }

    pub async fn is_instance_online(&self, instance_id: &str) -> bool {
        let instance_sessions = self.instance_sessions.read().await;
        instance_sessions.contains_key(instance_id)
    }
}

pub struct WebSocketHandler {
    auth_service: Arc<AuthService>,
    registry: Arc<ConnectionRegistry>,
    config: Arc<Config>,
}

impl WebSocketHandler {
    pub fn new(auth_service: Arc<AuthService>, config: Arc<Config>) -> Self {
        let registry = Arc::new(ConnectionRegistry::new());

        // Start cleanup task
        let cleanup_registry = registry.clone();
        let connection_timeout = config.websocket_connection_timeout_secs;
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(300)); // 5 minutes
            loop {
                interval.tick().await;
                cleanup_registry
                    .cleanup_stale_connections(connection_timeout)
                    .await;
            }
        });

        Self {
            auth_service,
            registry,
            config,
        }
    }

    pub fn get_registry(&self) -> Arc<ConnectionRegistry> {
        self.registry.clone()
    }

    pub async fn remove_connection_with_cleanup(&self, session_id: &str) {
        if let Some(info) = self.registry.remove_connection(session_id).await {
            // Clear the database session if this was the current session
            if let Err(e) = self
                .clear_database_session(&info.instance_id, session_id)
                .await
            {
                log::error!(
                    "Failed to clear database session for instance {}: {}",
                    info.instance_id,
                    e
                );
            }
        }
    }

    async fn clear_database_session(
        &self,
        instance_id: &str,
        session_id: &str,
    ) -> Result<(), crate::errors::AuthError> {
        // Clear database session through AuthService
        self.auth_service
            .clear_session_if_matches(instance_id.to_string(), session_id.to_string())
            .await?;

        log::info!(
            "Cleared database session for instance {} (session: {})",
            instance_id,
            session_id
        );

        Ok(())
    }
}

impl WebSocketHandler {
    pub async fn handle_upgrade(
        &self,
        ws: WebSocketUpgrade,
        instance_id: String,
        client_ip: String,
    ) -> Response {
        let auth_service = self.auth_service.clone();
        let registry = self.registry.clone();
        let config = self.config.clone();
        let websocket_handler = Arc::new(WebSocketHandler {
            auth_service: self.auth_service.clone(),
            registry: self.registry.clone(),
            config: self.config.clone(),
        });

        ws.on_upgrade(move |socket| {
            handle_websocket_connection(
                socket,
                instance_id,
                client_ip,
                auth_service,
                registry,
                config,
                websocket_handler,
            )
        })
    }
}

async fn handle_websocket_connection(
    socket: WebSocket,
    instance_id: String,
    client_ip: String,
    auth_service: Arc<AuthService>,
    registry: Arc<ConnectionRegistry>,
    config: Arc<Config>,
    websocket_handler: Arc<WebSocketHandler>,
) {
    let (mut sender, mut receiver) = socket.split();
    let mut session_id: Option<String> = None;
    let mut authenticated = false;

    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(axum::extract::ws::Message::Text(text)) => {
                match serde_json::from_str::<WebSocketMessage>(&text) {
                    Ok(ws_msg) => match ws_msg {
                        WebSocketMessage::AuthRequest {
                            jwt,
                            device_fingerprint,
                        } => {
                            match authenticate_connection(
                                &jwt,
                                &instance_id,
                                &device_fingerprint,
                                &client_ip,
                                &auth_service,
                                &registry,
                                &config,
                            )
                            .await
                            {
                                Ok((new_session_id, offline_token)) => {
                                    session_id = Some(new_session_id);
                                    authenticated = true;

                                    let response = WebSocketMessage::AuthResponse {
                                        success: true,
                                        offline_token: Some(offline_token),
                                        error: None,
                                    };

                                    if let Ok(response_text) = serde_json::to_string(&response) {
                                        let _ = sender.send(Message::Text(response_text)).await;
                                    }
                                }
                                Err(e) => {
                                    let response = WebSocketMessage::AuthResponse {
                                        success: false,
                                        offline_token: None,
                                        error: Some(e.to_string()),
                                    };

                                    if let Ok(response_text) = serde_json::to_string(&response) {
                                        let _ = sender.send(Message::Text(response_text)).await;
                                    }
                                    break;
                                }
                            }
                        }
                        WebSocketMessage::Ping { timestamp: _ } => {
                            log::debug!(
                                "Ping received from client: {:?} instance: {} authenticated: {}",
                                session_id,
                                instance_id,
                                authenticated
                            );
                            if authenticated {
                                if let Some(ref sid) = session_id {
                                    registry.update_ping(sid).await;

                                    let pong = WebSocketMessage::Pong {
                                        timestamp: chrono::Utc::now(),
                                    };

                                    if let Ok(pong_text) = serde_json::to_string(&pong) {
                                        let _ = sender.send(Message::Text(pong_text)).await;
                                    }
                                }
                            }
                        }
                        WebSocketMessage::EntitlementsRequest { timestamp: _ } => {
                            log::info!(
                                "Entitlements request received from client: {:?} instance: {} authenticated: {}",
                                session_id,
                                instance_id,
                                authenticated
                            );
                            if authenticated {
                                match handle_entitlements_request(&instance_id, &auth_service).await
                                {
                                    Ok(entitlements) => {
                                        let response = WebSocketMessage::EntitlementsResponse {
                                            success: true,
                                            entitlements: Some(entitlements),
                                            error: None,
                                        };

                                        if let Ok(response_text) = serde_json::to_string(&response)
                                        {
                                            let _ = sender.send(Message::Text(response_text)).await;
                                        }
                                    }
                                    Err(e) => {
                                        log::error!(
                                            "Failed to get entitlements for instance {}: {}",
                                            instance_id,
                                            e
                                        );
                                        let response = WebSocketMessage::EntitlementsResponse {
                                            success: false,
                                            entitlements: None,
                                            error: Some(e.to_string()),
                                        };

                                        if let Ok(response_text) = serde_json::to_string(&response)
                                        {
                                            let _ = sender.send(Message::Text(response_text)).await;
                                        }
                                    }
                                }
                            } else {
                                log::warn!("Entitlements request from unauthenticated client: instance: {}", instance_id);
                            }
                        }
                        _ => {
                            log::warn!("Unexpected message type from client");
                        }
                    },
                    Err(e) => {
                        log::error!("Failed to parse WebSocket message: {}", e);
                        break;
                    }
                }
            }
            Ok(Message::Close(_)) => {
                break;
            }
            Err(e) => {
                log::error!("WebSocket error: {}", e);
                break;
            }
            _ => {}
        }
    }

    // Cleanup on disconnect - remove from both registry and database
    if let Some(sid) = session_id {
        websocket_handler.remove_connection_with_cleanup(&sid).await;
    }
}

async fn authenticate_connection(
    jwt: &str,
    instance_id: &str,
    device_fingerprint: &str,
    client_ip: &str,
    auth_service: &Arc<AuthService>,
    registry: &Arc<ConnectionRegistry>,
    config: &Arc<Config>,
) -> Result<(String, SignedOfflineToken), AuthError> {
    // Verify JWT token
    let claims = auth_service.key_manager.verify_jwt(jwt)?;

    if claims.sub != instance_id {
        return Err(AuthError::Unauthorized);
    }

    // Get instance from database
    let instance = auth_service
        .get_instance_by_id(instance_id.to_string())
        .await?;

    // Check if license is expired
    if instance.license_expires_at < chrono::Utc::now() {
        return Err(AuthError::LicenseExpired);
    }

    // Create new session
    let session_id = Uuid::new_v4().to_string();

    // Create connection info
    let connection_info = ConnectionInfo {
        instance_id: instance_id.to_string(),
        session_id: session_id.clone(),
        device_fingerprint: device_fingerprint.to_string(),
        client_ip: client_ip.to_string(),
        connected_at: chrono::Utc::now(),
        last_ping: chrono::Utc::now(),
    };

    // Add connection to registry (this will fail if instance already connected)
    registry.add_connection(connection_info).await?;

    // Create offline token with configured validity
    let offline_token = OfflineToken::new(
        instance_id.to_string(),
        device_fingerprint.to_string(),
        session_id.clone(),
        instance.entitlements,
        config.offline_token_duration_hours,
    );

    let signed_offline_token = offline_token.sign(&auth_service.key_manager)?;

    Ok((session_id, signed_offline_token))
}

pub async fn websocket_upgrade_handler(
    ws: WebSocketUpgrade,
    Path(instance_id): Path<String>,
    headers: axum::http::HeaderMap,
    State(handler): State<Arc<WebSocketHandler>>,
) -> Response {
    // Extract client IP from headers
    let client_ip = headers
        .get("x-forwarded-for")
        .and_then(|v| v.to_str().ok())
        .and_then(|forwarded| forwarded.split(',').next())
        .or_else(|| headers.get("x-real-ip").and_then(|v| v.to_str().ok()))
        .unwrap_or("127.0.0.1")
        .trim()
        .to_string();

    handler.handle_upgrade(ws, instance_id, client_ip).await
}

async fn handle_entitlements_request(
    instance_id: &str,
    auth_service: &Arc<AuthService>,
) -> Result<serde_json::Value, AuthError> {
    log::info!(
        "Processing entitlements request for instance: {}",
        instance_id
    );

    // Get instance from database to retrieve current entitlements
    let instance = auth_service
        .get_instance_by_id(instance_id.to_string())
        .await?;

    // Check if license is expired
    if instance.license_expires_at < chrono::Utc::now() {
        return Err(AuthError::LicenseExpired);
    }

    log::info!(
        "Successfully retrieved entitlements for instance {}: {:?}",
        instance_id,
        instance.entitlements
    );

    Ok(instance.entitlements)
}
