use anyhow::Result;
use bcrypt::{hash, DEFAULT_COST};
use clap::{Parser, Subcommand};
use std::sync::Arc;
use zf_auth_server::prisma::{admin_user, PrismaClient};

#[derive(Parser)]
#[command(name = "zf-auth-cli")]
#[command(about = "ZF Auth Server CLI tool for administrative tasks")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
    #[arg(short, env = "ZF_AUTH_DATABASE_URL", long, help = "Database URL")]
    pub database_url: String,
}

#[derive(Subcommand)]
pub enum Commands {
    #[command(about = "Admin user management commands")]
    Admin {
        #[command(subcommand)]
        command: AdminCommands,
    },
}

#[derive(Subcommand)]
pub enum AdminCommands {
    #[command(about = "Change admin user password")]
    ChangePassword {
        #[arg(short, long, help = "Admin username")]
        username: String,
        #[arg(short, long, help = "New password")]
        password: String,
    },
    #[command(about = "Create initial admin user")]
    CreateUser {
        #[arg(short, long, help = "Admin username")]
        username: String,
        #[arg(short, long, help = "Password")]
        password: String,
    },
    #[command(about = "List all admin users")]
    List,
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv::dotenv().ok();
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();

    let cli = Cli::parse();
    // Initialize database connection
    let db = Arc::new(
        PrismaClient::_builder()
            .with_url(cli.database_url)
            .build()
            .await?,
    );

    match cli.command {
        Commands::Admin { command } => {
            handle_admin_command(command, db).await?;
        }
    }

    Ok(())
}

async fn handle_admin_command(command: AdminCommands, db: Arc<PrismaClient>) -> Result<()> {
    match command {
        AdminCommands::ChangePassword { username, password } => {
            change_admin_password(db, username, password).await?;
        }
        AdminCommands::CreateUser { username, password } => {
            create_admin_user(db, username, password).await?;
        }
        AdminCommands::List => {
            list_admin_users(db).await?;
        }
    }
    Ok(())
}

async fn change_admin_password(
    db: Arc<PrismaClient>,
    username: String,
    password: String,
) -> Result<()> {
    // Check if admin user exists
    let admin_user = db
        .admin_user()
        .find_unique(admin_user::username::equals(username.clone()))
        .exec()
        .await?;

    match admin_user {
        Some(user) => {
            // Hash the new password
            let password_hash = hash(password, DEFAULT_COST)?;

            // Update the password
            db.admin_user()
                .update(
                    admin_user::id::equals(user.id),
                    vec![admin_user::password_hash::set(password_hash)],
                )
                .exec()
                .await?;

            println!(
                "✓ Password successfully changed for admin user: {}",
                username
            );
        }
        None => {
            eprintln!("✗ Admin user '{}' not found", username);
            std::process::exit(1);
        }
    }

    Ok(())
}

async fn create_admin_user(
    db: Arc<PrismaClient>,
    username: String,
    password: String,
) -> Result<()> {
    // Check if any admin user already exists
    let admin_count = db.admin_user().count(vec![]).exec().await?;

    if admin_count > 0 {
        eprintln!("✗ Admin user already exists. Use 'change-password' command instead.");
        std::process::exit(1);
    }

    // Hash the password
    let password_hash = hash(password, DEFAULT_COST)?;

    // Create the admin user
    let admin_user = db
        .admin_user()
        .create(username.clone(), password_hash, vec![])
        .exec()
        .await?;

    println!(
        "✓ Admin user '{}' created successfully (ID: {})",
        username, admin_user.id
    );

    Ok(())
}

async fn list_admin_users(db: Arc<PrismaClient>) -> Result<()> {
    let users = db.admin_user().find_many(vec![]).exec().await?;

    if users.is_empty() {
        println!("No admin users found");
        return Ok(());
    }

    println!("Admin Users:");
    println!(
        "{:<36} {:<20} {:<20} {:<20}",
        "ID", "Username", "Created At", "Last Login"
    );
    println!("{:-<96}", "");

    for user in users {
        let last_login = user
            .last_login_at
            .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
            .unwrap_or_else(|| "Never".to_string());

        println!(
            "{:<36} {:<20} {:<20} {:<20}",
            user.id,
            user.username,
            user.created_at.format("%Y-%m-%d %H:%M:%S"),
            last_login
        );
    }

    Ok(())
}
