use crate::{crypto::KeyManager, errors::AuthError};
use common::{OfflineToken, SignedOfflineToken};
use std::sync::Arc;

pub trait OfflineTokenExt {
    fn sign(&self, key_manager: &Arc<KeyManager>) -> Result<SignedOfflineToken, AuthError>;
}

impl OfflineTokenExt for OfflineToken {
    fn sign(&self, key_manager: &Arc<KeyManager>) -> Result<SignedOfflineToken, AuthError> {
        let token_json = serde_json::to_string(self).map_err(|e| {
            AuthError::Internal(anyhow::anyhow!("Failed to serialize token: {}", e))
        })?;

        let signature = key_manager
            .sign_data(token_json.as_bytes())
            .map_err(|e| AuthError::Internal(anyhow::anyhow!("Failed to sign token: {}", e)))?;

        Ok(SignedOfflineToken::new(
            self.clone(),
            hex::encode(signature),
        ))
    }
}

pub trait SignedOfflineTokenExt {
    fn verify(&self, key_manager: &Arc<KeyManager>) -> Result<bool, AuthError>;
    fn is_valid_for_device(
        &self,
        device_fingerprint: &str,
        key_manager: &Arc<KeyManager>,
    ) -> Result<bool, AuthError>;
}

impl SignedOfflineTokenExt for SignedOfflineToken {
    fn verify(&self, key_manager: &Arc<KeyManager>) -> Result<bool, AuthError> {
        let token_json = serde_json::to_string(&self.token).map_err(|e| {
            AuthError::Internal(anyhow::anyhow!("Failed to serialize token: {}", e))
        })?;

        let signature_bytes = hex::decode(&self.signature)
            .map_err(|e| AuthError::Internal(anyhow::anyhow!("Invalid signature format: {}", e)))?;

        let is_valid = key_manager
            .verify_signature(token_json.as_bytes(), &signature_bytes)
            .map_err(|e| {
                AuthError::Internal(anyhow::anyhow!("Failed to verify signature: {}", e))
            })?;

        Ok(is_valid && !self.token.is_expired())
    }

    fn is_valid_for_device(
        &self,
        device_fingerprint: &str,
        key_manager: &Arc<KeyManager>,
    ) -> Result<bool, AuthError> {
        if !self.verify(key_manager)? {
            return Ok(false);
        }

        Ok(self.token.is_valid_for_device(device_fingerprint))
    }
}

// Device fingerprint generation is handled by the client

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_offline_token_creation() {
        let token = OfflineToken::new(
            "test-instance".to_string(),
            "test-device".to_string(),
            "test-session".to_string(),
            serde_json::json!({"max_workers": 1}),
            24,
        );

        assert_eq!(token.instance_id, "test-instance");
        assert_eq!(token.device_fingerprint, "test-device");
        assert!(!token.is_expired());
    }

    // Device fingerprint tests are in the client crate
}
