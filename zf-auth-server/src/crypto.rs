use anyhow::{anyhow, Result};
use jsonwebtoken::{decode, encode, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use rsa::sha2::{Digest, Sha256};
use rsa::{
    pkcs8::{Decode<PERSON>ri<PERSON><PERSON><PERSON>, <PERSON>ode<PERSON><PERSON><PERSON><PERSON><PERSON>, Encode<PERSON><PERSON><PERSON><PERSON><PERSON>, EncodePublic<PERSON><PERSON>},
    Pkcs1v15Sign, RsaPrivateKey, RsaPublicKey,
};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
pub struct JwtClaims {
    pub sub: String, // instance ID
    pub sid: String, // session ID
    pub exp: u64,    // expiration timestamp
    pub iat: u64,    // issued at timestamp
    pub entitlements: serde_json::Value,
}

pub struct KeyManager {
    rsa_private_key: RsaPrivateKey,
    rsa_public_key: RsaP<PERSON><PERSON><PERSON><PERSON>,
}

impl KeyManager {
    pub fn new() -> Result<Self> {
        Self::new_with_path("./keys")
    }

    pub fn new_with_path<P: AsRef<Path>>(keys_path: P) -> Result<Self> {
        let keys_path = keys_path.as_ref();
        let private_key_path = keys_path.join("private_key.pem");
        let public_key_path = keys_path.join("public_key.pem");

        // Try to load existing keys
        if private_key_path.exists() && public_key_path.exists() {
            match Self::load_keys_from_disk(&private_key_path, &public_key_path) {
                Ok((rsa_private_key, rsa_public_key)) => {
                    log::info!("Loaded existing RSA key pair from {}", keys_path.display());
                    return Ok(Self {
                        rsa_private_key,
                        rsa_public_key,
                    });
                }
                Err(e) => {
                    log::warn!(
                        "Failed to load existing keys from {}: {}. Generating new keys.",
                        keys_path.display(),
                        e
                    );
                }
            }
        }

        // Generate new RSA key pair for JWT signing
        let mut rng = rand::thread_rng();
        let rsa_private_key = RsaPrivateKey::new(&mut rng, 2048)
            .map_err(|e| anyhow!("Failed to generate RSA private key: {}", e))?;
        let rsa_public_key = RsaPublicKey::from(&rsa_private_key);

        // Save keys to disk
        if let Err(e) = Self::save_keys_to_disk(&rsa_private_key, &rsa_public_key, keys_path) {
            log::warn!("Failed to save keys to disk: {}", e);
        } else {
            log::info!("Saved new RSA key pair to {}", keys_path.display());
        }

        log::info!("Generated new RSA key pair for JWT signing");

        Ok(Self {
            rsa_private_key,
            rsa_public_key,
        })
    }

    fn load_keys_from_disk(
        private_key_path: &Path,
        public_key_path: &Path,
    ) -> Result<(RsaPrivateKey, RsaPublicKey)> {
        let private_key_pem = fs::read_to_string(private_key_path)
            .map_err(|e| anyhow!("Failed to read private key file: {}", e))?;
        let public_key_pem = fs::read_to_string(public_key_path)
            .map_err(|e| anyhow!("Failed to read public key file: {}", e))?;

        let rsa_private_key = RsaPrivateKey::from_pkcs8_pem(&private_key_pem)
            .map_err(|e| anyhow!("Failed to parse private key: {}", e))?;
        let rsa_public_key = RsaPublicKey::from_public_key_pem(&public_key_pem)
            .map_err(|e| anyhow!("Failed to parse public key: {}", e))?;

        Ok((rsa_private_key, rsa_public_key))
    }

    fn save_keys_to_disk(
        rsa_private_key: &RsaPrivateKey,
        rsa_public_key: &RsaPublicKey,
        keys_path: &Path,
    ) -> Result<()> {
        // Create keys directory if it doesn't exist
        fs::create_dir_all(keys_path)
            .map_err(|e| anyhow!("Failed to create keys directory: {}", e))?;

        let private_key_pem = rsa_private_key
            .to_pkcs8_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize private key: {}", e))?;
        let public_key_pem = rsa_public_key
            .to_public_key_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize public key: {}", e))?;

        let private_key_path = keys_path.join("private_key.pem");
        let public_key_path = keys_path.join("public_key.pem");

        fs::write(&private_key_path, private_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to write private key file: {}", e))?;
        fs::write(&public_key_path, public_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to write public key file: {}", e))?;

        // Set secure permissions on private key file (readable only by owner)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(&private_key_path)?.permissions();
            perms.set_mode(0o600); // rw-------
            fs::set_permissions(&private_key_path, perms)?;
        }

        Ok(())
    }

    pub fn get_rsa_public_key_pem(&self) -> Result<String> {
        self.rsa_public_key
            .to_public_key_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize RSA public key: {}", e))
    }

    pub fn get_rsa_private_key_pem(&self) -> Result<String> {
        Ok(self
            .rsa_private_key
            .to_pkcs8_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize RSA private key: {}", e))?
            .to_string())
    }

    pub fn create_jwt(&self, claims: &JwtClaims) -> Result<String> {
        let header = Header::new(Algorithm::RS256);
        let private_key_pem = self.get_rsa_private_key_pem()?;
        let encoding_key = EncodingKey::from_rsa_pem(private_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to create encoding key: {}", e))?;

        encode(&header, claims, &encoding_key).map_err(|e| anyhow!("Failed to create JWT: {}", e))
    }

    pub fn verify_jwt(&self, token: &str) -> Result<JwtClaims> {
        let mut validation = Validation::new(Algorithm::RS256);
        validation.validate_exp = true;

        let public_key_pem = self.get_rsa_public_key_pem()?;
        let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to create decoding key: {}", e))?;

        let token_data = decode::<JwtClaims>(token, &decoding_key, &validation)
            .map_err(|e| anyhow!("Failed to verify JWT: {}", e))?;

        Ok(token_data.claims)
    }

    pub fn sign_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        let mut hasher = Sha256::new();
        hasher.update(data);
        let digest = hasher.finalize();

        self.rsa_private_key
            .sign(Pkcs1v15Sign::new::<Sha256>(), &digest)
            .map_err(|e| anyhow!("Failed to sign data: {}", e))
    }

    pub fn verify_signature(&self, data: &[u8], signature: &[u8]) -> Result<bool> {
        let mut hasher = Sha256::new();
        hasher.update(data);
        let digest = hasher.finalize();

        match self
            .rsa_public_key
            .verify(Pkcs1v15Sign::new::<Sha256>(), &digest, signature)
        {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}
