use clap::<PERSON>rse<PERSON>;

#[derive(Parse<PERSON>, Debug, <PERSON><PERSON>)]
#[command(author, version, about, long_about = None)]
pub struct Config {
    #[arg(long, env = "ZF_AUTH_DATABASE_URL")]
    pub database_url: String,

    #[arg(long, env = "ZF_AUTH_BIND_ADDRESS", default_value = "0.0.0.0:8080")]
    pub bind_address: String,

    #[arg(long, env = "ZF_AUTH_JWT_EXPIRY_HOURS", default_value = "24")]
    pub jwt_expiry_hours: u64,

    #[arg(long, env = "ZF_AUTH_SESSION_TIMEOUT_HOURS", default_value = "24")]
    pub session_timeout_hours: u64,

    #[arg(
        long,
        env = "ZF_AUTH_OFFLINE_TOKEN_DURATION_HOURS",
        default_value = "24"
    )]
    pub offline_token_duration_hours: u64,

    #[arg(
        long,
        env = "ZF_AUTH_WEBSOCKET_HEARTBEAT_INTERVAL_SECS",
        default_value = "30"
    )]
    pub websocket_heartbeat_interval_secs: u64,

    #[arg(
        long,
        env = "ZF_AUTH_WEBSOCKET_CONNECTION_TIMEOUT_SECS",
        default_value = "120"
    )]
    pub websocket_connection_timeout_secs: u64,

    #[arg(long, env = "ZF_AUTH_KEYS_PATH", default_value = "./keys")]
    pub keys_path: String,

    #[arg(long, env = "ZF_AUTH_WORKER_BINARY_PATH", default_value = "./zf-worker")]
    pub worker_binary_path: String,

    #[arg(long, env = "ZF_AUTH_FORWARDER_BINARY_PATH", default_value = "./forwarder-server")]
    pub forwarder_binary_path: String,
}

impl Config {
    pub fn from_env() -> anyhow::Result<Self> {
        let config = Self::parse();

        // Validate required fields
        if config.database_url.is_empty() {
            return Err(anyhow::anyhow!("ZF_AUTH_DATABASE_URL is required"));
        }

        Ok(config)
    }
}
