use crate::{
    auth::AuthService,
    config::Config,
    crypto::KeyManager,
    handlers,
    prisma::PrismaClient,
    websocket::{websocket_upgrade_handler, WebSocketHandler},
};
use axum::{
    http::{header, StatusCode, Uri},
    middleware,
    response::{IntoResponse, Response},
    routing::{delete, get, post, put},
    Router,
};
use rust_embed::RustEmbed;
use std::sync::Arc;
use tower_http::cors::CorsLayer;

#[derive(RustEmbed, Clone)]
#[folder = "../zfc-license-admin/dist/"]
struct FrontendAssets;

// Custom handler for SPA routing - serves static files or falls back to index.html
async fn serve_spa(uri: Uri) -> impl IntoResponse {
    let path = uri.path().trim_start_matches('/');
    log::warn!("SPA fallback handler called for path: {}", path);

    // For root path, serve index.html
    if path.is_empty() {
        return serve_file("index.html");
    }

    // First try to serve the requested file (for CSS, JS, images, etc.)
    if let Some(content) = FrontendAssets::get(path) {
        let mime = mime_guess::from_path(path).first_or_octet_stream();
        return Response::builder()
            .status(StatusCode::OK)
            .header(header::CONTENT_TYPE, mime.as_ref())
            .header(header::CACHE_CONTROL, "public, max-age=********")
            .body(content.data.into())
            .unwrap();
    }

    // For any path that doesn't match a static file, serve index.html
    // This allows the Vue router to handle client-side routing
    serve_file("index.html")
}

fn serve_file(path: &str) -> Response {
    match FrontendAssets::get(path) {
        Some(content) => {
            let mime = mime_guess::from_path(path).first_or_octet_stream();
            Response::builder()
                .status(StatusCode::OK)
                .header(header::CONTENT_TYPE, mime.as_ref())
                .body(content.data.into())
                .unwrap()
        }
        None => Response::builder()
            .status(StatusCode::NOT_FOUND)
            .body("404 Not Found".into())
            .unwrap(),
    }
}

pub async fn start_server(config: Config) -> anyhow::Result<()> {
    // Initialize database
    let db = Arc::new(
        PrismaClient::_builder()
            .with_url(config.database_url.clone())
            .build()
            .await?,
    );

    // Initialize key manager with RSA keys for JWT signing
    let key_manager = Arc::new(KeyManager::new_with_path(&config.keys_path)?);

    // Initialize auth service
    let auth_service = Arc::new(AuthService::new(
        db,
        key_manager,
        config.jwt_expiry_hours,
        config.session_timeout_hours,
    ));

    // Initialize WebSocket handler
    let websocket_handler = Arc::new(WebSocketHandler::new(
        auth_service.clone(),
        Arc::new(config.clone()),
    ));

    let config_arc = Arc::new(config.clone());

    // Protected admin routes
    let protected_admin_routes = Router::new()
        .route("/admin/me", get(handlers::get_current_admin))
        .route("/admin/dashboard/stats", get(handlers::get_dashboard_stats))
        .route("/admin/instances", post(handlers::create_instance))
        .route(
            "/admin/instances/batch",
            post(handlers::batch_create_instances),
        )
        .route("/admin/instances", get(handlers::get_instances))
        .route("/admin/instances/:id", put(handlers::update_instance))
        .route("/admin/instances/:id", delete(handlers::delete_instance))
        .route(
            "/admin/instances/:id/force-expire",
            post(handlers::force_expire_session),
        )
        .route(
            "/admin/renewal-codes",
            post(handlers::generate_renewal_codes),
        )
        .route("/admin/renewal-codes", get(handlers::get_renewal_codes))
        .route(
            "/admin/renewal-codes/:id",
            delete(handlers::delete_renewal_code),
        )
        .route(
            "/admin/instances/:id/renew",
            post(handlers::apply_renewal_code),
        )
        // 管理员续费请求管理路由
        .route(
            "/admin/renewal-requests",
            get(handlers::admin_get_renewal_requests),
        )
        .route(
            "/admin/renewal-requests/:id",
            get(handlers::admin_get_renewal_request_detail),
        )
        .route(
            "/admin/renewal-requests/:id/process",
            post(handlers::admin_process_renewal_request),
        )
        .route(
            "/admin/renewal-requests/:id/close",
            post(handlers::admin_close_renewal_request),
        )
        .route(
            "/admin/renewal-requests/:id/messages",
            post(handlers::admin_send_renewal_request_message),
        )
        .route(
            "/admin/renewal-requests/:id/messages",
            get(handlers::admin_get_renewal_request_messages),
        )
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::admin_auth,
        ));

    // Online client management routes (separate router with combined state)
    let online_client_routes = Router::new()
        .route("/admin/online-clients", get(handlers::get_online_clients))
        .route(
            "/admin/online-clients/:session_id",
            get(handlers::get_client_details),
        )
        .route(
            "/admin/online-clients/:session_id/disconnect",
            post(handlers::disconnect_client),
        )
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::admin_auth,
        ))
        .with_state((auth_service.clone(), websocket_handler.clone()));

    log::info!("Online client routes created successfully");

    // Instance-level routes (for software instances to query their own info)
    let instance_routes = Router::new()
        .route("/instance/:id/heartbeat", post(handlers::heartbeat))
        .route("/instance/:id/status", get(handlers::get_instance_status))
        .route(
            "/instance/:id/worker-version",
            get(handlers::get_worker_version),
        )
        .route(
            "/instance/:id/forwarder-version",
            get(handlers::get_forwarder_version),
        )
        .route(
            "/instance/:id/renewal-history",
            get(handlers::get_instance_renewal_history),
        )
        .route(
            "/instance/:id/renew",
            post(handlers::apply_renewal_code_for_instance),
        )
        // 价格计算相关路由
        .route(
            "/instance/:id/calculate-renewal-price",
            get(handlers::calculate_renewal_price),
        )
        // 续费请求相关路由 - 按具体程度排序，避免路由冲突
        // 最具体的路由放在前面
        .route(
            "/instance/:id/renewal-requests/:request_id/messages",
            post(handlers::send_renewal_request_message),
        )
        .route(
            "/instance/:id/renewal-requests/:request_id/messages",
            get(handlers::get_renewal_request_messages),
        )
        .route(
            "/instance/:id/renewal-requests/:request_id",
            delete(handlers::cancel_renewal_request),
        )
        .route(
            "/instance/:id/renewal-requests",
            post(handlers::create_renewal_request),
        )
        .route(
            "/instance/:id/renewal-requests",
            get(handlers::get_instance_renewal_requests),
        )
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::instance_auth,
        ))
        .with_state((auth_service.clone(), websocket_handler.clone()));

    // Binary download routes (separate from other instance routes due to different state)
    let download_routes = Router::new()
        .route(
            "/instance/:id/download-worker",
            get(handlers::download_worker_binary),
        )
        .route(
            "/instance/:id/download-forwarder",
            get(handlers::download_forwarder_binary),
        )
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::instance_auth,
        ))
        .with_state((auth_service.clone(), config_arc.clone()));

    // WebSocket routes
    let websocket_routes = Router::new()
        .route("/ws/instance/:id/auth", get(websocket_upgrade_handler))
        .with_state(websocket_handler);

    // Create routes that need auth_service state (but not online_clients)
    let basic_auth_service_routes = Router::new()
        // Public admin initialization endpoints
        .route("/admin/init/check", get(handlers::check_initialization))
        .route("/admin/init/setup", post(handlers::create_admin_user))
        .route("/admin/login", post(handlers::admin_login))
        // Public key endpoint for JWT verification
        .route("/public-key", get(handlers::get_public_key))
        // Merge protected routes
        .merge(protected_admin_routes)
        .merge(instance_routes)
        .with_state(auth_service.clone());

    // Create online client routes with their special state requirements
    log::info!("Creating online client routes with combined state...");
    let online_client_routes_with_combined_state = online_client_routes;

    // Request logging middleware
    let request_logger = axum::middleware::from_fn(
        |req: axum::extract::Request, next: axum::middleware::Next| async move {
            log::info!(
                "Incoming request: {} {} (full path: {})",
                req.method(),
                req.uri(),
                req.uri().path()
            );
            // If this is an admin request, log more details
            if req.uri().path().starts_with("/admin") {
                log::info!(
                    "Admin request details - Path: {}, Query: {:?}",
                    req.uri().path(),
                    req.uri().query()
                );
            }
            next.run(req).await
        },
    );

    // Build the application
    log::info!("Building main application router...");
    let app = Router::new()
        // Merge routes with basic auth_service state
        .merge(basic_auth_service_routes)
        // Merge online client routes with their combined state (FIRST to ensure priority)
        .merge(online_client_routes_with_combined_state)
        // Merge other routes with their own states
        .merge(download_routes)
        .merge(websocket_routes)
        .layer(request_logger)
        .layer(CorsLayer::permissive())
        // Serve SPA - this should be last to act as a fallback for client-side routing
        .fallback(serve_spa);

    log::info!("Application router built successfully");

    // Start the server
    let listener = tokio::net::TcpListener::bind(&config.bind_address).await?;
    log::info!("ZF Auth Server listening on {}", config.bind_address);

    axum::serve(listener, app).await?;

    Ok(())
}
