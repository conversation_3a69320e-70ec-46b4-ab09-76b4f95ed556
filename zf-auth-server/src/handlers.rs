use crate::{
    auth::AuthService, config::Config, errors::AuthError, models::*, websocket::WebSocketHandler,
};
use axum::{
    body::Body,
    extract::{Path, Query, State},
    http::{HeaderMap, HeaderValue, StatusCode},
    response::{IntoResponse, Json, Response},
};
use common::{CreateRenewalRequestRequest, PaginatedResponse, RenewalRequestResponse};
use std::sync::Arc;
use tokio_util::io::ReaderStream;

pub async fn heartbeat(
    State((auth_service, websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    headers: HeaderMap,
    Json(_request): Json<HeartbeatRequest>,
) -> Result<Json<HeartbeatResponse>, AuthError> {
    // Extract client IP (in real deployment, this would come from a load balancer header)
    let client_ip = headers
        .get("x-forwarded-for")
        .and_then(|v| v.to_str().ok())
        .or_else(|| headers.get("x-real-ip").and_then(|v| v.to_str().ok()))
        .unwrap_or("127.0.0.1")
        .to_string();

    // Extract existing JWT from Authorization header
    let existing_jwt = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .map(|token| token.to_string());

    // Create a heartbeat request with the instance_id from the path
    let heartbeat_request = HeartbeatRequest {
        instance_id: instance_id,
    };

    let registry = websocket_handler.get_registry();
    let token = auth_service
        .heartbeat(heartbeat_request, client_ip, existing_jwt, Some(&registry))
        .await?;

    Ok(Json(HeartbeatResponse { token }))
}

pub async fn create_instance(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateInstanceRequest>,
) -> Result<(StatusCode, Json<ApiResponse<AdminInstanceResponse>>), AuthError> {
    let instance = auth_service
        .create_instance(
            request.name,
            request.initial_duration,
            request.entitlements,
            request.monthly_rate,
            request.setup_fee,
        )
        .await?;

    let response = AdminInstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        activated_at: instance.activated_at.map(|dt| dt.into()),
        api_key: instance.api_key,
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
        monthly_rate: instance.monthly_rate.map(|v| v.to_string()),
        setup_fee: instance.setup_fee.map(|v| v.to_string()),
    };

    Ok((StatusCode::CREATED, Json(ApiResponse::success(response))))
}

pub async fn batch_create_instances(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<BatchCreateInstancesRequest>,
) -> Result<Json<ApiResponse<Vec<AdminInstanceResponse>>>, AuthError> {
    let instances = auth_service
        .batch_create_instances(
            request.name_template,
            request.initial_duration,
            request.entitlements,
            request.quantity,
        )
        .await?;

    let instances_response: Vec<AdminInstanceResponse> = instances
        .into_iter()
        .map(|instance| AdminInstanceResponse {
            id: instance.id,
            name: instance.name,
            license_expires_at: instance.license_expires_at.into(),
            activated_at: instance.activated_at.map(|dt| dt.into()),
            api_key: instance.api_key,
            entitlements: instance.entitlements,
            last_seen_ip: instance.last_seen_ip,
            last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
            current_session_id: instance.current_session_id,
            created_at: instance.created_at.into(),
            updated_at: instance.updated_at.into(),
            monthly_rate: instance.monthly_rate.map(|d| d.to_string()),
            setup_fee: instance.setup_fee.map(|d| d.to_string()),
        })
        .collect();

    Ok(Json(ApiResponse::success(instances_response)))
}

pub async fn get_instances(
    State(auth_service): State<Arc<AuthService>>,
    Query(pagination): Query<PaginationQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<AdminInstanceResponse>>>, AuthError> {
    let page = pagination.page.unwrap_or(1);
    let page_size = pagination.page_size.unwrap_or(20);
    let search = pagination.search;

    // Use optimized pagination at database level
    let (instances, total) = auth_service
        .get_instances_paginated(page, page_size, search)
        .await?;

    let total_pages = (total + page_size - 1) / page_size;

    let instances_response: Vec<AdminInstanceResponse> = instances
        .into_iter()
        .map(|instance| AdminInstanceResponse {
            id: instance.id,
            name: instance.name,
            license_expires_at: instance.license_expires_at.into(),
            activated_at: instance.activated_at.map(|dt| dt.into()),
            api_key: instance.api_key,
            entitlements: instance.entitlements,
            last_seen_ip: instance.last_seen_ip,
            last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
            current_session_id: instance.current_session_id,
            created_at: instance.created_at.into(),
            updated_at: instance.updated_at.into(),
            monthly_rate: instance.monthly_rate.map(|d| d.to_string()),
            setup_fee: instance.setup_fee.map(|d| d.to_string()),
        })
        .collect();

    let paginated_response = PaginatedResponse {
        data: instances_response,
        page,
        page_size,
        total,
        total_pages,
    };

    Ok(Json(ApiResponse::success(paginated_response)))
}

pub async fn update_instance(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
    Json(request): Json<UpdateInstanceRequest>,
) -> Result<Json<ApiResponse<AdminInstanceResponse>>, AuthError> {
    let instance = auth_service
        .update_instance(
            id,
            request.name,
            request.entitlements,
            request.monthly_rate,
            request.setup_fee,
        )
        .await?;

    let response = AdminInstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        activated_at: instance.activated_at.map(|dt| dt.into()),
        api_key: instance.api_key,
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
        monthly_rate: instance.monthly_rate.map(|v| v.to_string()),
        setup_fee: instance.setup_fee.map(|v| v.to_string()),
    };

    Ok(Json(ApiResponse::success(response)))
}

pub async fn delete_instance(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.delete_instance(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn force_expire_session(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.force_expire_session(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn create_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateRenewalCodeRequest>,
) -> Result<(StatusCode, Json<ApiResponse<serde_json::Value>>), AuthError> {
    let id = auth_service.create_renewal_code(request.duration).await?;

    Ok((
        StatusCode::CREATED,
        Json(ApiResponse::success(serde_json::json!({ "id": id }))),
    ))
}

pub async fn apply_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
    Json(request): Json<ApplyRenewalCodeRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service
        .apply_renewal_code(instance_id, request.renewal_code_id)
        .await?;

    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn get_renewal_codes(
    State(auth_service): State<Arc<AuthService>>,
    Query(query): Query<RenewalCodeQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<RenewalCodeResponse>>>, AuthError> {
    let page = query.page.unwrap_or(1);
    let page_size = query.page_size.unwrap_or(20);
    let status = query.status;

    // Use optimized pagination at database level
    let (codes, total) = auth_service
        .get_renewal_codes_paginated(page, page_size, status)
        .await?;

    let total_pages = (total + page_size - 1) / page_size;

    let codes_response: Vec<RenewalCodeResponse> = codes
        .into_iter()
        .map(|code| RenewalCodeResponse {
            id: code.id.clone(),
            code: code.id, // Using id as code for now, or you might want to add a separate code field
            duration: code.duration,
            created_at: code.created_at.into(),
            used_at: code.used_at.map(|dt| dt.into()),
            used_by: code.used_by_instance_id,
            amount: code.amount.map(|v| v.to_string()),
        })
        .collect();

    let paginated_response = PaginatedResponse {
        data: codes_response,
        page,
        page_size,
        total,
        total_pages,
    };

    Ok(Json(ApiResponse::success(paginated_response)))
}

pub async fn generate_renewal_codes(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<GenerateCodesRequest>,
) -> Result<Json<ApiResponse<Vec<RenewalCodeResponse>>>, AuthError> {
    let codes = auth_service
        .generate_renewal_codes(request.duration, request.quantity, request.amount)
        .await?;

    let codes_response: Vec<RenewalCodeResponse> = codes
        .into_iter()
        .map(|code| RenewalCodeResponse {
            id: code.id.clone(),
            code: code.id,
            duration: code.duration,
            created_at: code.created_at.into(),
            used_at: code.used_at.map(|dt| dt.into()),
            used_by: code.used_by_instance_id,
            amount: code.amount.map(|v| v.to_string()),
        })
        .collect();

    Ok(Json(ApiResponse::success(codes_response)))
}

pub async fn delete_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.delete_renewal_code(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

// Check if the system is initialized (has admin user)
pub async fn check_initialization(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<CheckInitializationResponse>>, AuthError> {
    let is_initialized = auth_service.is_initialized().await?;
    let response_data = CheckInitializationResponse { is_initialized };
    Ok(Json(ApiResponse::success(response_data)))
}

// Create the first admin user (only works if no admin user exists)
pub async fn create_admin_user(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateAdminUserRequest>,
) -> Result<(StatusCode, Json<ApiResponse<serde_json::Value>>), AuthError> {
    let id = auth_service
        .create_admin_user(request.username, request.password)
        .await?;

    Ok((
        StatusCode::CREATED,
        Json(ApiResponse::success(serde_json::json!({ "id": id }))),
    ))
}

// Admin login
pub async fn admin_login(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<AdminLoginRequest>,
) -> Result<Json<ApiResponse<AdminLoginResponse>>, AuthError> {
    let token = auth_service
        .admin_login(request.username, request.password)
        .await?;

    let response_data = AdminLoginResponse { token };
    Ok(Json(ApiResponse::success(response_data)))
}

// Get current admin user info
pub async fn get_current_admin(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<AdminUserResponse>>, AuthError> {
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .ok_or(AuthError::Unauthorized)?;

    // Verify the admin token and get claims
    let claims = auth_service.key_manager.verify_jwt(auth_header)?;

    // Check if the token has admin entitlements
    if let Some(admin) = claims.entitlements.get("admin") {
        if admin.as_bool() != Some(true) {
            return Err(AuthError::Unauthorized);
        }
    } else {
        return Err(AuthError::Unauthorized);
    }

    let admin_user = auth_service.get_admin_user(claims.sub).await?;

    let response_data = AdminUserResponse {
        id: admin_user.id,
        username: admin_user.username,
        created_at: admin_user.created_at.into(),
        last_login_at: admin_user.last_login_at.map(|dt| dt.into()),
    };
    Ok(Json(ApiResponse::success(response_data)))
}

// Admin authentication middleware
pub async fn admin_auth(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
    request: axum::extract::Request,
    next: axum::middleware::Next,
) -> Result<axum::response::Response, AuthError> {
    log::info!(
        "Admin auth middleware - Processing request to: {}",
        request.uri()
    );
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .ok_or(AuthError::Unauthorized)?;

    // Use the new admin token verification
    if !auth_service.verify_admin_token(auth_header).await? {
        return Err(AuthError::Unauthorized);
    }

    Ok(next.run(request).await)
}

// Instance authentication middleware
pub async fn instance_auth(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
    request: axum::extract::Request,
    next: axum::middleware::Next,
) -> Result<axum::response::Response, AuthError> {
    let path = request.uri().path();
    log::info!("Instance auth middleware - path: {}", path);

    let api_key = headers
        .get("x-api-key")
        .and_then(|v| v.to_str().ok())
        .ok_or_else(|| {
            log::error!("Missing x-api-key header");
            AuthError::Unauthorized
        })?;

    // Extract instance ID from the path
    let instance_id = path.split('/').nth(2).ok_or_else(|| {
        log::error!("Could not extract instance_id from path: {}", path);
        AuthError::Unauthorized
    })?;

    log::info!("Verifying API key for instance: {}", instance_id);

    // Verify the API key belongs to this instance
    auth_service
        .verify_instance_api_key(instance_id, api_key)
        .await
        .map_err(|e| {
            log::error!(
                "API key verification failed for instance {}: {}",
                instance_id,
                e
            );
            e
        })?;

    log::info!(
        "API key verification successful for instance: {}",
        instance_id
    );
    Ok(next.run(request).await)
}

// Get dashboard statistics
pub async fn get_dashboard_stats(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<DashboardStatsResponse>>, AuthError> {
    let stats = auth_service.get_dashboard_stats().await?;
    Ok(Json(ApiResponse::success(stats)))
}

// Instance-level APIs (for software instances to query their own info)

// Get instance status (for the instance itself)
pub async fn get_instance_status(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
) -> Result<Json<ApiResponse<InstanceResponse>>, AuthError> {
    let instance = auth_service.get_instance_by_id(instance_id).await?;
    let instance_response = InstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        activated_at: instance.activated_at.map(|dt| dt.into()),
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
    };
    Ok(Json(ApiResponse::success(instance_response)))
}

// Get instance renewal history
pub async fn get_instance_renewal_history(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
) -> Result<Json<ApiResponse<Vec<RenewalHistoryItem>>>, AuthError> {
    let history = auth_service
        .get_instance_renewal_history(instance_id)
        .await?;
    Ok(Json(ApiResponse::success(history)))
}

// Apply renewal code for instance (simplified version)
pub async fn apply_renewal_code_for_instance(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Json(request): Json<ApplyRenewalCodeRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service
        .apply_renewal_code(instance_id, request.renewal_code_id)
        .await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

// Get RSA public key for JWT verification (public endpoint)
pub async fn get_public_key(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<PublicKeyResponse>>, AuthError> {
    let public_key_pem = auth_service
        .key_manager
        .get_rsa_public_key_pem()
        .map_err(|e| AuthError::Internal(anyhow::anyhow!("Failed to get public key: {}", e)))?;

    let response = PublicKeyResponse {
        public_key: public_key_pem,
        algorithm: "RS256".to_string(),
    };

    Ok(Json(ApiResponse::success(response)))
}

// Online client management endpoints

// Get all online clients
pub async fn get_online_clients(
    State((auth_service, websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
) -> Result<Response, AuthError> {
    log::info!("GET /admin/online-clients - Starting request processing");
    let registry = websocket_handler.get_registry();
    let connections = registry.get_all_connections().await;

    let mut online_clients = Vec::new();
    let now = chrono::Utc::now();
    let stale_timeout = chrono::Duration::seconds(60); // 60 seconds timeout for stale detection

    for connection in connections {
        // Get instance details
        let instance = auth_service
            .get_instance_by_id(connection.instance_id.clone())
            .await?;

        let is_stale = now.signed_duration_since(connection.last_ping) > stale_timeout;

        online_clients.push(OnlineClientResponse {
            session_id: connection.session_id,
            instance_id: connection.instance_id,
            instance_name: instance.name,
            device_fingerprint: connection.device_fingerprint,
            client_ip: connection.client_ip,
            connected_at: connection.connected_at,
            last_ping: connection.last_ping,
            is_stale,
        });
    }

    log::info!(
        "GET /admin/online-clients - Successfully returning {} clients",
        online_clients.len()
    );

    // Create response with no-cache headers to prevent caching issues
    let mut response = Json(ApiResponse::success(online_clients)).into_response();
    let headers = response.headers_mut();
    headers.insert(
        "Cache-Control",
        HeaderValue::from_static("no-cache, no-store, must-revalidate"),
    );
    headers.insert("Pragma", HeaderValue::from_static("no-cache"));
    headers.insert("Expires", HeaderValue::from_static("0"));

    Ok(response)
}

// Get specific client details
pub async fn get_client_details(
    State((auth_service, websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(session_id): Path<String>,
) -> Result<Json<ApiResponse<OnlineClientResponse>>, AuthError> {
    let registry = websocket_handler.get_registry();

    match registry.get_connection(&session_id).await {
        Some(connection) => {
            // Get instance details
            let instance = auth_service
                .get_instance_by_id(connection.instance_id.clone())
                .await?;

            let now = chrono::Utc::now();
            let stale_timeout = chrono::Duration::seconds(60);
            let is_stale = now.signed_duration_since(connection.last_ping) > stale_timeout;

            let client_response = OnlineClientResponse {
                session_id: connection.session_id,
                instance_id: connection.instance_id,
                instance_name: instance.name,
                device_fingerprint: connection.device_fingerprint,
                client_ip: connection.client_ip,
                connected_at: connection.connected_at,
                last_ping: connection.last_ping,
                is_stale,
            };

            Ok(Json(ApiResponse::success(client_response)))
        }
        None => Err(AuthError::NotFound("Client session not found".to_string())),
    }
}

// Disconnect specific client
pub async fn disconnect_client(
    State((_auth_service, websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(session_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    let registry = websocket_handler.get_registry();

    // Check if the connection exists
    if registry.get_connection(&session_id).await.is_some() {
        // Remove the connection from registry
        registry.remove_connection(&session_id).await;

        log::info!("Forcibly disconnected client session: {}", session_id);
        Ok(Json(ApiResponse::success(serde_json::json!({
            "message": "Client disconnected successfully"
        }))))
    } else {
        Err(AuthError::NotFound("Client session not found".to_string()))
    }
}

// Download worker binary for instances
pub async fn download_worker_binary(
    State((auth_service, config)): State<(Arc<AuthService>, Arc<Config>)>,
    Path(instance_id): Path<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Response<Body>, AuthError> {
    // Verify that the instance exists and is valid
    let instance = auth_service.get_instance_by_id(instance_id).await?;

    // Check if license is still valid
    let now = chrono::Utc::now();
    if instance.license_expires_at < now {
        return Err(AuthError::LicenseExpired);
    }

    // Get controller version from query parameters
    let controller_version = params
        .get("controller_version")
        .cloned()
        .unwrap_or_else(|| "unknown".to_string());

    log::info!(
        "Controller version {} requesting worker binary for instance: {}",
        controller_version,
        instance.id
    );

    // Determine worker binary path based on controller version
    let worker_binary_path =
        determine_worker_binary_path(&config.worker_binary_path, &controller_version);

    // Check if file exists before attempting to serve
    if !std::path::Path::new(&worker_binary_path).exists() {
        log::error!("Worker binary not found at path: {}", worker_binary_path);
        return Err(AuthError::Internal(anyhow::anyhow!(
            "Worker binary not found"
        )));
    }

    log::info!(
        "Serving worker binary to instance: {} from path: {}",
        instance.id,
        worker_binary_path
    );

    // Use tokio::fs with better error handling and streaming
    match tokio::fs::File::open(&worker_binary_path).await {
        Ok(file) => {
            // Get file metadata for content length
            let metadata = file.metadata().await.map_err(|e| {
                log::error!(
                    "Failed to get file metadata for {}: {}",
                    worker_binary_path,
                    e
                );
                AuthError::Internal(anyhow::anyhow!("Failed to get file information"))
            })?;

            // Convert file to body using tokio-util's ReaderStream for efficient streaming
            let stream = ReaderStream::new(file);
            let body = Body::from_stream(stream);

            log::info!(
                "Successfully serving worker binary to instance: {} ({} bytes)",
                instance.id,
                metadata.len()
            );

            Ok(Response::builder()
                .status(StatusCode::OK)
                .header("Content-Type", "application/octet-stream")
                .header("Content-Disposition", "attachment; filename=\"zf-worker\"")
                .header("Content-Length", metadata.len())
                .header("Cache-Control", "no-cache")
                .body(body)
                .unwrap())
        }
        Err(e) => {
            log::error!(
                "Failed to open worker binary file at {}: {}",
                worker_binary_path,
                e
            );
            Err(AuthError::Internal(anyhow::anyhow!(
                "Failed to access worker binary: {}",
                e
            )))
        }
    }
}

// Get recommended worker version for a given controller version
pub async fn get_worker_version(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<ApiResponse<WorkerVersionResponse>>, AuthError> {
    // Verify that the instance exists and is valid
    let instance = auth_service.get_instance_by_id(instance_id).await?;

    // Check if license is still valid
    let now = chrono::Utc::now();
    if instance.license_expires_at < now {
        return Err(AuthError::LicenseExpired);
    }

    // Get controller version from query parameters
    let controller_version = params
        .get("controller_version")
        .cloned()
        .unwrap_or_else(|| "unknown".to_string());

    log::info!(
        "Instance {} requesting worker version for controller: {}",
        instance.id,
        controller_version
    );

    // Determine recommended worker version based on controller version
    let recommended_version = get_recommended_worker_version(&controller_version);

    let response = WorkerVersionResponse {
        recommended_version: recommended_version.clone(),
        controller_version: controller_version.clone(),
    };

    log::info!(
        "Recommending worker version {} for controller version {}",
        recommended_version,
        controller_version
    );

    Ok(Json(ApiResponse::success(response)))
}

// Determine the worker binary path based on controller version
fn determine_worker_binary_path(base_path: &str, controller_version: &str) -> String {
    // Try version-specific binary first
    let versioned_path = format!("{}-{}", base_path, controller_version);

    // If version-specific binary doesn't exist, fall back to the base path
    if std::path::Path::new(&versioned_path).exists() {
        log::info!("Using version-specific worker binary: {}", versioned_path);
        versioned_path
    } else {
        log::info!(
            "Version-specific binary not found, using default: {}",
            base_path
        );
        base_path.to_string()
    }
}

// Get recommended worker version for a given controller version
fn get_recommended_worker_version(controller_version: &str) -> String {
    // Version compatibility mapping
    // In a real implementation, this could be loaded from config or database
    match controller_version {
        // Exact version mappings
        "0.1.88" => "0.1.88".to_string(),
        "0.1.89" => "0.1.89".to_string(),
        "0.1.90" => "0.1.90".to_string(),

        // For unknown or newer versions, recommend the same version
        version if version.starts_with("0.1.") => version.to_string(),

        // For completely unknown versions, recommend latest stable
        _ => {
            log::warn!(
                "Unknown controller version: {}, recommending latest",
                controller_version
            );
            "latest".to_string()
        }
    }
}

// Download forwarder binary for instances
pub async fn download_forwarder_binary(
    State((auth_service, config)): State<(Arc<AuthService>, Arc<Config>)>,
    Path(instance_id): Path<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Response<Body>, AuthError> {
    // Verify that the instance exists and is valid
    let instance = auth_service.get_instance_by_id(instance_id).await?;

    // Check if license is still valid
    let now = chrono::Utc::now();
    if instance.license_expires_at < now {
        return Err(AuthError::LicenseExpired);
    }

    // Get controller version from query parameters
    let controller_version = params
        .get("controller_version")
        .cloned()
        .unwrap_or_else(|| "unknown".to_string());

    log::info!(
        "Controller version {} requesting forwarder binary for instance: {}",
        controller_version,
        instance.id
    );

    // Determine forwarder binary path based on controller version
    let forwarder_binary_path =
        determine_forwarder_binary_path(&config.forwarder_binary_path, &controller_version);

    // Check if file exists before attempting to serve
    if !std::path::Path::new(&forwarder_binary_path).exists() {
        log::error!(
            "Forwarder binary not found at path: {}",
            forwarder_binary_path
        );
        return Err(AuthError::Internal(anyhow::anyhow!(
            "Forwarder binary not found"
        )));
    }

    log::info!(
        "Serving forwarder binary to instance: {} from path: {}",
        instance.id,
        forwarder_binary_path
    );

    // Use tokio::fs with better error handling and streaming
    match tokio::fs::File::open(&forwarder_binary_path).await {
        Ok(file) => {
            // Get file metadata for content length
            let metadata = file.metadata().await.map_err(|e| {
                log::error!(
                    "Failed to get file metadata for {}: {}",
                    forwarder_binary_path,
                    e
                );
                AuthError::Internal(anyhow::anyhow!("Failed to get file information"))
            })?;

            // Convert file to body using tokio-util's ReaderStream for efficient streaming
            let stream = ReaderStream::new(file);
            let body = Body::from_stream(stream);

            log::info!(
                "Successfully serving forwarder binary to instance: {} ({} bytes)",
                instance.id,
                metadata.len()
            );

            Ok(Response::builder()
                .status(StatusCode::OK)
                .header("Content-Type", "application/octet-stream")
                .header(
                    "Content-Disposition",
                    "attachment; filename=\"forwarder-server\"",
                )
                .header("Content-Length", metadata.len())
                .header("Cache-Control", "no-cache")
                .body(body)
                .unwrap())
        }
        Err(e) => {
            log::error!(
                "Failed to open forwarder binary file at {}: {}",
                forwarder_binary_path,
                e
            );
            Err(AuthError::Internal(anyhow::anyhow!(
                "Failed to access forwarder binary: {}",
                e
            )))
        }
    }
}

// Get recommended forwarder version for a given controller version
pub async fn get_forwarder_version(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<ApiResponse<ForwarderVersionResponse>>, AuthError> {
    // Verify that the instance exists and is valid
    let instance = auth_service.get_instance_by_id(instance_id).await?;

    // Check if license is still valid
    let now = chrono::Utc::now();
    if instance.license_expires_at < now {
        return Err(AuthError::LicenseExpired);
    }

    // Get controller version from query parameters
    let controller_version = params
        .get("controller_version")
        .cloned()
        .unwrap_or_else(|| "unknown".to_string());

    log::info!(
        "Instance {} requesting forwarder version for controller: {}",
        instance.id,
        controller_version
    );

    // Determine recommended forwarder version based on controller version
    let recommended_version = get_recommended_forwarder_version(&controller_version);

    let response = ForwarderVersionResponse {
        recommended_version: recommended_version.clone(),
        controller_version: controller_version.clone(),
    };

    log::info!(
        "Recommending forwarder version {} for controller version {}",
        recommended_version,
        controller_version
    );

    Ok(Json(ApiResponse::success(response)))
}

// Determine the forwarder binary path based on controller version
fn determine_forwarder_binary_path(base_path: &str, controller_version: &str) -> String {
    // Try version-specific binary first
    let versioned_path = format!("{}-{}", base_path, controller_version);

    // If version-specific binary doesn't exist, fall back to the base path
    if std::path::Path::new(&versioned_path).exists() {
        log::info!(
            "Using version-specific forwarder binary: {}",
            versioned_path
        );
        versioned_path
    } else {
        log::info!(
            "Version-specific binary not found, using default: {}",
            base_path
        );
        base_path.to_string()
    }
}

// Get recommended forwarder version for a given controller version
fn get_recommended_forwarder_version(controller_version: &str) -> String {
    // Version compatibility mapping
    // In a real implementation, this could be loaded from config or database
    match controller_version {
        // Exact version mappings
        "0.1.88" => "0.1.88".to_string(),
        "0.1.89" => "0.1.89".to_string(),
        "0.1.90" => "0.1.90".to_string(),

        // For unknown or newer versions, recommend the same version
        version if version.starts_with("0.1.") => version.to_string(),

        // For completely unknown versions, recommend latest stable
        _ => {
            log::warn!(
                "Unknown controller version: {}, recommending latest",
                controller_version
            );
            "latest".to_string()
        }
    }
}

// 价格计算相关API handlers

pub async fn calculate_renewal_price(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Query(query): Query<std::collections::HashMap<String, String>>,
) -> Result<(StatusCode, Json<ApiResponse<CalculateRenewalPriceResponse>>), AuthError> {
    log::info!(
        "Calculate renewal price request - instance_id: {}, query: {:?}",
        instance_id,
        query
    );

    let requested_duration = query
        .get("requested_duration")
        .and_then(|d| d.parse::<i32>().ok())
        .unwrap_or(1); // 默认1个月

    log::info!("Parsed requested_duration: {}", requested_duration);

    let response = auth_service
        .calculate_renewal_price(instance_id, requested_duration)
        .await?;

    Ok((
        StatusCode::OK,
        Json(ApiResponse {
            success: true,
            data: Some(response),
            message: None,
            error: None,
        }),
    ))
}

// 续费请求相关API handlers

// 客户端创建续费请求
pub async fn create_renewal_request(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Json(request): Json<CreateRenewalRequestRequest>,
) -> Result<(StatusCode, Json<ApiResponse<RenewalRequestResponse>>), AuthError> {
    // Input validation
    if instance_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Instance ID cannot be empty".to_string(),
        ));
    }

    if request.payment_proof.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Payment proof is required".to_string(),
        ));
    }

    if request.payment_amount.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Payment amount is required".to_string(),
        ));
    }

    if request.requested_duration < 1 || request.requested_duration > 12 {
        return Err(AuthError::BadRequest(
            "Requested duration must be between 1 and 12 months".to_string(),
        ));
    }

    // Validate payment method
    if !["alipay_hongbao", "cryptocurrency"].contains(&request.payment_method.as_str()) {
        return Err(AuthError::BadRequest("Invalid payment method".to_string()));
    }

    let renewal_request = auth_service
        .create_renewal_request(instance_id, request)
        .await
        .map_err(|e| {
            log::error!("Failed to create renewal request: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to create renewal request")),
            }
        })?;

    Ok((
        StatusCode::CREATED,
        Json(ApiResponse::success(renewal_request)),
    ))
}

// 客户端获取自己的续费请求列表
pub async fn get_instance_renewal_requests(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path(instance_id): Path<String>,
    Query(query): Query<RenewalRequestQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<RenewalRequestResponse>>>, AuthError> {
    let mut filtered_query = query;
    filtered_query.instance_id = Some(instance_id);

    let requests = auth_service.get_renewal_requests(filtered_query).await?;

    Ok(Json(ApiResponse::success(requests)))
}

// 客户端取消pending状态的续费请求
pub async fn cancel_renewal_request(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path((instance_id, request_id)): Path<(String, String)>,
) -> Result<Json<ApiResponse<RenewalRequestResponse>>, AuthError> {
    // Input validation
    if instance_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Instance ID cannot be empty".to_string(),
        ));
    }

    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }

    let renewal_request = auth_service
        .cancel_renewal_request(instance_id, request_id)
        .await
        .map_err(|e| {
            log::error!("Failed to cancel renewal request: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to cancel renewal request")),
            }
        })?;

    Ok(Json(ApiResponse::success(renewal_request)))
}

// 管理员获取所有续费请求列表
pub async fn admin_get_renewal_requests(
    State(auth_service): State<Arc<AuthService>>,
    Query(query): Query<RenewalRequestQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<RenewalRequestResponse>>>, AuthError> {
    log::info!("Admin get renewal requests - Query parameters: page={:?}, page_size={:?}, status={:?}, instance_id={:?}", 
               query.page, query.page_size, query.status, query.instance_id);

    let requests = auth_service.get_renewal_requests(query).await?;

    Ok(Json(ApiResponse::success(requests)))
}

// 管理员获取续费请求详情
pub async fn admin_get_renewal_request_detail(
    State(auth_service): State<Arc<AuthService>>,
    Path(request_id): Path<String>,
) -> Result<Json<ApiResponse<RenewalRequestResponse>>, AuthError> {
    let renewal_request = auth_service.get_renewal_request_by_id(request_id).await?;

    Ok(Json(ApiResponse::success(renewal_request)))
}

// 管理员处理续费请求
pub async fn admin_process_renewal_request(
    State(auth_service): State<Arc<AuthService>>,
    Path(request_id): Path<String>,
    headers: HeaderMap,
    Json(request): Json<ProcessRenewalRequestRequest>,
) -> Result<Json<ApiResponse<RenewalRequestResponse>>, AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }

    // Validate renewal duration if provided
    if let Some(duration) = request.renewal_duration {
        if duration < 1 || duration > 3650 {
            return Err(AuthError::BadRequest(
                "Renewal duration must be between 1 and 3650 days".to_string(),
            ));
        }
    }

    // 从JWT token中提取管理员ID
    let admin_id = extract_admin_id_from_headers(&headers, &auth_service)
        .await
        .map_err(|e| {
            log::error!("Failed to extract admin ID from headers: {}", e);
            AuthError::Unauthorized
        })?;

    let renewal_request = auth_service
        .process_renewal_request(request_id, admin_id, request)
        .await
        .map_err(|e| {
            log::error!("Failed to process renewal request: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to process renewal request")),
            }
        })?;

    Ok(Json(ApiResponse::success(renewal_request)))
}

// 辅助函数：从header中提取管理员ID
async fn extract_admin_id_from_headers(
    headers: &HeaderMap,
    auth_service: &AuthService,
) -> Result<String, AuthError> {
    let token = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .ok_or_else(|| AuthError::Unauthorized)?;

    // 验证管理员token并获取claims
    let claims = auth_service.key_manager.verify_jwt(token)?;

    // 检查是否有管理员权限
    if let Some(admin) = claims.entitlements.get("admin") {
        if admin.as_bool() != Some(true) {
            return Err(AuthError::Unauthorized);
        }
    } else {
        return Err(AuthError::Unauthorized);
    }

    // 从subject中获取管理员ID
    Ok(claims.sub)
}

// 消息相关 API handlers

// 发送续费请求消息（客户端或管理员）
pub async fn send_renewal_request_message(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path((instance_id, request_id)): Path<(String, String)>,
    headers: HeaderMap,
    Json(request): Json<SendMessageRequest>,
) -> Result<(StatusCode, Json<ApiResponse<MessageResponse>>), AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }
    if request.content.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Message content cannot be empty".to_string(),
        ));
    }
    if !["customer", "admin"].contains(&request.message_type.as_str()) {
        return Err(AuthError::BadRequest(
            "Invalid message type. Must be 'customer' or 'admin'".to_string(),
        ));
    }

    // 根据消息类型确定发送者信息
    let (author_id, author_name) = if request.message_type == "admin" {
        // 管理员消息需要验证权限
        let admin_id = extract_admin_id_from_headers(&headers, &auth_service)
            .await
            .map_err(|e| {
                log::error!("Failed to extract admin ID from headers: {}", e);
                AuthError::Unauthorized
            })?;
        (Some(admin_id.clone()), Some(format!("Admin-{}", admin_id)))
    } else {
        // 客户消息：验证实例所有权
        // TODO: 这里可以添加更严格的客户身份验证
        (None, Some("Customer".to_string()))
    };

    let message_response = auth_service
        .send_renewal_request_message(request_id, instance_id, request, author_id, author_name)
        .await
        .map_err(|e| {
            log::error!("Failed to send renewal request message: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to send message")),
            }
        })?;

    Ok((StatusCode::CREATED, Json(ApiResponse::success(message_response))))
}

// 获取续费请求的消息历史
pub async fn get_renewal_request_messages(
    State((auth_service, _websocket_handler)): State<(Arc<AuthService>, Arc<WebSocketHandler>)>,
    Path((instance_id, request_id)): Path<(String, String)>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<Vec<MessageResponse>>>, AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }

    // 验证访问权限（管理员或该实例的所有者）
    let is_admin = extract_admin_id_from_headers(&headers, &auth_service).await.is_ok();
    
    if !is_admin {
        // TODO: 对于客户端，应该验证实例所有权
        // 这里暂时允许通过，实际部署时需要添加更严格的验证
    }

    let messages = auth_service
        .get_renewal_request_messages(request_id, instance_id)
        .await
        .map_err(|e| {
            log::error!("Failed to get renewal request messages: {}", e);
            match e {
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to get messages")),
            }
        })?;

    Ok(Json(ApiResponse::success(messages)))
}

// 管理员关闭续费请求（不执行延期）
pub async fn admin_close_renewal_request(
    State(auth_service): State<Arc<AuthService>>,
    Path(request_id): Path<String>,
    headers: HeaderMap,
    Json(request): Json<AdminCloseRequestRequest>,
) -> Result<Json<ApiResponse<RenewalRequestResponse>>, AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }

    // 验证管理员权限
    let admin_id = extract_admin_id_from_headers(&headers, &auth_service)
        .await
        .map_err(|e| {
            log::error!("Failed to extract admin ID from headers: {}", e);
            AuthError::Unauthorized
        })?;

    let renewal_request = auth_service
        .close_renewal_request(request_id, admin_id, request)
        .await
        .map_err(|e| {
            log::error!("Failed to close renewal request: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to close renewal request")),
            }
        })?;

    Ok(Json(ApiResponse::success(renewal_request)))
}

// 管理员发送续费请求消息
pub async fn admin_send_renewal_request_message(
    State(auth_service): State<Arc<AuthService>>,
    Path(request_id): Path<String>,
    headers: HeaderMap,
    Json(request): Json<SendMessageRequest>,
) -> Result<(StatusCode, Json<ApiResponse<MessageResponse>>), AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }
    if request.content.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Message content cannot be empty".to_string(),
        ));
    }

    // 验证管理员权限并获取管理员ID
    let admin_id = extract_admin_id_from_headers(&headers, &auth_service)
        .await
        .map_err(|e| {
            log::error!("Failed to extract admin ID from headers: {}", e);
            AuthError::Unauthorized
        })?;

    // 创建管理员消息请求
    let admin_message_request = SendMessageRequest {
        content: request.content,
        message_type: "admin".to_string(),
    };

    let message_response = auth_service
        .send_renewal_request_message(
            request_id,
            "".to_string(), // instance_id not needed for admin messages
            admin_message_request,
            Some(admin_id.clone()),
            Some(format!("Admin-{}", admin_id))
        )
        .await
        .map_err(|e| {
            log::error!("Failed to send admin renewal request message: {}", e);
            match e {
                AuthError::BadRequest(msg) => AuthError::BadRequest(msg),
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to send message")),
            }
        })?;

    Ok((StatusCode::CREATED, Json(ApiResponse::success(message_response))))
}

// 管理员获取续费请求的消息历史
pub async fn admin_get_renewal_request_messages(
    State(auth_service): State<Arc<AuthService>>,
    Path(request_id): Path<String>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<Vec<MessageResponse>>>, AuthError> {
    // Input validation
    if request_id.trim().is_empty() {
        return Err(AuthError::BadRequest(
            "Request ID cannot be empty".to_string(),
        ));
    }

    // 验证管理员权限
    let _admin_id = extract_admin_id_from_headers(&headers, &auth_service)
        .await
        .map_err(|e| {
            log::error!("Failed to extract admin ID from headers: {}", e);
            AuthError::Unauthorized
        })?;

    let messages = auth_service
        .get_renewal_request_messages(request_id, "".to_string()) // instance_id not needed for admin
        .await
        .map_err(|e| {
            log::error!("Failed to get renewal request messages: {}", e);
            match e {
                AuthError::NotFound(msg) => AuthError::NotFound(msg),
                _ => AuthError::Internal(anyhow::anyhow!("Failed to get messages")),
            }
        })?;

    Ok(Json(ApiResponse::success(messages)))
}
