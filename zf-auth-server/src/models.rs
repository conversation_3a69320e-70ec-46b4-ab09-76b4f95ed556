use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

// Re-export common message structures
pub use common::{HeartbeatRequest, HeartbeatResponse, PublicKeyResponse};

// 统一的API响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }
    
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
        }
    }
    
    pub fn error(error_message: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error_message),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateInstanceRequest {
    pub name: String,
    #[serde(rename = "initialDuration")]
    pub initial_duration: i32, // days
    pub entitlements: serde_json::Value,
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: Option<String>, // Monthly subscription rate
    #[serde(rename = "setupFee")]
    pub setup_fee: Option<String>, // One-time setup fee
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateInstanceRequest {
    pub name: Option<String>,
    pub entitlements: Option<serde_json::Value>,
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: Option<String>,
    #[serde(rename = "setupFee")]
    pub setup_fee: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRenewalCodeRequest {
    pub duration: i32, // days
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GenerateCodesRequest {
    pub duration: i32, // days
    pub quantity: i32,
    pub amount: Option<String>, // Optional price for the renewal codes
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchCreateInstancesRequest {
    #[serde(rename = "nameTemplate")]
    pub name_template: String,
    #[serde(rename = "initialDuration")]
    pub initial_duration: i32, // days
    pub entitlements: serde_json::Value,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    pub search: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalCodeQuery {
    pub page: Option<u32>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    pub status: Option<String>, // "available" or "used"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApplyRenewalCodeRequest {
    #[serde(rename = "renewalCode")]
    pub renewal_code_id: String,
}

// 价格计算相关数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct CalculateRenewalPriceRequest {
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32, // 月数
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CalculateRenewalPriceResponse {
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: String, // 月费价格
    #[serde(rename = "requestedDuration")]
    pub requested_duration: i32, // 请求的月数
    #[serde(rename = "totalPrice")]
    pub total_price: String, // 总价格
    pub currency: String, // 货币单位，默认 "CNY"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessRenewalRequestRequest {
    #[serde(rename = "renewalDuration")]
    pub renewal_duration: Option<i32>, // 可选，默认使用请求的周期
    #[serde(rename = "adminNotes")]
    pub admin_notes: Option<String>, // 管理员备注
}

// 消息相关的数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub content: String, // 消息内容
    #[serde(rename = "messageType")]
    pub message_type: String, // "customer" or "admin"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MessageResponse {
    pub id: String,
    #[serde(rename = "renewalRequestId")]
    pub renewal_request_id: String,
    #[serde(rename = "messageType")]
    pub message_type: String,
    pub content: String,
    #[serde(rename = "authorId")]
    pub author_id: Option<String>,
    #[serde(rename = "authorName")]
    pub author_name: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminCloseRequestRequest {
    #[serde(rename = "adminNotes")]
    pub admin_notes: Option<String>, // 关闭原因
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalRequestQuery {
    pub page: Option<u32>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    pub status: Option<String>, // "pending", "processed", "cancelled", "closed_by_admin"
    #[serde(rename = "instanceId")]
    pub instance_id: Option<String>,
    #[serde(rename = "hasCustomerReplies")]
    pub has_customer_replies: Option<bool>, // 过滤有客户回复的请求
}


#[derive(Debug, Serialize, Deserialize)]
pub struct InstanceResponse {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    #[serde(rename = "activatedAt")]
    pub activated_at: Option<DateTime<Utc>>,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminInstanceResponse {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    #[serde(rename = "activatedAt")]
    pub activated_at: Option<DateTime<Utc>>,
    #[serde(rename = "apiKey")]
    pub api_key: String,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
    #[serde(rename = "monthlyRate")]
    pub monthly_rate: Option<String>, // Monthly subscription rate
    #[serde(rename = "setupFee")]
    pub setup_fee: Option<String>, // One-time setup fee
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalCodeResponse {
    pub id: String,
    pub code: String,
    pub duration: i32,
    pub amount: Option<String>, // Renewal amount/price
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "usedAt")]
    pub used_at: Option<DateTime<Utc>>,
    #[serde(rename = "usedBy")]
    pub used_by: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAdminUserRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminLoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminLoginResponse {
    pub token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminUserResponse {
    pub id: String,
    pub username: String,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "lastLoginAt")]
    pub last_login_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CheckInitializationResponse {
    pub is_initialized: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardStatsResponse {
    #[serde(rename = "activeInstances")]
    pub active_instances: u64,
    #[serde(rename = "expiredInstances")]
    pub expired_instances: u64,
    #[serde(rename = "nearExpirationInstances")]
    pub near_expiration: u64,
    #[serde(rename = "pendingInstances")]
    pub pending_instances: u64,
    #[serde(rename = "availableRenewalCodes")]
    pub available_codes: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalHistoryItem {
    pub date: chrono::DateTime<chrono::Utc>,
    pub code_id: String,
    pub extended_days: i32,
    pub new_expiry_date: chrono::DateTime<chrono::Utc>,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnlineClientResponse {
    #[serde(rename = "sessionId")]
    pub session_id: String,
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    #[serde(rename = "instanceName")]
    pub instance_name: String,
    #[serde(rename = "deviceFingerprint")]
    pub device_fingerprint: String,
    #[serde(rename = "clientIp")]
    pub client_ip: String,
    #[serde(rename = "connectedAt")]
    pub connected_at: DateTime<Utc>,
    #[serde(rename = "lastPing")]
    pub last_ping: DateTime<Utc>,
    #[serde(rename = "isStale")]
    pub is_stale: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DisconnectClientRequest {
    #[serde(rename = "sessionId")]
    pub session_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkerVersionResponse {
    #[serde(rename = "recommendedVersion")]
    pub recommended_version: String,
    #[serde(rename = "controllerVersion")]
    pub controller_version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ForwarderVersionResponse {
    #[serde(rename = "recommendedVersion")]
    pub recommended_version: String,
    #[serde(rename = "controllerVersion")]
    pub controller_version: String,
}