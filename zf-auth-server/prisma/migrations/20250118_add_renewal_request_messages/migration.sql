-- CreateEnum for MessageType
CREATE TYPE "MessageType" AS ENUM ('CUSTOMER', 'ADMIN');

-- Update RenewalRequestStatus enum to include CLOSED_BY_ADMIN
ALTER TYPE "RenewalRequestStatus" ADD VALUE 'CLOSED_BY_ADMIN';

-- Add new fields to RenewalRequest table
ALTER TABLE "RenewalRequest" ADD COLUMN "hasCustomerReplies" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "RenewalRequest" ADD COLUMN "lastMessageAt" TIMESTAMP(3);
ALTER TABLE "RenewalRequest" ADD COLUMN "canReply" BOOLEAN NOT NULL DEFAULT true;

-- Create RenewalRequestMessage table
CREATE TABLE "RenewalRequestMessage" (
    "id" TEXT NOT NULL,
    "renewalRequestId" TEXT NOT NULL,
    "messageType" "MessageType" NOT NULL,
    "content" TEXT NOT NULL,
    "authorId" TEXT,
    "authorName" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RenewalRequestMessage_pkey" PRIMARY KEY ("id")
);

-- Create indexes for RenewalRequestMessage
CREATE INDEX "RenewalRequestMessage_renewalRequestId_idx" ON "RenewalRequestMessage"("renewalRequestId");
CREATE INDEX "RenewalRequestMessage_createdAt_idx" ON "RenewalRequestMessage"("createdAt");
CREATE INDEX "RenewalRequestMessage_messageType_idx" ON "RenewalRequestMessage"("messageType");

-- Create indexes for new RenewalRequest fields
CREATE INDEX "RenewalRequest_hasCustomerReplies_idx" ON "RenewalRequest"("hasCustomerReplies");
CREATE INDEX "RenewalRequest_lastMessageAt_idx" ON "RenewalRequest"("lastMessageAt");

-- Add foreign key constraint
ALTER TABLE "RenewalRequestMessage" ADD CONSTRAINT "RenewalRequestMessage_renewalRequestId_fkey" FOREIGN KEY ("renewalRequestId") REFERENCES "RenewalRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;