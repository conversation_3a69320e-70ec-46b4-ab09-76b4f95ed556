-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('ALIPAY_HONGBAO', 'CRYPTOCURRENCY');

-- CreateEnum
CREATE TYPE "RenewalRequestStatus" AS ENUM ('PENDING', 'PROCESSED', 'CANCELLED');

-- CreateTable
CREATE TABLE "RenewalRequest" (
    "id" TEXT NOT NULL,
    "paymentMethod" "PaymentMethod" NOT NULL,
    "paymentAmount" TEXT NOT NULL,
    "paymentProof" TEXT NOT NULL,
    "customerMessage" TEXT,
    "status" "RenewalRequestStatus" NOT NULL DEFAULT 'PENDING',
    "requestedDuration" INTEGER NOT NULL,
    "instanceId" TEXT NOT NULL,
    "processedAt" TIMESTAMP(3),
    "processedBy" TEXT,
    "adminNotes" TEXT,
    "generatedRenewalCodeId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RenewalRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "RenewalRequest_instanceId_idx" ON "RenewalRequest"("instanceId");

-- CreateIndex
CREATE INDEX "RenewalRequest_status_idx" ON "RenewalRequest"("status");

-- CreateIndex
CREATE INDEX "RenewalRequest_createdAt_idx" ON "RenewalRequest"("createdAt");

-- AddForeignKey
ALTER TABLE "RenewalRequest" ADD CONSTRAINT "RenewalRequest_instanceId_fkey" FOREIGN KEY ("instanceId") REFERENCES "ControllerInstance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RenewalRequest" ADD CONSTRAINT "RenewalRequest_generatedRenewalCodeId_fkey" FOREIGN KEY ("generatedRenewalCodeId") REFERENCES "RenewalCode"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update RenewalCode table to add relationship
ALTER TABLE "RenewalCode" ADD COLUMN IF NOT EXISTS "amount" TEXT;
