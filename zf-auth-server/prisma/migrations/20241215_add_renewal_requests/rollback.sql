-- Rollback script for renewal requests migration

-- Drop foreign key constraints first
ALTER TABLE "RenewalRequest" DROP CONSTRAINT IF EXISTS "RenewalRequest_instanceId_fkey";
ALTER TABLE "RenewalRequest" DROP CONSTRAINT IF EXISTS "RenewalRequest_generatedRenewalCodeId_fkey";

-- Drop indexes
DROP INDEX IF EXISTS "RenewalRequest_instanceId_idx";
DROP INDEX IF EXISTS "RenewalRequest_status_idx";
DROP INDEX IF EXISTS "RenewalRequest_createdAt_idx";

-- Drop table
DROP TABLE IF EXISTS "RenewalRequest";

-- Drop enums
DROP TYPE IF EXISTS "RenewalRequestStatus";
DROP TYPE IF EXISTS "PaymentMethod";

-- Note: We don't remove the amount column from RenewalCode as it might be used elsewhere
-- If needed, run: ALTER TABLE "RenewalCode" DROP COLUMN IF EXISTS "amount";
