use std::path::Path;

fn main() {
    println!("cargo:rerun-if-changed=prisma/schema.prisma");
    
    // Watch for changes in the frontend dist directory
    let frontend_dist = "../zfc-license-admin/dist";
    if Path::new(frontend_dist).exists() {
        println!("cargo:rerun-if-changed={}", frontend_dist);
        // Also watch for changes in frontend source files to trigger rebuild
        println!("cargo:rerun-if-changed=../zfc-license-admin/src");
        println!("cargo:rerun-if-changed=../zfc-license-admin/package.json");
        println!("cargo:rerun-if-changed=../zfc-license-admin/vite.config.ts");
    }
}