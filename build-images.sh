#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default image configurations
DEFAULT_REGISTRY="zfc"
DEFAULT_TAG="latest"

# Parse command line arguments
PUSH_IMAGES=false
REGISTRY=""
TAG=""
BUILD_TARGETS=()
NO_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --target)
            BUILD_TARGETS+=("$2")
            shift 2
            ;;
        --targets)
            # Allow comma-separated targets like --targets zfc-util,zfc-admin
            IFS=',' read -ra TARGETS_ARRAY <<< "$2"
            BUILD_TARGETS+=("${TARGETS_ARRAY[@]}")
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --push              推送镜像到仓库"
            echo "  --registry REGISTRY 指定镜像仓库前缀 (默认: $DEFAULT_REGISTRY)"
            echo "  --tag TAG           指定镜像标签 (默认: $DEFAULT_TAG)"
            echo "  --target TARGET     添加构建目标 (可多次使用)"
            echo "  --targets LIST      逗号分隔的构建目标列表"
            echo "  --no-cache          Docker 构建不使用缓存"
            echo "  -h, --help          显示帮助信息"
            echo ""
            echo "可用的构建目标:"
            echo "  zf-web zf-controler rrd-service zfc-util zfc-admin"
            echo ""
            echo "示例:"
            echo "  $0 --target zfc-util --target zfc-admin --push"
            echo "  $0 --targets zfc-util,zfc-admin --registry hub.covm.net --push"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# Set defaults
REGISTRY=${REGISTRY:-$DEFAULT_REGISTRY}
TAG=${TAG:-$DEFAULT_TAG}

log_info "构建配置:"
log_info "  镜像仓库: $REGISTRY"
log_info "  镜像标签: $TAG"
log_info "  推送镜像: $PUSH_IMAGES"
if [[ ${#BUILD_TARGETS[@]} -gt 0 ]]; then
    log_info "  构建目标: ${BUILD_TARGETS[*]}"
fi
log_info "  不使用缓存: $NO_CACHE"

# Check if docker is available
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装或不在 PATH 中"
    exit 1
fi

# Create build context
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

log_info "创建临时构建目录: $TEMP_DIR"

# Function to determine which binaries to build
get_build_targets() {
    local targets=()
    
    if [[ ${#BUILD_TARGETS[@]} -eq 0 ]]; then
        targets=("zf-web" "zf-controler" "rrd-service" "zfc-util" "zfc-admin")
    else
        # Build specified targets
        targets=("${BUILD_TARGETS[@]}")
    fi
    
    echo "${targets[@]}"
}

# Function to build specified targets
build_specified_targets() {
    local targets=($(get_build_targets))
    local needs_librrd=false
    
    log_info "准备编译目标: ${targets[*]}"
    
    # Check if we need librrd-dev for any target
    for target in "${targets[@]}"; do
        if [[ "$target" == "rrd-service" ]]; then
            needs_librrd=true
            break
        fi
    done
    
    # Create build context with both repositories
    local build_context="$TEMP_DIR/targeted_build_context"
    mkdir -p "$build_context"
    
    log_info "复制源代码到构建上下文..."
    
    # Copy zfc repository
    cp -r . "$build_context/zfc/"
    
    # Copy private_tun repository
    cp -r ../private_tun "$build_context/private_tun/"
    
    # Determine librrd package installation
    local librrd_install=""
    if [[ "$needs_librrd" == "true" ]]; then
        log_info "检测到 rrd-service 目标，安装 RRD 相关依赖..."
        librrd_install="librrd-dev clang libclang-dev"
    else
        log_info "未检测到 rrd-service 目标，跳过 RRD 相关依赖安装"
    fi
    
    # Build individual compile commands for each target
    local build_commands=""
    local copy_commands=""
    
    for target in "${targets[@]}"; do
        log_info "添加编译命令: $target"
        build_commands+="RUN echo \"编译 $target...\" && cargo build --release --bin $target"$'\n'
        copy_commands+=" && cp target/release/$target /output/"
    done
    
    # Create targeted Dockerfile
    cat > "$build_context/Dockerfile" << EOF
FROM rust:1.75-slim as builder

# Install base build dependencies
RUN apt-get update && apt-get install -y \\
    pkg-config \\
    libssl-dev \\
    perl \\
    build-essential \\
    curl \\
    $librrd_install \\
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18 LTS
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \\
    && apt-get install -y nodejs

# Instruct Prisma to ignore any .env files it might discover
ENV PRISMA_IGNORE_ENV_FILE="1"
# Prevent @prisma/client postinstall from invoking "prisma generate" inside npm install
ENV PRISMA_SKIP_POSTINSTALL_GENERATE="true"
# Tell npm to ignore lifecycle scripts during auto-install (belt-and-suspenders)
ENV NPM_CONFIG_IGNORE_SCRIPTS="true"

WORKDIR /workspace

# Copy both repositories
COPY zfc/ zfc/
COPY private_tun/ private_tun/

WORKDIR /workspace/zfc

RUN cargo prisma generate --schema=prisma/schema.prisma

# Build each binary individually to avoid conflicts
$build_commands

# Create output directory and copy all compiled binaries
RUN mkdir -p /output$copy_commands
EOF
    
    # Build the targeted image
    log_info "构建环境并逐个编译二进制文件..."
    if [[ "$NO_CACHE" == "true" ]]; then
        docker build --no-cache -f "$build_context/Dockerfile" -t "zfc-targeted-builder:temp" "$build_context"
    else
        docker build -f "$build_context/Dockerfile" -t "zfc-targeted-builder:temp" "$build_context"
    fi
    
    # Extract specified binaries
    log_info "提取编译好的二进制文件..."
    mkdir -p "$TEMP_DIR/binaries"
    docker run --rm -v "$TEMP_DIR/binaries:/output_host" "zfc-targeted-builder:temp" sh -c "cp /output/* /output_host/"
    
    # Cleanup build context
    rm -rf "$build_context"
    
    log_success "编译完成，已提取二进制文件: ${targets[*]}"
}

# Function to extract specific binary (no longer builds, just copies)
extract_binary() {
    local binary_name=$1
    local target_name=${2:-$binary_name}
    
    if [[ ! -f "$TEMP_DIR/binaries/$binary_name" ]]; then
        log_error "二进制文件不存在: $binary_name"
        return 1
    fi
    
    log_info "提取二进制文件: $binary_name"
    
    # Binary is already available, no need to build
    log_success "二进制文件已就绪: $binary_name"
}

# Function to create final image
create_final_image() {
    local image_name=$1
    local binary_name=$2
    local base_image=${3:-"debian:12-slim"}
    local extra_packages=${4:-""}
    
    log_info "创建最终镜像: $image_name"
    
    # Create runtime Dockerfile
    cat > "$TEMP_DIR/Dockerfile.$image_name" << EOF
FROM $base_image

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    $extra_packages \\
    && rm -rf /var/lib/apt/lists/*

# Copy binary
COPY binaries/$binary_name /usr/local/bin/$binary_name

# Set executable permissions
RUN chmod +x /usr/local/bin/$binary_name

# Create non-root user
RUN useradd -r -s /bin/false appuser

# Switch to non-root user
USER appuser

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/$binary_name"]
EOF
    
    # Build final image
    if [[ "$NO_CACHE" == "true" ]]; then
        docker build --no-cache -f "$TEMP_DIR/Dockerfile.$image_name" -t "$REGISTRY/$image_name:$TAG" "$TEMP_DIR"
    else
        docker build -f "$TEMP_DIR/Dockerfile.$image_name" -t "$REGISTRY/$image_name:$TAG" "$TEMP_DIR"
    fi
    
    log_success "镜像构建完成: $REGISTRY/$image_name:$TAG"
    
    # Push if requested
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        log_info "推送镜像: $REGISTRY/$image_name:$TAG"
        docker push "$REGISTRY/$image_name:$TAG"
        log_success "推送完成: $REGISTRY/$image_name:$TAG"
    fi
}

# Build functions for each service (now just package pre-built binaries)
build_zf_web() {
    # Check if binary exists before trying to package
    if [[ ! -f "$TEMP_DIR/binaries/zf-web" ]]; then
        log_info "跳过 ZF-Web (二进制文件未构建)"
        return
    fi
    
    log_info "=== 打包 ZF-Web ==="
    extract_binary "zf-web"
    
    # Create initialization script for directory permissions
    cat > "$TEMP_DIR/init-zf-web.sh" << 'EOF'
#!/bin/bash
set -e

# Create download directory with proper permissions if it doesn't exist
DOWNLOAD_DIR="${APP_DOWNLOAD_DIR:-./downloads}"
if [ ! -d "$DOWNLOAD_DIR" ]; then
    echo "创建下载目录: $DOWNLOAD_DIR"
    mkdir -p "$DOWNLOAD_DIR"
    echo "下载目录创建完成: $DOWNLOAD_DIR"
fi

# Execute the main application
exec /usr/local/bin/zf-web "$@"
EOF

    # Create a custom Dockerfile for zf-web with dynamic directory creation
    cat > "$TEMP_DIR/Dockerfile.zf-web" << EOF
FROM debian:12-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy binary and init script
COPY binaries/zf-web /usr/local/bin/zf-web
COPY init-zf-web.sh /usr/local/bin/init-zf-web.sh

# Set executable permissions
RUN chmod +x /usr/local/bin/zf-web /usr/local/bin/init-zf-web.sh

# Create non-root user
RUN useradd -r -s /bin/false appuser

# Create default download directory
RUN mkdir -p /app/downloads

# Set default environment variable for download directory
ENV APP_DOWNLOAD_DIR=./downloads

# Change ownership of /app directory to appuser
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Set working directory
WORKDIR /app

# Note: We switch to non-root user here because downloads directory is in user space
# The init script will create directories as needed

# Set entrypoint to init script
ENTRYPOINT ["/usr/local/bin/init-zf-web.sh"]
EOF
    
    # Build the custom zf-web image
    if [[ "$NO_CACHE" == "true" ]]; then
        docker build --no-cache -f "$TEMP_DIR/Dockerfile.zf-web" -t "$REGISTRY/zf-web:$TAG" "$TEMP_DIR"
    else
        docker build -f "$TEMP_DIR/Dockerfile.zf-web" -t "$REGISTRY/zf-web:$TAG" "$TEMP_DIR"
    fi
    
    log_success "镜像构建完成: $REGISTRY/zf-web:$TAG"
    
    # Push if requested
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        log_info "推送镜像: $REGISTRY/zf-web:$TAG"
        docker push "$REGISTRY/zf-web:$TAG"
        log_success "推送完成: $REGISTRY/zf-web:$TAG"
    fi
}

build_zf_controler() {
    # Check if binary exists before trying to package
    if [[ ! -f "$TEMP_DIR/binaries/zf-controler" ]]; then
        log_info "跳过 ZF-Controller (二进制文件未构建)"
        return
    fi
    
    log_info "=== 打包 ZF-Controller ==="
    extract_binary "zf-controler"
    
    # Create initialization script for machine-id generation
    cat > "$TEMP_DIR/init-zf-controler.sh" << 'EOF'
#!/bin/bash
set -e

# Generate unique machine-id at container startup if not exists
if [ ! -f /etc/machine-id ] || [ ! -s /etc/machine-id ]; then
    echo "生成唯一的 machine-id..."
    mkdir -p /var/lib/dbus
    # Generate a unique UUID and format it as machine-id
    cat /proc/sys/kernel/random/uuid | tr -d '-' > /etc/machine-id
    cp /etc/machine-id /var/lib/dbus/machine-id
    chmod 644 /etc/machine-id /var/lib/dbus/machine-id
    echo "machine-id 生成完成: $(cat /etc/machine-id)"
fi

# Execute the main application
exec /usr/local/bin/zf-controler "$@"
EOF

    # Create a custom Dockerfile for zf-controler with dynamic machine-id generation
    cat > "$TEMP_DIR/Dockerfile.zf-controler" << EOF
FROM debian:12-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy binary and init script
COPY binaries/zf-controler /usr/local/bin/zf-controler
COPY init-zf-controler.sh /usr/local/bin/init-zf-controler.sh

# Set executable permissions
RUN chmod +x /usr/local/bin/zf-controler /usr/local/bin/init-zf-controler.sh

# Create directories for machine-id
RUN mkdir -p /var/lib/dbus

# Create non-root user
RUN useradd -r -s /bin/false appuser

# Note: We don't switch to non-root user here because we need to write machine-id files
# The init script will handle permissions properly

# Set entrypoint to init script
ENTRYPOINT ["/usr/local/bin/init-zf-controler.sh"]
EOF
    
    # Build the custom zf-controler image
    if [[ "$NO_CACHE" == "true" ]]; then
        docker build --no-cache -f "$TEMP_DIR/Dockerfile.zf-controler" -t "$REGISTRY/zf-controler:$TAG" "$TEMP_DIR"
    else
        docker build -f "$TEMP_DIR/Dockerfile.zf-controler" -t "$REGISTRY/zf-controler:$TAG" "$TEMP_DIR"
    fi
    
    log_success "镜像构建完成: $REGISTRY/zf-controler:$TAG"
    
    # Push if requested
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        log_info "推送镜像: $REGISTRY/zf-controler:$TAG"
        docker push "$REGISTRY/zf-controler:$TAG"
        log_success "推送完成: $REGISTRY/zf-controler:$TAG"
    fi
}

build_rrd_service_image() {
    # Check if binary exists before trying to package
    if [[ ! -f "$TEMP_DIR/binaries/rrd-service" ]]; then
        log_info "跳过 RRD Service (二进制文件未构建)"
        return
    fi
    
    log_info "=== 打包 RRD Service ==="
    extract_binary "rrd-service"
    
    # Create custom Dockerfile for rrd-service with proper permissions
    cat > "$TEMP_DIR/Dockerfile.rrd-service" << EOF
FROM debian:12-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    librrd-dev \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy binary
COPY binaries/rrd-service /usr/local/bin/rrd-service

# Set executable permissions
RUN chmod +x /usr/local/bin/rrd-service

# Create non-root user
RUN useradd -r -s /bin/false appuser

# Create data directory with proper permissions
RUN mkdir -p /data/rrd && chown -R appuser:appuser /data/rrd

# Switch to non-root user
USER appuser

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/rrd-service"]
EOF
    
    # Build the custom rrd-service image
    if [[ "$NO_CACHE" == "true" ]]; then
        docker build --no-cache -f "$TEMP_DIR/Dockerfile.rrd-service" -t "$REGISTRY/rrd-service:$TAG" "$TEMP_DIR"
    else
        docker build -f "$TEMP_DIR/Dockerfile.rrd-service" -t "$REGISTRY/rrd-service:$TAG" "$TEMP_DIR"
    fi
    
    log_success "镜像构建完成: $REGISTRY/rrd-service:$TAG"
    
    # Push if requested
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        log_info "推送镜像: $REGISTRY/rrd-service:$TAG"
        docker push "$REGISTRY/rrd-service:$TAG"
        log_success "推送完成: $REGISTRY/rrd-service:$TAG"
    fi
}

build_zfc_util() {
    # Check if binary exists before trying to package
    if [[ ! -f "$TEMP_DIR/binaries/zfc-util" ]]; then
        log_info "跳过 ZFC-Util (二进制文件未构建)"
        return
    fi
    
    log_info "=== 打包 ZFC-Util ==="
    extract_binary "zfc-util"
    create_final_image "zfc-util" "zfc-util" "debian:12-slim"
}

build_zfc_admin() {
    # Check if binary exists before trying to package
    if [[ ! -f "$TEMP_DIR/binaries/zfc-admin" ]]; then
        log_info "跳过 ZFC-Admin (二进制文件未构建)"
        return
    fi
    
    log_info "=== 打包 ZFC-Admin ==="
    extract_binary "zfc-admin"
    create_final_image "zfc-admin" "zfc-admin" "debian:12-slim"
}

# Main build process
main() {
    log_info "开始构建 ZFC Docker 镜像..."
    
    # Check if we're in the right directory
    if [[ ! -f "Cargo.toml" ]]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # Check if private_tun directory exists
    if [[ ! -d "../private_tun" ]]; then
        log_error "未找到 ../private_tun 目录，请确保 private_tun 和 zfc 在同一个父目录下"
        log_error "目录结构应该是:"
        log_error "  parent_dir/"
        log_error "    ├── zfc/"
        log_error "    └── private_tun/"
        exit 1
    fi
    
    log_info "检测到 private_tun 依赖: ../private_tun"
    
    # Build only specified targets for better efficiency
    build_specified_targets
    
    # Package individual services into final images (only build what's needed)
    build_zf_web
    build_zf_controler
    build_rrd_service_image
    build_zfc_util
    build_zfc_admin
    
    # Cleanup builder image
    log_info "清理构建环境..."
    docker rmi "zfc-targeted-builder:temp" 2>/dev/null || true
    
    log_success "所有镜像构建完成！"
    
    # Show built images
    echo
    log_info "构建的镜像列表:"
    
    # Check which images were actually built by looking for existing Docker images
    for target in "zf-web" "zf-controler" "rrd-service" "zfc-util" "zfc-admin"; do
        if docker image inspect "$REGISTRY/$target:$TAG" >/dev/null 2>&1; then
            echo "  $REGISTRY/$target:$TAG"
        fi
    done
    echo
    
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        log_success "所有镜像已推送到仓库"
    else
        log_info "使用 --push 参数可推送镜像到仓库"
    fi
}

# Run main function
main "$@"