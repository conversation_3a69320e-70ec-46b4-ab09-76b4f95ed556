{"permissions": {"allow": ["mcp__graphiti-memory__search_memory_nodes", "mcp__graphiti-memory__add_memory", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run dev:*)", "Bash(cp:*)", "Bash(cargo build:*)", "mcp__graphiti-memory__search_memory_facts", "Bash(cargo check:*)", "Bash(cargo prisma:*)", "Bash(cargo run:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(ls:*)", "Bash(npm run build:*)", "Bash(cargo test:*)", "WebFetch(domain:crates.io)", "<PERSON><PERSON>(chmod:*)", "Bash(timeout 30 cargo build)", "Bash(bash:*)", "Bash(rg:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npx vue-tsc:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": []}