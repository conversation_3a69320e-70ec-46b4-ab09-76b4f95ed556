// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  // Corresponds to the cargo alias created earlier
  provider = "cargo prisma"
  // The location to generate the client. Is relative to the position of the schema
  output   = "../common/src/prisma.generated.rs"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // directUrl = env("DIRECT_URL")
}

model Subscription {
  id                  Int               @id @default(autoincrement())
  tokenId             String            @unique
  ownerAddress        String
  tgUser              String?
  tgChatId            String?
  billingType         Int
  basePrice           Int
  recurringPrice      Int
  bandwidth           Int?
  traffic             BigInt
  usedTrafficLow      BigInt?
  usedTrafficHigh     BigInt?
  validUntil          DateTime
  resetDays           Int?
  lastReset           DateTime?
  activated           Boolean
  maxPortNumPerServer Int
  maxForwardEndpointNum Int             @default(5)
  lines               Int[]
  allowForwardEndpoint        Boolean           @default(false)
  note                String            @default("")
  ports               Port[]
  allowIpNum          Int?
  allowConnNum        Int?
  packageId           Int?              // 关联的套餐ID
  package             Package?          @relation(fields: [packageId], references: [id])
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  ForwardEndpoint     ForwardEndpoint[]
  isAdmin             Boolean           @default(false)
  lineUsages          SubscriptionLineUsage[]
  createdLatencyConfigs LatencyTestConfig[]
  @@index([tokenId])
  @@index([ownerAddress])
  @@index([billingType])
  @@index([tokenId, ownerAddress])
  @@index([bandwidth])
  @@index([validUntil(sort: Desc)])
  @@index([packageId])
}

// model OutboundEndpointGroup {
//   id               Int                @id @default(autoincrement())
//   name             String             @unique
//   displayName      String
//   hidden           String
//   OutboundEndpoint OutboundEndpoint[]

//   @@index([name])
//   @@index([hidden])
// }

model OutboundEndpoint {
  id           Int      @id @default(autoincrement())
  name         String   @unique
  pubkey       String   @unique
  displayName  String
  hidden       Boolean  @default(true)
  ingressIpv4  String
  portStart    Int?
  portEnd      Int?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  ports        Port[]
  trafficScale Float?
  allowForward Boolean? @default(true)
  allowLatencyTest Boolean? @default(false)
  allowIpNum          Int?
  allowConnNum        Int?
  privateKey String?
  interfaceName String?
  version String?
  proxyConfig String?
  relatedForwardEndpointIds Int[]
  allowIpV6      Boolean       @default(false)
  useForwardAsTun Boolean? @default(false)
  protocolFilters String[]     @default([])
  relatedTotServerIds Int[]
  latencyTestConfigs LatencyTestConfig[]
  customConfig String?
  tags         String[]     @default([])

  @@index([hidden])
  @@index([pubkey])
  @@index([tags])
}

model ForwardEndpoint {
  id             Int           @id @default(autoincrement())
  name           String        @unique
  displayName    String
  ingressAddress String
  servePort      Int
  innerPort      Int?
  protocol       String // server protocol socks5 or hammer, current only support hammer
  protocolConfig String // protocol config json
  privKey        String // ws private key
  wsPort         Int? // ws port
  isPublic       Boolean       @default(false) // is public for all users
  subscriptionId Int? // belong to which subscription user
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
  setupScript    String?
  tokenId        String        @unique
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  trafficScale   Float?
  allowIpV6      Boolean       @default(false)
  @@index([tokenId])
}

model Port {
  id                        Int               @id @default(autoincrement())
  displayName               String
  portV4                    Int
  targetAddressV4           String
  targetPortV4              Int
  traffic_in                BigInt
  traffic_out               BigInt
  createdAt                 DateTime          @default(now())
  updatedAt                 DateTime          @updatedAt
  subscription              Subscription?     @relation(fields: [subscriptionId], references: [id])
  subscriptionId            Int?
  outboundEndpoint          OutboundEndpoint? @relation(fields: [outboundEndpointId], references: [id])
  outboundEndpointId        Int?
  forwardProtocol           String?
  forwardConfig             String?
  relatedForwardEndpointIds Int[]
  targetAddrList          String[]
  selectMode               Int?
  testMethod               Int?
  isSuspended              Boolean           @default(false)
  bandwidth                 Int?              // Port-specific bandwidth limit in Mbps
  acceptProxyProtocol       Boolean           @default(false) // Whether to accept PROXY protocol from client
  sendProxyProtocolVersion  Int?              // PROXY protocol version: null/0=disabled, 1=v1, 2=v2

  relatedTotServerIds Int[]

  @@index([portV4])
  @@index([subscriptionId])
  @@index([outboundEndpointId])
  @@index([outboundEndpointId, subscriptionId])
  @@index([outboundEndpointId, portV4])
  @@index([outboundEndpointId, subscriptionId, portV4])
  @@index([isSuspended])
}

model MgmtAuthorization {
  id        Int      @id @default(autoincrement())
  name      String
  pubkey    String   @unique
  blocked   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([pubkey])
  @@index([blocked])
}

model TgData {
  id      Int    @id @default(autoincrement())
  chat_id BigInt @unique
  data    String
}

model Package {
  id                    Int           @id @default(autoincrement())
  name                  String        @unique // 套餐名称
  displayName           String        // 套餐显示名称
  description           String?       // 套餐描述
  
  // 基本配置
  bandwidth             Int?          // 带宽限制 (Mbps)
  totalTraffic          BigInt        // 总流量 (字节)
  maxPortsPerServer     Int           // 每服务器最大端口数
  allowForwardEndpoint  Boolean       @default(false)
  allowIpNum            Int?          // 允许IP数量
  allowConnNum          Int?          // 每IP连接数限制
  
  // 计费配置 (复用Subscription模型的字段设计)
  billingType           Int?          // 计费类型: 0=周期计费, 1=一次性计费
  basePrice             Int?          // 基础价格
  recurringPrice        Int?          // 周期价格
  resetDays             Int?          // 重置天数（周期计费使用）
  trafficResetDays      Int?          // 流量重置周期天数
  
  // 套餐状态
  isActive              Boolean       @default(true)
  isDefault             Boolean       @default(false) // 是否为默认套餐
  
  // 线路配置
  packageLines          PackageLine[]
  
  // 关联的订阅
  subscriptions         Subscription[]
  
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  
  @@index([name])
  @@index([isActive])
  @@index([isDefault])
}

model PackageLine {
  id              Int       @id @default(autoincrement())
  packageId       Int
  package         Package   @relation(fields: [packageId], references: [id], onDelete: Cascade)
  lineId          Int       // 关联 OutboundEndpoint.id
  
  // 线路特定配置
  bandwidthLimit  Int?      // 该线路的带宽限制 (Mbps)
  trafficScale    Float?    // 流量倍率 (默认 1.0)
  lineTraffic     BigInt?   // 该线路的流量限制 (字节)
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@unique([packageId, lineId])
  @@index([packageId])
  @@index([lineId])
}

model SubscriptionLineUsage {
  id                 Int           @id @default(autoincrement())
  subscriptionId     Int
  lineId             Int           // OutboundEndpoint.id
  usedTrafficLow     BigInt?       // 该订阅在该线路上的总使用流量低64位 (字节)
  usedTrafficHigh    BigInt?       // 该订阅在该线路上的总使用流量高64位 (字节)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  
  subscription       Subscription  @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  
  @@unique([subscriptionId, lineId])
  @@index([subscriptionId])
  @@index([lineId])
}

model LatencyTestConfig {
  id                Int            @id @default(autoincrement())
  serverId          Int            // 关联OutboundEndpoint.id
  displayName       String         // 测试目标显示名称
  targetAddress     String         // 目标地址(支持IP或域名)
  targetPort        Int?           // TCP端口(TCP ping需要)
  testType          String         // "tcp" 或 "icmp"
  packetSize        Int?           // ICMP包大小(默认64字节)
  timeout           Int            @default(5000) // 超时时间(毫秒)
  interval          Int            @default(60) // 测试间隔(秒)
  alertThreshold    Int            @default(3) // 连续失败告警阈值
  isPublic          Boolean        @default(false) // 是否所有用户可见
  isEnabled         Boolean        @default(true) // 是否启用
  createdBy         Int            // 创建者subscription ID
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  
  server            OutboundEndpoint @relation(fields: [serverId], references: [id], onDelete: Cascade)
  creator           Subscription?    @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  
  @@index([serverId])
  @@index([isEnabled])
  @@index([isPublic])
  @@index([serverId, isEnabled])
}

model SystemSettings {
  id            Int      @id @default(autoincrement())
  key           String   @unique // 设置键名
  value         String   // JSON字符串格式的设置值
  description   String?  // 设置描述
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@index([key])
}
