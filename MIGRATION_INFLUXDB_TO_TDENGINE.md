# InfluxDB to TDengine Migration Guide

This document outlines the steps to migrate from InfluxDB to TDengine in the ZFC project.

## Overview

The migration involves:
1. Updating code dependencies and configurations
2. Setting up TDengine database
3. Migrating existing data (optional)
4. Testing functionality

## Prerequisites

- TDengine server running (via Docker or standalone)
- Access to existing InfluxDB data (if migration needed)
- Backup of current system

## Step 1: Environment Configuration

Update your environment variables:

```bash
# Replace InfluxDB variables with TDengine equivalents
TDENGINE_URL=http://127.0.0.1:6041
TDENGINE_USER=zfcuser
TDENGINE_PASSWORD=
TDENGINE_DB=zfc
```

## Step 2: Database Initialization

⭐ **自动初始化 (推荐)**: 数据库和表现在会在 `zf-controler` 和 `zf-web` 启动时自动创建，无需手动操作！

如果需要手动初始化，可以运行 TDengine 初始化脚本：

```sql
-- Connect to TDengine
taos

-- Run initialization
source init-tdengine.sql
```

Or using REST API:
```bash
curl -u zfcuser: -d "source init-tdengine.sql" http://127.0.0.1:6041/rest/sql
```

## Step 3: Data Migration (Optional)

If you need to migrate existing InfluxDB data to TDengine:

### Export from InfluxDB

```bash
# Export netcard_speed data
influx -database 'your_db' -execute 'SELECT * FROM netcard_speed' -format csv > netcard_speed.csv

# Export system_stats data
influx -database 'your_db' -execute 'SELECT * FROM system_stats' -format csv > system_stats.csv

# Export user_line_info data
influx -database 'your_db' -execute 'SELECT * FROM user_line_info' -format csv > user_line_info.csv
```

### Import to TDengine

Create a migration script to convert CSV data to TDengine INSERT statements:

```python
#!/usr/bin/env python3
import csv
import requests
from datetime import datetime

def migrate_netcard_speed(csv_file):
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Convert InfluxDB timestamp to TDengine format
            ts = datetime.fromisoformat(row['time'].replace('Z', '+00:00'))
            
            sql = f"""INSERT INTO netcard_speed_{row['agent_id']}_{row['interface']}_{int(ts.timestamp()*1000)} 
                     USING netcard_speed TAGS ({row['agent_id']}, '{row['interface']}') 
                     VALUES ('{ts.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}', '{row['interface']}', {row['agent_id']}, 
                            {row['tx']}, {row['rx']}, {row['total_tx']}, {row['total_rx']})"""
            
            # Execute SQL via REST API
            response = requests.post('http://127.0.0.1:6041/rest/sql/zfc', 
                                   auth=('zfcuser', ''), 
                                   data=sql)
            print(f"Inserted netcard_speed record: {response.status_code}")

# Similar functions for system_stats and user_line_info
```

## Step 4: Testing

After migration, test the following endpoints:

1. **GET /api/line_stats** - Network card and system statistics
2. **GET /api/user_speeds** - User speed aggregation  
3. **POST /api/user_historical_speeds** - Historical speed data
4. **GET /api/user_line_speeds** - Line speed information

## Step 5: Production Deployment

1. **Update Docker Compose:**
   ```bash
   docker-compose down
   docker-compose up -d tdengine
   # Wait for TDengine to be ready
   docker-compose up -d
   ```

2. **Verify Services:**
   ```bash
   # Check TDengine health
   curl -f http://localhost:6041/rest/sql/zfc -u zfcuser: -d "show databases"
   
   # Check application logs
   docker-compose logs zf-controller
   docker-compose logs zf-web
   ```

## Data Model Differences

### InfluxDB vs TDengine Schema

**InfluxDB (old):**
- Measurements: netcard_speed, system_stats, user_line_info
- Tags: agent_id, interface, subscription_id, line_id
- Time series data with flexible schema

**TDengine (new):**
- Super Tables: netcard_speed, system_stats, user_line_info  
- Tags: Same as InfluxDB but defined in stable schema
- Child tables automatically created for each tag combination

### Query Differences

**InfluxDB:**
```sql
SELECT LAST(rx) FROM netcard_speed WHERE agent_id = '1' AND time > now() - 1m GROUP BY agent_id
```

**TDengine:**
```sql
SELECT agent_id, LAST(rx) FROM netcard_speed WHERE agent_id = 1 AND ts > NOW() - 1m GROUP BY agent_id
```

## Rollback Plan

If issues occur:

1. **Stop new services:**
   ```bash
   docker-compose stop zf-controller zf-web
   ```

2. **Restore InfluxDB configuration:**
   ```bash
   git checkout HEAD~1 docker-compose.yml
   # Restore code changes if needed
   ```

3. **Restart with InfluxDB:**
   ```bash
   docker-compose up -d influxdb
   docker-compose up -d zf-controller zf-web
   ```

## Performance Considerations

- TDengine is generally faster for time-series workloads
- Better compression compared to InfluxDB 1.x
- Native SQL interface vs InfluxQL
- Automatic table partitioning by time

## Monitoring

Monitor the following after migration:

- **Application metrics:** Response times, error rates
- **Database metrics:** Query performance, storage usage
- **Resource usage:** CPU, memory, disk I/O

## Troubleshooting

### Common Issues

1. **Connection failures:**
   - Check TDengine service status
   - Verify network connectivity and ports
   - Check authentication credentials

2. **Data insertion errors:**
   - Verify table schemas match data structure  
   - Check timestamp format compatibility
   - Validate tag values and data types

3. **Query performance:**
   - Review query patterns and indexes
   - Check table partitioning strategy
   - Monitor resource utilization

### Useful Commands

```bash
# TDengine status
systemctl status taosd

# Show databases
taos -s "show databases"

# Show tables in database
taos -s "use zfc; show stables"

# Check table statistics  
taos -s "use zfc; show table status"
```