# InfluxDB 1.8 配置文件

# 全局配置
reporting-disabled = false
bind-address = "127.0.0.1:8088"

# Meta 节点配置
[meta]
  dir = "/var/lib/influxdb/meta"
  retention-autocreate = true
  logging-enabled = true

# Data 节点配置
[data]
  dir = "/var/lib/influxdb/data"
  wal-dir = "/var/lib/influxdb/wal"
  series-id-set-cache-size = 100
  
  # 查询日志
  query-log-enabled = true
  
  # 缓存配置
  cache-max-memory-size = 1073741824
  cache-snapshot-memory-size = 26214400
  cache-snapshot-write-cold-duration = "10m0s"
  
  # 压缩配置
  compact-full-write-cold-duration = "4h0m0s"
  max-series-per-database = 1000000
  max-values-per-tag = 100000

# Coordinator 查询管理
[coordinator]
  write-timeout = "10s"
  max-concurrent-queries = 0
  query-timeout = "0s"
  log-queries-after = "0s"
  max-select-point = 0
  max-select-series = 0
  max-select-buckets = 0

# 数据保留策略配置
[retention]
  enabled = true
  check-interval = "30m0s"

# 分片预创建
[shard-precreation]
  enabled = true
  check-interval = "10m0s"
  advance-period = "30m0s"

# 监控配置
[monitor]
  store-enabled = true
  store-database = "_internal"
  store-interval = "10s"

# 订阅者配置
[subscriber]
  enabled = true
  http-timeout = "30s"
  insecure-skip-verify = false
  ca-certs = ""
  write-concurrency = 40
  write-buffer-size = 1000

# HTTP 服务配置
[http]
  enabled = true
  bind-address = ":8086"
  auth-enabled = false
  log-enabled = true
  write-tracing = false
  pprof-enabled = true
  https-enabled = false
  max-row-limit = 0
  max-connection-limit = 0
  shared-secret = ""
  realm = "InfluxDB"
  unix-socket-enabled = false
  flux-enabled = false

# 输入插件配置
[[graphite]]
  enabled = false

[[collectd]]
  enabled = false

[[opentsdb]]
  enabled = false

[[udp]]
  enabled = false

# 连续查询配置
[continuous_queries]
  log-enabled = true
  enabled = true
  run-interval = "1s"