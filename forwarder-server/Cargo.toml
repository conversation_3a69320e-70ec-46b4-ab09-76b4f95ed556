[package]
name = "forwarder-server"
version = "0.1.92"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[profile.release]
strip = true  # Automatically strip symbols from the binary.
lto = "fat"
codegen-units = 1
overflow-checks = false
panic = "abort"

[features]
jemalloc = ["private_tun/jemalloc"]
metric = ["private_tun/metric"]
mmsg = ["private_tun/mmsg"]

[dependencies]
private_tun = { workspace = true, default-features=false, features=["cache_udp"] }
warp = "0.3"
clap = { version = "4.1", features = ["derive", "env"] }
anyhow = "1"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { workspace = true}
futures-util = "0"
thiserror = "2"
serde = { version = "1.0", features = ["derive"] }
surge-ping = "0.8.0"
serde_json = "1.0"
flexi_logger = {version = "0.29.0", default-features = false, features=["async"]}
chrono = {version = "0.4.26", features = ["serde"]}
common = { path = "../common" }
async-trait = "0.1.73"
socket2 = "0.6"
tcp_over_multi_tcp_server = { workspace = true }
lru_time_cache = "0"