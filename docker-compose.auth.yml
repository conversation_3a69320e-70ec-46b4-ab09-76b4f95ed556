version: '3.8'

services:
  zf-auth-server:
    build:
      context: .
      dockerfile: Dockerfile.auth-server
    ports:
      - "8080:8080"
    environment:
      - ZF_AUTH_DATABASE_URL=*************************************************/auth_db
      - ZF_AUTH_BIND_ADDRESS=0.0.0.0:8080
      - ZF_AUTH_ED25519_PRIVATE_KEY=${ZF_AUTH_ED25519_PRIVATE_KEY}
      - ZF_AUTH_JWT_EXPIRY_HOURS=24
      - ZF_AUTH_SESSION_TIMEOUT_HOURS=24
      - RUST_LOG=info
    depends_on:
      - auth-db
    restart: unless-stopped

  auth-db:
    image: postgres:15
    environment:
      - POSTGRES_DB=auth_db
      - POSTGRES_USER=auth_user
      - POSTGRES_PASSWORD=auth_password
    volumes:
      - auth_db_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  auth_db_data:
  controller_db_data: