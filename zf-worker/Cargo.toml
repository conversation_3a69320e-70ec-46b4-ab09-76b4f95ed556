[package]
name = "zf-worker"
version = "0.1.93"
edition = "2021"
publish = false

[profile.release]
opt-level = 3
lto = "fat"
strip = true
#debug = true
panic = "abort"

[features]
default=["retry_conn"]
mem_prof=["leak-detect-allocator", "rustc-demangle"]
jemalloc = ["private_tun/jemalloc"]
metric = ["private_tun/metric"]
mmsg = ["private_tun/mmsg"]
#aya = ["dep:aya", "aya-log"]
audit_log =[]
retry_conn = ["private_tun/retry_conn"]


[target.'cfg(target_os = "linux")'.dependencies]
#aya = { git = "https://github.com/aya-rs/aya", features = ["async_tokio"], optional=true }
#aya-log = { git = "https://github.com/aya-rs/aya", optional=true  }
libc = "0.2"
pnet_datalink = "0.35"
ipnetwork = "0.20.0"
#tobaru = { version = "0.9.0", path = "../vendor/tobaru" }


[dependencies]
parking_lot = {workspace = true}
tokio = {workspace = true}
clap = { version = "4", default-features=false, features = ["derive", "env"] }
anyhow = { version = "1", default-features=false, features = ["backtrace"] }
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = {version = "0.4", default-features=false}
serde_json = {version = "1", default-features=false}
common = { path = "../common" }
primitive-types = {version = "0.13", default-features=false}
tokio-websockets = { version = "0.12.0", default-features=false, features = ["client", "rustls-webpki-roots","openssl", "rand"] }
hex = { version = "0.4.3", default-features=false}
http = {version = "1", default-features=false}
futures-util = { version = "0.3", default-features=false }
socks5-impl = {version = "0", default-features=false, features = ["client"]}
#realm_core = {path="../vendor/realm_core", features = ["multi-thread", "batched-udp","brutal-shutdown"]}
realm_core = {version = "0", default-features=false, features = ["multi-thread", "batched-udp","brutal-shutdown"]}
realm_io = { version = "0",  default-features=false, features = ["brutal-shutdown"] }
bytes = { version = "1", default-features=false}
socket2 = {version = "0.6", default-features=false}
socks5-proto = { version = "0.4", default-features = false }
socks5-server = { version = "0.10", default-features = false }
lru_time_cache = { version = "0.11", default-features=false}
serde = {version = "1", default-features=false}
rand = {version = "0.9", default-features=false}
tokio-io-timeout = {version = "1", default-features=false}
sysinfo = {version = "0.35", default-features=false, features=["network","system"]}
private_tun = { workspace = true, default-features=false, features=["for_zfc"] }
tcp_over_multi_tcp_client = { workspace=true }
surge-ping = {version = "0", default-features=false}
tokio-util = { version = "0.7", default-features=false, features = ["io", "codec"] }
reqwest = { version = "0.12", default-features = false, features = ["json", "rustls-tls", "socks"] }
async-trait = {version = "0", default-features=false}
base64 = {version = "0.22", default-features=false}
futures = { version = "0.3", default-features=false}
thiserror = {version = "2", default-features=false}
pin-project = {version = "1", default-features=false}
cfg-if = {version = "1", default-features=false}
smallvec = {version = "1", default-features=false}
chrono = {version = "0.4.31", default-features=false}
leak-detect-allocator = {git = "https://github.com/BlackLuny/leak-detect-allocator.git", default-features=false, optional = true}
rustc-demangle = { version = "0", default-features=false, optional = true}
tokio-metrics = { version = "0.4.2", default-features=false}
lazy_static = {version = "1.5.0", default-features=false}
async-ringbuf = {version ="0.3", default-features=false}
dashmap = "6.1.0"
uuid = { version = "1.8.0", features = ["v4", "fast-rng"] }

[[bin]]
name = "zf-worker"
path = "src/main.rs"
