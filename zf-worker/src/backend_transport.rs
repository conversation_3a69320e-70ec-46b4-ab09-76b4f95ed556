use std::{net::SocketAddr, sync::Arc};

use base64::Engine;
use common::TotConfig;
use private_tun::snell_impl_ver::config::ClientConfig;

use crate::{
    protocol::{
        hammer::{
            setup_for_worker_tun_proxy_from_worker_proxy, start_hammer_client, HammerCtx,
            HammerHandlerForWorkerProxy, HammerHandlerForWorkerTun,
        },
        tot::{TotClientForWorkerTun, TotCtx},
    },
    server::SocksBackend,
};

#[derive(Debug, Clone)]
pub enum BackendTransport {
    Socks5(Arc<SocksBackend>),
    <PERSON>(HammerHandlerForWorkerTun),
    Tot(TotClientForWorkerTun),
}

impl BackendTransport {
    pub async fn new_from_str(
        backend: &Option<String>,
        outbound_proxy_auth: &Option<String>,
        hammer_proxyer: Option<Arc<HammerHandlerForWorkerProxy>>,
        hammer_ctx: &HammerCtx,
        tot_ctx: &TotCtx,
    ) -> Option<Self> {
        let Some(backend) = backend.as_ref() else {
            return None;
        };
        if backend.trim().is_empty() || backend.trim() == "direct" {
            return None;
        } else if backend.trim() == "use_proxy" {
            if let Some(hammer_proxyer) = hammer_proxyer.as_ref() {
                return Some(Self::Hammer(setup_for_worker_tun_proxy_from_worker_proxy(
                    hammer_proxyer.clone(),
                )));
            } else {
                log::warn!("hammer proxyer is not set direct forwarding");
            }
        } else if let Ok(addr) = backend.parse::<SocketAddr>() {
            // socks5
            if let Some(auth) = outbound_proxy_auth.as_ref() {
                let mut sp = auth.split(':');
                let user = sp.next()?;
                let password = sp.next()?;
                return Some(Self::Socks5(Arc::new(SocksBackend::new(
                    addr,
                    Some(socks5_impl::protocol::UserKey {
                        username: user.to_owned(),
                        password: password.to_owned(),
                    }),
                ))));
            } else {
                return Some(Self::Socks5(Arc::new(SocksBackend::new(addr, None))));
            }
        } else {
            let base64_config = base64::engine::general_purpose::STANDARD
                .decode(backend)
                .ok()?;
            // hammer
            if let Ok(client_config) = serde_json::from_slice::<ClientConfig>(&base64_config) {
                let client = start_hammer_client(
                    hammer_ctx.rt_provider.clone(),
                    client_config,
                    "for-zfc-worker".to_string(),
                    true,
                )
                .await
                .ok()?;
                return Some(Self::Hammer(HammerHandlerForWorkerTun::new(client)));
            }
            // tot
            if let Ok(tot_config) = serde_json::from_slice::<TotConfig>(&base64_config) {
                let client = TotClientForWorkerTun::new(tot_ctx, &tot_config)
                    .await
                    .ok()?;
                return Some(Self::Tot(client));
            }
        }
        None
    }
}
