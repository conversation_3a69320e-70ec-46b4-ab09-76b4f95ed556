use std::net::SocketAddr;

use common::app_message::{LatencyTestMethod, Mode};
use private_tun::address::Address;

#[derive(Debug)]
pub struct TargetData {
    pub addr_list: Vec<SocketAddr>,
    pub mode: Option<Mode>,
    pub latency_test_method: Option<LatencyTestMethod>,
    pub sub_id: i32,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
    pub speed_rate: Option<u32>,
    pub accept_proxy_protocol: Option<bool>,
    pub send_proxy_protocol_version: Option<i32>,
}

impl Into<Vec<Address>> for &TargetData {
    fn into(self) -> Vec<Address> {
        self.addr_list
            .iter()
            .map(|addr| Address::Socket(addr.clone()))
            .collect()
    }
}
