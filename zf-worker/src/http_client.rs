use std::time::Duration;

use reqwest::{Request, Response};

use crate::{backend_transport::BackendTransport, protocol::hammer::HammerHandlerForWorkerTun};

#[derive(Clone)]
pub enum HttpClient {
    ProxyedHammer(HammerHandlerForWorkerTun),
    // TODO: implement tiny_http_client instead of reqwest
    Reqwest(reqwest::Client),
}

impl HttpClient {
    pub fn new(backend: Option<BackendTransport>) -> anyhow::Result<Self> {
        if let Some(backend) = backend {
            match backend {
                BackendTransport::Hammer(hammer) => Ok(Self::ProxyedHammer(hammer)),
                BackendTransport::Tot(tot) => Ok(Self::ProxyedHammer(
                    HammerHandlerForWorkerTun::new(tot.fwd_handler_client().clone()),
                )),
                BackendTransport::Socks5(socks) => {
                    let proxy_url = socks.proxy_url();
                    log::info!("http client using socks5 proxy url: {}", proxy_url);
                    let client = reqwest::Client::builder()
                        .proxy(reqwest::Proxy::all(proxy_url)?)
                        .timeout(Duration::from_secs(10))
                        .build()?;
                    Ok(Self::Reqwest(client))
                }
            }
        } else {
            Ok(Self::Reqwest(reqwest::Client::new()))
        }
    }
    pub async fn request(&self, req: Request) -> anyhow::Result<Response> {
        match self {
            Self::ProxyedHammer(hammer) => hammer.request(req).await,
            Self::Reqwest(client) => Ok(client.execute(req).await?),
        }
    }
}
