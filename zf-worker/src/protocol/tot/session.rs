struct TotServerSetupBindPlugin {
    target_list: Vec<Address>,
    rst_tx: Option<tokio::sync::oneshot::Sender<u16>>,
    mode: Mode,
    latency_test_method: LatencyTestMethod,
}
impl TotServerSetupBindPlugin {
    pub fn new(
        target_list: Vec<Address>,
        rst_tx: tokio::sync::oneshot::Sender<u16>,
        mode: Mode,
        latency_test_method: LatencyTestMethod,
    ) -> Self {
        Self {
            target_list,
            rst_tx: Some(rst_tx),
            mode,
            latency_test_method,
        }
    }
}
use std::{
    collections::HashMap,
    hash::{DefaultHasher, Hash, Hasher},
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::Context;
use common::{
    app_message::{LatencyTestMethod, Mode},
    hammer_message::{
        SetupTotServerReq, SetupTotServerRst, StopTotServerReq, StopTotServerRst,
        SETUP_TOT_SERVER_RST, STOP_TOT_SERVER_RST,
    },
    rechecker::Rechecker,
    replace_address_port,
};
use futures::{future::join_all, stream, StreamExt};
use private_tun::{
    address::Address,
    self_proxy::MemDuplex,
    snell_impl_ver::{
        client_plugin::{ClientPlugin, PluginResult},
        client_zfc::{ConnType, Limitter},
        relayer::EncryptIO,
        server_plugin::MessageCodec,
        TestLatencyMethod,
    },
};
use tokio::{io::AsyncReadExt, sync::RwLock};
use tokio_util::sync::{CancellationToken, DropGuard};

use crate::protocol::{
    hammer::HammerClient,
    tot::{TotClientConfigWrapper, TotIdAndTarget},
};

use super::HammerHandlerForTot;
#[async_trait::async_trait]
impl ClientPlugin for TotServerSetupBindPlugin {
    async fn call(&mut self, io: &mut EncryptIO) -> anyhow::Result<PluginResult> {
        log::debug!(
            "begin send tot setup req target_list: {:?} mode: {:?} latency_test_method: {:?}",
            self.target_list,
            self.mode,
            self.latency_test_method
        );
        let req = SetupTotServerReq {
            remote_list: self.target_list.clone(),
            mode: self.mode,
            latency_test_method: self.latency_test_method,
        };
        SetupTotServerReq::encode(io, &req).await?;
        log::debug!("send tot setup req success");
        let msg_id = io.read_u8().await?;
        if msg_id != SETUP_TOT_SERVER_RST {
            return Err(anyhow::anyhow!(
                "tot setup req msg_id not match msg_id: {}",
                msg_id
            ));
        }
        let rst = SetupTotServerRst::decode(io).await?;
        log::debug!("recv tot setup rst success: {:?}", rst);
        if let Err(e) = self.rst_tx.take().unwrap().send(rst.server_bind) {
            log::error!("send bind rst closed rst: {:?}", e);
        }
        Ok(PluginResult::OK)
    }
}

struct TotServerStopBindPlugin {
    target_list: Vec<Address>,
    rst_tx: Option<tokio::sync::oneshot::Sender<u8>>,
    mode: Mode,
    latency_test_method: LatencyTestMethod,
    server_bind: u16,
}
impl TotServerStopBindPlugin {
    pub fn new(
        target_list: Vec<Address>,
        rst_tx: tokio::sync::oneshot::Sender<u8>,
        mode: Mode,
        latency_test_method: LatencyTestMethod,
        server_bind: u16,
    ) -> Self {
        Self {
            target_list,
            rst_tx: Some(rst_tx),
            mode,
            latency_test_method,
            server_bind,
        }
    }
}
#[async_trait::async_trait]
impl ClientPlugin for TotServerStopBindPlugin {
    async fn call(&mut self, io: &mut EncryptIO) -> anyhow::Result<PluginResult> {
        log::debug!(
            "begin send tot stop req target_list: {:?} mode: {:?} latency_test_method: {:?}",
            self.target_list,
            self.mode,
            self.latency_test_method
        );
        let req = StopTotServerReq {
            remote_list: self.target_list.clone(),
            mode: self.mode,
            latency_test_method: self.latency_test_method,
            server_bind: self.server_bind,
        };
        StopTotServerReq::encode(io, &req).await?;
        log::debug!("send tot stop req: {:?}", req);
        let msg_id = io.read_u8().await?;
        if msg_id != STOP_TOT_SERVER_RST {
            return Err(anyhow::anyhow!(
                "tot stop req msg_id not match msg_id: {}",
                msg_id
            ));
        }
        let rst = StopTotServerRst::decode(io).await?;
        log::debug!("recv tot stop rst: {:?}", rst);
        if let Err(e) = self.rst_tx.take().unwrap().send(rst.error_code) {
            log::error!("send stop rst closed rst: {:?}", e);
        }
        Ok(PluginResult::OK)
    }
}

pub struct FwdToServerMapper {
    pub map: Vec<(Box<str>, (Address, u16))>,
}

impl FwdToServerMapper {
    pub fn new() -> Self {
        Self { map: Vec::new() }
    }
    pub fn update(&mut self, mapper: FwdToServerMapper) {
        self.map.clear();
        for (server_name, v) in mapper.map.into_iter() {
            self.map.push((server_name, v));
        }
    }
    pub fn get_bind_address(&self, server_name: &str) -> Option<Address> {
        self.map
            .iter()
            .find(|(name, _)| name.as_ref() == server_name)
            .map(|(_, v)| replace_address_port(&v.0, v.1))
    }
    pub fn push_server(&mut self, server_name: &str, addr: Address, port: u16) {
        self.map
            .push((server_name.to_string().into_boxed_str(), (addr, port)));
    }
}
pub async fn create_piped_stream(
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    target_addr: Address,
    cancel: CancellationToken,
    limitter: Option<Limitter>,
) -> MemDuplex {
    let (dup_a, dup_b) = tokio::io::duplex(16384);
    tokio::spawn(async move {
        let _ = fwd_hammer_handler
            .proc_tot_proxy_stream(dup_b, target_addr, None, None, limitter)
            .await;
    });
    MemDuplex::new(dup_a, cancel)
}
pub async fn setup_fwd_tot_server(
    tot_hammer_handler: &HammerClient,
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    target_list: &Vec<Address>,
    mode: Mode,
    latency_test_method: LatencyTestMethod,
) -> anyhow::Result<FwdToServerMapper> {
    let cancel = CancellationToken::new();
    let mut tasks = Vec::with_capacity(tot_hammer_handler.servers_iter().count());

    for tot_server in tot_hammer_handler.servers_iter() {
        let target_list_clone = target_list.clone();
        let (bind_rst_tx, bind_rst_rx) = tokio::sync::oneshot::channel();
        tasks.push(async {
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
            let piped_stream = create_piped_stream(
                fwd_hammer_handler.clone(),
                tot_server.address.clone(),
                cancel.clone(),
                None,
            )
            .await;
            if let Err(e) = tot_hammer_handler
                .push_evt(ConnType::PluginCall {
                    plugin: Box::new(TotServerSetupBindPlugin::new(
                        target_list_clone,
                        bind_rst_tx,
                        mode,
                        latency_test_method,
                    )),
                    rst_tx,
                    server_name: Some(tot_server.name.clone()),
                    piped_stream: Some(piped_stream),
                    reuse_tcp: true,
                })
                .await
            {
                log::error!(
                    "setup_fwd_tot_server send plugin call error: {:?} for server: {}",
                    e,
                    tot_server.name
                );
                return Err(anyhow::anyhow!(
                    "setup_fwd_tot_server send plugin call error: {:?} for server: {}",
                    e,
                    tot_server.name
                ));
            }
            let _rst = match tokio::time::timeout(Duration::from_secs(10), rst_rx).await {
                Ok(Ok(rst)) => rst,
                Ok(Err(e)) => {
                    log::error!(
                        "setup_fwd_tot_server recv channel error: {} for server: {}",
                        e,
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "setup_fwd_tot_server recv channel error: {} for server: {}",
                        e,
                        tot_server.name
                    ));
                }
                Err(_e) => {
                    log::error!(
                        "setup_fwd_tot_server timeout for server: {}",
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "setup_fwd_tot_server timeout for server: {}",
                        tot_server.name
                    ));
                }
            };
            let bind_rst = match tokio::time::timeout(Duration::from_secs(10), bind_rst_rx).await {
                Ok(Ok(rst)) => rst,
                Ok(Err(e)) => {
                    log::error!(
                        "setup_fwd_tot_server recv bind rst channel error: {} for server: {}",
                        e,
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "setup_fwd_tot_server recv bind rst channel error: {} for server: {}",
                        e,
                        tot_server.name
                    ));
                }
                Err(_e) => {
                    log::error!(
                        "setup_fwd_tot_server wait bind rst timeout for server: {}",
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "setup_fwd_tot_server wait bind rst timeout for server: {}",
                        tot_server.name
                    ));
                }
            };
            Ok::<_, anyhow::Error>((
                tot_server.name.clone(),
                bind_rst,
                tot_server.address.clone(),
            ))
        });
    }
    let rsts = join_all(tasks).await;
    let _guard = cancel.drop_guard();
    let mut mapper = FwdToServerMapper::new();
    // keep order
    for tot_server in tot_hammer_handler.servers_iter() {
        let server_name = tot_server.name.clone();
        // find the bind rst
        let Some(rst) = rsts
            .iter()
            .filter_map(|x| x.as_ref().ok())
            .find(|rst| rst.0 == server_name)
        else {
            log::error!("setup_fwd_tot_server server: {} failed", server_name);
            mapper.push_server(
                &format!("tot-server-{}", server_name),
                tot_server.address.clone(),
                0,
            );
            continue;
        };
        let (server_name, bind_rst, server_addr) = rst;
        mapper.push_server(
            &format!("tot-server-{}", server_name),
            server_addr.clone(),
            *bind_rst,
        );
    }

    if mapper.map.len() == 0 {
        return Err(anyhow::anyhow!(
            "setup_fwd_tot_server failed: no server bind success"
        ));
    }
    Ok(mapper)
}

pub async fn tot_test_latency_for_target_list(
    tot_hammer_handler: &HammerClient,
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    target_list: &[Address],
    latency_test_method: TestLatencyMethod,
) -> anyhow::Result<Vec<(Address, u32)>> {
    let cancel = CancellationToken::new();
    let mut tasks = Vec::with_capacity(tot_hammer_handler.servers_iter().count());

    for tot_server in tot_hammer_handler.servers_iter() {
        for target in target_list {
            tasks.push(async {
                let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
                let piped_stream = create_piped_stream(
                    fwd_hammer_handler.clone(),
                    tot_server.address.clone(),
                    cancel.clone(),
                    None,
                )
                .await;
                let now = Instant::now();
                if let Err(e) = tot_hammer_handler
                    .push_evt(ConnType::TestLatency {
                        target: target.clone(),
                        method: latency_test_method,
                        rst_tx,
                        server_name: tot_server.name.clone(),
                        piped_stream: Some(piped_stream),
                        reuse_tcp: true,
                    })
                    .await
                {
                    log::error!(
                        "tot_test_latency_for_target_list send test latency command error: {:?} for server: {}",
                        e,
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "tot_test_latency_for_target_list send test latency command error: {:?} for server: {}",
                        e,
                        tot_server.name
                    ));
                }
                let latency = match tokio::time::timeout(Duration::from_secs(10), rst_rx).await {
                    Ok(Ok(Ok(rst))) => rst,
                    Ok(Ok(Err(e))) => {
                        log::error!(
                            "tot_test_latency_for_target_list proc latency result error: {:?} for server: {}",
                            e,
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list proc latency result error: {:?} for server: {}",
                            e,
                            tot_server.name
                        ));
                    }
                    Ok(Err(e)) => {
                        log::error!(
                            "tot_test_latency_for_target_list recv test latency result error: {} for server: {}",
                            e,
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list recv test latency result error: {} for server: {}",
                            e,
                            tot_server.name
                        ));
                    }
                    Err(_e) => {
                        log::error!(
                            "tot_test_latency_for_target_list timeout for server: {}",
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list timeout for server: {}",
                            tot_server.name
                        ));
                    }
                };
                log::info!("tot_test_latency_for_target_list from remote: {} test latency: {}micro sec elapsed: {:?}micro sec tot server: {}", target.clone(), latency, now.elapsed().as_micros(), tot_server.name);
                let middle_latency = (now.elapsed().as_micros() as u32 - latency) / 2; // 经验值 for tcp connection time
                Ok::<_, anyhow::Error>((
                    tot_server.name.clone(),
                    target.clone(),
                    middle_latency + latency
                ))
            });
        }
    }
    let rsts = join_all(tasks).await;
    let mut merged_rsts = HashMap::new();
    // merge rsts get the min latency for each target
    for rst in rsts {
        let (_server_name, target, latency) = match rst {
            Ok(v) => v,
            Err(e) => {
                log::error!("tot_test_latency_for_target_list failed: {}", e);
                continue;
            }
        };
        let entry = merged_rsts.entry(target).or_insert(65535000);
        if latency < *entry {
            *entry = latency;
        }
    }
    let _guard = cancel.drop_guard();
    let mut latency_result = Vec::new();
    for target_addr in target_list {
        let latency = merged_rsts.get(target_addr).cloned().unwrap_or(65535000);
        latency_result.push((target_addr.clone(), latency));
    }
    Ok(latency_result)
}

pub async fn stop_fwd_tot_server(
    hammer_handler: &HammerClient,
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    target_list: &Vec<Address>,
    mode: Mode,
    latency_test_method: LatencyTestMethod,
    mapper: &FwdToServerMapper,
) -> anyhow::Result<()> {
    let cancel = CancellationToken::new();
    let mut tasks = Vec::with_capacity(hammer_handler.servers_iter().count());
    for tot_server in hammer_handler.servers_iter() {
        let target_list_clone = target_list.clone();
        let (bind_rst_tx, bind_rst_rx) = tokio::sync::oneshot::channel();
        let Some(bind_addr) = mapper.get_bind_address(&tot_server.name) else {
            log::error!("stop_fwd_tot_server server: {} not bind", tot_server.name);
            continue;
        };
        let server_name = tot_server.name.clone();
        let bind_port = bind_addr.port().clone();
        let plugin = Box::new(TotServerStopBindPlugin::new(
            target_list_clone,
            bind_rst_tx,
            mode,
            latency_test_method,
            bind_port,
        ));
        tasks.push(async {
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
            let piped_stream = create_piped_stream(
                fwd_hammer_handler.clone(),
                tot_server.address.clone(),
                cancel.clone(),
                None,
            )
            .await;
            hammer_handler
                .push_evt(ConnType::PluginCall {
                    plugin,
                    rst_tx,
                    server_name: Some(server_name.clone()),
                    piped_stream: Some(piped_stream),
                    reuse_tcp: true,
                })
                .await?;
            let _rst = tokio::time::timeout(Duration::from_secs(10), rst_rx)
                .await
                .context(anyhow::anyhow!("stop_fwd_tot_server timeout"))??;
            let stop_rst = tokio::time::timeout(Duration::from_secs(10), bind_rst_rx)
                .await
                .context(anyhow::anyhow!("stop_fwd_tot_server wait bind rsttimeout"))??;
            log::debug!(
                "recv tot stop at server: {} rst: {:?}",
                server_name,
                stop_rst
            );
            Ok::<_, anyhow::Error>((server_name, stop_rst))
        });
    }
    let rsts = join_all(tasks).await;
    let _guard = cancel.drop_guard();
    for rst in rsts {
        let (server_name, stop_rst) = match rst {
            Ok(v) => v,
            Err(e) => {
                log::error!("stop_fwd_tot_server failed: {}", e);
                continue;
            }
        };
        if stop_rst != 0 {
            log::error!(
                "stop tot server at server: {} failed error_code: {}",
                server_name,
                stop_rst
            );
        }
    }
    Ok(())
}

pub struct TotSession {
    pub mapper: Arc<RwLock<FwdToServerMapper>>,
    pub hammer_handler: Arc<HammerClient>, // to server hammer tunnel
    pub fwd_hammer_handler: Arc<HammerHandlerForTot>, // to forward hammer tunnel
    pub target_list: Vec<Address>,
    pub mode: Mode,
    pub latency_test_method: LatencyTestMethod,
}
impl TotSession {
    pub fn new(
        mapper: Arc<RwLock<FwdToServerMapper>>,
        hammer_handler: Arc<HammerClient>,
        fwd_hammer_handler: Arc<HammerHandlerForTot>,
        target_list: Vec<Address>,
        mode: Mode,
        latency_test_method: LatencyTestMethod,
    ) -> Self {
        Self {
            mapper,
            hammer_handler,
            fwd_hammer_handler,
            target_list,
            mode,
            latency_test_method,
        }
    }

    pub async fn test_latency(
        &self,
        remote: &Address,
        method: TestLatencyMethod,
    ) -> anyhow::Result<u64> {
        let latency_result = tot_test_latency_for_target_list(
            &self.hammer_handler,
            self.fwd_hammer_handler.clone(),
            &[remote.clone()],
            method,
        )
        .await?;
        if latency_result.len() == 0 {
            log::error!(
                "tot session test latency failed for target: {} no result method: {:?}",
                remote,
                method
            );
            return Err(anyhow::anyhow!(
                "tot session test latency failed for target: {} no result method: {:?}",
                remote,
                method
            ));
        }
        Ok(latency_result[0].1 as u64)
    }
}

// 维持session
pub struct TotSessionManager {
    sessions: Arc<RwLock<HashMap<TotIdAndTarget, Arc<TotSession>>>>,
    _cancel: DropGuard,
    tot_port_bind_trigger: Rechecker<tokio::sync::mpsc::Sender<()>>,
}

impl TotSessionManager {
    pub fn new() -> Self {
        let sessions = Arc::new(RwLock::new(
            HashMap::<TotIdAndTarget, Arc<TotSession>>::new(),
        ));
        let cancel = CancellationToken::new();
        let cancel_clone = cancel.clone();
        let sessions_clone = sessions.clone();
        let (recheck_tx, mut recheck_rx) = tokio::sync::mpsc::channel(4);
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    biased;
                    _ = recheck_rx.recv() => {
                        log::debug!("tot session manager recheck trig by cmd");
                    }
                    _ = tokio::time::sleep(tokio::time::Duration::from_secs(60)) => {
                        log::debug!("tot session manager recheck trig by timer");
                    }
                    _ = cancel_clone.cancelled() => {
                        log::info!("tot session manager canceled");
                        break;
                    }
                }

                // clear terminated session
                sessions_clone.write().await.retain(|k, v| {
                    if Arc::strong_count(v) > 1 {
                        true
                    } else {
                        log::info!("tot session manager remove session: {}", k);
                        false
                    }
                });
                let tasks = {
                    let lock = sessions_clone.read().await;
                    stream::iter(
                        lock.iter()
                            .map(|(id, session)| (id.clone(), session.clone()))
                            .collect::<Vec<_>>()
                            .into_iter(),
                    )
                    .for_each_concurrent(
                        Some(50),
                        |(id, session)| async move {
                            let mapper = match setup_fwd_tot_server(
                                &session.hammer_handler,
                                session.fwd_hammer_handler.clone(),
                                &session.target_list,
                                session.mode,
                                session.latency_test_method,
                            )
                            .await
                            {
                                Ok(mapper) => mapper,
                                Err(e) => {
                                    log::error!(
                                        "setup_fwd_tot_server failed: {} for id: {}",
                                        e,
                                        id
                                    );
                                    return;
                                }
                            };
                            // update mapper
                            log::info!("update tot server mapper: {:?} for id: {}", mapper.map, id);
                            session.mapper.write().await.update(mapper);
                        },
                    )
                };
                tasks.await;
            }
        });
        Self {
            sessions,
            _cancel: cancel.drop_guard(),
            tot_port_bind_trigger: Rechecker::new(recheck_tx, Duration::from_secs(5)),
        }
    }
    pub async fn insert_session(&self, id: TotIdAndTarget, session: Arc<TotSession>) {
        let mut lock = self.sessions.write().await;
        lock.insert(id, session);
    }

    pub async fn get_session_by_id(&self, id: &TotIdAndTarget) -> Option<Arc<TotSession>> {
        let lock = self.sessions.read().await;
        lock.get(id).cloned()
    }

    pub async fn get_session_by_config(
        &self,
        config: &TotClientConfigWrapper,
    ) -> Option<Arc<TotSession>> {
        let lock = self.sessions.read().await;
        let mut hasher = DefaultHasher::new();
        config.0.hash(&mut hasher);
        let hash = hasher.finish();
        lock.iter()
            .find(|(id, _)| id.id.hash == hash)
            .map(|x| x.1.clone())
    }
    pub fn get_tot_port_bind_trigger(&self) -> Rechecker<tokio::sync::mpsc::Sender<()>> {
        self.tot_port_bind_trigger.clone()
    }

    pub async fn stop_related_session(
        &self,
        tot_hammer_client: &HammerClient,
        fwd_hammer_handler: &Arc<HammerHandlerForTot>,
    ) {
        let hammer_identifier = tot_hammer_client.get_identifier();
        let fwd_hammer_identifier = fwd_hammer_handler.client.get_identifier();
        let read_lock = self.sessions.read().await;
        let all_task = read_lock
            .iter()
            .filter(|(_id, session)| {
                session.hammer_handler.get_identifier() == hammer_identifier
                    && session.fwd_hammer_handler.client.get_identifier() == fwd_hammer_identifier
            })
            .map(|(id, session)| {
                let session = session.clone();
                let all_sessions = self.sessions.clone();
                let id_clone = id.clone();
                async move {
                    match tokio::time::timeout(
                        Duration::from_secs(20),
                        stop_fwd_tot_server(
                            &session.hammer_handler,
                            session.fwd_hammer_handler.clone(),
                            &session.target_list,
                            session.mode,
                            session.latency_test_method,
                            &*session.mapper.read().await,
                        ),
                    )
                    .await
                    {
                        Ok(rst) => match rst {
                            Ok(_) => {
                                log::info!("stop_fwd_tot_server: {} success", id_clone);
                            }
                            Err(e) => {
                                log::error!("stop_fwd_tot_server: {} failed: {}", id_clone, e);
                            }
                        },
                        Err(_) => {
                            log::error!("stop_fwd_tot_server: {} failed: timeout", id_clone);
                        }
                    };
                    all_sessions.write().await.remove(&id_clone);
                }
            });
        futures::stream::iter(all_task).for_each(|f| f).await;
    }
}
