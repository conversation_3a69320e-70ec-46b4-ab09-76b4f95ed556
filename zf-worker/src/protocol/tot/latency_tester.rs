use std::{
    collections::HashMap,
    net::{Ip<PERSON>dd<PERSON>, SocketAddr},
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::anyhow;
use common::{
    app_message::LatencyTestMethod,
    into_socket_addr,
    server_provider::{LatencyTester, ServerInfo},
    PingClient,
};
use futures::{future::join_all, stream, StreamExt};
use private_tun::{
    address::Address,
    snell_impl_ver::{client_zfc::ConnType, config::ClientConfig, TestLatencyMethod},
};
use rand::Rng;
use tokio::{net::TcpStream, sync::RwLock};
use tokio_util::sync::CancellationToken;

use crate::{
    constant::{FWD_TEST_TIMEOUT, TARGET_TEST_TIMEOUT},
    latency::into_test_latency_method,
    protocol::{
        hammer::HammerClient,
        tot::{session::create_piped_stream, HammerHandlerForTot},
    },
};

use super::session::FwdToServerMapper;

#[derive(Clone)]
pub struct TotLatencyChecker {
    method: LatencyTestMethod,
    target_test_method: LatencyTestMethod,
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    tot_server_handler: Arc<HammerClient>,
    config: ClientConfig,
    ping_client: Arc<PingClient>,
    tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
}

impl TotLatencyChecker {
    pub fn new(
        ping_client: Arc<PingClient>,
        method: LatencyTestMethod,
        target_test_method: LatencyTestMethod,
        fwd_hammer_handler: Arc<HammerHandlerForTot>,
        tot_server_handler: Arc<HammerClient>,
        config: ClientConfig,
        tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
    ) -> Self {
        Self {
            method,
            target_test_method,
            fwd_hammer_handler,
            tot_server_handler,
            config,
            ping_client,
            tot_server_bind_mapper,
        }
    }
}

pub async fn get_all_server_from_config(
    config: &ClientConfig,
) -> anyhow::Result<Vec<(Box<str>, SocketAddr)>> {
    let mut servers = Vec::new();
    // test main server
    if let Some(main_server_name) = config.name.as_ref() {
        let server_addr =
            tokio::net::lookup_host((config.server_addr.as_ref(), config.server_port))
                .await?
                .next()
                .ok_or(anyhow!("test latency: get server from config failed"))?;
        servers.push((main_server_name.clone(), server_addr));
    }
    // test backup server
    for backup_server in config
        .multi_server_config
        .as_ref()
        .map(|x| &x.back_servers)
        .into_iter()
        .flatten()
    {
        if let Some(backup_server_name) = backup_server.name.as_ref() {
            let server_addr = into_socket_addr(&backup_server.addr_port).await?;
            servers.push((backup_server_name.clone(), server_addr));
        }
    }
    Ok(servers)
}

async fn test_tot_server_to_remote_list_latency(
    tot_hammer_handler: &Arc<HammerClient>,
    fwd_hammer_handler: &Arc<HammerHandlerForTot>,
    tot_server_name: &Box<str>,
    target_list: &[Address],
    method: &LatencyTestMethod,
) -> Result<Vec<(Address, u32)>, anyhow::Error> {
    let cancel = CancellationToken::new();
    let mut tasks = Vec::with_capacity(target_list.len());
    let tot_server = tot_hammer_handler
        .servers_iter()
        .find(|x| x.name.as_ref() == tot_server_name)
        .ok_or(anyhow!("test tot server to remote: tot server not found"))?;
    for target in target_list {
        tasks.push(async {
                let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
                let piped_stream = create_piped_stream(
                    fwd_hammer_handler.clone(),
                    tot_server.address.clone(),
                    cancel.clone(),
                    None,
                )
                .await;
                let now = Instant::now();
                if let Err(e) = tot_hammer_handler
                    .push_evt(ConnType::TestLatency {
                        target: target.clone(),
                        method: into_test_latency_method(method.clone()),
                        rst_tx,
                        server_name: tot_server.name.clone(),
                        piped_stream: Some(piped_stream),
                        reuse_tcp: true,
                    })
                    .await
                {
                    log::error!(
                        "tot_test_latency_for_target_list send test latency command error: {:?} for server: {}",
                        e,
                        tot_server.name
                    );
                    return Err(anyhow::anyhow!(
                        "tot_test_latency_for_target_list send test latency command error: {:?} for server: {}",
                        e,
                        tot_server.name
                    ));
                }
                let latency = match tokio::time::timeout(Duration::from_secs(10), rst_rx).await {
                    Ok(Ok(Ok(rst))) => rst,
                    Ok(Ok(Err(e))) => {
                        log::error!(
                            "tot_test_latency_for_target_list proc latency result error: {:?} for server: {}",
                            e,
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list proc latency result error: {:?} for server: {}",
                            e,
                            tot_server.name
                        ));
                    }
                    Ok(Err(e)) => {
                        log::error!(
                            "tot_test_latency_for_target_list recv test latency result error: {} for server: {}",
                            e,
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list recv test latency result error: {} for server: {}",
                            e,
                            tot_server.name
                        ));
                    }
                    Err(_e) => {
                        log::error!(
                            "tot_test_latency_for_target_list timeout for server: {}",
                            tot_server.name
                        );
                        return Err(anyhow::anyhow!(
                            "tot_test_latency_for_target_list timeout for server: {}",
                            tot_server.name
                        ));
                    }
                };
                log::info!("tot_test_latency_for_target_list from remote: {} test latency: {}micro sec elapsed: {:?}micro sec tot server: {}", target.clone(), latency, now.elapsed().as_micros(), tot_server.name);
                Ok::<_, anyhow::Error>((
                    tot_server.name.clone(),
                    target.clone(),
                    latency
                ))
            });
    }
    let rsts = join_all(tasks).await;
    let mut merged_rsts = HashMap::new();
    // merge rsts get the min latency for each target
    for rst in rsts {
        let (_server_name, target, latency) = match rst {
            Ok(v) => v,
            Err(e) => {
                log::error!("tot_test_latency_for_target_list failed: {}", e);
                continue;
            }
        };
        let entry = merged_rsts.entry(target).or_insert(65535000);
        if latency < *entry {
            *entry = latency;
        }
    }
    let _guard = cancel.drop_guard();
    let mut latency_result = Vec::new();
    for target_addr in target_list {
        let latency = merged_rsts.get(target_addr).cloned().unwrap_or(65535000);
        latency_result.push((target_addr.clone(), latency));
    }
    Ok(latency_result)
}

async fn test_one_hammer_server_and_remote(
    fwd_server_name: &Box<str>,
    fwd_server_addr: &SocketAddr,
    remote: &Address,
    method: &LatencyTestMethod,
    ping_client: &Arc<PingClient>,
    fwd_hammer_handler: &Arc<HammerHandlerForTot>,
) -> Result<std::time::Duration, anyhow::Error> {
    let rand_num: u16 = rand::rng().random_range(0..65535);
    let fwd_server_name = Arc::new(fwd_server_name.clone());
    let server_name_clone = fwd_server_name.clone();
    let to_fwd_server = async {
        let latency = match &method {
            LatencyTestMethod::Tcpping => {
                let now = std::time::Instant::now();
                match tokio::time::timeout(
                    std::time::Duration::from_secs(FWD_TEST_TIMEOUT),
                    TcpStream::connect(fwd_server_addr),
                )
                .await
                {
                    Ok(_) => now.elapsed(),
                    Err(_) => {
                        log::error!(
                            "test latency: tcpping timeout for fwd server: {}",
                            server_name_clone
                        );
                        return Err(anyhow!(
                            "test latency: tcpping timeout for fwd server: {}",
                            server_name_clone
                        ));
                    }
                }
            }
            LatencyTestMethod::Icmp => {
                let client = match fwd_server_addr.ip() {
                    IpAddr::V4(_ip) => ping_client.v4_client(),
                    IpAddr::V6(_ip) => ping_client.v6_client(),
                };
                let mut pinger = client
                    .pinger(fwd_server_addr.ip(), surge_ping::PingIdentifier(rand_num))
                    .await;
                let rst = pinger.ping(surge_ping::PingSequence(0), &[0; 64]).await?;
                rst.1
            }
        };
        Ok::<_, anyhow::Error>(latency)
    };
    // fwd-server -> remote
    let to_remote = async {
        let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
        fwd_hammer_handler
            .push_evt(ConnType::TestLatency {
                rst_tx,
                target: remote.clone(),
                server_name: fwd_server_name.clone(),
                method: match &method {
                    LatencyTestMethod::Tcpping => TestLatencyMethod::Tcpping,
                    LatencyTestMethod::Icmp => TestLatencyMethod::Icmp,
                },
                piped_stream: None,
                reuse_tcp: false,
            })
            .await
            .map_err(|_| anyhow::anyhow!("push test latency error"))?;
        let rst = tokio::time::timeout(std::time::Duration::from_secs(TARGET_TEST_TIMEOUT), rst_rx)
            .await??;
        Ok::<_, anyhow::Error>(rst)
    };
    let (from_fwd_server, from_remote) = tokio::join!(to_fwd_server, to_remote);
    let from_fwd_server = match from_fwd_server {
        Ok(v) => v,
        Err(e) => {
            log::error!(
                "test latency from fwd server: {} failed: {e:?}",
                fwd_server_name
            );
            return Err(e);
        }
    };
    let from_remote = match from_remote {
        Ok(v) => match v {
            Ok(v) => v,
            Err(e) => {
                log::error!(
                    "test latency from remote: {} failed: {e:?} fwd server: {}",
                    remote,
                    fwd_server_name
                );
                return Err(e.into());
            }
        },
        Err(e) => {
            log::error!(
                "test latency from remote: {} failed: {e:?} fwd server: {}",
                remote,
                fwd_server_name
            );
            return Err(e);
        }
    };
    log::debug!(
            "tot latency measure for: {} to fwd server: {}ms, fwd server to remote: {:?}ms, total: {:?}ms",
            remote,
            from_fwd_server.as_millis(),
            from_remote / 1000,
            from_fwd_server.as_millis() + from_remote as u128 / 1000
        );
    Ok(std::time::Duration::from_micros(
        from_fwd_server.as_micros() as u64 + from_remote as u64,
    ))
}

impl LatencyTester for TotLatencyChecker {
    type Server = ServerInfo;
    async fn test_latency(
        &self,
        server: &Self::Server,
    ) -> Result<std::time::Duration, anyhow::Error> {
        // self -> hammer inbound
        let remote = match self
            .tot_server_bind_mapper
            .read()
            .await
            .get_bind_address(&server.name)
        {
            Some(addr) => addr,
            None => {
                log::error!("test tot latency: server: {} not found", server.name);
                return Err(anyhow!(
                    "test tot latency: server: {} not found",
                    server.name
                ));
            }
        };
        if remote.port() == 0 {
            // todo: trig tot bind setup again?
            log::error!(
                "test tot latency: no bind port found for server: {} remote: {}",
                server.name,
                remote
            );
            return Err(anyhow!(
                "test tot latency: no bind port found for server: {} remote: {}",
                server.name,
                remote
            ));
        }
        let method = &self.method;
        let all_fwd_servers = match get_all_server_from_config(&self.config).await {
            Ok(v) => v,
            Err(e) => {
                log::error!("test tot latency: get all fwd servers failed: {}", e);
                return Err(e);
            }
        };
        let total_tasks = all_fwd_servers.len();
        let (sum, count) = stream::iter(0..total_tasks)
            .map(|idx| {
                let (server_name, server_addr) = &all_fwd_servers[idx];
                test_one_hammer_server_and_remote(
                    &server_name,
                    &server_addr,
                    &remote,
                    &method,
                    &self.ping_client,
                    &self.fwd_hammer_handler,
                )
            })
            .buffer_unordered(8)
            .fold((std::time::Duration::from_secs(0), 0), |(acc, count), r| {
                let r = if let Ok(v) = r {
                    (acc + v, count + 1)
                } else {
                    (acc, count)
                };
                std::future::ready(r)
            })
            .await;
        // let (sum, count) = rst;
        if count == 0 {
            log::error!(
                "test tot server: {} latency: no valid latency found",
                server.name
            );
            return Err(anyhow!(
                "test tot server: {} latency: no valid latency found",
                server.name
            ));
        }
        let to_tot_server_latency = sum / count;

        let tot_server_name_removed_prefix = server
            .name
            .split("tot-server-")
            .last()
            .map(|s| Arc::new(s.to_string().into_boxed_str()))
            .unwrap_or(server.name.clone());
        // test tot server to remote
        let tot_server_to_remote_latency = match tokio::time::timeout(
            std::time::Duration::from_secs(10),
            test_tot_server_to_remote_list_latency(
                &self.tot_server_handler,
                &self.fwd_hammer_handler,
                &tot_server_name_removed_prefix,
                &[remote.clone()],
                &self.target_test_method,
            ),
        )
        .await
        {
            Ok(Ok(v)) => v,
            Ok(Err(e)) => {
                log::error!(
                    "test tot server: {} latency: tot server to remote error: {}",
                    server.name,
                    e
                );
                return Err(anyhow!(
                    "test tot server: {} latency: tot server to remote error: {}",
                    server.name,
                    e
                ));
            }
            Err(_) => {
                log::error!(
                    "test tot server: {} latency: tot server to remote timeout",
                    server.name
                );
                return Err(anyhow!(
                    "test tot server: {} latency: tot server to remote timeout",
                    server.name
                ));
            }
        };

        let total_latency =
            to_tot_server_latency.as_micros() as u64 + tot_server_to_remote_latency[0].1 as u64;
        log::info!(
            "test tot server: {} latency: to tot server: {:?}micro sec, tot server to remote: {remote:?}:  {:?}micro sec total: {:?}micro sec",
            server.name,
            to_tot_server_latency.as_micros(),
            tot_server_to_remote_latency[0].1,
            total_latency
        );

        Ok(std::time::Duration::from_micros(total_latency))
    }
}
