use std::{
    net::{Ip<PERSON>ddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use rand::Rng;

use anyhow::{anyhow, bail};
use common::{
    app_message::{LatencyTestMethod, Protocol, TestLatencyInfo, TestLatencyType},
    constant::TCP_CONNECT_TIMEOUT,
    into_socket_addr, TotConfig,
};
use private_tun::{
    address::Address,
    snell_impl_ver::{client_zfc::ConnType, config::ClientConfig, TestLatencyMethod},
};
use tokio::net::TcpStream;

use crate::{
    backend_transport::BackendTransport,
    protocol::{
        hammer::{HammerClientLite, HammerHandlerForWorkerTun},
        tot::{
            session::{tot_test_latency_for_target_list, TotSession},
            TotClientConfigWrapper, TotClientForWorkerTun,
        },
    },
    WorkerAppContext,
};

pub async fn direct_ping_test(
    ctx: Arc<WorkerAppContext>,
    ip_addr: IpAddr,
    sample_count: usize,
) -> anyhow::Result<u64> {
    direct_ping_test_with_size(ctx, ip_addr, 64, sample_count).await
}

/// SmokePing风格的延迟统计结果
#[derive(Debug, Clone)]
pub struct LatencyStatistics {
    pub samples: Vec<u64>, // 所有成功的延迟样本（微秒）
    pub failed_count: u32, // 失败的测试次数
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub median_latency_us: Option<u64>,
    pub avg_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,
    pub packet_loss_rate: f32,
}

impl LatencyStatistics {
    /// 转换为SmokePing风格的ping样本数组（包含None表示失败的ping）
    pub fn to_ping_samples(&self, target_count: usize) -> Vec<Option<u64>> {
        let mut ping_samples = Vec::with_capacity(target_count);

        // 添加成功的样本
        for &sample in &self.samples {
            ping_samples.push(Some(sample));
        }

        // 添加失败的样本 (None)
        for _ in 0..self
            .failed_count
            .min(target_count as u32 - self.samples.len() as u32)
        {
            ping_samples.push(None);
        }

        // 如果样本不足，用None填充到目标数量
        while ping_samples.len() < target_count {
            ping_samples.push(None);
        }

        // 如果样本过多，截断到目标数量
        ping_samples.truncate(target_count);

        ping_samples
    }

    /// 获取SmokePing标准的loss_count (基于20个ping的标准)
    pub fn get_loss_count(&self, total_samples: u32) -> u32 {
        self.failed_count.min(total_samples)
    }
}

impl LatencyStatistics {
    pub fn calculate_from_samples(mut samples: Vec<u64>, failed_count: u32) -> Self {
        let total_tests = samples.len() as u32 + failed_count;
        let packet_loss_rate = if total_tests > 0 {
            (failed_count as f32 / total_tests as f32) * 100.0
        } else {
            100.0
        };

        if samples.is_empty() {
            return Self {
                samples,
                failed_count,
                min_latency_us: None,
                max_latency_us: None,
                median_latency_us: None,
                avg_latency_us: None,
                stddev_latency_us: None,
                packet_loss_rate,
            };
        }

        samples.sort();
        let min_latency_us = Some(samples[0]);
        let max_latency_us = Some(*samples.last().unwrap());

        let median_latency_us = if samples.len() % 2 == 0 {
            let mid = samples.len() / 2;
            Some((samples[mid - 1] + samples[mid]) / 2)
        } else {
            Some(samples[samples.len() / 2])
        };

        let avg_latency_us = Some(samples.iter().sum::<u64>() / samples.len() as u64);

        let stddev_latency_us = if samples.len() > 1 {
            let avg = avg_latency_us.unwrap() as f64;
            let variance = samples
                .iter()
                .map(|&x| (x as f64 - avg).powi(2))
                .sum::<f64>()
                / samples.len() as f64;
            Some(variance.sqrt())
        } else {
            Some(0.0)
        };

        Self {
            samples,
            failed_count,
            min_latency_us,
            max_latency_us,
            median_latency_us,
            avg_latency_us,
            stddev_latency_us,
            packet_loss_rate,
        }
    }
}

pub async fn direct_ping_test_with_size(
    ctx: Arc<WorkerAppContext>,
    ip_addr: IpAddr,
    packet_size: usize,
    sample_count: usize,
) -> anyhow::Result<u64> {
    let stats = direct_ping_test_with_statistics(ctx, ip_addr, packet_size, sample_count).await?;
    stats
        .avg_latency_us
        .ok_or_else(|| anyhow!("All ping tests failed"))
}

/// SmokePing风格的ping测试，支持多次采样和统计计算
pub async fn direct_ping_test_with_statistics(
    ctx: Arc<WorkerAppContext>,
    ip_addr: IpAddr,
    packet_size: usize,
    sample_count: usize,
) -> anyhow::Result<LatencyStatistics> {
    let mut handles = Vec::with_capacity(sample_count);
    let mut config = surge_ping::Config::default();
    if ip_addr.is_ipv4() {
        config.kind = surge_ping::ICMP::V4;
    } else {
        config.kind = surge_ping::ICMP::V6;
    }
    let client = if ip_addr.is_ipv4() {
        ctx.ping_client.v4_client()
    } else {
        ctx.ping_client.v6_client()
    };

    {
        let mut rng = rand::rng();
        for _i in 0..sample_count {
            let ip_addr = ip_addr.clone();
            let client_clone = client.clone();

            // Create random payload with specified size
            let payload: Vec<u8> = (0..packet_size).map(|_| rng.random::<u8>()).collect();
            let ping_id = surge_ping::PingIdentifier(rng.random::<u16>());
            let ping_seq = surge_ping::PingSequence(rng.random::<u16>());
            handles.push(tokio::spawn(async move {
                let mut pingger = client_clone.pinger(ip_addr, ping_id).await;
                pingger.ping(ping_seq, &payload).await
            }));
        }
    }

    let mut successful_samples = Vec::new();
    let mut failed_count = 0u32;

    for handle in handles {
        match handle.await {
            Ok(Ok((_, duration))) => {
                successful_samples.push(duration.as_micros() as u64);
            }
            _ => {
                failed_count += 1;
            }
        }
    }

    Ok(LatencyStatistics::calculate_from_samples(
        successful_samples,
        failed_count,
    ))
}

pub async fn hammer_backend_latency_test(
    fwd_client: &HammerHandlerForWorkerTun,
    ip_addr: IpAddr,
    port: Option<u16>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let all_servers = fwd_client
        .client()
        .lock()
        .await
        .servers_iter()
        .cloned()
        .collect::<Vec<_>>();
    let mut result_rxs = Vec::with_capacity(all_servers.len());
    for server in all_servers {
        let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
        fwd_client
            .client()
            .lock()
            .await
            .push_evt(ConnType::TestLatency {
                rst_tx: rst_tx,
                target: Address::Socket((ip_addr, port.unwrap_or(80)).into()),
                server_name: server.name.clone(),
                method: into_test_latency_method(method),
                piped_stream: None,
                reuse_tcp: true,
            })
            .await
            .map_err(|_| anyhow!("hammer_backend_latency_test push error"))?;
        result_rxs.push(rst_rx);
    }
    let mut best_latency = 65535000;
    let mut need_recheck_server_health = false;
    for rst_rx in result_rxs {
        let latency = tokio::time::timeout(Duration::from_secs(5), rst_rx).await;
        match latency {
            Ok(Ok(Ok(latency))) => {
                if latency > 6000000 {
                    need_recheck_server_health = true;
                }
                if latency < best_latency {
                    best_latency = latency;
                }
            }
            _ => {
                need_recheck_server_health = true;
            }
        }
    }
    if need_recheck_server_health {
        fwd_client
            .client()
            .lock()
            .await
            .recheck_server_health()
            .await;
    }

    log::info!(
        "hammer_backend_latency_test remote result: {best_latency} micros sec for target: {ip_addr}:{port:?} method: {method:?}"
    );
    Ok(best_latency as u64)
}

pub async fn tot_backend_latency_test(
    fwd_client: &TotClientForWorkerTun,
    ip_addr: IpAddr,
    port: Option<u16>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let remote = Address::Socket((ip_addr, port.unwrap_or(80)).into());
    let method = into_test_latency_method(method);
    let latency_result = tot_test_latency_for_target_list(
        fwd_client.tot_server_handler(),
        fwd_client.fwd_handler(),
        &[remote.clone()],
        method,
    )
    .await?;
    if latency_result.len() == 0 {
        log::error!(
            "tot backend test latency failed for target: {} no result method: {:?}",
            remote,
            method
        );
        return Err(anyhow::anyhow!(
            "tot backend test latency failed for target: {} no result method: {:?}",
            remote,
            method
        ));
    }
    Ok(latency_result[0].1 as u64)
}

pub async fn direct_tcpping_test(
    out_ip_addr: &Option<IpAddr>,
    ip_addr: IpAddr,
    port: u16,
    sample_count: usize,
) -> anyhow::Result<u64> {
    let stats =
        direct_tcpping_test_with_statistics(out_ip_addr, ip_addr, port, sample_count).await?;
    stats
        .avg_latency_us
        .ok_or_else(|| anyhow!("All ping tests failed"))
}

pub async fn direct_tcpping_test_with_statistics(
    out_ip_addr: &Option<IpAddr>,
    ip_addr: IpAddr,
    port: u16,
    sample_count: usize,
) -> anyhow::Result<LatencyStatistics> {
    let mut tasks = Vec::with_capacity(sample_count);
    for _i in 0..sample_count {
        let out_ip_addr_clone = out_ip_addr.clone();
        tasks.push(tokio::spawn(async move {
            let now = std::time::Instant::now();
            let target_addr = (ip_addr, port).into();
            let stream = if let Some(out_ip_addr) = out_ip_addr_clone {
                log::info!(
                "direct_tcpping_test with out_ip_addr: {out_ip_addr} ip_addr: {ip_addr} port: {port}"
            );
                match out_ip_addr {
                    IpAddr::V4(_v4) => {
                        let socket = tokio::net::TcpSocket::new_v4()?;
                        socket.bind(SocketAddr::new(out_ip_addr, 0))?;
                        tokio::time::timeout(
                            Duration::from_secs(TCP_CONNECT_TIMEOUT),
                            socket.connect(target_addr),
                        )
                        .await
                    }
                    IpAddr::V6(_v6) => {
                        let socket = tokio::net::TcpSocket::new_v6()?;
                        socket.bind(SocketAddr::new(out_ip_addr, 0))?;
                        tokio::time::timeout(
                            Duration::from_secs(TCP_CONNECT_TIMEOUT),
                            socket.connect(target_addr),
                        )
                        .await
                    }
                }
            } else {
                tokio::time::timeout(
                    Duration::from_secs(TCP_CONNECT_TIMEOUT),
                    TcpStream::connect(target_addr),
                )
                .await
            };
            // 尝试在5秒内建立连接
            match stream {
                Ok(connect_result) => {
                    match connect_result {
                        Ok(_stream) => {
                            // 连接成功，返回延迟时间
                            Ok(now.elapsed().as_micros() as u64)
                        }
                        Err(e) => {
                            // 连接失败，返回错误
                            Err(anyhow!("test latency for: {ip_addr}:{port} failed: {e}"))
                        }
                    }
                }
                Err(_) => {
                    // 超时未连接成功，返回超时错误
                    Err(anyhow!(
                        "test latency for: {ip_addr}:{port} failed: timeout"
                    ))
                }
            }
        }));
    }

    let mut successful_samples = Vec::new();
    let mut failed_count = 0u32;

    for handle in tasks {
        match handle.await {
            Ok(Ok(duration)) => {
                successful_samples.push(duration);
            }
            _ => {
                failed_count += 1;
            }
        }
    }

    Ok(LatencyStatistics::calculate_from_samples(
        successful_samples,
        failed_count,
    ))
}
pub fn into_test_latency_method(method: LatencyTestMethod) -> TestLatencyMethod {
    match method {
        LatencyTestMethod::Tcpping => private_tun::snell_impl_ver::TestLatencyMethod::Tcpping,
        LatencyTestMethod::Icmp => private_tun::snell_impl_ver::TestLatencyMethod::Icmp,
    }
}
pub async fn fwd_server_to_remote_test(
    ip_addr: IpAddr,
    port: Option<u16>,
    fwd_client: &HammerClientLite,
    fwd_server: Box<str>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let latency = fwd_client
        .test_latency(
            fwd_server.as_ref(),
            ip_addr,
            port,
            into_test_latency_method(method),
        )
        .await?;
    Ok(latency as u64)
}

pub async fn tot_direct_remote_test(
    tot_session: &Arc<TotSession>,
    remote: &Address,
    method: TestLatencyMethod,
) -> anyhow::Result<u64> {
    let latency = tot_session.test_latency(remote, method).await?;
    Ok(latency as u64)
}

pub async fn setup_fwd_server(
    ctx: Arc<WorkerAppContext>,
    fwd_configs: Vec<Protocol>,
) -> anyhow::Result<(Vec<HammerClientLite>, Vec<Arc<TotSession>>)> {
    if fwd_configs.len() == 0 {
        return Ok((vec![], vec![]));
    }
    let hammer_clients = fwd_configs
        .iter()
        .filter_map(|fwd_config| match fwd_config {
            Protocol::Hammer { config } => serde_json::from_str::<ClientConfig>(config)
                .ok()
                .into_iter()
                .filter_map(|x| HammerClientLite::new(x).ok())
                .next(),
            _ => None,
        })
        .collect();
    let tot_session_manager = ctx.tot_manager.get_ctx().tot_session_manager_ref();

    let mut tot_sessions = Vec::new();
    for fwd_config in fwd_configs {
        let tot_config: TotConfig = match fwd_config {
            Protocol::Tot { config } => serde_json::from_str(&config)?,
            _ => {
                continue;
            }
        };
        let tot_config_wrapper = TotClientConfigWrapper::from(tot_config);
        match tot_session_manager
            .get_session_by_config(&tot_config_wrapper)
            .await
        {
            Some(session) => {
                tot_sessions.push(session);
            }
            None => {}
        };
    }
    Ok((hammer_clients, tot_sessions))
}

pub async fn test_latency_with_backend_transport_with_statistics(
    ctx: Arc<WorkerAppContext>,
    remote: &SocketAddr,
    test_method: LatencyTestMethod,
    icmp_packet_size: Option<usize>,
) -> anyhow::Result<LatencyStatistics> {
    match &ctx.backend_transport {
        Some(BackendTransport::Hammer(fwd)) => {
            // SmokePing标准采样数量
            let sample_count = 20;
            let mut tasks = Vec::with_capacity(sample_count);
            for _i in 0..sample_count {
                let ip_addr = remote.ip();
                let port = remote.port();
                let fwd_clone = fwd.clone();
                tasks.push(tokio::spawn(async move {
                    hammer_backend_latency_test(&fwd_clone, ip_addr, Some(port), test_method).await
                }));
            }
            let mut latency_samples = Vec::new();
            let mut failed_count = 0u32;
            for handle in tasks {
                match handle.await {
                    Ok(Ok(latency)) => {
                        if latency > 6000000 {
                            failed_count += 1;
                        } else {
                            latency_samples.push(latency);
                        }
                    }
                    _ => {
                        failed_count += 1;
                    }
                }
            }
            Ok(LatencyStatistics::calculate_from_samples(
                latency_samples,
                failed_count,
            ))
        }
        Some(BackendTransport::Tot(fwd)) => {
            // SmokePing标准采样数量
            let sample_count = 20;
            let mut tasks = Vec::with_capacity(sample_count);
            for _i in 0..sample_count {
                let ip_addr = remote.ip();
                let port = remote.port();
                let fwd_clone = fwd.clone();
                tasks.push(tokio::spawn(async move {
                    tot_backend_latency_test(&fwd_clone, ip_addr, Some(port), test_method).await
                }));
            }
            let mut latency_samples = Vec::new();
            let mut failed_count = 0u32;
            for handle in tasks {
                match handle.await {
                    Ok(Ok(latency)) => {
                        if latency > 6000000 {
                            failed_count += 1;
                        } else {
                            latency_samples.push(latency);
                        }
                    }
                    _ => {
                        failed_count += 1;
                    }
                }
            }
            Ok(LatencyStatistics::calculate_from_samples(
                latency_samples,
                failed_count,
            ))
        }
        _ => match test_method {
            LatencyTestMethod::Icmp => {
                // SmokePing标准采样数量
                let sample_count = 20;
                if let Some(icmp_packet_size) = icmp_packet_size {
                    let stats = direct_ping_test_with_statistics(
                        ctx,
                        remote.ip(),
                        icmp_packet_size,
                        sample_count,
                    )
                    .await?;
                    Ok(stats)
                } else {
                    let stats =
                        direct_ping_test_with_statistics(ctx, remote.ip(), 64, sample_count)
                            .await?;
                    Ok(stats)
                }
            }
            LatencyTestMethod::Tcpping => {
                // SmokePing标准采样数量
                let sample_count = 20;
                let stats = direct_tcpping_test_with_statistics(
                    &ctx.out_ip_addr,
                    remote.ip(),
                    remote.port(),
                    sample_count,
                )
                .await?;
                Ok(stats)
            }
        },
    }
}
pub async fn test_latency_msg(
    ctx: Arc<WorkerAppContext>,
    info: &TestLatencyInfo,
    hammer_clients: &Vec<HammerClientLite>,
    tot_sessions: &Vec<Arc<TotSession>>,
) -> Result<u64, anyhow::Error> {
    let latency = match &info.test_type {
        TestLatencyType::FwdServerToRemote { fwd_name, remote } => {
            let Some(client) = hammer_clients
                .iter()
                .find(|c| c.get_server_by_name(&fwd_name).is_some())
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            fwd_server_to_remote_test(
                remote.ip(),
                Some(remote.port()),
                client,
                fwd_name.to_string().into_boxed_str(),
                info.test_method,
            )
            .await
        }
        TestLatencyType::DirectRemote { remote } => match &ctx.backend_transport {
            Some(BackendTransport::Hammer(fwd)) => {
                hammer_backend_latency_test(fwd, remote.ip(), Some(remote.port()), info.test_method)
                    .await
            }
            Some(BackendTransport::Tot(fwd)) => {
                tot_backend_latency_test(fwd, remote.ip(), Some(remote.port()), info.test_method)
                    .await
            }
            _ => match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, remote.ip(), 3).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(&ctx.out_ip_addr, remote.ip(), remote.port(), 1).await
                }
            },
        },
        TestLatencyType::TotDirectRemote { remote } => {
            let Some(tot_session) = tot_sessions.first() else {
                bail!("tot_session not found");
            };
            tot_direct_remote_test(
                tot_session,
                &Address::Socket(remote.clone()),
                into_test_latency_method(info.test_method),
            )
            .await
        }

        TestLatencyType::ToFwdServer { fwd_name } => {
            let Some(fwd_server_addr) =
                hammer_clients
                    .iter()
                    .find_map(|c| match c.get_server_by_name(&fwd_name) {
                        Some(addr) => Some(addr.server_addr.clone()),
                        None => None,
                    })
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            let fwd_server_addr = into_socket_addr(&fwd_server_addr).await?;
            match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, fwd_server_addr.ip(), 3).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(
                        &ctx.out_ip_addr,
                        fwd_server_addr.ip(),
                        fwd_server_addr.port(),
                        1,
                    )
                    .await
                }
            }
        }
    };
    Ok(latency?)
}
