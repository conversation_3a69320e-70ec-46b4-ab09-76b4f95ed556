use std::{
    collections::{<PERSON>hMap, HashSet},
    net::{Ip<PERSON>ddr, SocketAddr},
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::{anyhow, Result};
use chrono::Utc;
use common::app_message::{LatencyMonitorConfig, LatencyTestMethod, LatencyTestResult};
use futures::StreamExt;
use log::{error, info, warn};
use tokio::{
    sync::{mpsc, RwLock},
    time::interval,
};
use tokio_util::sync::CancellationToken;

use crate::WorkerAppContext;

#[derive(Debug, Clone)]
pub struct MonitoringTask {
    pub config: LatencyMonitorConfig,
    pub next_run: Instant,
    pub consecutive_failures: u32,
}

impl MonitoringTask {
    pub fn new(config: LatencyMonitorConfig) -> Self {
        Self {
            next_run: Instant::now(),
            consecutive_failures: 0,
            config,
        }
    }

    pub fn should_run(&self) -> bool {
        Instant::now() >= self.next_run
    }

    pub fn schedule_next_run(&mut self) {
        self.next_run = Instant::now() + Duration::from_secs(self.config.interval as u64);
    }

    pub fn record_failure(&mut self) {
        self.consecutive_failures += 1;
    }

    pub fn record_success(&mut self) {
        self.consecutive_failures = 0;
    }

    pub fn should_alert(&self) -> bool {
        // 使用配置中的告警阈值，默认3次连续失败
        let threshold = self.config.alert_threshold;
        self.consecutive_failures >= threshold
    }
}

pub struct LatencyMonitoringService {
    ctx: Arc<WorkerAppContext>,
    tasks: Arc<RwLock<HashMap<i32, MonitoringTask>>>, // config_id -> task
    dns_cache: Arc<RwLock<HashMap<String, Vec<IpAddr>>>>, // 域名 -> IP地址列表
}

impl LatencyMonitoringService {
    pub fn new(ctx: Arc<WorkerAppContext>) -> Self {
        Self {
            ctx,
            tasks: Arc::new(RwLock::new(HashMap::new())),
            dns_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 启动延迟监控服务
    pub async fn start(
        &self,
        result_tx: mpsc::Sender<LatencyTestResult>,
        cancel_token: CancellationToken,
    ) -> Result<()> {
        info!("Starting latency monitoring service for worker");

        let tasks = self.tasks.clone();
        let ctx = self.ctx.clone();
        let dns_cache = self.dns_cache.clone();
        let cancel_token_clone = cancel_token.clone();
        // 每5秒检查一次需要执行的任务
        tokio::spawn(async move {
            let mut check_interval = interval(Duration::from_secs(5));
            loop {
                tokio::select! {
                    _ = check_interval.tick() => {
                    }
                    _ = cancel_token_clone.cancelled() => {
                        info!("Latency monitoring service cancelled for running pending tests");
                        break;
                    }
                }
                if let Err(e) = Self::run_pending_tests(
                    tasks.clone(),
                    ctx.clone(),
                    dns_cache.clone(),
                    result_tx.clone(),
                )
                .await
                {
                    error!("Error running pending latency tests: {}", e);
                }
            }
            return;
        });

        // 每30分钟清理DNS缓存
        let dns_cache_cleanup = self.dns_cache.clone();
        tokio::spawn(async move {
            let mut cleanup_interval = interval(Duration::from_secs(1800)); // 30分钟
            loop {
                tokio::select! {
                    _ = cleanup_interval.tick() => {
                    }
                    _ = cancel_token.cancelled() => {
                        info!("Latency monitoring service cancelled for dns cache cleanup");
                        break;
                    }
                }
                let mut cache = dns_cache_cleanup.write().await;
                cache.clear();
                info!("DNS cache cleared");
            }
        });

        Ok(())
    }

    /// 更新延迟监控配置
    pub async fn update_configs(&self, configs: Vec<LatencyMonitorConfig>) -> Result<()> {
        info!(
            "Updating latency monitoring configs: {} tasks",
            configs.len()
        );
        let remote_all_config_ids = configs.iter().map(|c| c.config_id).collect::<HashSet<_>>();
        let mut tasks = self.tasks.write().await;

        for config in configs {
            let existing_task = tasks.get(&config.config_id);
            let need_update = if let Some(task) = existing_task {
                task.config != config
            } else {
                true
            };
            if need_update {
                let task = MonitoringTask::new(config.clone());
                tasks.insert(config.config_id, task);
                info!(
                    "Added/modify monitoring task for config_id: {}, target: {} config: {:?}",
                    config.config_id, config.target_address, config
                );
            } else {
                info!(
                    "No need to update monitoring task for config_id: {}",
                    config.config_id
                );
            }
        }

        // remove tasks that are not in the new configs
        tasks.retain(|config_id, _| remote_all_config_ids.contains(config_id));

        Ok(())
    }

    /// 运行待执行的延迟测试
    async fn run_pending_tests(
        tasks: Arc<RwLock<HashMap<i32, MonitoringTask>>>,
        ctx: Arc<WorkerAppContext>,
        dns_cache: Arc<RwLock<HashMap<String, Vec<IpAddr>>>>,
        result_tx: mpsc::Sender<LatencyTestResult>,
    ) -> Result<()> {
        let mut pending_tasks = Vec::new();

        // 收集需要执行的任务
        {
            let tasks_read = tasks.read().await;
            for (config_id, task) in tasks_read.iter() {
                if task.should_run() {
                    pending_tasks.push((*config_id, task.config.clone()));
                }
            }
        }

        if pending_tasks.is_empty() {
            return Ok(());
        }

        info!("Running {} pending latency tests", pending_tasks.len());
        let mut results = Vec::new();
        let task_results = futures::stream::iter(pending_tasks)
            .map(|(config_id, config)| {
                let ctx_clone = ctx.clone();
                let tasks_clone = tasks.clone();
                let dns_cache_clone = dns_cache.clone();
                async move {
                let result = Self::execute_latency_test(ctx_clone, dns_cache_clone, &config).await;
                // 更新任务状态
                {
                    let mut tasks_write = tasks_clone.write().await;
                    if let Some(task) = tasks_write.get_mut(&config_id) {
                        task.schedule_next_run();

                        match &result {
                            Ok(test_result) => {
                                // SmokePing风格的成功判断：基于loss_count而不是success字段
                                let is_success = test_result.loss_count < 20; // 非全丢包即视为成功
                                if is_success {
                                    task.record_success();
                                } else {
                                    task.record_failure();
                                    if task.should_alert() {
                                        warn!(
                                            "Latency test alert: {} consecutive failures for config_id: {}, target: {}, loss_count: {}/20",
                                            task.consecutive_failures,
                                            config_id,
                                            config.target_address,
                                            test_result.loss_count
                                        );
                                    }
                                }
                            }
                            Err(e) => {
                                task.record_failure();
                                warn!("Latency test failed for config_id: {}: {}", config_id, e);
                            }
                        }
                    }
                }
                (config, result)
            }
    })
            .buffer_unordered(32)
            .collect::<Vec<_>>().await;

        for (config, result) in task_results {
            match result {
                Ok(test_result) => results.push(test_result),
                Err(e) => {
                    // 创建SmokePing风格的失败结果
                    results.push(LatencyTestResult {
                        config_id: config.config_id,
                        server_id: config.server_id,
                        target_address: config.target_address.clone(),
                        timestamp: Utc::now(),

                        // SmokePing核心字段 - 全丢包
                        loss_count: 20, // SmokePing标准的20个全失败
                        median_latency_us: None,
                        uptime: Some(3600),           // 默认1小时正常运行时间
                        ping_samples: vec![None; 20], // 20个全失败的ping

                        // 统计字段 - 全部为None
                        avg_latency_us: None,
                        min_latency_us: None,
                        max_latency_us: None,
                        stddev_latency_us: None,
                        total_samples: 20,

                        // 兼容性字段
                        error_msg: Some(e.to_string()),
                        test_round: Some(1),
                    });
                }
            }
        }

        // TODO: 将结果发送给控制器
        // 这里需要实现向zf-controler发送结果的逻辑
        Self::send_results_to_controller(result_tx, results).await?;

        Ok(())
    }

    /// 执行单个延迟测试
    async fn execute_latency_test(
        ctx: Arc<WorkerAppContext>,
        dns_cache: Arc<RwLock<HashMap<String, Vec<IpAddr>>>>,
        config: &LatencyMonitorConfig,
    ) -> Result<LatencyTestResult> {
        // 解析目标地址
        let target_addrs = Self::resolve_target_address(dns_cache, &config.target_address).await?;
        if target_addrs.is_empty() {
            return Err(anyhow!(
                "No IP addresses resolved for target: {}",
                config.target_address
            ));
        }

        let target_addr = target_addrs[0]; // 使用第一个解析的IP地址
        let target_socket_addr = SocketAddr::new(target_addr, config.target_port.unwrap_or(80));

        // SmokePing标准采样次数
        let sample_count = 20; // SmokePing 标准值
        let test_round = 1; // 可以是时间戳或序列号

        let latency_result = match config.test_type.to_lowercase().as_str() {
            "icmp" => {
                let packet_size = config.packet_size.unwrap_or(64) as usize;
                // 使用SmokePing标准的20个样本ICMP测试
                crate::latency::test_latency_with_backend_transport_with_statistics(
                    ctx,
                    &target_socket_addr,
                    LatencyTestMethod::Icmp,
                    Some(packet_size),
                )
                .await
            }
            "tcp" => {
                // 使用SmokePing标准的20个样本TCP测试
                crate::latency::test_latency_with_backend_transport_with_statistics(
                    ctx,
                    &target_socket_addr,
                    LatencyTestMethod::Tcpping,
                    None,
                )
                .await
            }
            _ => {
                return Err(anyhow!("Unsupported test type: {}", config.test_type));
            }
        };

        match latency_result {
            Ok(stats) => {
                let success = stats.packet_loss_rate < 100.0;

                // 转换为SmokePing风格的LatencyTestResult
                Ok(LatencyTestResult {
                    config_id: config.config_id,
                    server_id: config.server_id,
                    target_address: config.target_address.clone(),
                    timestamp: Utc::now(),

                    // SmokePing核心字段
                    loss_count: stats.get_loss_count(sample_count as u32),
                    median_latency_us: stats.median_latency_us,
                    uptime: Some(3600), // 默认1小时正常运行时间
                    ping_samples: stats.to_ping_samples(sample_count),

                    // 统计字段
                    avg_latency_us: stats.avg_latency_us,
                    min_latency_us: stats.min_latency_us,
                    max_latency_us: stats.max_latency_us,
                    stddev_latency_us: stats.stddev_latency_us,
                    total_samples: sample_count as u32,

                    // 兼容性字段
                    error_msg: if !success && stats.packet_loss_rate >= 100.0 {
                        Some("All ping tests failed".to_string())
                    } else {
                        None
                    },
                    test_round: Some(test_round),
                })
            }
            Err(e) => {
                // 完全失败的情况下，创建SmokePing风格的全丢包数据
                Ok(LatencyTestResult {
                    config_id: config.config_id,
                    server_id: config.server_id,
                    target_address: config.target_address.clone(),
                    timestamp: Utc::now(),

                    // SmokePing核心字段 - 全丢包
                    loss_count: sample_count as u32, // 全部失败
                    median_latency_us: None,
                    uptime: Some(3600), // 默认1小时正常运行时间
                    ping_samples: vec![None; sample_count], // 20个全失败的ping

                    // 统计字段 - 全部为None
                    avg_latency_us: None,
                    min_latency_us: None,
                    max_latency_us: None,
                    stddev_latency_us: None,
                    total_samples: sample_count as u32,

                    // 兼容性字段
                    error_msg: Some(e.to_string()),
                    test_round: Some(test_round),
                })
            }
        }
    }

    /// 解析目标地址（支持域名解析和DNS缓存）
    async fn resolve_target_address(
        dns_cache: Arc<RwLock<HashMap<String, Vec<IpAddr>>>>,
        target_address: &str,
    ) -> Result<Vec<IpAddr>> {
        // 尝试直接解析为IP地址
        if let Ok(ip_addr) = target_address.parse::<IpAddr>() {
            return Ok(vec![ip_addr]);
        }

        // 检查DNS缓存
        {
            let cache = dns_cache.read().await;
            if let Some(cached_addrs) = cache.get(target_address) {
                if !cached_addrs.is_empty() {
                    return Ok(cached_addrs.clone());
                }
            }
        }

        // 执行DNS解析
        match tokio::time::timeout(
            Duration::from_secs(5),
            tokio::net::lookup_host((target_address, 80)),
        )
        .await
        {
            Ok(Ok(addr_iter)) => {
                let addrs: Vec<IpAddr> = addr_iter.map(|addr| addr.ip()).collect();
                if !addrs.is_empty() {
                    // 更新DNS缓存
                    let mut cache = dns_cache.write().await;
                    cache.insert(target_address.to_string(), addrs.clone());
                    Ok(addrs)
                } else {
                    Err(anyhow!(
                        "No IP addresses found for domain: {}",
                        target_address
                    ))
                }
            }
            Ok(Err(e)) => Err(anyhow!(
                "DNS resolution failed for {}: {}",
                target_address,
                e
            )),
            Err(_e) => Err(anyhow!(
                "DNS resolution failed for {}: timeout",
                target_address,
            )),
        }
    }

    /// 向控制器发送测试结果
    async fn send_results_to_controller(
        result_tx: mpsc::Sender<LatencyTestResult>,
        results: Vec<LatencyTestResult>,
    ) -> Result<()> {
        if results.is_empty() {
            return Ok(());
        }

        info!(
            "Would send {} latency test results to controller",
            results.len()
        );

        // 临时实现：记录结果到日志
        for result in &results {
            if let Err(e) = result_tx.send(result.clone()).await {
                error!("Failed to send latency test result to controller: {}", e);
            }
            // SmokePing风格的日志输出，包含丢包计数和中位数延迟
            let is_success = result.loss_count < 20; // 非全丢包即视为成功
            if is_success {
                info!(
                    "Latency test success: config_id={}, target={}, loss_count={}/20, median={}μs, avg={}μs",
                    result.config_id,
                    result.target_address,
                    result.loss_count,
                    result.median_latency_us.map_or("N/A".to_string(), |v| v.to_string()),
                    result.avg_latency_us.map_or("N/A".to_string(), |v| v.to_string())
                );
            } else {
                warn!(
                    "Latency test failed: config_id={}, target={}, loss_count={}/20, error={}",
                    result.config_id,
                    result.target_address,
                    result.loss_count,
                    result.error_msg.as_deref().unwrap_or("Unknown error")
                );
            }
        }

        Ok(())
    }
}
