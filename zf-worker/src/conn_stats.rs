use std::{net::SocketAddr, sync::Arc};

use chrono::{DateTime, Utc};
use dashmap::DashMap;

const MAX_CONNECTIONS_PER_PORT: usize = 1000;

#[derive(Debug, Clone)]
pub enum ConnectionType {
    Tcp,
    Udp,
}

#[derive(Debu<PERSON>, Clone)]
pub struct ConnStats {
    pub id: uuid::Uuid,
    pub start_time: DateTime<Utc>,
    pub client_addr: SocketAddr,
    pub target_addr: SocketAddr,
    pub forwarders: Option<Vec<String>>,
    pub conn_type: ConnectionType,
}

impl ConnStats {
    pub fn new(
        client_addr: SocketAddr,
        target_addr: SocketAddr,
        forwarders: Option<Vec<String>>,
        conn_type: ConnectionType,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4(),
            start_time: Utc::now(),
            client_addr,
            target_addr,
            forwarders,
            conn_type,
        }
    }
}

pub struct ConnStatsManager {
    pub stats: DashMap<u16, Vec<ConnStats>>,
}

pub struct ConnStatsHandle {
    pub stats: Arc<ConnStatsManager>,
    pub port: u16,
    pub id: uuid::Uuid,
}
impl Drop for ConnStatsHandle {
    fn drop(&mut self) {
        self.stats.remove_stats(self.port, self.id);
    }
}

impl ConnStatsManager {
    pub fn new() -> Self {
        Self {
            stats: DashMap::new(),
        }
    }
    pub fn add_stats(
        self: Arc<Self>,
        port: u16,
        client_addr: SocketAddr,
        target_addr: SocketAddr,
        forwarders: Option<Vec<String>>,
        conn_type: ConnectionType,
    ) -> ConnStatsHandle {
        let stats = ConnStats::new(client_addr, target_addr, forwarders, conn_type);
        let id = stats.id.clone();

        let mut entry = self.stats.entry(port).or_insert_with(Vec::new);

        // Enforce memory limit by removing oldest connections if needed
        if entry.len() >= MAX_CONNECTIONS_PER_PORT {
            log::warn!(
                "Port {} reached max connection limit ({}), removing oldest connection",
                port,
                MAX_CONNECTIONS_PER_PORT
            );
            entry.remove(0); // Remove oldest connection
        }

        entry.push(stats);

        ConnStatsHandle {
            stats: self.clone(),
            port,
            id,
        }
    }

    pub fn remove_stats(&self, port: u16, id: uuid::Uuid) {
        let is_empty = self.stats.get_mut(&port).and_then(|mut stats| {
            stats.retain(|s| s.id != id);
            Some(stats.is_empty())
        });
        if is_empty.unwrap_or(false) {
            self.stats.remove(&port);
        }
    }
    pub fn get_all_stats_by_port(&self, port: u16) -> Option<Vec<ConnStats>> {
        self.stats.get(&port).map(|stats| (&*stats).clone())
    }
}
