#!/bin/bash
# InfluxDB 初始化脚本 - 创建数据库和7天保留策略

set -e

# 等待 InfluxDB 启动
echo "等待 InfluxDB 启动..."
while ! curl -sf http://localhost:8086/ping > /dev/null 2>&1; do
    sleep 2
done

echo "InfluxDB 已启动，开始初始化..."

# 创建数据库
echo "创建数据库 ${INFLUXDB_DB}..."
curl -i -XPOST "http://localhost:8086/query" \
  --data-urlencode "q=CREATE DATABASE \"${INFLUXDB_DB}\""

# 创建7天数据保留策略
echo "创建7天数据保留策略..."
curl -i -XPOST "http://localhost:8086/query" \
  --data-urlencode "q=CREATE RETENTION POLICY \"7_days\" ON \"${INFLUXDB_DB}\" DURATION 7d REPLICATION 1 DEFAULT"

# 如果设置了用户，创建用户
if [ ! -z "${INFLUXDB_USER}" ] && [ ! -z "${INFLUXDB_USER_PASSWORD}" ]; then
    echo "创建应用用户 ${INFLUXDB_USER}..."
    curl -i -XPOST "http://localhost:8086/query" \
      --data-urlencode "q=CREATE USER \"${INFLUXDB_USER}\" WITH PASSWORD '${INFLUXDB_USER_PASSWORD}'"
    
    # 授予用户数据库权限
    curl -i -XPOST "http://localhost:8086/query" \
      --data-urlencode "q=GRANT ALL ON \"${INFLUXDB_DB}\" TO \"${INFLUXDB_USER}\""
fi

echo "InfluxDB 初始化完成！"