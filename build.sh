#!/bin/bash

# Frontend embedding build script for zf-auth-server
# This script detects frontend changes and automatically builds both frontend and backend

set -e

FRONTEND_DIR="zfc-license-admin"
BACKEND_DIR="zf-auth-server"
DIST_DIR="$FRONTEND_DIR/dist"

echo "🔧 ZF Auth Server Frontend Embedding Build Script"

# Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ Frontend directory '$FRONTEND_DIR' not found!"
    exit 1
fi

# Check if backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
    echo "❌ Backend directory '$BACKEND_DIR' not found!"
    exit 1
fi

# Function to check if frontend needs rebuilding
needs_frontend_build() {
    # If dist directory doesn't exist, we need to build
    if [ ! -d "$DIST_DIR" ]; then
        echo "📦 Dist directory not found, frontend build required"
        return 0
    fi
    
    # Check if any source files are newer than the dist directory
    if find "$FRONTEND_DIR/src" -newer "$DIST_DIR" -type f | grep -q .; then
        echo "🔍 Source files changed, frontend build required"
        return 0
    fi
    
    # Check if package.json or config files are newer
    for file in "$FRONTEND_DIR/package.json" "$FRONTEND_DIR/vite.config.ts" "$FRONTEND_DIR/package-lock.json"; do
        if [ -f "$file" ] && [ "$file" -nt "$DIST_DIR" ]; then
            echo "🔍 Config file '$file' changed, frontend build required"
            return 0
        fi
    done
    
    echo "✅ Frontend is up to date"
    return 1
}

# Build frontend if needed
if needs_frontend_build; then
    echo "🚀 Building frontend..."
    cd "$FRONTEND_DIR"
    
    # Check if node_modules exists, install dependencies if not
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing frontend dependencies..."
        npm install
    fi
    
    # Build the frontend
    echo "🔨 Building frontend assets..."
    npm run build
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend build completed successfully"
    else
        echo "❌ Frontend build failed!"
        exit 1
    fi
    
    cd ..
else
    echo "⏭️  Skipping frontend build (no changes detected)"
fi

# Build backend
echo "🦀 Building Rust backend with embedded frontend..."
cd "$BACKEND_DIR"

# Build the Rust project
cargo build --release

if [ $? -eq 0 ]; then
    echo "✅ Backend build completed successfully"
    echo ""
    echo "🎉 Build completed! The frontend is now embedded in the zf-auth-server binary."
    echo "📍 Binary location: $BACKEND_DIR/target/release/zf-auth-server"
    echo ""
    echo "To run the server:"
    echo "  cd $BACKEND_DIR && ./target/release/zf-auth-server"
    echo ""
    echo "The frontend will be served at the root path (/) and all API endpoints remain unchanged."
else
    echo "❌ Backend build failed!"
    exit 1
fi