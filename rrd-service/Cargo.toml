[package]
name = "rrd-service"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
warp = "0.3.6"
tokio = { version = "1", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync", "fs"] }
rrd = "0.2.0"
anyhow = { version = "1.0.75", features = ["backtrace"] }
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.107"
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"
env_logger = { version = "0.10", default-features = false, features = ["humantime"] }
base64 = "0.22"
brotli = "8.0.1"
thiserror = "1.0"
rand = "0.9"
tempfile = "3.8"
clap = { version = "4.0", features = ["derive", "env"] }
dotenv = { version = "0.15.0" }
regex = "1.10"

[[bin]]
name = "rrd-service"
path = "src/main.rs"