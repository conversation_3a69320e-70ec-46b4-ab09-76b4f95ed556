use clap::<PERSON>rse<PERSON>;
use std::path::PathBuf;

#[derive(Parser, Debug)]
#[command(name = "rrd-service")]
#[command(about = "RRD service for time series data management")]
#[command(version)]
pub struct Config {
    /// Path to the RRD data directory
    #[arg(long, env = "RRD_DATA_PATH", default_value = "/data/rrd")]
    pub rrd_data_path: PathBuf,

    /// Bind address for the HTTP server
    #[arg(long, env = "BIND_ADDRESS", default_value = "0.0.0.0")]
    pub bind_address: String,

    /// Port number to listen on
    #[arg(short, long, env = "PORT", default_value = "8082")]
    pub port: u16,

    /// Log level (error, warn, info, debug, trace)
    #[arg(long, env = "LOG_LEVEL", default_value = "info")]
    pub log_level: String,
}

impl Config {
    pub fn new() -> Self {
        Self::parse()
    }

    pub fn get_bind_addr(&self) -> ([u8; 4], u16) {
        let octets: Vec<u8> = self
            .bind_address
            .split('.')
            .map(|s| s.parse().unwrap_or(0))
            .collect();

        if octets.len() == 4 {
            ([octets[0], octets[1], octets[2], octets[3]], self.port)
        } else {
            ([0, 0, 0, 0], self.port)
        }
    }
}
