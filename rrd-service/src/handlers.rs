use std::io::Write;
use std::sync::Arc;
use warp::{reject::Rejection, Reply};

use crate::{error::Error, models::*, rrd_manager::RrdManager};

/// 创建RRD文件
pub async fn create_rrd(
    request: CreateRrdRequest,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Creating RRD file for server_id: {}, config_id: {}",
        request.server_id,
        request.config_id
    );

    match rrd_manager
        .create_rrd_file(request.server_id, request.config_id)
        .await
    {
        Ok(_) => {
            log::info!(
                "Successfully created RRD file for server_id: {}, config_id: {}",
                request.server_id,
                request.config_id
            );
            Ok(warp::reply::json(&ApiResponse {
                success: true,
                message: Some("RRD file created successfully".to_string()),
            }))
        }
        Err(e) => {
            log::error!(
                "Failed to create RRD file for server_id: {}, config_id: {}, error: {:?}",
                request.server_id,
                request.config_id,
                e
            );
            Err(warp::reject::custom(Error::RrdError(e)))
        }
    }
}

/// 更新RRD数据
pub async fn update_rrd(
    request: UpdateRrdRequest,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    log::debug!(
        "Updating RRD data for server_id: {}, config_id: {}, timestamp: {}",
        request.server_id,
        request.config_id,
        request.data_point.timestamp
    );

    match rrd_manager
        .update_rrd_data(request.server_id, request.config_id, &request.data_point)
        .await
    {
        Ok(_) => {
            log::debug!(
                "Successfully updated RRD data for server_id: {}, config_id: {}",
                request.server_id,
                request.config_id
            );
            Ok(warp::reply::json(&ApiResponse {
                success: true,
                message: Some("RRD data updated successfully".to_string()),
            }))
        }
        Err(e) => {
            log::error!(
                "Failed to update RRD data for server_id: {}, config_id: {}, error: {:?}",
                request.server_id,
                request.config_id,
                e
            );
            Err(warp::reject::custom(Error::RrdError(e)))
        }
    }
}

/// 生成图表
pub async fn generate_chart(
    request: GenerateChartRequest,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Generating chart for server_id: {}, config_id: {}, time_range: {}, target_address: {}",
        request.server_id,
        request.config_id,
        request.time_range,
        request.target_address
    );

    let image_data = match rrd_manager
        .generate_chart(
            request.server_id,
            request.config_id,
            &request.time_range,
            &request.target_address,
            request.end_time,
        )
        .await
    {
        Ok(data) => {
            log::info!(
                "Successfully generated chart for server_id: {}, config_id: {}, image_size: {} bytes",
                request.server_id, request.config_id, data.len()
            );
            data
        }
        Err(e) => {
            log::error!(
                "Failed to generate chart for server_id: {}, config_id: {}, time_range: {}, error: {:?}",
                request.server_id, request.config_id, request.time_range, e
            );
            return Err(warp::reject::custom(Error::RrdError(e)));
        }
    };

    // 使用brotli压缩图片数据
    log::debug!("Compressing chart image data with brotli");
    let mut compressed_data = Vec::new();
    {
        let mut compressor = brotli::CompressorWriter::new(&mut compressed_data, 4096, 6, 20);
        if let Err(e) = compressor.write_all(&image_data) {
            log::error!("Failed to compress image data: {}", e);
            return Err(warp::reject::custom(Error::RrdError(anyhow::anyhow!(
                "Failed to compress image data: {}",
                e
            ))));
        }
        if let Err(e) = compressor.flush() {
            log::error!("Failed to flush compressor: {}", e);
            return Err(warp::reject::custom(Error::RrdError(anyhow::anyhow!(
                "Failed to flush compressor: {}",
                e
            ))));
        }
    }

    // 编码为base64
    log::debug!(
        "Encoding compressed data to base64, compressed_size: {} bytes",
        compressed_data.len()
    );
    let encoded_data =
        base64::Engine::encode(&base64::engine::general_purpose::STANDARD, &compressed_data);

    log::info!(
        "Chart generation completed for server_id: {}, config_id: {}, final_encoded_size: {} chars",
        request.server_id,
        request.config_id,
        encoded_data.len()
    );

    Ok(warp::reply::json(&GenerateChartResponse {
        image_data: encoded_data,
    }))
}

/// 检查RRD文件是否存在
pub async fn check_exists(
    server_id: i32,
    config_id: i32,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    let exists = rrd_manager.rrd_file_exists(server_id, config_id);

    Ok(warp::reply::json(&ExistsResponse { exists }))
}

/// 删除RRD文件
pub async fn delete_rrd(
    server_id: i32,
    config_id: i32,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    rrd_manager
        .delete_rrd_file(server_id, config_id)
        .await
        .map_err(|e| warp::reject::custom(Error::RrdError(e)))?;

    Ok(warp::reply::json(&ApiResponse {
        success: true,
        message: Some("RRD file deleted successfully".to_string()),
    }))
}

/// 批量初始化历史数据
pub async fn init_historical_data(
    request: InitHistoricalDataRequest,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    rrd_manager
        .initialize_historical_data(
            request.server_id,
            request.config_id,
            &request.history_points,
        )
        .await
        .map_err(|e| warp::reject::custom(Error::RrdError(e)))?;

    Ok(warp::reply::json(&ApiResponse {
        success: true,
        message: Some(format!(
            "Successfully initialized {} historical data points",
            request.history_points.len()
        )),
    }))
}

/// 清理过期文件
pub async fn cleanup(
    request: CleanupRequest,
    rrd_manager: Arc<RrdManager>,
) -> Result<impl Reply, Rejection> {
    rrd_manager
        .cleanup_expired_rrd_files(&request.active_configs)
        .await
        .map_err(|e| warp::reject::custom(Error::RrdError(e)))?;

    Ok(warp::reply::json(&ApiResponse {
        success: true,
        message: Some("Cleanup completed successfully".to_string()),
    }))
}

/// 健康检查
pub async fn health() -> Result<impl Reply, Rejection> {
    Ok(warp::reply::json(&serde_json::json!({
        "status": "healthy",
        "service": "rrd-service",
        "timestamp": chrono::Utc::now()
    })))
}
