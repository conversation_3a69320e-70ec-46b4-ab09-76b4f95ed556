use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// SmokePing风格的延迟历史数据点结构
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct LatencyHistoryPoint {
    pub timestamp: DateTime<Utc>,
    pub config_id: i32,
    pub target_address: String,
    
    // SmokePing标准字段
    /// 丢包计数 (0-20)
    pub loss_count: u32,
    /// 中位数延迟 (微秒)
    pub median_latency_us: Option<u64>,
    /// 正常运行时间
    pub uptime: Option<u64>,
    /// Ping样本数据
    pub ping_samples: Vec<Option<u64>>,
    
    // 兼容性字段
    pub error_msg: Option<String>,
    pub test_round: Option<i32>,
    pub total_samples: u32,
    
    // 统计字段
    pub avg_latency_us: Option<u64>,
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,
}

/// SmokePing风格的RRD数据点结构
/// 
/// 基于SmokePing的成熟设计，使用整数丢包计数而非百分比，
/// 确保loss字段始终有效，避免100%丢包时的NaN问题
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct RrdDataPoint {
    pub timestamp: DateTime<Utc>,
    
    // SmokePing核心字段
    /// 丢包计数 (0-20)，SmokePing标准
    pub loss_count: u32,
    /// 中位数延迟 (微秒)
    pub median_latency_us: Option<u64>,
    /// 正常运行时间 (秒)
    pub uptime: Option<u64>,
    
    // 20个ping样本 (SmokePing标准ping数量)
    /// 个别ping结果，最多20个样本
    pub ping_samples: Vec<Option<u64>>,
    
    // 兼容性和统计字段
    /// 平均延迟 (基于有效样本计算)
    pub avg_latency_us: Option<u64>,
    pub min_latency_us: Option<u64>,
    pub max_latency_us: Option<u64>,
    pub stddev_latency_us: Option<f64>,
    
    // 元数据
    /// 总样本数（通常是20）
    pub total_samples: u32,
}

/// 创建RRD文件请求
#[derive(Debug, Deserialize)]
pub struct CreateRrdRequest {
    pub server_id: i32,
    pub config_id: i32,
}

/// 更新RRD数据请求
#[derive(Debug, Deserialize)]
pub struct UpdateRrdRequest {
    pub server_id: i32,
    pub config_id: i32,
    pub data_point: RrdDataPoint,
}

/// 生成图表请求
#[derive(Debug, Deserialize)]
pub struct GenerateChartRequest {
    pub server_id: i32,
    pub config_id: i32,
    pub time_range: String,
    pub target_address: String,
    pub end_time: Option<DateTime<Utc>>,
}

/// 图表生成响应
#[derive(Debug, Serialize)]
pub struct GenerateChartResponse {
    pub image_data: String, // base64 encoded
}

/// 批量初始化历史数据请求
#[derive(Debug, Deserialize)]
pub struct InitHistoricalDataRequest {
    pub server_id: i32,
    pub config_id: i32,
    pub history_points: Vec<LatencyHistoryPoint>,
}

/// 清理过期文件请求
#[derive(Debug, Deserialize)]
pub struct CleanupRequest {
    pub active_configs: Vec<(i32, i32)>,
}

/// 通用响应
#[derive(Debug, Serialize)]
pub struct ApiResponse {
    pub success: bool,
    pub message: Option<String>,
}

/// 文件存在性检查响应
#[derive(Debug, Serialize)]
pub struct ExistsResponse {
    pub exists: bool,
}

impl RrdDataPoint {
    /// 创建一个SmokePing风格的100%丢包数据点
    pub fn create_full_loss(timestamp: DateTime<Utc>, total_samples: u32) -> Self {
        Self {
            timestamp,
            loss_count: total_samples, // 全部丢包
            median_latency_us: None,
            uptime: None,
            ping_samples: vec![None; total_samples as usize],
            avg_latency_us: None,
            min_latency_us: None,
            max_latency_us: None,
            stddev_latency_us: None,
            total_samples,
        }
    }

    /// 从ping样本创建RrdDataPoint
    pub fn from_ping_samples(
        timestamp: DateTime<Utc>,
        ping_samples: Vec<Option<u64>>,
        uptime: Option<u64>,
    ) -> Self {
        let total_samples = ping_samples.len() as u32;
        let successful_samples: Vec<u64> = ping_samples.iter().filter_map(|&x| x).collect();
        let loss_count = total_samples - successful_samples.len() as u32;

        let (avg, min, max, median, stddev) = if successful_samples.is_empty() {
            (None, None, None, None, None)
        } else {
            let mut sorted = successful_samples.clone();
            sorted.sort();
            
            let avg = Some(successful_samples.iter().sum::<u64>() / successful_samples.len() as u64);
            let min = Some(*sorted.first().unwrap());
            let max = Some(*sorted.last().unwrap());
            let median = Some(sorted[sorted.len() / 2]);
            
            // 计算标准差
            let mean = avg.unwrap() as f64;
            let variance: f64 = successful_samples
                .iter()
                .map(|&x| {
                    let diff = x as f64 - mean;
                    diff * diff
                })
                .sum::<f64>() / successful_samples.len() as f64;
            let stddev = Some(variance.sqrt());
            
            (avg, min, max, median, stddev)
        };

        Self {
            timestamp,
            loss_count,
            median_latency_us: median,
            uptime,
            ping_samples,
            avg_latency_us: avg,
            min_latency_us: min,
            max_latency_us: max,
            stddev_latency_us: stddev,
            total_samples,
        }
    }

    /// 获取成功率 (0.0-1.0)
    pub fn success_rate(&self) -> f64 {
        if self.total_samples == 0 {
            0.0
        } else {
            (self.total_samples - self.loss_count) as f64 / self.total_samples as f64
        }
    }

    /// 获取丢包率百分比 (0.0-100.0)
    pub fn packet_loss_percentage(&self) -> f64 {
        if self.total_samples == 0 {
            100.0
        } else {
            (self.loss_count as f64 / self.total_samples as f64) * 100.0
        }
    }
}