/// 隐私保护工具模块
/// 用于隐藏IP地址和域名中的敏感信息

use regex::Regex;

/// 检测字符串是否为IPv4地址
pub fn is_ipv4(addr: &str) -> bool {
    // IPv4正则表达式
    let ipv4_regex = Regex::new(r"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").unwrap();
    ipv4_regex.is_match(addr)
}

/// 检测字符串是否为IPv6地址
pub fn is_ipv6(addr: &str) -> bool {
    // 简化的IPv6正则表达式
    let ipv6_regex = Regex::new(r"^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$|^(?:[0-9a-fA-F]{1,4}:)*::[0-9a-fA-F]{1,4}(?::[0-9a-fA-F]{1,4})*$").unwrap();
    ipv6_regex.is_match(addr)
}

/// 检测字符串是否为域名
pub fn is_domain(addr: &str) -> bool {
    // 域名正则表达式
    let domain_regex = Regex::new(r"^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$").unwrap();
    domain_regex.is_match(addr) && addr.contains('.')
}

/// 隐藏IPv4地址后两段
/// 例如: ************* -> 192.168.xxx.xxx
pub fn mask_ipv4(ipv4: &str) -> String {
    if !is_ipv4(ipv4) {
        return ipv4.to_string();
    }
    
    let parts: Vec<&str> = ipv4.split('.').collect();
    if parts.len() == 4 {
        format!("{}.{}.xxx.xxx", parts[0], parts[1])
    } else {
        ipv4.to_string()
    }
}

/// 隐藏IPv6地址后六段
/// 例如: 2001:db8:85a3:8d3:1319:8a2e:370:7348 -> 2001:db8:xxx:xxx:xxx:xxx:xxx:xxx
pub fn mask_ipv6(ipv6: &str) -> String {
    if !is_ipv6(ipv6) {
        return ipv6.to_string();
    }
    
    let parts: Vec<&str> = ipv6.split(':').collect();
    if parts.len() >= 2 {
        format!("{}:{}:xxx:xxx:xxx:xxx:xxx:xxx", parts[0], parts[1])
    } else {
        ipv6.to_string()
    }
}

/// 隐藏域名中间部分
/// 例如: example.domain.com -> example.xxx.com
pub fn mask_domain(domain: &str) -> String {
    if !is_domain(domain) {
        return domain.to_string();
    }
    
    let parts: Vec<&str> = domain.split('.').collect();
    match parts.len() {
        0 | 1 => domain.to_string(),
        2 => format!("{}.xxx", parts[0]), // 二级域名
        _ => {
            // 多级域名，保留第一段和最后一段
            let first = parts[0];
            let last = parts[parts.len() - 1];
            format!("{}.xxx.{}", first, last)
        }
    }
}

/// 统一的地址隐藏函数
/// 
/// # Arguments
/// * `address` - IP地址或域名
/// * `show_full` - 是否显示完整地址，默认为false
/// 
/// # Returns
/// * 处理后的地址字符串
pub fn mask_address(address: &str, show_full: bool) -> String {
    if address.is_empty() || show_full {
        return address.to_string();
    }
    
    if is_ipv4(address) {
        mask_ipv4(address)
    } else if is_ipv6(address) {
        mask_ipv6(address)
    } else if is_domain(address) {
        mask_domain(address)
    } else {
        address.to_string()
    }
}

/// 格式化目标地址显示（带端口处理）
/// 
/// # Arguments
/// * `address` - 目标地址
/// * `port` - 可选的端口号
/// * `show_full` - 是否显示完整地址
/// 
/// # Returns
/// * 格式化后的地址字符串
pub fn format_target_address(address: &str, port: Option<u16>, show_full: bool) -> String {
    if address.is_empty() {
        return String::new();
    }
    
    let masked_address = mask_address(address, show_full);
    
    if let Some(port) = port {
        format!("{}:{}", masked_address, port)
    } else {
        masked_address
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_ipv4() {
        assert!(is_ipv4("***********"));
        assert!(is_ipv4("*******"));
        assert!(is_ipv4("0.0.0.0"));
        assert!(is_ipv4("***************"));
        assert!(!is_ipv4("256.1.1.1"));
        assert!(!is_ipv4("192.168.1"));
        assert!(!is_ipv4("example.com"));
    }

    #[test]
    fn test_is_ipv6() {
        assert!(is_ipv6("2001:db8:85a3:8d3:1319:8a2e:370:7348"));
        assert!(is_ipv6("::1"));
        assert!(is_ipv6("::"));
        assert!(!is_ipv6("***********"));
        assert!(!is_ipv6("example.com"));
    }

    #[test]
    fn test_is_domain() {
        assert!(is_domain("example.com"));
        assert!(is_domain("sub.example.com"));
        assert!(is_domain("test.example.co.uk"));
        assert!(!is_domain("***********"));
        assert!(!is_domain("localhost"));
    }

    #[test]
    fn test_mask_ipv4() {
        assert_eq!(mask_ipv4("*************"), "192.168.xxx.xxx");
        assert_eq!(mask_ipv4("*******"), "8.8.xxx.xxx");
        assert_eq!(mask_ipv4("example.com"), "example.com"); // 非IPv4保持不变
    }

    #[test]
    fn test_mask_ipv6() {
        assert_eq!(mask_ipv6("2001:db8:85a3:8d3:1319:8a2e:370:7348"), "2001:db8:xxx:xxx:xxx:xxx:xxx:xxx");
        assert_eq!(mask_ipv6("2001:db8::1"), "2001:db8:xxx:xxx:xxx:xxx:xxx:xxx");
        assert_eq!(mask_ipv6("example.com"), "example.com"); // 非IPv6保持不变
    }

    #[test]
    fn test_mask_domain() {
        assert_eq!(mask_domain("example.com"), "example.xxx");
        assert_eq!(mask_domain("sub.example.com"), "sub.xxx.com");
        assert_eq!(mask_domain("test.sub.example.com"), "test.xxx.com");
        assert_eq!(mask_domain("***********"), "***********"); // 非域名保持不变
    }

    #[test]
    fn test_mask_address() {
        // IPv4
        assert_eq!(mask_address("*************", false), "192.168.xxx.xxx");
        assert_eq!(mask_address("*************", true), "*************");
        
        // IPv6
        assert_eq!(mask_address("2001:db8::1", false), "2001:db8:xxx:xxx:xxx:xxx:xxx:xxx");
        assert_eq!(mask_address("2001:db8::1", true), "2001:db8::1");
        
        // Domain
        assert_eq!(mask_address("example.com", false), "example.xxx");
        assert_eq!(mask_address("example.com", true), "example.com");
        
        // Empty
        assert_eq!(mask_address("", false), "");
    }

    #[test]
    fn test_format_target_address() {
        // IPv4 with port
        assert_eq!(format_target_address("*************", Some(8080), false), "192.168.xxx.xxx:8080");
        assert_eq!(format_target_address("*************", Some(8080), true), "*************:8080");
        
        // Domain without port
        assert_eq!(format_target_address("example.com", None, false), "example.xxx");
        assert_eq!(format_target_address("example.com", None, true), "example.com");
        
        // Empty address
        assert_eq!(format_target_address("", None, false), "");
    }
}