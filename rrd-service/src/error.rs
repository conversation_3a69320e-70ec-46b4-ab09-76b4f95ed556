use std::convert::Infallible;
use warp::{http::StatusCode, Rejection, Reply};

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("RRD operation failed: {0}")]
    RrdError(#[from] anyhow::Error),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("File not found: {0}")]
    FileNotFound(String),
    
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}

impl warp::reject::Reject for Error {}

pub async fn handle_rejection(err: Rejection) -> Result<impl Reply, Infallible> {
    let code;
    let message;
    let mut error_details = None;

    if err.is_not_found() {
        code = StatusCode::NOT_FOUND;
        message = "Not Found";
        log::warn!("Request not found: {:?}", err);
    } else if let Some(custom_error) = err.find::<Error>() {
        match custom_error {
            Error::RrdError(inner_err) => {
                code = StatusCode::INTERNAL_SERVER_ERROR;
                message = "RRD operation failed";
                error_details = Some(format!("{:?}", inner_err));
                log::error!("RRD operation failed: {:?}", inner_err);
                
                // 记录完整的错误链
                let mut current_error = inner_err.source();
                let mut error_chain = Vec::new();
                while let Some(err) = current_error {
                    error_chain.push(err.to_string());
                    current_error = err.source();
                }
                if !error_chain.is_empty() {
                    log::error!("Error chain: {}", error_chain.join(" -> "));
                }
            }
            Error::InvalidRequest(details) => {
                code = StatusCode::BAD_REQUEST;
                message = "Invalid request";
                error_details = Some(details.clone());
                log::warn!("Invalid request: {}", details);
            }
            Error::FileNotFound(path) => {
                code = StatusCode::NOT_FOUND;
                message = "File not found";
                error_details = Some(path.clone());
                log::warn!("File not found: {}", path);
            }
            Error::SerializationError(serde_err) => {
                code = StatusCode::INTERNAL_SERVER_ERROR;
                message = "Serialization error";
                error_details = Some(serde_err.to_string());
                log::error!("Serialization error: {:?}", serde_err);
            }
        }
    } else if let Some(body_err) = err.find::<warp::filters::body::BodyDeserializeError>() {
        code = StatusCode::BAD_REQUEST;
        message = "Invalid body";
        error_details = Some(body_err.to_string());
        log::warn!("Body deserialization error: {:?}", body_err);
    } else if err.find::<warp::reject::MethodNotAllowed>().is_some() {
        code = StatusCode::METHOD_NOT_ALLOWED;
        message = "Method not allowed";
        log::warn!("Method not allowed: {:?}", err);
    } else {
        log::error!("Unhandled rejection: {:?}", err);
        code = StatusCode::INTERNAL_SERVER_ERROR;
        message = "Internal server error";
        error_details = Some(format!("{:?}", err));
    }

    let mut response_json = serde_json::json!({
        "error": message,
        "status": code.as_u16()
    });

    // 在开发环境中包含详细错误信息
    if let Some(details) = error_details {
        if std::env::var("RUST_LOG").unwrap_or_default().contains("debug") {
            response_json["details"] = serde_json::Value::String(details);
        }
    }

    let json = warp::reply::json(&response_json);

    Ok(warp::reply::with_status(json, code))
}