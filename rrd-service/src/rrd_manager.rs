use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use log::{debug, info};
use rrd::{
    ops::graph::{elements, props},
    ops::{create, graph, update},
    ConsolidationFn,
};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
};
use std::{
    sync::{Arc, Mutex},
    time::Duration,
};
use tokio::fs;

use crate::models::{LatencyHistoryPoint, RrdDataPoint};
use crate::privacy::mask_address;

/// RRD 数据管理器
pub struct RrdManager {
    /// RRD 文件存储根目录
    base_path: PathBuf,
    generate_lock: Arc<Mutex<()>>,
}

impl RrdManager {
    /// 创建新的 RRD 管理器
    pub fn new<P: AsRef<Path>>(base_path: P) -> Self {
        Self {
            base_path: base_path.as_ref().to_path_buf(),
            generate_lock: Arc::new(Mutex::new(())),
        }
    }

    /// 获取 RRD 文件路径
    fn get_rrd_path(&self, server_id: i32, config_id: i32) -> PathBuf {
        self.base_path
            .join(format!("server_{}", server_id))
            .join(format!("config_{}.rrd", config_id))
    }

    /// 确保目录存在
    async fn ensure_directory_exists(&self, path: &Path) -> Result<()> {
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).await.map_err(|e| {
                    anyhow!("Failed to create directory {}: {}", parent.display(), e)
                })?;
                info!("Created directory: {}", parent.display());
            }
        }
        Ok(())
    }

    /// 创建新的 RRD 文件
    pub async fn create_rrd_file(&self, server_id: i32, config_id: i32) -> Result<()> {
        let rrd_path = self.get_rrd_path(server_id, config_id);

        // 确保目录存在
        self.ensure_directory_exists(&rrd_path).await?;

        // 如果文件已存在，跳过创建
        if rrd_path.exists() {
            debug!("RRD file already exists: {}", rrd_path.display());
            return Ok(());
        }

        // SmokePing标准数据源定义
        let mut data_sources = vec![
            // SmokePing核心字段
            create::DataSource::gauge(create::DataSourceName::new("uptime"), 300, Some(0.0), None),
            create::DataSource::gauge(
                create::DataSourceName::new("loss"),
                300,
                Some(0.0),
                Some(20.0), // SmokePing标准：最多20个ping
            ),
            create::DataSource::gauge(create::DataSourceName::new("median"), 300, Some(0.0), None),
        ];

        // 添加20个ping样本数据源 (SmokePing标准)
        for i in 1..=20 {
            data_sources.push(create::DataSource::gauge(
                create::DataSourceName::new(&format!("ping{}", i)),
                300,
                Some(0.0),
                None,
            ));
        }

        // 补充统计字段
        data_sources.extend(vec![
            create::DataSource::gauge(
                create::DataSourceName::new("avg_latency"),
                300,
                Some(0.0),
                None,
            ),
            create::DataSource::gauge(
                create::DataSourceName::new("min_latency"),
                300,
                Some(0.0),
                None,
            ),
            create::DataSource::gauge(
                create::DataSourceName::new("max_latency"),
                300,
                Some(0.0),
                None,
            ),
            create::DataSource::gauge(
                create::DataSourceName::new("stddev_latency"),
                300,
                Some(0.0),
                None,
            ),
        ]);

        // 定义归档策略
        let archives = vec![
            // 1分钟原始数据，保存24小时 (1440点)
            create::Archive::new(ConsolidationFn::Avg, 0.5, 1, 1440)?,
            create::Archive::new(ConsolidationFn::Max, 0.5, 1, 1440)?,
            create::Archive::new(ConsolidationFn::Min, 0.5, 1, 1440)?,
            // 5分钟聚合数据，保存7天 (2016点)
            create::Archive::new(ConsolidationFn::Avg, 0.5, 5, 2016)?,
            create::Archive::new(ConsolidationFn::Max, 0.5, 5, 2016)?,
            create::Archive::new(ConsolidationFn::Min, 0.5, 5, 2016)?,
            // 1小时聚合数据，保存90天 (2160点)
            create::Archive::new(ConsolidationFn::Avg, 0.5, 60, 2160)?,
            create::Archive::new(ConsolidationFn::Max, 0.5, 60, 2160)?,
            create::Archive::new(ConsolidationFn::Min, 0.5, 60, 2160)?,
            // 1天聚合数据，保存3年 (1095点)
            create::Archive::new(ConsolidationFn::Avg, 0.5, 1440, 1095)?,
            create::Archive::new(ConsolidationFn::Max, 0.5, 1440, 1095)?,
            create::Archive::new(ConsolidationFn::Min, 0.5, 1440, 1095)?,
        ];

        // 创建数据库，使用 Utc::now() 作为起始时间，60秒步长（与SmokePing一致）
        let lock = Arc::clone(&self.generate_lock);
        let rrd_path_clone = rrd_path.clone();
        tokio::task::spawn_blocking(move || {
            let _guard = lock.lock().unwrap();
            create::create(
                &rrd_path_clone,
                Utc::now(),
                Duration::from_secs(60),
                true, // no-overwrite
                None,
                &[],
                &data_sources,
                &archives,
            )
        })
        .await?;

        info!("Created RRD file: {}", rrd_path.display());
        Ok(())
    }

    /// 更新 RRD 数据
    pub async fn update_rrd_data(
        &self,
        server_id: i32,
        config_id: i32,
        data_point: &RrdDataPoint,
    ) -> Result<()> {
        let rrd_path = self.get_rrd_path(server_id, config_id);

        // 确保 RRD 文件存在
        if !rrd_path.exists() {
            self.create_rrd_file(server_id, config_id).await?;
        }

        // SmokePing标准数据源名称顺序
        let mut ds_names: Vec<String> = vec![
            "uptime".to_string(),
            "loss".to_string(),
            "median".to_string(),
        ];

        // 添加20个ping样本数据源名称 (需要先创建字符串)
        let mut ping_names = Vec::new();
        for i in 1..=20 {
            ping_names.push(format!("ping{}", i));
        }
        for name in &ping_names {
            ds_names.push(name.clone());
        }

        // 添加统计字段数据源名称
        ds_names.extend([
            "avg_latency".to_string(),
            "min_latency".to_string(),
            "max_latency".to_string(),
            "stddev_latency".to_string(),
        ]);

        // 准备SmokePing风格的数据值
        let mut values = Vec::new();

        // 核心SmokePing字段
        values.push(
            data_point
                .uptime
                .map(|u| update::Datum::from(u as f64))
                .unwrap_or(update::Datum::Unspecified),
        );
        values.push(update::Datum::from(data_point.loss_count as f64)); // loss字段始终有效
        values.push(
            data_point
                .median_latency_us
                .map(|l| update::Datum::from(l as f64))
                .unwrap_or(update::Datum::Unspecified),
        );

        // 20个ping样本
        for i in 0..20 {
            if i < data_point.ping_samples.len() {
                values.push(
                    data_point.ping_samples[i]
                        .map(|p| update::Datum::from(p as f64))
                        .unwrap_or(update::Datum::Unspecified),
                );
            } else {
                values.push(update::Datum::Unspecified);
            }
        }

        // 统计字段
        values.push(
            data_point
                .avg_latency_us
                .map(|l| update::Datum::from(l as f64))
                .unwrap_or(update::Datum::Unspecified),
        );
        values.push(
            data_point
                .min_latency_us
                .map(|l| update::Datum::from(l as f64))
                .unwrap_or(update::Datum::Unspecified),
        );
        values.push(
            data_point
                .max_latency_us
                .map(|l| update::Datum::from(l as f64))
                .unwrap_or(update::Datum::Unspecified),
        );
        values.push(
            data_point
                .stddev_latency_us
                .map(|s| update::Datum::from(s))
                .unwrap_or(update::Datum::Unspecified),
        );

        // 转换为数组以匹配update API
        let values_array: Vec<update::Datum> = values;

        // 更新数据
        let lock = Arc::clone(&self.generate_lock);
        let rrd_path_clone = rrd_path.clone();
        let timestamp = data_point.timestamp.into();
        tokio::task::spawn_blocking(move || {
            let ds_name_strs: Vec<&str> = ds_names.iter().map(|s| s.as_ref()).collect();
            let _guard = lock.lock().unwrap();
            update::update(
                &rrd_path_clone,
                &ds_name_strs,
                update::ExtraFlags::empty(),
                &[(timestamp, values_array.as_slice())],
            )
        })
        .await?;

        debug!(
            "Updated RRD data for server {} config {} at {}",
            server_id, config_id, data_point.timestamp
        );
        Ok(())
    }

    /// 批量初始化历史数据到 RRD
    pub async fn initialize_historical_data(
        &self,
        server_id: i32,
        config_id: i32,
        history_points: &[LatencyHistoryPoint],
    ) -> Result<()> {
        info!(
            "Initializing {} historical data points for server {} config {}",
            history_points.len(),
            server_id,
            config_id
        );

        // 确保 RRD 文件存在
        self.create_rrd_file(server_id, config_id).await?;

        let rrd_path = self.get_rrd_path(server_id, config_id);

        // SmokePing标准数据源名称顺序
        let mut ds_names: Vec<String> = vec![
            "uptime".to_string(),
            "loss".to_string(),
            "median".to_string(),
        ];

        // 添加20个ping样本数据源名称
        for i in 1..=20 {
            ds_names.push(format!("ping{}", i));
        }

        // 添加统计字段数据源名称
        ds_names.extend(
            [
                "avg_latency",
                "min_latency",
                "max_latency",
                "stddev_latency",
            ]
            .iter()
            .map(|s| s.to_string()),
        );

        // 批量准备更新数据
        let mut updates = Vec::new();
        for point in history_points {
            // 将历史数据点转换为新的RrdDataPoint格式
            let data_point = RrdDataPoint {
                timestamp: point.timestamp,
                loss_count: point.loss_count,
                median_latency_us: point.median_latency_us,
                uptime: point.uptime,
                ping_samples: point.ping_samples.clone(),
                avg_latency_us: point.avg_latency_us,
                min_latency_us: point.min_latency_us,
                max_latency_us: point.max_latency_us,
                stddev_latency_us: point.stddev_latency_us,
                total_samples: point.total_samples,
            };

            // 准备SmokePing风格的数据值
            let mut values = Vec::new();

            // 核心SmokePing字段
            values.push(
                data_point
                    .uptime
                    .map(|u| update::Datum::from(u as f64))
                    .unwrap_or(update::Datum::Unspecified),
            );
            values.push(update::Datum::from(data_point.loss_count as f64)); // loss字段始终有效
            values.push(
                data_point
                    .median_latency_us
                    .map(|l| update::Datum::from(l as f64))
                    .unwrap_or(update::Datum::Unspecified),
            );

            // 20个ping样本
            for i in 0..20 {
                if i < data_point.ping_samples.len() {
                    values.push(
                        data_point.ping_samples[i]
                            .map(|p| update::Datum::from(p as f64))
                            .unwrap_or(update::Datum::Unspecified),
                    );
                } else {
                    values.push(update::Datum::Unspecified);
                }
            }

            // 统计字段
            values.push(
                data_point
                    .avg_latency_us
                    .map(|l| update::Datum::from(l as f64))
                    .unwrap_or(update::Datum::Unspecified),
            );
            values.push(
                data_point
                    .min_latency_us
                    .map(|l| update::Datum::from(l as f64))
                    .unwrap_or(update::Datum::Unspecified),
            );
            values.push(
                data_point
                    .max_latency_us
                    .map(|l| update::Datum::from(l as f64))
                    .unwrap_or(update::Datum::Unspecified),
            );
            values.push(
                data_point
                    .stddev_latency_us
                    .map(|s| update::Datum::from(s))
                    .unwrap_or(update::Datum::Unspecified),
            );

            updates.push((data_point.timestamp.into(), values));
        }

        // 批量更新
        let updates_count = updates.len();
        if !updates.is_empty() {
            // 这里需要特殊处理，因为update API需要统一的值数组
            for (timestamp, values) in updates {
                let dns_names_clone = ds_names.clone();
                let rrd_path_clone = rrd_path.clone();
                let lock = Arc::clone(&self.generate_lock);
                tokio::task::spawn_blocking(move || {
                    let ds_name_strs: Vec<&str> =
                        dns_names_clone.iter().map(|s| s.as_ref()).collect();
                    let _guard = lock.lock().unwrap();
                    update::update(
                        &rrd_path_clone,
                        &ds_name_strs,
                        update::ExtraFlags::empty(),
                        &[(timestamp, values.as_slice())],
                    )
                })
                .await?;
            }
            info!(
                "Successfully initialized {} data points for server {} config {}",
                updates_count, server_id, config_id
            );
        }

        Ok(())
    }

    /// 生成图表
    pub async fn generate_chart(
        &self,
        server_id: i32,
        config_id: i32,
        time_range: &str,
        target_address: &str,
        end_time: Option<DateTime<Utc>>,
    ) -> Result<Vec<u8>> {
        let rrd_path = self.get_rrd_path(server_id, config_id);

        if !rrd_path.exists() {
            return Err(anyhow!("RRD file not found: {}", rrd_path.display()));
        }

        // 计算时间范围
        let now = Utc::now();
        let start_time = match time_range {
            "1h" => now - Duration::from_secs(3600),
            "3h" => now - Duration::from_secs(10800),
            "6h" => now - Duration::from_secs(21600),
            "12h" => now - Duration::from_secs(43200),
            "24h" => now - Duration::from_secs(86400),
            "7d" => now - Duration::from_secs(604800),
            _ => return Err(anyhow!("Invalid time range: {}", time_range)),
        };

        // 定义变量名称
        let latency_min_var: elements::VarName = "latency_min"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_max_var: elements::VarName = "latency_max"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_median_var: elements::VarName = "latency_median"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let packet_loss_var: elements::VarName = "packet_loss"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let packet_loss_percent_var: elements::VarName = "packet_loss_pct"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;

        // 毫秒级变量名称（用于图表显示）
        let latency_min_ms_var: elements::VarName = "latency_min_ms"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_min_ms_filled_var: elements::VarName = "latency_min_ms_filled"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_max_ms_var: elements::VarName = "latency_max_ms"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_max_ms_filled_var: elements::VarName = "latency_max_ms_filled"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        let latency_median_ms_var: elements::VarName = "latency_median_ms"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;
        // 连续填充缺口的中位数变量（空值用上一个值代替）
        let latency_median_ms_filled_var: elements::VarName = "latency_median_ms_filled"
            .try_into()
            .map_err(|e| anyhow!("Invalid var name: {}", e))?;

        // 丢包率分段变量（对应 SmokePing 7 档：0 1/20 2/20 3/20 4/20 10/20 19/20）
        let median_loss0_var: elements::VarName = "median_loss0".try_into()?; // 0/20
        let median_loss5_var: elements::VarName = "median_loss5".try_into()?; // 1/20 (≈5%)
        let median_loss10_var: elements::VarName = "median_loss10".try_into()?; // 2/20 (≈10%)
        let median_loss15_var: elements::VarName = "median_loss15".try_into()?; // 3/20 (≈15%)
        let median_loss20_var: elements::VarName = "median_loss20".try_into()?; // 4/20 (≈20%)
        let median_loss50_var: elements::VarName = "median_loss50".try_into()?; // 10/20 (50%)
        let median_loss_hi_var: elements::VarName = "median_loss_hi".try_into()?; // 19/20 (≈95%)

        // 统计变量
        let median_last_var: elements::VarName = "median_last".try_into()?;
        let median_avg_var: elements::VarName = "median_avg".try_into()?;
        let median_max_var: elements::VarName = "median_max".try_into()?;
        let median_min_var: elements::VarName = "median_min".try_into()?;

        let loss_last_var: elements::VarName = "loss_last".try_into()?;
        let loss_avg_var: elements::VarName = "loss_avg".try_into()?;
        let loss_max_var: elements::VarName = "loss_max".try_into()?;
        let loss_min_var: elements::VarName = "loss_min".try_into()?;

        // SmokePing标准数据源定义
        let mut graph_elements: Vec<elements::GraphElement> = vec![
            // 核心SmokePing数据源
            elements::Def {
                var_name: "loss".try_into()?,
                rrd: rrd_path.clone(),
                ds_name: "loss".to_string(),
                consolidation_fn: ConsolidationFn::Avg,
                step: None,
                start: None,
                end: None,
                reduce: None,
            }
            .into(),
            elements::Def {
                var_name: "median".try_into()?,
                rrd: rrd_path.clone(),
                ds_name: "median".to_string(),
                consolidation_fn: ConsolidationFn::Avg,
                step: None,
                start: None,
                end: None,
                reduce: None,
            }
            .into(),
            elements::Def {
                var_name: "uptime".try_into()?,
                rrd: rrd_path.clone(),
                ds_name: "uptime".to_string(),
                consolidation_fn: ConsolidationFn::Avg,
                step: None,
                start: None,
                end: None,
                reduce: None,
            }
            .into(),
        ];

        // 添加20个ping样本数据源
        for i in 1..=20 {
            graph_elements.push(
                elements::Def {
                    var_name: format!("ping{}", i).try_into()?,
                    rrd: rrd_path.clone(),
                    ds_name: format!("ping{}", i),
                    consolidation_fn: ConsolidationFn::Avg,
                    step: None,
                    start: None,
                    end: None,
                    reduce: None,
                }
                .into(),
            );
        }

        // SmokePing核心算法：基于真实数据的计算
        graph_elements.extend(vec![
            // 计算丢包率百分比：loss/20*100 (SmokePing标准)
            elements::CDef {
                var_name: "ploss".try_into()?,
                rpn: "loss,20,/,100,*".to_string(),
            }
            .into(),
            // SmokePing关键：无数据检测 - loss为UNKNOWN时显示
            elements::CDef {
                var_name: "nodata".try_into()?,
                rpn: "loss,UN,INF,UNKN,IF".to_string(),
            }
            .into(),
            // 将微秒转换为毫秒并处理数据有效性
            elements::CDef {
                var_name: "median_ms".try_into()?,
                rpn: "median,1000,/".to_string(),
            }
            .into(),
            // SmokePing算法：median有效性检查
            elements::CDef {
                var_name: "me".try_into()?,
                rpn: "loss,20,LT,median_ms,UNKN,IF".to_string(), // 只有loss<20时才显示median
            }
            .into(),
            // 处理100%丢包的情况 - 当loss=20时，不显示任何median数据
            elements::CDef {
                var_name: "me_valid".try_into()?,
                rpn: "median_ms,UN,0,median_ms,IF".to_string(), // 如果median未定义则用0
            }
            .into(),
            // SmokePing标准：计算丢包率百分比
            elements::CDef {
                var_name: packet_loss_percent_var.clone(),
                rpn: "loss,100,*,20,/".to_string(), // (loss/20)*100 = 丢包百分比
            }
            .into(),
            // 中位数延迟别名（与旧代码兼容）
            elements::CDef {
                var_name: latency_median_ms_var.clone(),
                rpn: "median_ms".to_string(), // 简单别名
            }
            .into(),
            // 中位数延迟填充版本（用于面积图）
            elements::CDef {
                var_name: latency_median_ms_filled_var.clone(),
                rpn: "median_ms,DUP,ISINF,EXC,0,EQ,EXC,UNKN,IF".to_string(), // 将无效值替换为0
            }
            .into(),
        ]);

        // SmokePing真实算法：处理20个ping样本创建烟雾效果
        // 将ping样本转换为毫秒并排序
        for i in 1..=20 {
            graph_elements.push(
                elements::CDef {
                    var_name: format!("p{}_ms", i).try_into()?,
                    rpn: format!("ping{},1000,/", i),
                }
                .into(),
            );

            // 添加排序处理：如果ping存在且loss<20，则显示
            graph_elements.push(
                elements::CDef {
                    var_name: format!("cp{}", i).try_into()?,
                    rpn: format!("loss,20,LT,p{}_ms,UNKN,IF", i),
                }
                .into(),
            );
        }

        // SmokePing百分比阈值常量
        let per01 = 1.0;
        let per05 = 5.0;
        let per10 = 10.0;
        let per15 = 15.0;
        let per20 = 20.0;
        let per50 = 50.0;

        // SmokePing烟雾效果：创建AREA+STACK的分层可视化
        // 使用真实的ping数据创建从底到顶的烟雾层
        for i in 1..=10 {
            let bottom_ping = i;
            let top_ping = 21 - i; // 对称分布

            if bottom_ping < top_ping {
                let smoke_var: elements::VarName = format!("smoke{}", bottom_ping).try_into()?;

                // SmokePing算法：smoke = cp_top - cp_bottom
                graph_elements.push(
                    elements::CDef {
                        var_name: smoke_var.clone(),
                        rpn: format!("cp{},cp{},-", top_ping, bottom_ping),
                    }
                    .into(),
                );

                // 灰度颜色：越靠近中位数颜色越深
                let color_intensity = 80 + (100 * (10 - i)) / 10;

                // 底部透明区域
                graph_elements.push(
                    elements::Area {
                        value: format!("cp{}", bottom_ping).try_into()?,
                        color: None,
                        stack: false,
                        skip_scale: false,
                    }
                    .into(),
                );

                // 烟雾层堆叠
                graph_elements.push(
                    elements::Area {
                        value: smoke_var,
                        color: Some(elements::ColorWithLegend {
                            color: elements::AreaColor::Color(graph::Color {
                                red: color_intensity as u8,
                                green: color_intensity as u8,
                                blue: color_intensity as u8,
                                alpha: Some(150), // 半透明效果
                            }),
                            legend: None,
                        }),
                        stack: true, // 关键：堆叠创建烟雾
                        skip_scale: false,
                    }
                    .into(),
                );
            }
        }

        // SmokePing标准丢包率处理和统计变量定义

        graph_elements.extend(vec![
            // SmokePing真实算法：按loss值范围创建连续的CDEF变量，而不是精确匹配
            // 0/20 丢包 (0%) - 使用LT而不是EQ来确保连续性
            elements::CDef {
                var_name: median_loss0_var.clone(),
                rpn: "loss,0.5,LT,median_ms,UNKN,IF".to_string(), // loss < 0.5
            }
            .into(),
            // 1/20 丢包 (5%)
            elements::CDef {
                var_name: median_loss5_var.clone(),
                rpn: "loss,0.5,GE,loss,1.5,LT,*,median_ms,UNKN,IF".to_string(), // 0.5 <= loss < 1.5
            }
            .into(),
            // 2/20 丢包 (10%)
            elements::CDef {
                var_name: median_loss10_var.clone(),
                rpn: "loss,1.5,GE,loss,2.5,LT,*,median_ms,UNKN,IF".to_string(), // 1.5 <= loss < 2.5
            }
            .into(),
            // 3/20 丢包 (15%)
            elements::CDef {
                var_name: median_loss15_var.clone(),
                rpn: "loss,2.5,GE,loss,3.5,LT,*,median_ms,UNKN,IF".to_string(), // 2.5 <= loss < 3.5
            }
            .into(),
            // 4/20 丢包 (20%)
            elements::CDef {
                var_name: median_loss20_var.clone(),
                rpn: "loss,3.5,GE,loss,4.5,LT,*,median_ms,UNKN,IF".to_string(), // 3.5 <= loss < 4.5
            }
            .into(),
            // 5-10/20 丢包 (25-50%)
            elements::CDef {
                var_name: median_loss50_var.clone(),
                rpn: "loss,4.5,GE,loss,10.5,LT,*,median_ms,UNKN,IF".to_string(), // 4.5 <= loss < 10.5
            }
            .into(),
            // 11-20/20 丢包 (55-100%)
            elements::CDef {
                var_name: median_loss_hi_var.clone(),
                rpn: "loss,10.5,GE,median_ms,UNKN,IF".to_string(), // loss >= 10.5
            }
            .into(),
            // SmokePing算法：先绘制连续的基础中位数线（深灰色，包含所有有效数据）
            elements::Line {
                width: 2.0,
                value: "me".try_into()?, // 使用me变量，它已经处理了loss<20的条件
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x20,
                        green: 0x20,
                        blue: 0x20,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 按SmokePing标准顺序和颜色绘制 - 从高损失到低损失
            // 11-20/20 丢包 (55-100%) - 红色 (#ff0000)
            elements::Line {
                width: 2.0,
                value: median_loss_hi_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x00,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 5-10/20 丢包 (25-50%) - 橙色 (#ff5500) - SmokePing标准颜色
            elements::Line {
                width: 2.0,
                value: median_loss50_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x55,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 4/20 丢包 (20%) - 品红 (#ff00ff)
            elements::Line {
                width: 2.0,
                value: median_loss20_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x00,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 3/20 丢包 (15%) - 紫色 (#7e00ff)
            elements::Line {
                width: 2.0,
                value: median_loss15_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x7E,
                        green: 0x00,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 2/20 丢包 (10%) - 蓝色 (#0059ff)
            elements::Line {
                width: 2.0,
                value: median_loss10_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x00,
                        green: 0x59,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 1/20 丢包 (5%) - 浅蓝色 (#00b8ff)
            elements::Line {
                width: 2.0,
                value: median_loss5_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x00,
                        green: 0xB8,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 0/20 丢包 (0%) - 绿色 (#26ff00) - 最后绘制
            elements::Line {
                width: 2.0,
                value: median_loss0_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x26,
                        green: 0xFF,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: None,
                }),
                stack: false,
                skip_scale: false,
                dashes: None,
            }
            .into(),
            // 移除"无数据"灰色区域显示 - Smokeping标准不显示此类背景
            elements::VDef {
                var_name: median_last_var.clone(),
                rpn: "latency_median_ms,LAST".into(),
            }
            .into(),
            elements::VDef {
                var_name: median_avg_var.clone(),
                rpn: "latency_median_ms,AVERAGE".into(),
            }
            .into(),
            elements::VDef {
                var_name: median_max_var.clone(),
                rpn: "latency_median_ms,MAXIMUM".into(),
            }
            .into(),
            elements::VDef {
                var_name: median_min_var.clone(),
                rpn: "latency_median_ms,MINIMUM".into(),
            }
            .into(),
            elements::VDef {
                var_name: loss_avg_var.clone(),
                rpn: "packet_loss_pct,AVERAGE".into(),
            }
            .into(),
            elements::VDef {
                var_name: loss_max_var.clone(),
                rpn: "packet_loss_pct,MAXIMUM".into(),
            }
            .into(),
            elements::VDef {
                var_name: loss_min_var.clone(),
                rpn: "packet_loss_pct,MINIMUM".into(),
            }
            .into(),
            elements::VDef {
                var_name: loss_last_var.clone(),
                rpn: "packet_loss_pct,LAST".into(),
            }
            .into(),
            // 基准线 - 0线
            elements::HRule {
                value: elements::Value::Constant(0.0),
                color: graph::Color {
                    red: 0x00,
                    green: 0x00,
                    blue: 0x00,
                    alpha: None,
                },
                legend: None,
                dashes: None,
            }
            .into(),
            // SmokePing统计信息打印
            elements::GPrint {
                var_name: median_avg_var.clone(),
                format: "median rtt\\: %6.2lf ms avg ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: median_max_var.clone(),
                format: "%6.2lf ms max ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: median_min_var.clone(),
                format: "%6.2lf ms min ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: median_last_var.clone(),
                format: "%6.2lf ms now ".into(),
            }
            .into(),
            // 添加标准差显示（6.3 k am/s 对应标准差）
            elements::VDef {
                var_name: "median_stddev".try_into()?,
                rpn: "latency_median_ms,STDEV".into(),
            }
            .into(),
            elements::GPrint {
                var_name: "median_stddev".try_into()?,
                format: "%6.1lf ms sd ".into(),
            }
            .into(),
            // 添加每秒采样数显示 (samples/second) - 使用COMMENT避免格式错误
            elements::Comment {
                text: "0.3 am/s\\l".into(), // 20 pings per 60 seconds ≈ 0.33
            }
            .into(),
            elements::GPrint {
                var_name: loss_avg_var.clone(),
                format: "packet loss\\: %4.2lf %% avg ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: loss_max_var.clone(),
                format: "%4.2lf %% max ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: loss_min_var.clone(),
                format: "%4.2lf %% min ".into(),
            }
            .into(),
            elements::GPrint {
                var_name: loss_last_var.clone(),
                format: "%4.2lf %% now\\l".into(),
            }
            .into(),
            // SmokePing 风格的 loss colour 图例 - 使用带颜色的线段而非文本颜色代码
            elements::Comment {
                text: "loss colour\\: ".into(),
            }
            .into(),
            // SmokePing标准图例 - 使用精确的颜色值
            // 0/20 丢包 - 绿色 (#26ff00)
            elements::Line {
                width: 2.0,
                value: median_loss0_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x26,
                        green: 0xFF,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: Some("0 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 1/20 丢包 - 浅蓝色 (#00b8ff)
            elements::Line {
                width: 2.0,
                value: median_loss5_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x00,
                        green: 0xB8,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: Some("1/20 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 2/20 丢包 - 蓝色 (#0059ff)
            elements::Line {
                width: 2.0,
                value: median_loss10_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x00,
                        green: 0x59,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: Some("2/20 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 3/20 丢包 - 紫色 (#7e00ff)
            elements::Line {
                width: 2.0,
                value: median_loss15_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0x7E,
                        green: 0x00,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: Some("3/20 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 4/20 丢包 - 品红 (#ff00ff)
            elements::Line {
                width: 2.0,
                value: median_loss20_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x00,
                        blue: 0xFF,
                        alpha: None,
                    },
                    legend: Some("4/20 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 10/20 丢包 - 橙色 (#ff5500)
            elements::Line {
                width: 2.0,
                value: median_loss50_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x55,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: Some("10/20 ".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
            // 19/20 丢包 - 红色 (#ff0000)
            elements::Line {
                width: 2.0,
                value: median_loss_hi_var.clone(),
                color: Some(elements::ColorWithLegend {
                    color: graph::Color {
                        red: 0xFF,
                        green: 0x00,
                        blue: 0x00,
                        alpha: None,
                    },
                    legend: Some("19/20\\l".into()),
                }),
                stack: false,
                skip_scale: true,
                dashes: None,
            }
            .into(),
        ]);

        // SmokePing风格的颜色设置
        let mut colors = HashMap::new();
        colors.insert(
            props::ColorTag::Back,
            graph::Color {
                red: 0xF8,
                green: 0xF8,
                blue: 0xF8,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Canvas,
            graph::Color {
                red: 0xFF,
                green: 0xFF,
                blue: 0xFF,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::ShadeA,
            graph::Color {
                red: 0xF0,
                green: 0xF0,
                blue: 0xF0,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::ShadeB,
            graph::Color {
                red: 0xE0,
                green: 0xE0,
                blue: 0xE0,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Grid,
            graph::Color {
                red: 0xC0,
                green: 0xC0,
                blue: 0xC0,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::MGrid,
            graph::Color {
                red: 0x80,
                green: 0x80,
                blue: 0x80,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Font,
            graph::Color {
                red: 0x00,
                green: 0x00,
                blue: 0x00,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Axis,
            graph::Color {
                red: 0x00,
                green: 0x00,
                blue: 0x00,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Frame,
            graph::Color {
                red: 0x00,
                green: 0x00,
                blue: 0x00,
                alpha: None,
            },
        );
        colors.insert(
            props::ColorTag::Arrow,
            graph::Color {
                red: 0x00,
                green: 0x00,
                blue: 0x00,
                alpha: None,
            },
        );

        let target_address = target_address.to_string();
        let time_range = time_range.to_string();

        // 移除重复的统计和绘制元素 - 这些已经在上面定义过了

        // 应用隐私保护到图表标题中的地址
        let masked_target_address = mask_address(&target_address, false);

        let chart_title = format!(
            "SmokePing Style Latency - {} ({})",
            masked_target_address, time_range
        );
        let vertical_label = "Latency (ms)".to_string();

        let generate_lock = Arc::clone(&self.generate_lock);
        // 生成图表
        let (image_data, _metadata) = tokio::task::spawn_blocking(move || {
            let _guard = generate_lock.lock().unwrap();
            graph::graph(
                props::ImageFormat::Png,
                props::GraphProps {
                    time_range: props::TimeRange {
                        start: Some(start_time),
                        end: end_time,
                        ..Default::default()
                    },
                    size: props::Size {
                        width: Some(800),
                        height: Some(400),
                        ..Default::default()
                    },
                    labels: props::Labels {
                        title: Some(chart_title),
                        vertical_label: Some(vertical_label),
                        ..Default::default()
                    },
                    limits: props::Limits {
                        lower_limit: None,
                        upper_limit: None,
                        rigid: false,
                        allow_shrink: false,
                        alt_autoscale: None,
                        no_grid_fit: false,
                    },
                    misc: props::Misc {
                        colors,
                        ..Default::default()
                    },
                    ..Default::default()
                },
                &graph_elements,
            )
        })
        .await?
        .map_err(|e| anyhow!("Failed to generate chart: {}", e))?;

        Ok(image_data)
    }

    /// 检查 RRD 文件是否存在
    pub fn rrd_file_exists(&self, server_id: i32, config_id: i32) -> bool {
        self.get_rrd_path(server_id, config_id).exists()
    }

    /// 删除 RRD 文件
    pub async fn delete_rrd_file(&self, server_id: i32, config_id: i32) -> Result<()> {
        let rrd_path = self.get_rrd_path(server_id, config_id);

        if rrd_path.exists() {
            fs::remove_file(&rrd_path)
                .await
                .map_err(|e| anyhow!("Failed to delete RRD file {}: {}", rrd_path.display(), e))?;
            info!("Deleted RRD file: {}", rrd_path.display());
        }

        Ok(())
    }

    /// 清理过期的 RRD 文件
    pub async fn cleanup_expired_rrd_files(&self, active_configs: &[(i32, i32)]) -> Result<()> {
        let active_set: std::collections::HashSet<(i32, i32)> =
            active_configs.iter().cloned().collect();

        // 遍历所有服务器目录
        let mut entries = fs::read_dir(&self.base_path)
            .await
            .map_err(|e| anyhow!("Failed to read RRD base directory: {}", e))?;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if path.is_dir() {
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if let Some(server_id_str) = dir_name.strip_prefix("server_") {
                        if let Ok(server_id) = server_id_str.parse::<i32>() {
                            // 遍历服务器目录下的配置文件
                            let mut config_entries = fs::read_dir(&path).await?;
                            while let Some(config_entry) = config_entries.next_entry().await? {
                                let config_path = config_entry.path();
                                if let Some(file_name) =
                                    config_path.file_name().and_then(|n| n.to_str())
                                {
                                    if let Some(config_name) = file_name.strip_suffix(".rrd") {
                                        if let Some(config_id_str) =
                                            config_name.strip_prefix("config_")
                                        {
                                            if let Ok(config_id) = config_id_str.parse::<i32>() {
                                                if !active_set.contains(&(server_id, config_id)) {
                                                    fs::remove_file(&config_path).await?;
                                                    info!(
                                                        "Cleaned up expired RRD file: {}",
                                                        config_path.display()
                                                    );
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_rrd_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = RrdManager::new(temp_dir.path());

        assert_eq!(manager.base_path, temp_dir.path().to_path_buf());
    }

    #[tokio::test]
    async fn test_rrd_file_path() {
        let temp_dir = TempDir::new().unwrap();
        let manager = RrdManager::new(temp_dir.path());

        let path = manager.get_rrd_path(1, 2);
        let expected = temp_dir.path().join("server_1").join("config_2.rrd");

        assert_eq!(path, expected);
    }
}
