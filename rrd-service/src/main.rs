use std::sync::Arc;
use warp::Filter;

use rrd_service::{error::handle_rejection, handlers, rrd_manager::RrdManager, Config};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();
    // 解析命令行参数
    let config = Config::new();

    // 初始化日志
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or(&config.log_level))
        .init();

    log::info!(
        "Starting RRD service with data path: {}",
        config.rrd_data_path.display()
    );
    log::info!(
        "Server will listen on {}:{}",
        config.bind_address,
        config.port
    );

    // 创建RRD管理器
    let rrd_manager = Arc::new(RrdManager::new(&config.rrd_data_path));

    // 创建warp过滤器，将RrdManager注入到处理器中
    let with_rrd_manager = warp::any().map(move || Arc::clone(&rrd_manager));

    // 健康检查路由
    let health = warp::path("health")
        .and(warp::get())
        .and_then(handlers::health);

    // RRD文件操作路由
    let create_rrd = warp::path!("rrd" / "create")
        .and(warp::post())
        .and(warp::body::json())
        .and(with_rrd_manager.clone())
        .and_then(handlers::create_rrd);

    let update_rrd = warp::path!("rrd" / "update")
        .and(warp::put())
        .and(warp::body::json())
        .and(with_rrd_manager.clone())
        .and_then(handlers::update_rrd);

    let check_exists = warp::path!("rrd" / "exists" / i32 / i32)
        .and(warp::get())
        .and(with_rrd_manager.clone())
        .and_then(handlers::check_exists);

    let delete_rrd = warp::path!("rrd" / i32 / i32)
        .and(warp::delete())
        .and(with_rrd_manager.clone())
        .and_then(handlers::delete_rrd);

    let cleanup = warp::path!("rrd" / "cleanup")
        .and(warp::post())
        .and(warp::body::json())
        .and(with_rrd_manager.clone())
        .and_then(handlers::cleanup);

    let init_historical = warp::path!("rrd" / "init-historical")
        .and(warp::post())
        .and(warp::body::json())
        .and(with_rrd_manager.clone())
        .and_then(handlers::init_historical_data);

    // 图表生成路由
    let generate_chart = warp::path!("chart" / "generate")
        .and(warp::post())
        .and(warp::body::json())
        .and(with_rrd_manager.clone())
        .and_then(handlers::generate_chart);

    // 组合所有路由
    let routes = health
        .or(create_rrd)
        .or(update_rrd)
        .or(check_exists)
        .or(delete_rrd)
        .or(cleanup)
        .or(init_historical)
        .or(generate_chart)
        .with(
            warp::cors()
                .allow_any_origin()
                .allow_headers(vec!["content-type"])
                .allow_methods(vec!["GET", "POST", "PUT", "DELETE"]),
        )
        .recover(handle_rejection);

    log::info!(
        "RRD service started on http://{}:{}",
        config.bind_address,
        config.port
    );

    // 启动服务器
    let bind_addr = config.get_bind_addr();
    warp::serve(routes).run(bind_addr).await;

    Ok(())
}
