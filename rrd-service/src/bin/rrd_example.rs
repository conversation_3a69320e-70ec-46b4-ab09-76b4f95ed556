#![recursion_limit = "256"]
use anyhow::Result;
use chrono::{Duration, Utc};
use rand::prelude::*;
use std::path::PathBuf;
use tokio::fs;

// 引入RRD服务的结构
use rrd_service::{
    models::{LatencyHistoryPoint, RrdDataPoint},
    RrdManager,
};

/// RRD 使用示例程序
///
/// 此示例展示了完整的 RRD 工作流程：
/// 1. 创建 RRD 数据库
/// 2. 生成随机延迟数据
/// 3. 将数据写入 RRD
/// 4. 生成延迟监控图表
#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    env_logger::init();

    tokio::fs::remove_dir_all("./rrd_data").await.ok();

    println!("🚀 开始 RRD 延迟监控示例");

    // 1. 创建 RRD 管理器
    let rrd_base_path = PathBuf::from("./rrd_data");
    let manager = RrdManager::new(&rrd_base_path);

    // 确保基础目录存在
    if !rrd_base_path.exists() {
        fs::create_dir_all(&rrd_base_path).await?;
        println!("📁 创建 RRD 数据目录: {}", rrd_base_path.display());
    }

    // 示例参数
    let server_id = 1;
    let config_id = 101;
    let target_address = "*******";

    // 2. 创建 RRD 数据库
    println!(
        "🗄️  创建 RRD 数据库 (server: {}, config: {})...",
        server_id, config_id
    );
    manager.create_rrd_file(server_id, config_id).await?;
    println!("✅ RRD 数据库创建成功");

    // 3. 生成并写入随机延迟数据
    println!("📊 生成随机延迟数据...");
    let data_points = generate_random_latency_data(120)?; // 生成120个数据点（2小时的数据）

    println!("💾 写入 {} 个数据点到 RRD...", data_points.len());
    for (i, data_point) in data_points.iter().enumerate() {
        println!("writing data point: {:?}", data_point);
        manager
            .update_rrd_data(server_id, config_id, data_point)
            .await?;

        if (i + 1) % 20 == 0 {
            println!("   已写入 {}/{} 个数据点", i + 1, data_points.len());
        }
    }
    println!("✅ 数据写入完成");

    // 4. 生成不同时间范围的延迟图表
    let time_ranges = vec!["1h", "3h", "6h", "12h", "24h"];

    for time_range in time_ranges {
        println!("📈 生成 {} 时间范围的延迟图表...", time_range);

        match manager
            .generate_chart(
                server_id,
                config_id,
                time_range,
                target_address,
                Some(Utc::now() + Duration::hours(1)),
            )
            .await
        {
            Ok(image_data) => {
                let output_path = format!("latency_chart_{}.png", time_range);
                fs::write(&output_path, image_data).await?;
                println!("✅ 图表已保存到: {}", output_path);
            }
            Err(e) => {
                println!("❌ 生成 {} 图表失败: {}", time_range, e);
            }
        }
    }

    // 5. 显示统计信息
    print_statistics(&data_points);

    // 6. 演示批量历史数据初始化
    println!("\n🔄 演示批量历史数据初始化...");
    let historical_points = generate_historical_latency_points()?;

    // 创建新的配置用于演示
    let demo_config_id = 102;
    manager
        .initialize_historical_data(server_id, demo_config_id, &historical_points)
        .await?;
    println!("✅ 历史数据初始化完成");

    // 为历史数据生成图表
    match manager
        .generate_chart(
            server_id,
            demo_config_id,
            "24h",
            &format!("{}-historical", target_address),
            Some(Utc::now()),
        )
        .await
    {
        Ok(image_data) => {
            let output_path = "latency_chart_historical.png";
            fs::write(output_path, image_data).await?;
            println!("✅ 历史数据图表已保存到: {}", output_path);
        }
        Err(e) => {
            println!("❌ 生成历史数据图表失败: {}", e);
        }
    }

    // 7. 演示100%丢包场景测试
    println!("\n🔴 演示100%丢包场景测试...");
    let packet_loss_config_id = 103;
    let packet_loss_data = generate_complete_packet_loss_data(60)?; // 生成60个全丢包数据点

    println!("📊 生成{}个全丢包数据点", packet_loss_data.len());
    for (i, data_point) in packet_loss_data.iter().enumerate() {
        manager
            .update_rrd_data(server_id, packet_loss_config_id, data_point)
            .await?;

        if (i + 1) % 20 == 0 {
            println!(
                "   已写入 {}/{} 个丢包数据点",
                i + 1,
                packet_loss_data.len()
            );
        }
    }
    println!("✅ 全丢包数据写入完成");

    // 为全丢包数据生成图表
    match manager
        .generate_chart(
            server_id,
            packet_loss_config_id,
            "1h",
            &format!("{}-packet-loss", target_address),
            Some(Utc::now() + Duration::hours(1)),
        )
        .await
    {
        Ok(image_data) => {
            let output_path = "latency_chart_packet_loss.png";
            fs::write(output_path, image_data).await?;
            println!("✅ 全丢包图表已保存到: {}", output_path);
        }
        Err(e) => {
            println!("❌ 生成全丢包图表失败: {}", e);
        }
    }

    // 显示全丢包数据统计
    print_packet_loss_statistics(&packet_loss_data);

    println!("\n🎉 RRD 延迟监控示例完成！");
    println!("📋 生成的文件:");
    println!(
        "   - RRD 数据库: {}/server_{}/config_{}.rrd",
        rrd_base_path.display(),
        server_id,
        config_id
    );
    println!(
        "   - RRD 数据库: {}/server_{}/config_{}.rrd",
        rrd_base_path.display(),
        server_id,
        demo_config_id
    );
    println!(
        "   - RRD 数据库: {}/server_{}/config_{}.rrd",
        rrd_base_path.display(),
        server_id,
        packet_loss_config_id
    );
    println!("   - 延迟图表: latency_chart_*.png");
    println!("   - 全丢包图表: latency_chart_packet_loss.png");

    Ok(())
}

/// 生成SmokePing风格的随机延迟数据
///
/// 模拟真实的网络延迟场景：
/// - 基础延迟：20-50ms
/// - 偶尔的高延迟峰值
/// - 随机的丢包事件
/// - 生成20个真实的ping样本
fn generate_random_latency_data(count: usize) -> Result<Vec<RrdDataPoint>> {
    let mut rng = rand::rng();
    let mut data_points = Vec::new();

    let now = Utc::now();
    let base_latency = 170_000; // 30ms 基础延迟（微秒）

    for i in 1..=count {
        // 时间戳：每分钟一个数据点，从现在往前推
        let timestamp = now + Duration::minutes(i as i64);

        // 生成20个ping样本 (SmokePing标准)
        let mut ping_samples = Vec::new();
        for _ping_idx in 0..20 {
            // 决定这个ping是否成功（基于丢包概率）
            let loss_probability = if rng.gen_range(0.0..1.0) < 0.001 {
                // 5% 概率有一些丢包
                rng.gen_range(0.10..0.60) // 5-20% 丢包率
            } else {
                0.0 // 没有丢包
            };

            if rng.gen_range(0.0..1.0) < loss_probability {
                // 这个ping丢包了
                ping_samples.push(None);
            } else {
                // 这个ping成功，生成延迟值
                let latency_variation: f64 = rng.gen_range(-3000.0..3000.0); // ±10ms变化
                let mut latency_us = (base_latency as f64 + latency_variation) as u64;

                // 偶尔产生延迟峰值
                // if rng.gen_range(0.0..1.0) < 0.05 {
                //     latency_us += rng.gen_range(10_000..20_000); // 峰值延迟
                // }

                ping_samples.push(Some(latency_us));
            }
        }

        // 使用ping样本创建数据点
        let uptime = Some(rng.gen_range(3600..86400)); // 1-24小时的运行时间
        let data_point = RrdDataPoint::from_ping_samples(timestamp, ping_samples, uptime);

        data_points.push(data_point);
    }

    Ok(data_points)
}

/// 生成SmokePing风格的历史延迟数据点（用于批量初始化演示）
fn generate_historical_latency_points() -> Result<Vec<LatencyHistoryPoint>> {
    let mut rng = rand::rng();
    let mut points = Vec::new();

    let now = Utc::now();
    let base_latency = 25_000; // 25ms 基础延迟

    // 生成过去24小时的数据，每5分钟一个点
    for i in 1..=288 {
        // 24小时 * 12个点/小时
        let timestamp = now + Duration::minutes(i * 5);

        // 生成20个ping样本
        let mut ping_samples = Vec::new();
        let mut loss_count = 0u32;

        for _ping_idx in 0..20 {
            if rng.gen_range(0.0..1.0) < 0.02 {
                // 2% 概率丢包
                ping_samples.push(None);
                loss_count += 1;
            } else {
                let latency_variation: f64 = rng.gen_range(-5_000.0..10_000.0);
                let latency_us = (base_latency as f64 + latency_variation) as u64;
                ping_samples.push(Some(latency_us));
            }
        }

        // 创建历史数据点 (使用ping样本计算统计数据)
        let temp_data_point =
            RrdDataPoint::from_ping_samples(timestamp, ping_samples.clone(), Some(3600 * 24));

        let point = LatencyHistoryPoint {
            timestamp,
            config_id: 102,
            target_address: "*******-historical".to_string(),
            loss_count,
            median_latency_us: temp_data_point.median_latency_us,
            uptime: Some(3600 * 24), // 24小时
            ping_samples,
            error_msg: None,
            test_round: Some(i as i32),
            total_samples: 20,
            avg_latency_us: temp_data_point.avg_latency_us,
            min_latency_us: temp_data_point.min_latency_us,
            max_latency_us: temp_data_point.max_latency_us,
            stddev_latency_us: temp_data_point.stddev_latency_us,
        };

        points.push(point);
    }

    Ok(points)
}

/// 生成SmokePing风格的100%丢包场景数据
///
/// 模拟网络完全中断的情况：
/// - loss_count = 20 (全部20个ping丢失)
/// - median_latency_us = None
/// - ping_samples = [None; 20] (20个全失败的ping)
fn generate_complete_packet_loss_data(count: usize) -> Result<Vec<RrdDataPoint>> {
    let mut data_points = Vec::new();
    let now = Utc::now();

    for i in 1..=count {
        // 时间戳：每分钟一个数据点，从现在往前推
        let timestamp = now + Duration::minutes(i as i64);

        let data_point = RrdDataPoint::create_full_loss(timestamp, 20);

        data_points.push(data_point);
    }

    Ok(data_points)
}

/// 打印SmokePing风格的全丢包数据统计信息
fn print_packet_loss_statistics(data_points: &[RrdDataPoint]) {
    println!("\n📊 SmokePing风格全丢包数据统计信息:");
    println!("   总数据点数: {}", data_points.len());

    // 验证SmokePing全丢包特征
    let all_full_loss_count = data_points.iter().all(|p| p.loss_count == 20);
    let all_no_median = data_points.iter().all(|p| p.median_latency_us.is_none());
    let all_empty_samples = data_points
        .iter()
        .all(|p| p.ping_samples.iter().all(|s| s.is_none()));
    let all_correct_total = data_points.iter().all(|p| p.total_samples == 20);

    println!("   SmokePing 100%丢包特征验证:");
    println!(
        "     loss_count都为20: {}",
        if all_full_loss_count { "✅" } else { "❌" }
    );
    println!(
        "     所有中位数延迟为None: {}",
        if all_no_median { "✅" } else { "❌" }
    );
    println!(
        "     所有ping样本都失败: {}",
        if all_empty_samples { "✅" } else { "❌" }
    );
    println!(
        "     total_samples都为20: {}",
        if all_correct_total { "✅" } else { "❌" }
    );

    if all_full_loss_count && all_no_median && all_empty_samples && all_correct_total {
        println!("   ✅ 这是标准的SmokePing 100%丢包场景数据");
        println!("   📈 loss字段始终有效，不会产生NaN显示问题");
    } else {
        println!("   ⚠️  数据不符合SmokePing 100%丢包场景特征");
    }

    // 验证成功率和丢包率计算
    if !data_points.is_empty() {
        let first_point = &data_points[0];
        println!("   计算验证:");
        println!(
            "     success_rate(): {:.1}%",
            first_point.success_rate() * 100.0
        );
        println!(
            "     packet_loss_percentage(): {:.1}%",
            first_point.packet_loss_percentage()
        );
    }
}

/// 打印SmokePing风格数据统计信息
fn print_statistics(data_points: &[RrdDataPoint]) {
    println!("\n📊 SmokePing风格数据统计信息:");
    println!("   总数据点数: {}", data_points.len());

    // 统计延迟数据（基于median_latency_us）
    let medians: Vec<u64> = data_points
        .iter()
        .filter_map(|p| p.median_latency_us)
        .collect();

    if !medians.is_empty() {
        let min_median = medians.iter().min().unwrap();
        let max_median = medians.iter().max().unwrap();
        let avg_median = medians.iter().sum::<u64>() / medians.len() as u64;

        println!("   中位数延迟统计 (微秒):");
        println!(
            "     最小中位数: {} µs ({:.2} ms)",
            min_median,
            *min_median as f64 / 1000.0
        );
        println!(
            "     最大中位数: {} µs ({:.2} ms)",
            max_median,
            *max_median as f64 / 1000.0
        );
        println!(
            "     平均中位数: {} µs ({:.2} ms)",
            avg_median,
            avg_median as f64 / 1000.0
        );
    }

    // SmokePing风格统计
    let loss_counts: Vec<u32> = data_points.iter().map(|p| p.loss_count).collect();
    let avg_loss_count = loss_counts.iter().sum::<u32>() as f64 / loss_counts.len() as f64;
    let avg_loss_percentage = (avg_loss_count / 20.0) * 100.0;

    println!("   SmokePing丢包统计:");
    println!("     平均丢包计数: {:.1}/20", avg_loss_count);
    println!("     平均丢包率: {:.2}%", avg_loss_percentage);

    // 统计有效ping样本
    let total_ping_attempts: usize = data_points.iter().map(|p| p.ping_samples.len()).sum();
    let successful_pings: usize = data_points
        .iter()
        .map(|p| p.ping_samples.iter().filter(|s| s.is_some()).count())
        .sum();

    println!("   Ping样本统计:");
    println!("     总ping尝试数: {}", total_ping_attempts);
    println!("     成功ping数: {}", successful_pings);
    println!(
        "     整体成功率: {:.2}%",
        (successful_pings as f64 / total_ping_attempts as f64) * 100.0
    );
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_complete_workflow() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let manager = RrdManager::new(temp_dir.path());

        let server_id = 999;
        let config_id = 888;

        // 创建 RRD 文件
        manager.create_rrd_file(server_id, config_id).await?;
        assert!(manager.rrd_file_exists(server_id, config_id));

        // 生成并写入测试数据
        let data_points = generate_random_latency_data(10)?;
        for data_point in &data_points {
            manager
                .update_rrd_data(server_id, config_id, data_point)
                .await?;
        }

        // 生成图表
        let chart_data = manager
            .generate_chart(
                server_id,
                config_id,
                "1h",
                "test.example.com",
                Some(Utc::now() + Duration::hours(1)),
            )
            .await?;
        assert!(!chart_data.is_empty());

        Ok(())
    }

    #[test]
    fn test_data_generation() {
        let data_points = generate_random_latency_data(50).unwrap();
        assert_eq!(data_points.len(), 50);

        // 验证SmokePing数据合理性
        for point in &data_points {
            // SmokePing基本约束
            assert!(point.loss_count <= 20);
            assert_eq!(point.total_samples, 20);
            assert_eq!(point.ping_samples.len(), 20);

            // 计算方法验证
            assert!(point.success_rate() >= 0.0 && point.success_rate() <= 1.0);
            assert!(
                point.packet_loss_percentage() >= 0.0 && point.packet_loss_percentage() <= 100.0
            );

            // 如果有成功的ping，应该有延迟数据
            let successful_pings = point.ping_samples.iter().filter(|s| s.is_some()).count();
            assert_eq!(successful_pings, (20 - point.loss_count) as usize);

            if successful_pings > 0 {
                // 有成功的ping时，应该能计算出统计数据
                assert!(point.median_latency_us.is_some() || point.avg_latency_us.is_some());
            }
        }
    }

    #[test]
    fn test_complete_packet_loss_data_generation() {
        let packet_loss_data = generate_complete_packet_loss_data(30).unwrap();
        assert_eq!(packet_loss_data.len(), 30);

        // 验证SmokePing 100%丢包数据特征
        for point in &packet_loss_data {
            // SmokePing特征：loss_count = 20 (全部丢包)
            assert_eq!(point.loss_count, 20);
            assert_eq!(point.total_samples, 20);

            // 所有延迟字段都应该是None
            assert!(point.median_latency_us.is_none());
            assert!(point.avg_latency_us.is_none());
            assert!(point.min_latency_us.is_none());
            assert!(point.max_latency_us.is_none());
            assert!(point.stddev_latency_us.is_none());

            // 所有ping样本都应该失败
            assert_eq!(point.ping_samples.len(), 20);
            assert!(point.ping_samples.iter().all(|s| s.is_none()));

            // 验证计算方法
            assert_eq!(point.success_rate(), 0.0);
            assert_eq!(point.packet_loss_percentage(), 100.0);
        }
    }

    #[tokio::test]
    async fn test_packet_loss_scenario_workflow() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let manager = RrdManager::new(temp_dir.path());

        let server_id = 999;
        let config_id = 777;

        // 创建 RRD 文件
        manager.create_rrd_file(server_id, config_id).await?;
        assert!(manager.rrd_file_exists(server_id, config_id));

        // 生成并写入100%丢包测试数据
        let packet_loss_data = generate_complete_packet_loss_data(10)?;
        for data_point in &packet_loss_data {
            manager
                .update_rrd_data(server_id, config_id, data_point)
                .await?;
        }

        // 生成图表（应该能够处理100%丢包场景而不出错）
        let chart_data = manager
            .generate_chart(
                server_id,
                config_id,
                "1h",
                "test-packet-loss.example.com",
                Some(Utc::now() + Duration::hours(1)),
            )
            .await?;
        assert!(!chart_data.is_empty());

        Ok(())
    }
}
